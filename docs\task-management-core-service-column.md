# 任务管理添加Core服务列

## 功能概述

在任务管理界面中新增"Core服务"列，显示每个任务在哪个Core服务下运行，便于多Core服务环境下的任务管理和调试。

## 修改内容

### 1. 后端API修改

#### 任务列表API (`backend/app/api/task.py`)

**文件**: `backend/app/api/task.py`

在任务数据格式化中添加Core服务信息：

```python
formatted_task = {
    "id": task.get("task_id", task.get("id", "")),
    "platform_id": platform_id_val,
    "platform_name": platform_name,
    "account_id": account_id,
    "account_name": account_name,
    "device_id": task.get("device_id", ""),
    "content_path": task.get("content_path", ""),
    "status": task.get("status", "unknown"),
    "progress": task.get("progress", 0),
    "task_type": task.get("task_type", "single"),
    "created_at": task.get("created_at", ""),
    "updated_at": task.get("updated_at", ""),
    "start_time": task.get("start_time", ""),
    "workflow_id": task.get("workflow_id", ""),
    "workflow_name": workflow_name,
    "content_type": content_type,
    "video_file": task.get("video_file", ""),
    # 🔧 新增：Core服务信息
    "core_service_id": task.get("core_service_id", "default"),
    "backend_instance_id": task.get("backend_instance_id", "")
}
```

**修改的API端点**：
- `GET /api/tasks/` - 获取任务列表
- `GET /api/tasks/running` - 获取运行中任务

### 2. 前端界面修改

#### 任务管理页面 (`frontend/src/views/task/Management.vue`)

**新增表格列**：
```vue
<!-- 🔧 新增：Core服务列 -->
<el-table-column label="Core服务" width="120" align="center">
  <template #default="{ row }">
    <el-tag 
      :type="getCoreServiceTagType(row.core_service_id)" 
      size="small"
      effect="plain"
    >
      {{ getCoreServiceName(row.core_service_id) }}
    </el-tag>
  </template>
</el-table-column>
```

**任务详情弹窗**：
```vue
<el-descriptions-item label="Core服务">
  <el-tag 
    :type="getCoreServiceTagType(selectedTask.core_service_id)" 
    size="small"
    effect="plain"
  >
    {{ getCoreServiceName(selectedTask.core_service_id) }}
  </el-tag>
</el-descriptions-item>
```

**JavaScript方法**：
```javascript
// 🔧 新增：获取Core服务名称
const getCoreServiceName = (coreServiceId) => {
  if (!coreServiceId || coreServiceId === 'default') {
    return '默认服务'
  }
  return `Core-${coreServiceId}`
}

// 🔧 新增：获取Core服务标签类型
const getCoreServiceTagType = (coreServiceId) => {
  if (!coreServiceId || coreServiceId === 'default') {
    return 'info'
  }
  // 根据Core服务ID生成不同颜色
  const colors = ['primary', 'success', 'warning', 'danger']
  const hash = coreServiceId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  return colors[Math.abs(hash) % colors.length]
}
```

#### 任务详情组件 (`frontend/src/components/TaskDetail.vue`)

同样添加了Core服务信息显示和相关方法。

## 功能特性

### 1. 视觉标识

- **默认服务**：使用灰色`info`标签显示"默认服务"
- **其他Core服务**：使用不同颜色标签显示`Core-{service_id}`
- **颜色算法**：根据Core服务ID生成一致的颜色，相同ID总是显示相同颜色

### 2. 显示位置

- **任务列表**：在账号列后面新增"Core服务"列
- **任务详情**：在设备信息后面显示Core服务信息
- **宽度设置**：列宽120px，适合显示Core服务标签

### 3. 数据来源

- **core_service_id**：任务运行的Core服务ID
- **backend_instance_id**：启动任务的Backend实例ID（用于调试）
- **默认值**：如果没有设置，显示为"default"

## 使用场景

### 1. 多Core服务环境

在有多个Core服务的环境中，可以清楚看到：
- 哪些任务在哪个Core服务上运行
- 不同Core服务的负载分布
- 任务分配是否均衡

### 2. 问题排查

当任务出现问题时，可以：
- 快速定位是哪个Core服务的问题
- 检查特定Core服务的任务执行情况
- 分析不同Core服务的成功率

### 3. 性能监控

- 观察不同Core服务的任务处理速度
- 识别性能瓶颈所在的Core服务
- 优化任务分配策略

## 界面效果

### 任务列表界面
```
任务信息          | 平台    | 账号      | Core服务    | 创建时间
task-abc123      | YouTube | 测试账号   | 默认服务     | 2025-07-22 10:00
task-def456      | YouTube | 测试账号2  | Core-server1 | 2025-07-22 10:01
task-ghi789      | 抖音    | 抖音账号   | Core-server2 | 2025-07-22 10:02
```

### 标签颜色示例
- `默认服务` - 灰色标签
- `Core-server1` - 蓝色标签
- `Core-server2` - 绿色标签
- `Core-server3` - 橙色标签

## 兼容性

- ✅ **向后兼容**：旧任务没有core_service_id时显示"默认服务"
- ✅ **数据完整性**：不影响现有任务数据结构
- ✅ **界面适配**：表格列宽自动适应不同长度的Core服务ID
- ✅ **响应式设计**：在不同屏幕尺寸下正常显示

## 技术实现

### 1. 数据流

```
MongoDB任务数据 → Backend API → 前端任务列表 → 界面显示
     ↓
包含core_service_id字段
```

### 2. 颜色生成算法

使用简单的哈希算法为不同的Core服务ID生成一致的颜色：
```javascript
const hash = coreServiceId.split('').reduce((a, b) => {
  a = ((a << 5) - a) + b.charCodeAt(0)
  return a & a
}, 0)
return colors[Math.abs(hash) % colors.length]
```

### 3. 性能考虑

- 颜色计算在前端进行，不增加后端负担
- 使用简单的字符串处理，性能开销很小
- 标签组件轻量级，不影响列表渲染性能

## 总结

通过添加Core服务列，任务管理界面现在可以清晰显示每个任务的运行环境，大大提升了多Core服务环境下的管理效率和问题排查能力。这个功能对于系统管理员和开发人员来说非常有用，特别是在需要监控和调试分布式任务执行环境时。
