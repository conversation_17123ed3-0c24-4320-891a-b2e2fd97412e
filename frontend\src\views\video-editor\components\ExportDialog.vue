<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="导出视频"
    width="700px"
    :before-close="handleClose"
  >
    <div class="export-dialog">
      <!-- 预设选择 -->
      <div class="export-presets">
        <h4>导出预设</h4>
        <el-radio-group v-model="selectedPreset" @change="onPresetChange">
          <div class="preset-grid">
            <el-radio 
              v-for="preset in exportPresets" 
              :key="preset.id"
              :label="preset.id"
              class="preset-item"
            >
              <div class="preset-content">
                <div class="preset-icon">
                  <el-icon><VideoCamera /></el-icon>
                </div>
                <div class="preset-info">
                  <div class="preset-name">{{ preset.name }}</div>
                  <div class="preset-desc">{{ preset.description }}</div>
                </div>
              </div>
            </el-radio>
          </div>
        </el-radio-group>
      </div>
      
      <el-divider />
      
      <!-- 详细设置 -->
      <el-form :model="exportForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>视频设置</h4>
            
            <el-form-item label="格式">
              <el-select v-model="exportForm.format" @change="onFormatChange">
                <el-option label="MP4 (H.264)" value="mp4" />
                <el-option label="MOV (QuickTime)" value="mov" />
                <el-option label="AVI" value="avi" />
                <el-option label="WebM" value="webm" />
                <el-option label="MKV" value="mkv" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="分辨率">
              <el-select v-model="exportForm.resolution">
                <el-option label="原始分辨率" value="original" />
                <el-option label="4K (3840×2160)" value="4k" />
                <el-option label="2K (2560×1440)" value="2k" />
                <el-option label="Full HD (1920×1080)" value="1080p" />
                <el-option label="HD (1280×720)" value="720p" />
                <el-option label="SD (854×480)" value="480p" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="帧率">
              <el-select v-model="exportForm.frameRate">
                <el-option label="原始帧率" value="original" />
                <el-option label="60 FPS" :value="60" />
                <el-option label="30 FPS" :value="30" />
                <el-option label="25 FPS" :value="25" />
                <el-option label="24 FPS" :value="24" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="质量">
              <el-slider 
                v-model="exportForm.quality" 
                :min="1" 
                :max="100" 
                :marks="qualityMarks"
                @change="onQualityChange"
              />
              <div class="quality-info">
                <span>{{ getQualityText(exportForm.quality) }}</span>
                <span class="file-size-estimate">预估大小: {{ estimatedFileSize }}</span>
              </div>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <h4>音频设置</h4>
            
            <el-form-item label="音频编码">
              <el-select v-model="exportForm.audioCodec">
                <el-option label="AAC" value="aac" />
                <el-option label="MP3" value="mp3" />
                <el-option label="FLAC" value="flac" />
                <el-option label="PCM" value="pcm" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="音频质量">
              <el-select v-model="exportForm.audioBitrate">
                <el-option label="320 kbps (最高)" value="320" />
                <el-option label="256 kbps (高)" value="256" />
                <el-option label="192 kbps (中)" value="192" />
                <el-option label="128 kbps (标准)" value="128" />
                <el-option label="96 kbps (低)" value="96" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="采样率">
              <el-select v-model="exportForm.audioSampleRate">
                <el-option label="48 kHz" value="48000" />
                <el-option label="44.1 kHz" value="44100" />
                <el-option label="22 kHz" value="22050" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="声道">
              <el-radio-group v-model="exportForm.audioChannels">
                <el-radio :label="1">单声道</el-radio>
                <el-radio :label="2">立体声</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-divider />
        
        <!-- 高级设置 -->
        <div class="advanced-settings">
          <h4>高级设置</h4>
          
          <el-form-item label="导出范围">
            <el-radio-group v-model="exportForm.exportRange">
              <el-radio label="all">整个项目</el-radio>
              <el-radio label="selection">选中片段</el-radio>
              <el-radio label="custom">自定义范围</el-radio>
            </el-radio-group>
            
            <div v-if="exportForm.exportRange === 'custom'" class="time-range">
              <el-input-number 
                v-model="exportForm.startTime" 
                :min="0" 
                :precision="2"
                placeholder="开始时间(秒)"
                style="width: 120px; margin-right: 8px;"
              />
              <span>到</span>
              <el-input-number 
                v-model="exportForm.endTime" 
                :min="0" 
                :precision="2"
                placeholder="结束时间(秒)"
                style="width: 120px; margin-left: 8px;"
              />
            </div>
          </el-form-item>
          
          <el-form-item label="输出路径">
            <el-input 
              v-model="exportForm.outputPath" 
              placeholder="选择输出路径"
              readonly
            >
              <template #append>
                <el-button @click="selectOutputPath">浏览</el-button>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="文件名">
            <el-input 
              v-model="exportForm.fileName" 
              placeholder="输入文件名"
            />
          </el-form-item>
          
          <el-form-item label="其他选项">
            <el-checkbox v-model="exportForm.includeAudio">包含音频</el-checkbox>
            <el-checkbox v-model="exportForm.hardwareAcceleration">硬件加速</el-checkbox>
            <el-checkbox v-model="exportForm.openAfterExport">导出后打开文件夹</el-checkbox>
          </el-form-item>
        </div>
      </el-form>
      
      <!-- 导出进度 -->
      <div v-if="isExporting" class="export-progress">
        <el-progress 
          :percentage="exportProgress" 
          :status="exportStatus"
          :stroke-width="8"
        />
        <div class="progress-info">
          <span>{{ exportStatusText }}</span>
          <span v-if="exportProgress > 0">{{ exportProgress }}%</span>
        </div>
        <div class="progress-details">
          <span>已处理: {{ processedFrames }} / {{ totalFrames }} 帧</span>
          <span>剩余时间: {{ estimatedTimeLeft }}</span>
        </div>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose" :disabled="isExporting">取消</el-button>
      <el-button 
        type="primary" 
        @click="startExport" 
        :loading="isExporting"
        :disabled="!canExport"
      >
        {{ isExporting ? '导出中...' : '开始导出' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoCamera } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  project: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'export-started': []
}>()

// 响应式数据
const selectedPreset = ref('high-quality')
const isExporting = ref(false)
const exportProgress = ref(0)
const exportStatus = ref('')
const exportStatusText = ref('')
const processedFrames = ref(0)
const totalFrames = ref(0)

const exportForm = ref({
  format: 'mp4',
  resolution: 'original',
  frameRate: 'original',
  quality: 80,
  audioCodec: 'aac',
  audioBitrate: '192',
  audioSampleRate: '48000',
  audioChannels: 2,
  exportRange: 'all',
  startTime: 0,
  endTime: 0,
  outputPath: '',
  fileName: '',
  includeAudio: true,
  hardwareAcceleration: true,
  openAfterExport: true
})

// 导出预设
const exportPresets = [
  {
    id: 'high-quality',
    name: '高质量',
    description: '最佳画质，文件较大',
    settings: {
      format: 'mp4',
      resolution: 'original',
      quality: 90,
      audioBitrate: '320'
    }
  },
  {
    id: 'standard',
    name: '标准质量',
    description: '平衡画质和文件大小',
    settings: {
      format: 'mp4',
      resolution: 'original',
      quality: 75,
      audioBitrate: '192'
    }
  },
  {
    id: 'web-optimized',
    name: '网络优化',
    description: '适合网络分享，文件较小',
    settings: {
      format: 'mp4',
      resolution: '1080p',
      quality: 60,
      audioBitrate: '128'
    }
  },
  {
    id: 'mobile',
    name: '移动设备',
    description: '适合手机播放',
    settings: {
      format: 'mp4',
      resolution: '720p',
      quality: 65,
      audioBitrate: '128'
    }
  }
]

// 质量标记
const qualityMarks = {
  20: '低',
  50: '中',
  80: '高',
  100: '最高'
}

// 计算属性
const canExport = computed(() => {
  return exportForm.value.fileName && exportForm.value.outputPath && !isExporting.value
})

const estimatedFileSize = computed(() => {
  // 简单的文件大小估算
  const duration = props.project?.timeline?.totalDuration || 60
  const quality = exportForm.value.quality
  const resolution = exportForm.value.resolution
  
  let baseSizeMB = duration * 0.5 // 基础大小
  
  // 根据质量调整
  baseSizeMB *= (quality / 100)
  
  // 根据分辨率调整
  const resolutionMultiplier: Record<string, number> = {
    '4k': 4,
    '2k': 2.5,
    '1080p': 1,
    '720p': 0.5,
    '480p': 0.25,
    'original': 1
  }
  
  baseSizeMB *= resolutionMultiplier[resolution] || 1
  
  if (baseSizeMB < 1) {
    return `${Math.round(baseSizeMB * 1024)} KB`
  } else if (baseSizeMB < 1024) {
    return `${Math.round(baseSizeMB)} MB`
  } else {
    return `${(baseSizeMB / 1024).toFixed(1)} GB`
  }
})

const estimatedTimeLeft = computed(() => {
  if (exportProgress.value === 0) return '--'
  
  const elapsed = 30 // 假设已经过了30秒
  const remaining = (elapsed / exportProgress.value * 100) - elapsed
  
  if (remaining < 60) {
    return `${Math.round(remaining)}秒`
  } else {
    return `${Math.round(remaining / 60)}分钟`
  }
})

// 监听项目变化
watch(() => props.project, (newProject) => {
  if (newProject) {
    exportForm.value.fileName = `${newProject.name}_${new Date().toISOString().slice(0, 10)}`
    exportForm.value.endTime = newProject.timeline?.totalDuration || 0
    totalFrames.value = Math.round((newProject.timeline?.totalDuration || 0) * (newProject.settings?.frameRate || 30))
  }
})

// 预设变化处理
const onPresetChange = (presetId: string) => {
  const preset = exportPresets.find(p => p.id === presetId)
  if (preset) {
    Object.assign(exportForm.value, preset.settings)
  }
}

// 格式变化处理
const onFormatChange = (format: string) => {
  // 根据格式调整音频编码
  const formatCodecMap: Record<string, string> = {
    mp4: 'aac',
    mov: 'aac',
    avi: 'mp3',
    webm: 'opus',
    mkv: 'aac'
  }
  
  exportForm.value.audioCodec = formatCodecMap[format] || 'aac'
}

// 质量变化处理
const onQualityChange = (quality: number) => {
  // 可以在这里添加实时预览逻辑
}

// 获取质量文本
const getQualityText = (quality: number): string => {
  if (quality >= 90) return '最高质量'
  if (quality >= 75) return '高质量'
  if (quality >= 50) return '中等质量'
  if (quality >= 25) return '低质量'
  return '最低质量'
}

// 选择输出路径
const selectOutputPath = () => {
  // 这里应该调用文件选择器
  // 暂时使用默认路径
  exportForm.value.outputPath = '/Users/<USER>/Videos'
}

// 开始导出
const startExport = async () => {
  try {
    isExporting.value = true
    exportProgress.value = 0
    exportStatus.value = ''
    exportStatusText.value = '准备导出...'
    processedFrames.value = 0
    
    // 构建导出参数
    const exportParams = {
      projectId: props.project.id,
      ...exportForm.value
    }
    
    // 这里应该调用导出API
    // const response = await exportVideo(exportParams)
    
    // 模拟导出进度
    simulateExportProgress()
    
    emit('export-started')
    
  } catch (error) {
    ElMessage.error('导出失败，请重试')
    isExporting.value = false
  }
}

// 模拟导出进度
const simulateExportProgress = () => {
  const interval = setInterval(() => {
    exportProgress.value += Math.random() * 5
    processedFrames.value = Math.round(totalFrames.value * exportProgress.value / 100)
    
    if (exportProgress.value < 30) {
      exportStatusText.value = '分析视频轨道...'
    } else if (exportProgress.value < 60) {
      exportStatusText.value = '渲染视频帧...'
    } else if (exportProgress.value < 90) {
      exportStatusText.value = '编码音频...'
    } else {
      exportStatusText.value = '完成导出...'
    }
    
    if (exportProgress.value >= 100) {
      clearInterval(interval)
      exportProgress.value = 100
      exportStatus.value = 'success'
      exportStatusText.value = '导出完成'
      
      setTimeout(() => {
        isExporting.value = false
        ElMessage.success('视频导出成功')
        emit('update:modelValue', false)
      }, 1000)
    }
  }, 500)
}

// 处理关闭
const handleClose = () => {
  if (isExporting.value) {
    ElMessage.warning('导出进行中，无法关闭')
    return
  }
  emit('update:modelValue', false)
}
</script>

<style scoped lang="scss">
.export-dialog {
  .export-presets {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #333333;
    }
    
    .preset-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }
    
    .preset-item {
      margin: 0;
      
      :deep(.el-radio__input) {
        display: none;
      }
      
      :deep(.el-radio__label) {
        padding: 0;
      }
      
      .preset-content {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #409eff;
          background: #f0f9ff;
        }
        
        .preset-icon {
          width: 40px;
          height: 40px;
          background: #f5f7fa;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          
          .el-icon {
            font-size: 20px;
            color: #409eff;
          }
        }
        
        .preset-info {
          flex: 1;
          
          .preset-name {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .preset-desc {
            font-size: 12px;
            color: #999999;
          }
        }
      }
      
      &.is-checked .preset-content {
        border-color: #409eff;
        background: #f0f9ff;
      }
    }
  }
  
  h4 {
    margin: 0 0 16px 0;
    color: #333333;
    font-size: 16px;
  }
  
  .quality-info {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666666;
    
    .file-size-estimate {
      color: #409eff;
    }
  }
  
  .time-range {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .advanced-settings {
    margin-top: 20px;
  }
  
  .export-progress {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      font-size: 14px;
      color: #333333;
    }
    
    .progress-details {
      display: flex;
      justify-content: space-between;
      margin-top: 4px;
      font-size: 12px;
      color: #666666;
    }
  }
}

:deep(.el-form-item__label) {
  color: #333333;
  font-weight: 500;
}

:deep(.el-checkbox) {
  margin-right: 16px;
  margin-bottom: 8px;
}
</style>
