# 任务管理Core服务显示名称和ID

## 功能概述

在任务管理界面中，Core服务列现在显示详细的服务信息，包括服务名称和服务ID，格式为"服务名称 (服务ID)"，提供更清晰的Core服务识别。

## 修改内容

### 1. 后端API支持

后端已经提供了完整的Core服务信息：

**Core服务API** (`backend/app/api/core.py`):
```python
# 获取Core服务列表
@router.get("/cores")
async def get_core_services():
    return [
        {
            "id": "core-123.2",
            "name": "雷电模拟器服务器",
            "host": "***************",
            "port": 50051
        }
    ]
```

**Core服务配置** (`core/config/core_config.yaml`):
```yaml
core:
  id: "core-123.2"
  name: "雷电模拟器服务器"
  service_name: "thunderhub-core"
```

### 2. 前端显示逻辑

#### 任务管理页面 (`frontend/src/views/task/Management.vue`)

**获取Core服务列表**：
```javascript
// 🔧 新增：获取Core服务列表
const fetchCoreServices = async () => {
  try {
    const response = await getCoreServices()
    coreServices.value = response.data || []
    console.log(`获取到 ${coreServices.value.length} 个Core服务:`, coreServices.value)
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    // 设置默认服务，避免显示异常
    coreServices.value = [
      {
        id: 'default',
        name: '默认Core服务'
      }
    ]
  }
}
```

**Core服务信息处理**：
```javascript
// 🔧 新增：获取Core服务信息
const getCoreServiceInfo = (coreServiceId) => {
  if (!coreServiceId || coreServiceId === 'default') {
    return {
      name: '默认服务',
      id: 'default',
      displayText: '默认服务'
    }
  }
  
  // 从Core服务列表中查找
  const service = coreServices.value.find(s => s.id === coreServiceId)
  if (service) {
    return {
      name: service.name,
      id: service.id,
      displayText: `${service.name} (${service.id})`
    }
  }
  
  // 如果没找到，使用默认格式
  return {
    name: `Core-${coreServiceId}`,
    id: coreServiceId,
    displayText: `Core-${coreServiceId}`
  }
}
```

**界面显示**：
```vue
<!-- Core服务列 -->
<el-table-column label="Core服务" width="120" align="center">
  <template #default="{ row }">
    <el-tag 
      :type="getCoreServiceTagType(row.core_service_id)" 
      size="small"
      effect="plain"
    >
      {{ getCoreServiceName(row.core_service_id) }}
    </el-tag>
  </template>
</el-table-column>
```

#### 任务详情组件 (`frontend/src/components/TaskDetail.vue`)

同样的逻辑也应用到任务详情组件中，确保在任务详情弹窗中也能看到完整的Core服务信息。

### 3. 显示效果

#### 不同Core服务的显示示例

| Core服务ID | Core服务名称 | 显示文本 | 标签颜色 |
|------------|-------------|----------|----------|
| `default` | - | `默认服务` | 灰色 (info) |
| `core-123.2` | `雷电模拟器服务器` | `雷电模拟器服务器 (core-123.2)` | 蓝色 (primary) |
| `core-server1` | `测试服务器1` | `测试服务器1 (core-server1)` | 绿色 (success) |
| `core-server2` | `生产服务器` | `生产服务器 (core-server2)` | 橙色 (warning) |

#### 界面效果

**任务列表界面**：
```
任务信息          | 平台    | 账号      | Core服务                        | 创建时间
task-abc123      | YouTube | 测试账号   | 默认服务                        | 2025-07-22 10:00
task-def456      | YouTube | 测试账号2  | 雷电模拟器服务器 (core-123.2)    | 2025-07-22 10:01
task-ghi789      | 抖音    | 抖音账号   | 测试服务器1 (core-server1)      | 2025-07-22 10:02
```

**任务详情弹窗**：
```
基本信息
├── 平台: YouTube
├── 账号: 测试账号
├── 设备: 32
├── Core服务: [雷电模拟器服务器 (core-123.2)]  # 带颜色标签
└── 进度: 45%
```

## 技术实现

### 1. 数据流

```
Core服务配置 → Consul注册 → Backend API → 前端获取 → 界面显示
     ↓              ↓           ↓          ↓         ↓
  core_config.yaml  服务发现    /api/cores  coreServices  显示名称+ID
```

### 2. 缓存机制

- **前端缓存**：Core服务列表在组件挂载时获取一次，避免重复请求
- **降级处理**：如果获取失败，显示默认服务，确保界面正常工作
- **实时更新**：支持Core服务动态注册和注销

### 3. 颜色算法

使用一致性哈希算法为不同Core服务生成固定颜色：
```javascript
const getCoreServiceTagType = (coreServiceId) => {
  if (!coreServiceId || coreServiceId === 'default') {
    return 'info'
  }
  const colors = ['primary', 'success', 'warning', 'danger']
  const hash = coreServiceId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  return colors[Math.abs(hash) % colors.length]
}
```

## 配置示例

### Core服务配置

**配置文件** (`core/config/core_config.yaml`):
```yaml
core:
  id: "core-production-01"
  name: "生产环境Core服务器01"
  service_name: "thunderhub-core"
  grpc_port: 50051
```

**环境变量**:
```bash
export CORE_ID="core-production-01"
export CORE_NAME="生产环境Core服务器01"
```

### 显示效果

配置后的显示效果：
- **标签文本**: `生产环境Core服务器01 (core-production-01)`
- **标签颜色**: 根据ID哈希生成的一致颜色
- **工具提示**: 鼠标悬停显示完整信息

## 优势

### 1. 清晰识别

- **服务名称**：提供人类可读的服务描述
- **服务ID**：提供唯一的技术标识
- **组合显示**：同时显示名称和ID，信息完整

### 2. 管理便利

- **快速定位**：通过名称快速识别服务用途
- **精确操作**：通过ID进行精确的技术操作
- **状态监控**：不同颜色标签便于状态识别

### 3. 扩展性

- **动态发现**：支持Core服务的动态注册
- **配置灵活**：支持自定义服务名称
- **多环境**：支持开发、测试、生产环境的区分

## 兼容性

- ✅ **向后兼容**：旧的Core服务ID仍然正常显示
- ✅ **降级处理**：API失败时显示默认服务
- ✅ **配置可选**：服务名称为可选配置，有默认值
- ✅ **界面适配**：自动调整列宽适应不同长度的文本

## 总结

通过显示Core服务的名称和ID，任务管理界面现在提供了更丰富和清晰的服务信息。管理员可以通过服务名称快速理解服务用途，同时通过服务ID进行精确的技术操作。这个改进大大提升了多Core服务环境下的管理体验和操作效率。
