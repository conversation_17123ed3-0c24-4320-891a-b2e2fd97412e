# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import task_pb2 as task__pb2

GRPC_GENERATED_VERSION = '1.72.0rc1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in task_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class TaskServiceStub(object):
    """任务服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateTask = channel.unary_unary(
                '/task.TaskService/CreateTask',
                request_serializer=task__pb2.TaskRequest.SerializeToString,
                response_deserializer=task__pb2.TaskResponse.FromString,
                _registered_method=True)
        self.StartTask = channel.unary_unary(
                '/task.TaskService/StartTask',
                request_serializer=task__pb2.TaskIdRequest.SerializeToString,
                response_deserializer=task__pb2.TaskResponse.FromString,
                _registered_method=True)
        self.PauseTask = channel.unary_unary(
                '/task.TaskService/PauseTask',
                request_serializer=task__pb2.TaskIdRequest.SerializeToString,
                response_deserializer=task__pb2.TaskResponse.FromString,
                _registered_method=True)
        self.CancelTask = channel.unary_unary(
                '/task.TaskService/CancelTask',
                request_serializer=task__pb2.TaskIdRequest.SerializeToString,
                response_deserializer=task__pb2.TaskResponse.FromString,
                _registered_method=True)
        self.GetTaskStatus = channel.unary_unary(
                '/task.TaskService/GetTaskStatus',
                request_serializer=task__pb2.TaskIdRequest.SerializeToString,
                response_deserializer=task__pb2.TaskStatusResponse.FromString,
                _registered_method=True)
        self.GetTaskLogs = channel.unary_unary(
                '/task.TaskService/GetTaskLogs',
                request_serializer=task__pb2.TaskIdRequest.SerializeToString,
                response_deserializer=task__pb2.TaskLogsResponse.FromString,
                _registered_method=True)
        self.GetWorkflowConfig = channel.unary_unary(
                '/task.TaskService/GetWorkflowConfig',
                request_serializer=task__pb2.WorkflowConfigRequest.SerializeToString,
                response_deserializer=task__pb2.WorkflowConfigResponse.FromString,
                _registered_method=True)


class TaskServiceServicer(object):
    """任务服务
    """

    def CreateTask(self, request, context):
        """创建任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartTask(self, request, context):
        """开始执行任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PauseTask(self, request, context):
        """暂停任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelTask(self, request, context):
        """取消任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskStatus(self, request, context):
        """获取任务状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskLogs(self, request, context):
        """获取任务日志
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWorkflowConfig(self, request, context):
        """获取工作流配置
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TaskServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateTask,
                    request_deserializer=task__pb2.TaskRequest.FromString,
                    response_serializer=task__pb2.TaskResponse.SerializeToString,
            ),
            'StartTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StartTask,
                    request_deserializer=task__pb2.TaskIdRequest.FromString,
                    response_serializer=task__pb2.TaskResponse.SerializeToString,
            ),
            'PauseTask': grpc.unary_unary_rpc_method_handler(
                    servicer.PauseTask,
                    request_deserializer=task__pb2.TaskIdRequest.FromString,
                    response_serializer=task__pb2.TaskResponse.SerializeToString,
            ),
            'CancelTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelTask,
                    request_deserializer=task__pb2.TaskIdRequest.FromString,
                    response_serializer=task__pb2.TaskResponse.SerializeToString,
            ),
            'GetTaskStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskStatus,
                    request_deserializer=task__pb2.TaskIdRequest.FromString,
                    response_serializer=task__pb2.TaskStatusResponse.SerializeToString,
            ),
            'GetTaskLogs': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskLogs,
                    request_deserializer=task__pb2.TaskIdRequest.FromString,
                    response_serializer=task__pb2.TaskLogsResponse.SerializeToString,
            ),
            'GetWorkflowConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWorkflowConfig,
                    request_deserializer=task__pb2.WorkflowConfigRequest.FromString,
                    response_serializer=task__pb2.WorkflowConfigResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'task.TaskService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('task.TaskService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TaskService(object):
    """任务服务
    """

    @staticmethod
    def CreateTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/task.TaskService/CreateTask',
            task__pb2.TaskRequest.SerializeToString,
            task__pb2.TaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/task.TaskService/StartTask',
            task__pb2.TaskIdRequest.SerializeToString,
            task__pb2.TaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PauseTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/task.TaskService/PauseTask',
            task__pb2.TaskIdRequest.SerializeToString,
            task__pb2.TaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/task.TaskService/CancelTask',
            task__pb2.TaskIdRequest.SerializeToString,
            task__pb2.TaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTaskStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/task.TaskService/GetTaskStatus',
            task__pb2.TaskIdRequest.SerializeToString,
            task__pb2.TaskStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTaskLogs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/task.TaskService/GetTaskLogs',
            task__pb2.TaskIdRequest.SerializeToString,
            task__pb2.TaskLogsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetWorkflowConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/task.TaskService/GetWorkflowConfig',
            task__pb2.WorkflowConfigRequest.SerializeToString,
            task__pb2.WorkflowConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
