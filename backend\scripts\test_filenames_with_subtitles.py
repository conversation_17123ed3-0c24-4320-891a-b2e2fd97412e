#!/usr/bin/env python3
"""
测试文件名+字幕内容功能
"""

import os
import sys
import tempfile
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def create_test_files(test_dir):
    """创建测试文件"""
    # 创建视频文件（空文件用于测试）
    video_files = [
        "搞笑视频1.mp4",
        "教程视频2.avi",
        "音乐MV3.mov"
    ]
    
    for video_file in video_files:
        video_path = os.path.join(test_dir, video_file)
        with open(video_path, 'w') as f:
            f.write("")
    
    # 创建对应的字幕文件
    subtitle_contents = {
        "搞笑视频1": "这是一个非常搞笑的视频，里面有很多有趣的内容，让人忍俊不禁。主要讲述了一个小猫咪的日常生活。",
        "教程视频2": "本教程将教你如何使用Python进行数据分析，包括pandas、numpy等库的使用方法。",
        "音乐MV3": "这是一首动听的歌曲，歌词优美，旋律动人，表达了对美好生活的向往。"
    }
    
    # 在主目录创建txt字幕文件
    for name, content in subtitle_contents.items():
        subtitle_path = os.path.join(test_dir, f"{name}.txt")
        with open(subtitle_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    # 在subtitles子目录创建srt字幕文件
    subtitles_dir = os.path.join(test_dir, "subtitles")
    os.makedirs(subtitles_dir, exist_ok=True)
    
    srt_content_template = """1
00:00:00,000 --> 00:00:05,000
{content}

2
00:00:05,000 --> 00:00:10,000
这是第二段字幕内容。
"""
    
    for name, content in subtitle_contents.items():
        srt_path = os.path.join(subtitles_dir, f"{name}.srt")
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(srt_content_template.format(content=content[:50]))
    
    return video_files, subtitle_contents

def test_api_call(test_dir):
    """测试API调用"""
    print("🌐 测试API调用...")
    
    # 假设后端服务运行在localhost:8000
    api_url = "http://localhost:8000/api/v1/filesystem/filenames-with-subtitles"
    
    payload = {
        "folder_path": test_dir
    }
    
    try:
        response = requests.post(api_url, json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功")
            print(f"📄 返回数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("⚠️  无法连接到后端服务，跳过API测试")
        return None
    except Exception as e:
        print(f"❌ API调用异常: {str(e)}")
        return None

def test_local_function(test_dir):
    """测试本地函数"""
    print("🔧 测试本地函数...")
    
    try:
        from app.api.v1.filesystem import find_subtitle_content, extract_text_from_subtitle
        
        # 测试查找字幕内容
        test_cases = ["搞笑视频1", "教程视频2", "音乐MV3", "不存在的文件"]
        
        for filename in test_cases:
            content = find_subtitle_content(test_dir, filename)
            if content:
                print(f"✅ {filename}: {content[:50]}...")
            else:
                print(f"❌ {filename}: 未找到字幕")
        
        # 测试SRT格式解析
        srt_content = """1
00:00:00,000 --> 00:00:05,000
这是第一段字幕

2
00:00:05,000 --> 00:00:10,000
这是第二段字幕
"""
        extracted_text = extract_text_from_subtitle(srt_content, 'srt')
        print(f"📝 SRT解析结果: {extracted_text}")
        
        return True
        
    except ImportError as e:
        print(f"⚠️  无法导入本地函数: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 本地函数测试异常: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("🎬 ThunderHub 文件名+字幕内容功能测试")
    print("=" * 60)
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 测试目录: {temp_dir}")
        
        # 创建测试文件
        video_files, subtitle_contents = create_test_files(temp_dir)
        
        print("📄 创建的测试文件:")
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, temp_dir)
                print(f"  - {relative_path}")
        
        print("\n" + "=" * 40)
        
        # 测试本地函数
        local_success = test_local_function(temp_dir)
        
        print("\n" + "=" * 40)
        
        # 测试API调用
        api_result = test_api_call(temp_dir)
        
        print("\n" + "=" * 60)
        
        # 总结
        if local_success:
            print("🎉 本地函数测试通过！")
        else:
            print("❌ 本地函数测试失败")
        
        if api_result:
            print("🎉 API测试通过！")
            print("\n📋 预期的使用效果:")
            for item in api_result.get('data', []):
                print(f"  {item}")
        else:
            print("⚠️  API测试跳过或失败")
        
        print("\n💡 使用说明:")
        print("1. 用户在批量重命名对话框中点击'复制文件名+字幕'按钮")
        print("2. 系统会复制类似上面格式的内容到剪贴板")
        print("3. 用户可以将这些内容提供给AI，获得更好的文件名建议")
        print("4. AI可以根据文件名和字幕内容生成更准确、更有意义的新文件名")
        
        print("=" * 60)

if __name__ == "__main__":
    main()
