# 前端部署资源缺失问题解决方案

## 问题描述

在 CI/CD 部署到服务器后，前端出现以下错误：
- `Failed to load resource: the server responded with a status of 404 (Not Found)`
- `Unable to preload CSS for /assets/Management.c1c65a5f.css`
- `Failed to fetch dynamically imported module`

错误显示期望的文件名与实际构建产生的文件名不匹配，说明存在构建缓存或部署同步问题。

## 根本原因

1. **构建缓存问题**：Vite 构建缓存导致文件哈希不一致
2. **部署同步问题**：构建产物没有完全同步到服务器
3. **浏览器缓存**：浏览器缓存了旧的 manifest 信息

## 解决方案

### 1. 优化 Vite 构建配置

**修改 `vite.config.ts`：**
- 添加明确的输出文件名格式
- 确保构建目录清理
- 统一文件命名规则

```typescript
build: {
  outDir: 'dist',
  emptyOutDir: true,
  rollupOptions: {
    output: {
      entryFileNames: 'assets/[name].[hash].js',
      chunkFileNames: 'assets/[name].[hash].js',
      assetFileNames: 'assets/[name].[hash].[ext]',
      // ... 其他配置
    }
  }
}
```

### 2. 改进构建流程

**修改 `Dockerfile.builder`：**
- 清理构建缓存
- 添加构建验证
- 确保构建产物完整性

```dockerfile
# 清理缓存并重新构建
RUN rm -rf dist node_modules/.vite .vite
RUN npm run build:verify
```

### 3. 优化 Nginx 配置

**修改 `nginx.conf`：**
- 改进缓存策略
- 添加资源文件404处理
- 确保静态资源正确服务

```nginx
# 对于 assets 目录下的文件，如果不存在则返回404
location /assets/ {
    try_files $uri =404;
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 4. 添加构建验证

**新增 `verify-build.js`：**
- 验证构建产物完整性
- 检查文件引用一致性
- 确保关键模块存在

**新增 npm 脚本：**
```json
{
  "scripts": {
    "build:verify": "npm run build && node verify-build.js",
    "verify": "node verify-build.js"
  }
}
```

### 5. 部署后验证

**新增 `deployment-check.sh`：**
- 检查部署后的资源可访问性
- 验证关键模块是否正常
- 提供详细的错误诊断

## 使用方法

### 本地验证构建

```bash
cd frontend
npm run build:verify
```

### 部署后验证

```bash
cd frontend
chmod +x deployment-check.sh
./deployment-check.sh http://***************
```

### CI/CD 集成

在 CI/CD 流程中：

1. **构建阶段**：使用 `npm run build:verify` 确保构建成功
2. **部署阶段**：确保所有文件都正确复制
3. **验证阶段**：运行 `deployment-check.sh` 验证部署结果

## 预防措施

### 1. 构建环境一致性
- 确保所有构建环境使用相同的 Node.js 版本
- 锁定依赖版本，避免版本漂移

### 2. 缓存管理
- 定期清理构建缓存
- 在 CI/CD 中使用干净的构建环境

### 3. 部署验证
- 每次部署后运行验证脚本
- 监控关键资源的可访问性

### 4. 错误监控
- 添加前端错误监控
- 及时发现资源加载问题

## 故障排除

### 如果仍然出现404错误：

1. **检查构建产物**：
   ```bash
   npm run verify
   ```

2. **检查服务器部署**：
   ```bash
   ./deployment-check.sh
   ```

3. **清理缓存**：
   - 清理浏览器缓存
   - 重新构建并部署

4. **检查 nginx 日志**：
   ```bash
   docker logs <nginx-container>
   ```

### 常见问题：

1. **文件名哈希不匹配**：重新构建并清理缓存
2. **部分文件缺失**：检查 Docker 构建过程
3. **nginx 配置问题**：验证 nginx.conf 配置

## 监控建议

1. 添加前端性能监控
2. 监控资源加载失败率
3. 设置部署后自动验证
4. 建立错误告警机制

通过这些改进，可以有效解决前端部署资源缺失问题，并建立可靠的部署验证机制。
