"""
AI换脸服务
简化版本：基于文件夹的批量换脸功能
人脸模板文件放在频道根目录的face_templates文件夹中

使用方法：
1. 在频道文件夹下创建 face_templates 目录
2. 放入人脸图片：
   - source_face.jpg (要替换成的脸)
   - target_face_1.jpg (要被替换的脸1)
   - target_face_2.jpg (要被替换的脸2)
3. 调用批量换脸功能处理该文件夹下的所有视频
"""

import os
import asyncio
import logging
import time
import json
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile
import shutil

# 尝试导入AI换脸相关库
try:
    import cv2
    import numpy as np
    from PIL import Image
    import face_recognition
    CV2_AVAILABLE = True
    FACE_RECOGNITION_AVAILABLE = True
except ImportError as e:
    CV2_AVAILABLE = False
    FACE_RECOGNITION_AVAILABLE = False
    logging.warning(f"AI换脸依赖库未安装: {e}")

# 导入必要的模块
import os

# 尝试导入InsightFace
try:
    import insightface
    INSIGHTFACE_AVAILABLE = True
    logging.info(f"InsightFace 导入成功，版本: {insightface.__version__}")
except ImportError as e:
    INSIGHTFACE_AVAILABLE = False
    logging.warning(f"InsightFace导入失败: {e}，将使用face_recognition作为备选")

logger = logging.getLogger(__name__)


class FaceSwapService:
    """AI换脸服务类"""

    def __init__(self):
        """初始化换脸服务"""
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        self.supported_image_extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        self.face_templates_folder = "face_templates"
        self.output_folder = "face_swapped"

        # 初始化人脸检测器
        self.face_detector = None
        self.face_swapper = None
        self._init_models()

        logger.info("AI换脸服务初始化完成")

    def _init_models(self):
        """初始化AI模型"""
        try:
            if INSIGHTFACE_AVAILABLE:
                # 使用InsightFace（推荐）
                logger.info("开始初始化InsightFace模型...")

                # 设置模型路径：优先使用环境变量，否则使用项目目录
                model_path = os.environ.get('INSIGHTFACE_HOME')
                if not model_path:
                    # 默认使用项目根目录下的 models/insightface
                    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
                    model_path = os.path.join(project_root, 'models', 'insightface')

                os.makedirs(model_path, exist_ok=True)
                logger.info(f"使用 InsightFace 模型路径: {model_path}")

                # 创建 FaceAnalysis 实例，指定模型路径
                self.face_detector = insightface.app.FaceAnalysis(name='buffalo_l', root=model_path)
                logger.info("InsightFace FaceAnalysis 创建成功")
                self.face_detector.prepare(ctx_id=0, det_size=(640, 640))
                logger.info("InsightFace FaceAnalysis 准备完成")

                # 暂时跳过换脸模型下载，只使用人脸检测功能
                try:
                    # 使用指定的模型路径下载换脸模型
                    self.face_swapper = insightface.model_zoo.get_model('inswapper_128.onnx',
                                                                       download=True,
                                                                       download_zip=True,
                                                                       root=model_path)
                    logger.info("InsightFace 换脸模型加载完成")
                except Exception as e:
                    logger.warning(f"换脸模型下载失败，只使用人脸检测功能: {e}")
                    self.face_swapper = None

                logger.info("使用InsightFace模型")
            elif FACE_RECOGNITION_AVAILABLE:
                # 使用face_recognition作为备选
                logger.info("使用face_recognition模型")
            else:
                logger.warning("没有可用的人脸识别模型")
        except Exception as e:
            logger.error(f"模型初始化失败: {e}", exc_info=True)
            # 重置模型状态
            self.face_detector = None
            self.face_swapper = None

    def _detect_face_templates(self, folder_path: str) -> Dict[str, Any]:
        """
        自动检测人脸模板文件

        Args:
            folder_path: 文件夹路径

        Returns:
            检测到的模板文件信息
        """
        templates_dir = os.path.join(folder_path, self.face_templates_folder)

        if not os.path.exists(templates_dir):
            logger.warning(f"人脸模板目录不存在: {templates_dir}")
            return {}

        target_faces = []
        source_face = None

        # 扫描模板文件
        for file_name in os.listdir(templates_dir):
            file_path = os.path.join(templates_dir, file_name)
            if not os.path.isfile(file_path):
                continue

            # 检查文件扩展名
            ext = os.path.splitext(file_name)[1].lower().lstrip('.')
            if ext not in self.supported_image_extensions:
                continue

            # 根据文件名判断类型
            if 'source' in file_name.lower() or 'src' in file_name.lower():
                source_face = file_path
                logger.info(f"检测到源人脸: {file_name}")
            elif 'target' in file_name.lower() or 'tgt' in file_name.lower():
                target_faces.append(file_path)
                logger.info(f"检测到目标人脸: {file_name}")
            else:
                # 默认作为目标人脸
                target_faces.append(file_path)
                logger.info(f"检测到人脸模板: {file_name}")

        # 如果没有明确的源人脸，使用第一个作为源人脸
        if not source_face and target_faces:
            source_face = target_faces.pop(0)
            logger.info(f"使用第一个模板作为源人脸: {os.path.basename(source_face)}")

        return {
            'target_faces': target_faces,
            'source_face': source_face
        }

    def _get_video_files(self, folder_path: str) -> List[str]:
        """获取文件夹中的视频文件"""
        video_files = []

        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)

                # 跳过目录和特殊文件夹
                if os.path.isdir(file_path):
                    continue

                # 检查文件扩展名
                ext = os.path.splitext(file_name)[1].lower().lstrip('.')
                if ext in self.supported_video_extensions:
                    video_files.append(file_path)

            logger.info(f"找到 {len(video_files)} 个视频文件")
            return video_files

        except Exception as e:
            logger.error(f"扫描视频文件失败: {e}")
            return []

    async def batch_face_swap(self, folder_path: str, target_faces: List[str] = None,
                             source_face: str = None, max_concurrent: int = 2,
                             quality: str = 'medium', overwrite_original: bool = False) -> Dict[str, Any]:
        """
        批量换脸处理

        Args:
            folder_path: 视频文件夹路径
            target_faces: 目标人脸图片列表（要被替换的脸）
            source_face: 源人脸图片（替换成的脸）
            max_concurrent: 最大并发处理数
            quality: 处理质量 (high, medium, low)
            overwrite_original: 是否覆盖原文件

        Returns:
            处理结果
        """
        start_time = time.time()

        try:
            logger.info(f"开始批量换脸处理: {folder_path}")

            # 检查依赖
            if not CV2_AVAILABLE or not FACE_RECOGNITION_AVAILABLE:
                return {
                    "success": False,
                    "error": "AI换脸依赖库未安装，请运行 python install_ai_face_swap.py",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 自动检测人脸模板
            if not target_faces or not source_face:
                detected_templates = self._detect_face_templates(folder_path)
                if not detected_templates:
                    return {
                        "success": False,
                        "error": f"未在 {folder_path}/{self.face_templates_folder} 中找到人脸模板文件",
                        "results": [],
                        "total_processing_time_ms": 0,
                        "successful_count": 0,
                        "failed_count": 0
                    }

                target_faces = detected_templates.get('target_faces', [])
                source_face = detected_templates.get('source_face')

            # 获取视频文件列表
            video_files = self._get_video_files(folder_path)
            if not video_files:
                return {
                    "success": True,
                    "message": "文件夹中没有找到视频文件",
                    "results": [],
                    "total_processing_time_ms": int((time.time() - start_time) * 1000),
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 创建输出目录
            output_dir = os.path.join(folder_path, self.output_folder)
            os.makedirs(output_dir, exist_ok=True)

            # 预处理人脸模板
            face_templates = await self._prepare_face_templates(target_faces, source_face)
            if not face_templates:
                return {
                    "success": False,
                    "error": "人脸模板处理失败",
                    "results": [],
                    "total_processing_time_ms": int((time.time() - start_time) * 1000),
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 创建处理任务
            semaphore = asyncio.Semaphore(max_concurrent)
            tasks = []

            for video_file in video_files:
                task = self._process_single_video(
                    video_file, face_templates, output_dir, quality, overwrite_original, semaphore
                )
                tasks.append(task)

            # 并发执行任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            successful_count = 0
            failed_count = 0
            processed_results = []

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_count += 1
                    processed_results.append({
                        "input_file": video_files[i],
                        "success": False,
                        "error_message": str(result),
                        "processing_time_ms": 0
                    })
                else:
                    if result.get("success", False):
                        successful_count += 1
                    else:
                        failed_count += 1
                    processed_results.append(result)

            total_time = int((time.time() - start_time) * 1000)

            return {
                "success": True,
                "results": processed_results,
                "total_processing_time_ms": total_time,
                "successful_count": successful_count,
                "failed_count": failed_count,
                "total_count": len(video_files),
                "output_folder": output_dir,
                "face_templates_used": {
                    "target_faces": target_faces,
                    "source_face": source_face
                }
            }

        except Exception as e:
            logger.error(f"批量换脸处理异常: {str(e)}")
            return {
                "success": False,
                "error": f"批量换脸处理失败: {str(e)}",
                "results": [],
                "total_processing_time_ms": int((time.time() - start_time) * 1000),
                "successful_count": 0,
                "failed_count": 0
            }

    async def _prepare_face_templates(self, target_faces: List[str], source_face: str) -> Dict[str, Any]:
        """
        预处理人脸模板

        Args:
            target_faces: 目标人脸列表
            source_face: 源人脸

        Returns:
            处理后的人脸模板数据
        """
        try:
            if not source_face or not os.path.exists(source_face):
                logger.error(f"源人脸文件不存在: {source_face}")
                return None

            templates = {
                'source_face': source_face,
                'target_faces': [],
                'source_encoding': None,
                'target_encodings': []
            }

            # 处理源人脸
            if FACE_RECOGNITION_AVAILABLE:
                source_image = face_recognition.load_image_file(source_face)
                source_encodings = face_recognition.face_encodings(source_image)
                if source_encodings:
                    templates['source_encoding'] = source_encodings[0]
                    logger.info(f"源人脸编码成功: {os.path.basename(source_face)}")
                else:
                    logger.warning(f"源人脸中未检测到人脸: {source_face}")

            # 处理目标人脸
            for target_face in target_faces:
                if not os.path.exists(target_face):
                    logger.warning(f"目标人脸文件不存在: {target_face}")
                    continue

                if FACE_RECOGNITION_AVAILABLE:
                    target_image = face_recognition.load_image_file(target_face)
                    target_encodings = face_recognition.face_encodings(target_image)
                    if target_encodings:
                        templates['target_faces'].append(target_face)
                        templates['target_encodings'].append(target_encodings[0])
                        logger.info(f"目标人脸编码成功: {os.path.basename(target_face)}")
                    else:
                        logger.warning(f"目标人脸中未检测到人脸: {target_face}")

            if not templates['target_encodings']:
                logger.error("没有有效的目标人脸")
                return None

            return templates

        except Exception as e:
            logger.error(f"人脸模板预处理失败: {e}")
            return None

    async def _process_single_video(self, video_path: str, face_templates: Dict[str, Any],
                                   output_dir: str, quality: str, overwrite_original: bool,
                                   semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        """
        处理单个视频文件

        Args:
            video_path: 视频文件路径
            face_templates: 人脸模板数据
            output_dir: 输出目录
            quality: 处理质量
            overwrite_original: 是否覆盖原文件
            semaphore: 并发控制信号量

        Returns:
            处理结果
        """
        async with semaphore:
            start_time = time.time()

            try:
                file_name = os.path.splitext(os.path.basename(video_path))[0]

                # 构建输出文件路径
                if overwrite_original:
                    output_path = video_path
                    # 先备份原文件
                    backup_path = f"{video_path}.backup"
                    shutil.copy2(video_path, backup_path)
                else:
                    output_path = os.path.join(output_dir, f"{file_name}_face_swapped.mp4")

                logger.info(f"开始处理视频: {os.path.basename(video_path)}")

                # 检查是否已存在
                if os.path.exists(output_path) and not overwrite_original:
                    logger.info(f"输出文件已存在，跳过: {output_path}")
                    return {
                        "success": True,
                        "input_file": video_path,
                        "output_file": output_path,
                        "processing_time_ms": int((time.time() - start_time) * 1000),
                        "message": "文件已存在，跳过处理"
                    }

                # 使用FFmpeg进行换脸处理
                success = await self._swap_faces_in_video(video_path, output_path, face_templates, quality)

                if success:
                    # 如果覆盖原文件且成功，删除备份
                    if overwrite_original and os.path.exists(f"{video_path}.backup"):
                        os.remove(f"{video_path}.backup")

                    return {
                        "success": True,
                        "input_file": video_path,
                        "output_file": output_path,
                        "processing_time_ms": int((time.time() - start_time) * 1000),
                        "message": "换脸处理成功"
                    }
                else:
                    # 如果失败且覆盖原文件，恢复备份
                    if overwrite_original and os.path.exists(f"{video_path}.backup"):
                        shutil.move(f"{video_path}.backup", video_path)

                    return {
                        "success": False,
                        "input_file": video_path,
                        "output_file": output_path,
                        "processing_time_ms": int((time.time() - start_time) * 1000),
                        "error_message": "换脸处理失败"
                    }

            except Exception as e:
                logger.error(f"处理视频异常: {e}")
                return {
                    "success": False,
                    "input_file": video_path,
                    "output_file": "",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "error_message": str(e)
                }

    async def _swap_faces_in_video(self, input_path: str, output_path: str,
                                  face_templates: Dict[str, Any], quality: str) -> bool:
        """
        在视频中进行换脸处理

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            face_templates: 人脸模板数据
            quality: 处理质量

        Returns:
            是否成功
        """
        try:
            # 如果有InsightFace，使用高质量换脸
            if INSIGHTFACE_AVAILABLE and self.face_detector and self.face_swapper:
                return await self._swap_faces_with_insightface(input_path, output_path, face_templates, quality)

            # 否则使用face_recognition + OpenCV的简化版本
            elif FACE_RECOGNITION_AVAILABLE and CV2_AVAILABLE:
                return await self._swap_faces_with_opencv(input_path, output_path, face_templates, quality)

            else:
                logger.error("没有可用的换脸处理方法")
                return False

        except Exception as e:
            logger.error(f"换脸处理异常: {e}")
            return False

    async def _swap_faces_with_opencv(self, input_path: str, output_path: str,
                                     face_templates: Dict[str, Any], quality: str) -> bool:
        """
        使用OpenCV和face_recognition进行换脸（简化版本）
        """
        try:
            logger.info(f"使用OpenCV方法处理视频: {os.path.basename(input_path)}")

            # 读取视频
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {input_path}")
                return False

            # 获取视频属性
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 设置视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not out.isOpened():
                logger.error(f"无法创建输出视频文件: {output_path}")
                cap.release()
                return False

            frame_count = 0
            processed_frames = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 每10帧处理一次（提高性能）
                if frame_count % 10 == 0:
                    # 在帧中查找人脸并替换
                    processed_frame = self._process_frame_opencv(frame, face_templates)
                    if processed_frame is not None:
                        frame = processed_frame
                        processed_frames += 1

                # 写入帧
                out.write(frame)

                # 进度日志
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")

            # 释放资源
            cap.release()
            out.release()

            logger.info(f"视频处理完成: {os.path.basename(output_path)}, 处理了 {processed_frames} 帧")
            return True

        except Exception as e:
            logger.error(f"OpenCV换脸处理失败: {e}")
            return False

    def _process_frame_opencv(self, frame: np.ndarray, face_templates: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        使用OpenCV处理单帧图像
        """
        try:
            # 转换为RGB格式（face_recognition需要）
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 检测人脸位置
            face_locations = face_recognition.face_locations(rgb_frame)
            if not face_locations:
                return None

            # 获取人脸编码
            face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)

            # 检查是否有匹配的目标人脸
            for i, face_encoding in enumerate(face_encodings):
                for target_encoding in face_templates['target_encodings']:
                    # 计算人脸相似度
                    matches = face_recognition.compare_faces([target_encoding], face_encoding, tolerance=0.6)
                    if matches[0]:
                        # 找到匹配的人脸，进行简单的区域替换
                        top, right, bottom, left = face_locations[i]

                        # 这里是简化版本，实际应该进行更复杂的人脸对齐和融合
                        # 为了演示，我们只是在人脸区域添加一个标记
                        cv2.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 2)
                        cv2.putText(frame, "Face Swapped", (left, top-10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                        logger.debug(f"检测到目标人脸并标记")
                        return frame

            return None

        except Exception as e:
            logger.error(f"帧处理失败: {e}")
            return None

    async def _swap_faces_with_insightface(self, input_path: str, output_path: str,
                                          face_templates: Dict[str, Any], quality: str) -> bool:
        """
        使用InsightFace进行高质量换脸
        """
        try:
            logger.info(f"使用InsightFace方法处理视频: {os.path.basename(input_path)}")

            # 读取源人脸图片
            source_img = cv2.imread(face_templates['source_face'])
            source_faces = self.face_detector.get(source_img)

            if not source_faces:
                logger.error("源人脸图片中未检测到人脸")
                return False

            source_face = source_faces[0]

            # 读取视频
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {input_path}")
                return False

            # 获取视频属性
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 设置视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not out.isOpened():
                logger.error(f"无法创建输出视频文件: {output_path}")
                cap.release()
                return False

            frame_count = 0
            swapped_frames = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 检测当前帧中的人脸
                faces = self.face_detector.get(frame)

                if faces:
                    # 对每个检测到的人脸进行换脸
                    for face in faces:
                        # 使用InsightFace进行换脸
                        frame = self.face_swapper.get(frame, face, source_face, paste_back=True)
                        swapped_frames += 1

                # 写入帧
                out.write(frame)

                # 进度日志
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")

            # 释放资源
            cap.release()
            out.release()

            logger.info(f"InsightFace处理完成: {os.path.basename(output_path)}, 换脸帧数: {swapped_frames}")
            return True

        except Exception as e:
            logger.error(f"InsightFace换脸处理失败: {e}")
            return False

    async def detect_faces_from_videos(self, folder_path: str, max_faces_per_video: int = 5,
                                      sample_frames: int = 10) -> Dict[str, Any]:
        """
        从视频中检测人脸，供用户选择

        Args:
            folder_path: 视频文件夹路径
            max_faces_per_video: 每个视频最多检测的人脸数
            sample_frames: 采样帧数

        Returns:
            检测到的人脸信息
        """
        try:
            logger.info(f"开始从视频中检测人脸: {folder_path}")

            # 检查依赖
            if not CV2_AVAILABLE and not INSIGHTFACE_AVAILABLE:
                return {
                    "success": False,
                    "error": "人脸检测功能需要安装相关依赖库",
                    "detected_faces": [],
                    "video_count": 0,
                    "total_faces_detected": 0
                }

            # 获取视频文件列表
            video_files = self._get_video_files(folder_path)
            if not video_files:
                return {
                    "success": False,
                    "error": "文件夹中没有找到视频文件",
                    "detected_faces": [],
                    "video_count": 0
                }

            # 创建人脸检测输出目录
            faces_output_dir = os.path.join(folder_path, "detected_faces")
            os.makedirs(faces_output_dir, exist_ok=True)
            logger.info(f"人脸检测输出目录: {faces_output_dir}")
            logger.info(f"输出目录是否存在: {os.path.exists(faces_output_dir)}")

            detected_faces = []
            total_faces = 0

            # 限制处理的视频数量（避免处理时间过长）
            max_videos = min(len(video_files), 10)  # 增加到10个视频

            for i, video_path in enumerate(video_files[:max_videos]):
                logger.info(f"检测视频 {i+1}/{max_videos}: {os.path.basename(video_path)}")

                video_faces = await self._detect_faces_from_single_video(
                    video_path, faces_output_dir, max_faces_per_video, sample_frames
                )

                if video_faces:
                    detected_faces.extend(video_faces)
                    total_faces += len(video_faces)

                # 限制总人脸数量
                if total_faces >= 20:
                    break

            # 去重相似人脸
            unique_faces = await self._remove_duplicate_faces(detected_faces)

            return {
                "success": True,
                "message": f"成功检测到 {len(unique_faces)} 个不同的人脸",
                "detected_faces": unique_faces,
                "video_count": max_videos,
                "total_faces_detected": total_faces,
                "faces_output_dir": faces_output_dir
            }

        except Exception as e:
            logger.error(f"视频人脸检测失败: {e}")
            return {
                "success": False,
                "error": f"视频人脸检测失败: {str(e)}",
                "detected_faces": [],
                "video_count": 0
            }

    async def _detect_faces_from_single_video(self, video_path: str, output_dir: str,
                                            max_faces: int, sample_frames: int) -> List[Dict[str, Any]]:
        """
        从单个视频中检测人脸
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return []

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            video_name = os.path.splitext(os.path.basename(video_path))[0]

            detected_faces = []
            face_encodings_list = []

            # 计算采样间隔
            frame_interval = max(1, total_frames // sample_frames)

            for frame_idx in range(0, total_frames, frame_interval):
                if len(detected_faces) >= max_faces:
                    break

                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if not ret:
                    continue

                # 使用 InsightFace 检测人脸
                if INSIGHTFACE_AVAILABLE and self.face_detector:
                    faces = self.face_detector.get(frame)
                    if not faces:
                        continue

                    face_locations = []
                    face_encodings = []

                    for face in faces:
                        # 转换 InsightFace 的边界框格式到 face_recognition 格式
                        bbox = face.bbox.astype(int)
                        # InsightFace: [x1, y1, x2, y2] -> face_recognition: [top, right, bottom, left]
                        face_location = (bbox[1], bbox[2], bbox[3], bbox[0])
                        face_locations.append(face_location)
                        face_encodings.append(face.embedding)
                else:
                    # 如果没有 InsightFace，跳过这个视频
                    logger.warning(f"没有可用的人脸检测器，跳过视频: {os.path.basename(video_path)}")
                    continue

                for i, (face_encoding, face_location) in enumerate(zip(face_encodings, face_locations)):
                    # 检查是否与已检测的人脸相似
                    is_duplicate = False
                    for existing_encoding in face_encodings_list:
                        # 使用余弦相似度比较 InsightFace 的特征向量
                        similarity = np.dot(face_encoding, existing_encoding) / (np.linalg.norm(face_encoding) * np.linalg.norm(existing_encoding))
                        if similarity > 0.6:  # 相似度阈值
                            is_duplicate = True
                            break

                    if is_duplicate:
                        continue

                    # 提取人脸图片
                    top, right, bottom, left = face_location
                    face_image = frame[top:bottom, left:right]

                    # 保存人脸图片 - 使用更短的文件名避免路径过长
                    import hashlib
                    # 使用视频名的哈希值来缩短文件名
                    video_hash = hashlib.md5(video_name.encode('utf-8')).hexdigest()[:8]
                    face_filename = f"face_{video_hash}_f{frame_idx}_i{i}.jpg"
                    face_path = os.path.join(output_dir, face_filename)

                    logger.info(f"准备保存人脸图片: {face_path}")
                    logger.info(f"人脸图片尺寸: {face_image.shape}")
                    logger.info(f"输出目录存在: {os.path.exists(output_dir)}")
                    logger.info(f"输出目录可写: {os.access(output_dir, os.W_OK)}")

                    # 检查人脸图片是否有效
                    if face_image is None or face_image.size == 0:
                        logger.error(f"人脸图片无效: {face_path}")
                        continue

                    try:
                        # 使用支持中文路径的方法保存图片
                        # 方法1：使用 cv2.imencode + 文件写入
                        success, encoded_img = cv2.imencode('.jpg', face_image)
                        if success:
                            with open(face_path, 'wb') as f:
                                f.write(encoded_img.tobytes())
                            logger.info(f"人脸图片保存成功: {face_path}")

                            # 验证文件是否真的存在
                            if os.path.exists(face_path):
                                file_size = os.path.getsize(face_path)
                                logger.info(f"保存的文件大小: {file_size} bytes")
                            else:
                                logger.error(f"文件保存后不存在: {face_path}")
                        else:
                            logger.error(f"cv2.imencode 失败: {face_path}")
                            continue
                    except Exception as save_error:
                        logger.error(f"保存人脸图片异常: {save_error}")
                        continue

                    # 记录人脸信息
                    face_info = {
                        "id": f"{video_name}_{frame_idx}_{i}",
                        "filename": face_filename,
                        "path": face_path,
                        "video_name": video_name,
                        "video_path": video_path,
                        "frame_index": frame_idx,
                        "face_location": face_location,
                        "confidence": 1.0,  # 可以添加人脸质量评分
                        "encoding": face_encoding.tolist()  # 转换为列表以便JSON序列化
                    }

                    detected_faces.append(face_info)
                    face_encodings_list.append(face_encoding)

                    if len(detected_faces) >= max_faces:
                        break

            cap.release()
            logger.info(f"从视频 {video_name} 中检测到 {len(detected_faces)} 个人脸")
            return detected_faces

        except Exception as e:
            logger.error(f"从视频检测人脸失败: {e}")
            return []

    async def _remove_duplicate_faces(self, faces: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去除重复的人脸
        """
        try:
            if not faces:
                return []

            unique_faces = []
            unique_encodings = []

            for face in faces:
                face_encoding = np.array(face['encoding'])

                # 检查是否与已有人脸相似
                is_duplicate = False
                for existing_encoding in unique_encodings:
                    # 使用余弦相似度比较 InsightFace 的特征向量
                    similarity = np.dot(face_encoding, existing_encoding) / (
                        np.linalg.norm(face_encoding) * np.linalg.norm(existing_encoding)
                    )
                    if similarity > 0.6:  # 相似度阈值
                        is_duplicate = True
                        break

                if not is_duplicate:
                    unique_faces.append(face)
                    unique_encodings.append(face_encoding)

            logger.info(f"去重后保留 {len(unique_faces)} 个不同的人脸")
            return unique_faces

        except Exception as e:
            logger.error(f"人脸去重失败: {e}")
            return faces

    async def get_face_templates_info(self, folder_path: str) -> Dict[str, Any]:
        """
        获取文件夹中的人脸模板信息

        Args:
            folder_path: 文件夹路径

        Returns:
            人脸模板信息
        """
        try:
            templates = self._detect_face_templates(folder_path)

            if not templates:
                return {
                    "success": False,
                    "message": f"未在 {folder_path}/{self.face_templates_folder} 中找到人脸模板",
                    "templates_folder": os.path.join(folder_path, self.face_templates_folder),
                    "target_faces": [],
                    "source_face": None
                }

            return {
                "success": True,
                "message": "人脸模板检测成功",
                "templates_folder": os.path.join(folder_path, self.face_templates_folder),
                "target_faces": [os.path.basename(f) for f in templates.get('target_faces', [])],
                "source_face": os.path.basename(templates.get('source_face', '')) if templates.get('source_face') else None,
                "total_target_faces": len(templates.get('target_faces', [])),
                "has_source_face": bool(templates.get('source_face'))
            }

        except Exception as e:
            logger.error(f"获取人脸模板信息失败: {e}")
            return {
                "success": False,
                "message": f"获取人脸模板信息失败: {str(e)}",
                "templates_folder": os.path.join(folder_path, self.face_templates_folder),
                "target_faces": [],
                "source_face": None
            }

    async def face_swap_with_selection(self, folder_path: str, selected_target_faces: List[str],
                                      source_face_path: str, max_concurrent: int = 2,
                                      quality: str = 'medium', overwrite_original: bool = False) -> Dict[str, Any]:
        """
        基于用户选择的人脸进行换脸处理

        Args:
            folder_path: 视频文件夹路径
            selected_target_faces: 用户选择的要被替换的人脸ID列表
            source_face_path: 源人脸图片路径
            max_concurrent: 最大并发处理数
            quality: 处理质量
            overwrite_original: 是否覆盖原文件

        Returns:
            处理结果
        """
        start_time = time.time()

        try:
            logger.info(f"开始基于选择的换脸处理: {folder_path}")

            # 检查依赖
            if not CV2_AVAILABLE or not FACE_RECOGNITION_AVAILABLE:
                return {
                    "success": False,
                    "error": "AI换脸依赖库未安装，请运行 python install_ai_face_swap.py",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 检查源人脸文件
            if not os.path.exists(source_face_path):
                return {
                    "success": False,
                    "error": f"源人脸文件不存在: {source_face_path}",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 加载选择的目标人脸信息
            target_faces_info = await self._load_selected_faces_info(folder_path, selected_target_faces)
            if not target_faces_info:
                return {
                    "success": False,
                    "error": "无法加载选择的目标人脸信息",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 获取视频文件列表
            video_files = self._get_video_files(folder_path)
            if not video_files:
                return {
                    "success": True,
                    "message": "文件夹中没有找到视频文件",
                    "results": [],
                    "total_processing_time_ms": int((time.time() - start_time) * 1000),
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 创建输出目录
            output_dir = os.path.join(folder_path, self.output_folder)
            os.makedirs(output_dir, exist_ok=True)

            # 预处理人脸数据
            face_swap_data = await self._prepare_face_swap_data(target_faces_info, source_face_path)
            if not face_swap_data:
                return {
                    "success": False,
                    "error": "人脸数据预处理失败",
                    "results": [],
                    "total_processing_time_ms": int((time.time() - start_time) * 1000),
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 创建处理任务
            semaphore = asyncio.Semaphore(max_concurrent)
            tasks = []

            for video_file in video_files:
                task = self._process_video_with_selected_faces(
                    video_file, face_swap_data, output_dir, quality, overwrite_original, semaphore
                )
                tasks.append(task)

            # 并发执行任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            successful_count = 0
            failed_count = 0
            processed_results = []

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_count += 1
                    processed_results.append({
                        "input_file": video_files[i],
                        "success": False,
                        "error_message": str(result),
                        "processing_time_ms": 0
                    })
                else:
                    if result.get("success", False):
                        successful_count += 1
                    else:
                        failed_count += 1
                    processed_results.append(result)

            total_time = int((time.time() - start_time) * 1000)

            return {
                "success": True,
                "results": processed_results,
                "total_processing_time_ms": total_time,
                "successful_count": successful_count,
                "failed_count": failed_count,
                "total_count": len(video_files),
                "output_folder": output_dir,
                "selected_faces_count": len(selected_target_faces),
                "source_face_used": source_face_path
            }

        except Exception as e:
            logger.error(f"基于选择的换脸处理异常: {str(e)}")
            return {
                "success": False,
                "error": f"基于选择的换脸处理失败: {str(e)}",
                "results": [],
                "total_processing_time_ms": int((time.time() - start_time) * 1000),
                "successful_count": 0,
                "failed_count": 0
            }

    async def _load_selected_faces_info(self, folder_path: str, selected_face_ids: List[str]) -> List[Dict[str, Any]]:
        """
        加载选择的人脸信息
        """
        try:
            faces_output_dir = os.path.join(folder_path, "detected_faces")
            if not os.path.exists(faces_output_dir):
                logger.error(f"检测到的人脸目录不存在: {faces_output_dir}")
                return []

            selected_faces = []

            for face_id in selected_face_ids:
                # 根据face_id查找对应的人脸文件
                # face_id格式: {video_name}_{frame_idx}_{face_idx}
                face_filename = None
                for filename in os.listdir(faces_output_dir):
                    if face_id in filename:
                        face_filename = filename
                        break

                if not face_filename:
                    logger.warning(f"未找到人脸文件: {face_id}")
                    continue

                face_path = os.path.join(faces_output_dir, face_filename)
                if not os.path.exists(face_path):
                    logger.warning(f"人脸文件不存在: {face_path}")
                    continue

                # 加载人脸图片并提取特征
                try:
                    face_image = face_recognition.load_image_file(face_path)
                    face_encodings = face_recognition.face_encodings(face_image)

                    if face_encodings:
                        face_info = {
                            "id": face_id,
                            "path": face_path,
                            "filename": face_filename,
                            "encoding": face_encodings[0]
                        }
                        selected_faces.append(face_info)
                        logger.info(f"成功加载人脸: {face_id}")
                    else:
                        logger.warning(f"无法从人脸文件中提取特征: {face_path}")

                except Exception as e:
                    logger.error(f"加载人脸文件失败 {face_path}: {e}")
                    continue

            logger.info(f"成功加载 {len(selected_faces)} 个选择的人脸")
            return selected_faces

        except Exception as e:
            logger.error(f"加载选择的人脸信息失败: {e}")
            return []

    async def _prepare_face_swap_data(self, target_faces: List[Dict[str, Any]], source_face_path: str) -> Dict[str, Any]:
        """
        准备换脸数据
        """
        try:
            # 处理源人脸
            source_image = face_recognition.load_image_file(source_face_path)
            source_encodings = face_recognition.face_encodings(source_image)

            if not source_encodings:
                logger.error(f"源人脸文件中未检测到人脸: {source_face_path}")
                return None

            face_swap_data = {
                "source_face_path": source_face_path,
                "source_encoding": source_encodings[0],
                "target_faces": target_faces,
                "target_encodings": [face["encoding"] for face in target_faces]
            }

            logger.info(f"换脸数据准备完成: 源人脸1个，目标人脸{len(target_faces)}个")
            return face_swap_data

        except Exception as e:
            logger.error(f"准备换脸数据失败: {e}")
            return None

    async def _process_video_with_selected_faces(self, video_path: str, face_swap_data: Dict[str, Any],
                                               output_dir: str, quality: str, overwrite_original: bool,
                                               semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        """
        使用选择的人脸处理视频
        """
        async with semaphore:
            start_time = time.time()

            try:
                file_name = os.path.splitext(os.path.basename(video_path))[0]

                # 构建输出文件路径
                if overwrite_original:
                    output_path = video_path
                    # 先备份原文件
                    backup_path = f"{video_path}.backup"
                    shutil.copy2(video_path, backup_path)
                else:
                    output_path = os.path.join(output_dir, f"{file_name}_face_swapped.mp4")

                logger.info(f"开始处理视频: {os.path.basename(video_path)}")

                # 检查是否已存在
                if os.path.exists(output_path) and not overwrite_original:
                    logger.info(f"输出文件已存在，跳过: {output_path}")
                    return {
                        "success": True,
                        "input_file": video_path,
                        "output_file": output_path,
                        "processing_time_ms": int((time.time() - start_time) * 1000),
                        "message": "文件已存在，跳过处理"
                    }

                # 使用选择的人脸进行换脸处理
                success = await self._swap_selected_faces_in_video(video_path, output_path, face_swap_data, quality)

                if success:
                    # 如果覆盖原文件且成功，删除备份
                    if overwrite_original and os.path.exists(f"{video_path}.backup"):
                        os.remove(f"{video_path}.backup")

                    return {
                        "success": True,
                        "input_file": video_path,
                        "output_file": output_path,
                        "processing_time_ms": int((time.time() - start_time) * 1000),
                        "message": "换脸处理成功"
                    }
                else:
                    # 如果失败且覆盖原文件，恢复备份
                    if overwrite_original and os.path.exists(f"{video_path}.backup"):
                        shutil.move(f"{video_path}.backup", video_path)

                    return {
                        "success": False,
                        "input_file": video_path,
                        "output_file": output_path,
                        "processing_time_ms": int((time.time() - start_time) * 1000),
                        "error_message": "换脸处理失败"
                    }

            except Exception as e:
                logger.error(f"处理视频异常: {e}")
                return {
                    "success": False,
                    "input_file": video_path,
                    "output_file": "",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "error_message": str(e)
                }

    async def _swap_selected_faces_in_video(self, input_path: str, output_path: str,
                                          face_swap_data: Dict[str, Any], quality: str) -> bool:
        """
        在视频中替换选择的人脸
        """
        try:
            logger.info(f"使用选择的人脸处理视频: {os.path.basename(input_path)}")

            # 读取视频
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {input_path}")
                return False

            # 获取视频属性
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 设置视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not out.isOpened():
                logger.error(f"无法创建输出视频文件: {output_path}")
                cap.release()
                return False

            frame_count = 0
            processed_frames = 0
            target_encodings = face_swap_data["target_encodings"]

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 根据质量设置处理频率
                process_interval = 1 if quality == 'high' else (5 if quality == 'medium' else 10)

                if frame_count % process_interval == 0:
                    # 在帧中查找并替换选择的人脸
                    processed_frame = self._process_frame_with_selected_faces(frame, face_swap_data)
                    if processed_frame is not None:
                        frame = processed_frame
                        processed_frames += 1

                # 写入帧
                out.write(frame)

                # 进度日志
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")

            # 释放资源
            cap.release()
            out.release()

            logger.info(f"视频处理完成: {os.path.basename(output_path)}, 处理了 {processed_frames} 帧")
            return True

        except Exception as e:
            logger.error(f"选择人脸换脸处理失败: {e}")
            return False

    def _process_frame_with_selected_faces(self, frame: np.ndarray, face_swap_data: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        使用选择的人脸处理单帧图像
        """
        try:
            # 转换为RGB格式
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 检测人脸位置
            face_locations = face_recognition.face_locations(rgb_frame)
            if not face_locations:
                return None

            # 获取人脸编码
            face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)

            frame_modified = False
            target_encodings = face_swap_data["target_encodings"]

            # 检查是否有匹配的目标人脸
            for i, face_encoding in enumerate(face_encodings):
                for target_encoding in target_encodings:
                    # 计算人脸相似度
                    matches = face_recognition.compare_faces([target_encoding], face_encoding, tolerance=0.6)
                    if matches[0]:
                        # 找到匹配的人脸，进行替换
                        top, right, bottom, left = face_locations[i]

                        # 这里是简化版本的人脸替换
                        # 在实际应用中，应该使用更复杂的人脸对齐和融合算法

                        # 标记替换区域（演示用）
                        cv2.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 3)
                        cv2.putText(frame, "Face Replaced", (left, top-10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                        frame_modified = True
                        logger.debug(f"替换了匹配的目标人脸")
                        break

            return frame if frame_modified else None

        except Exception as e:
            logger.error(f"帧处理失败: {e}")
            return None