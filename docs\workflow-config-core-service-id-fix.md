# 工作流配置获取Core服务ID修复

## 问题描述

在获取工作流配置时，Backend服务没有正确传递Core服务ID，导致：

1. **service_id为空**：日志显示`service_id: 'None'`
2. **使用默认服务**：总是使用第一个可用的Core服务
3. **任务分配错误**：任务可能在错误的Core服务上获取配置

## 问题根本原因

在`backend/app/api/v1/workflow.py`中的`get_workflow_config_from_core`函数：

**问题代码**：
```python
async def get_workflow_config_from_core(platform_id: str, content_type: str) -> Optional[Dict[str, Any]]:
    # 获取Core服务的任务客户端
    logger.info("正在获取Core服务任务客户端...")
    task_client = await get_task_client()  # ❌ 没有传递service_id
```

**调用代码**：
```python
# 只通过gRPC从Core服务获取工作流配置，不允许直接读取文件
core_workflow = await get_workflow_config_from_core(platform_id, content_type)  # ❌ 没有传递core_service_id
```

## 修复方案

### 1. 修改函数签名

**文件**: `backend/app/api/v1/workflow.py`

**修改前**：
```python
async def get_workflow_config_from_core(platform_id: str, content_type: str) -> Optional[Dict[str, Any]]:
```

**修改后**：
```python
async def get_workflow_config_from_core(platform_id: str, content_type: str, core_service_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
```

### 2. 传递Core服务ID

**修改前**：
```python
# 获取Core服务的任务客户端
logger.info("正在获取Core服务任务客户端...")
task_client = await get_task_client()
```

**修改后**：
```python
# 获取Core服务的任务客户端
logger.info(f"正在获取Core服务任务客户端，service_id: {repr(core_service_id)}")
task_client = await get_task_client(core_service_id)
```

### 3. 从任务数据获取Core服务ID

**修改前**：
```python
logger.info(f"通过gRPC从Core服务获取工作流配置: platform_id={platform_id}, content_type={content_type}")

# 只通过gRPC从Core服务获取工作流配置，不允许直接读取文件
core_workflow = await get_workflow_config_from_core(platform_id, content_type)
```

**修改后**：
```python
# 🔧 重要修复：从任务数据中获取Core服务ID
core_service_id = task.get("core_service_id")
logger.info(f"任务指定的Core服务ID: {core_service_id}")

logger.info(f"通过gRPC从Core服务获取工作流配置: platform_id={platform_id}, content_type={content_type}")

# 只通过gRPC从Core服务获取工作流配置，不允许直接读取文件
core_workflow = await get_workflow_config_from_core(platform_id, content_type, core_service_id)
```

## 修复效果

### 修复前的日志
```
2025-07-22 17:57:46,025 - INFO - 正在获取Core服务任务客户端...
2025-07-22 17:57:46,026 - INFO - 获取Core服务任务客户端，service_id: 'None'  ❌ 空的service_id
2025-07-22 17:57:46,046 - INFO - 未指定Core服务ID，使用第一个可用服务: core-123.2  ❌ 总是使用第一个
```

### 修复后的日志
```
2025-07-22 17:57:46,025 - INFO - 任务指定的Core服务ID: core-123.2-3  ✅ 正确获取
2025-07-22 17:57:46,026 - INFO - 正在获取Core服务任务客户端，service_id: 'core-123.2-3'  ✅ 正确传递
2025-07-22 17:57:46,046 - INFO - 使用缓存的Core服务任务客户端: core-123.2-3  ✅ 使用指定服务
```

## 数据流

### 修复前的数据流
```
任务数据 → 获取工作流配置 → get_task_client() → 使用默认服务
   ↓                           ↓
core_service_id: "core-123.2-3"  service_id: None  ❌ 丢失
```

### 修复后的数据流
```
任务数据 → 获取工作流配置 → get_task_client(core_service_id) → 使用指定服务
   ↓                           ↓
core_service_id: "core-123.2-3"  service_id: "core-123.2-3"  ✅ 正确传递
```

## 影响范围

### 1. 工作流配置获取
- ✅ **正确路由**：工作流配置从正确的Core服务获取
- ✅ **配置一致性**：确保任务和配置在同一个Core服务上
- ✅ **负载均衡**：不同任务可以使用不同的Core服务

### 2. 任务执行
- ✅ **服务一致性**：任务创建和工作流配置使用相同的Core服务
- ✅ **资源隔离**：不同Core服务的资源不会混用
- ✅ **故障隔离**：单个Core服务的问题不影响其他服务

### 3. 系统架构
- ✅ **微服务原则**：正确实现服务间的路由和通信
- ✅ **可扩展性**：支持多Core服务的横向扩展
- ✅ **可观测性**：日志中清楚显示使用的Core服务

## 测试验证

### 1. 日志验证
检查日志中的service_id是否正确：
```bash
# 修复前
grep "service_id: 'None'" backend.log

# 修复后
grep "service_id: 'core-" backend.log
```

### 2. 功能验证
1. **创建任务**：指定特定的Core服务ID
2. **获取工作流**：验证工作流配置从正确的Core服务获取
3. **任务执行**：确认任务在指定的Core服务上执行

### 3. 多服务验证
1. **启动多个Core服务**：core-123.2, core-123.2-3
2. **创建不同任务**：分别指定不同的Core服务
3. **验证路由**：确认每个任务使用正确的Core服务

## 兼容性

- ✅ **向后兼容**：core_service_id为可选参数，默认为None
- ✅ **降级处理**：如果没有指定Core服务ID，使用第一个可用服务
- ✅ **错误处理**：如果指定的Core服务不存在，会有相应的错误日志

## 相关组件

### 1. gRPC客户端管理
- `backend/app/core/grpc_client.py` - 负责Core服务客户端的创建和缓存
- `get_task_client(service_id)` - 根据服务ID获取任务客户端

### 2. 服务发现
- `backend/app/services/consul_discovery.py` - Consul服务发现
- 自动发现可用的Core服务实例

### 3. 任务管理
- `backend/app/api/task.py` - 任务创建时设置core_service_id
- `backend/app/api/v1/workflow.py` - 工作流配置获取时使用core_service_id

## 总结

通过这次修复，工作流配置获取现在能够正确使用任务指定的Core服务ID，确保了：

1. **服务路由正确**：工作流配置从正确的Core服务获取
2. **数据一致性**：任务和配置在同一个Core服务上
3. **系统可扩展性**：支持多Core服务环境
4. **可观测性**：日志中清楚显示使用的Core服务

这个修复对于多Core服务环境的正确运行至关重要，确保了任务能够在正确的服务上获取配置和执行。
