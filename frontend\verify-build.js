#!/usr/bin/env node
/**
 * 构建验证脚本
 * 用于验证构建产物的完整性和一致性
 */

const fs = require('fs');
const path = require('path');

const DIST_DIR = path.join(__dirname, 'dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

function checkFileExists(filePath) {
    return fs.existsSync(filePath);
}

function getFileSize(filePath) {
    const stats = fs.statSync(filePath);
    return stats.size;
}

function listAssets() {
    if (!checkFileExists(ASSETS_DIR)) {
        console.error('❌ Assets 目录不存在:', ASSETS_DIR);
        return [];
    }
    
    const files = fs.readdirSync(ASSETS_DIR);
    return files.filter(file => file.endsWith('.js') || file.endsWith('.css'));
}

function parseIndexHtml() {
    const indexPath = path.join(DIST_DIR, 'index.html');
    if (!checkFileExists(indexPath)) {
        console.error('❌ index.html 不存在');
        return { scripts: [], stylesheets: [] };
    }
    
    const content = fs.readFileSync(indexPath, 'utf-8');
    
    // 提取脚本引用
    const scriptMatches = content.match(/<script[^>]*src="([^"]*)"[^>]*>/g) || [];
    const scripts = scriptMatches.map(match => {
        const srcMatch = match.match(/src="([^"]*)"/);
        return srcMatch ? srcMatch[1] : null;
    }).filter(Boolean);
    
    // 提取样式表引用
    const linkMatches = content.match(/<link[^>]*href="([^"]*\.css)"[^>]*>/g) || [];
    const stylesheets = linkMatches.map(match => {
        const hrefMatch = match.match(/href="([^"]*)"/);
        return hrefMatch ? hrefMatch[1] : null;
    }).filter(Boolean);
    
    return { scripts, stylesheets };
}

function verifyBuild() {
    console.log('🔍 开始验证构建产物...\n');
    
    // 检查基本目录结构
    console.log('📁 检查目录结构:');
    console.log(`  dist: ${checkFileExists(DIST_DIR) ? '✅' : '❌'}`);
    console.log(`  assets: ${checkFileExists(ASSETS_DIR) ? '✅' : '❌'}`);
    console.log(`  index.html: ${checkFileExists(path.join(DIST_DIR, 'index.html')) ? '✅' : '❌'}`);
    console.log();
    
    // 列出所有资源文件
    console.log('📦 构建产物列表:');
    const assets = listAssets();
    if (assets.length === 0) {
        console.log('  ❌ 没有找到任何资源文件');
        return false;
    }
    
    assets.forEach(asset => {
        const filePath = path.join(ASSETS_DIR, asset);
        const size = getFileSize(filePath);
        const sizeKB = (size / 1024).toFixed(2);
        console.log(`  ✅ ${asset} (${sizeKB} KB)`);
    });
    console.log();
    
    // 解析 index.html 中的引用
    console.log('🔗 验证 index.html 中的资源引用:');
    const { scripts, stylesheets } = parseIndexHtml();
    
    let allReferencesValid = true;
    
    // 检查脚本引用
    console.log('  JavaScript 文件:');
    scripts.forEach(script => {
        const relativePath = script.startsWith('/') ? script.substring(1) : script;
        const fullPath = path.join(DIST_DIR, relativePath);
        const exists = checkFileExists(fullPath);
        console.log(`    ${exists ? '✅' : '❌'} ${script}`);
        if (!exists) allReferencesValid = false;
    });
    
    // 检查样式表引用
    console.log('  CSS 文件:');
    stylesheets.forEach(stylesheet => {
        const relativePath = stylesheet.startsWith('/') ? stylesheet.substring(1) : stylesheet;
        const fullPath = path.join(DIST_DIR, relativePath);
        const exists = checkFileExists(fullPath);
        console.log(`    ${exists ? '✅' : '❌'} ${stylesheet}`);
        if (!exists) allReferencesValid = false;
    });
    console.log();
    
    // 检查关键模块
    console.log('🎯 检查关键模块:');
    const criticalModules = [
        'Management',
        'publish-management',
        'api-module',
        'vue-vendor',
        'element-plus'
    ];
    
    criticalModules.forEach(module => {
        const jsFile = assets.find(asset => asset.includes(module) && asset.endsWith('.js'));
        const cssFile = assets.find(asset => asset.includes(module) && asset.endsWith('.css'));
        
        console.log(`  ${module}:`);
        console.log(`    JS: ${jsFile ? '✅ ' + jsFile : '❌ 未找到'}`);
        if (cssFile) {
            console.log(`    CSS: ✅ ${cssFile}`);
        }
    });
    console.log();
    
    // 总结
    if (allReferencesValid && assets.length > 0) {
        console.log('🎉 构建验证通过！所有文件都存在且引用正确。');
        return true;
    } else {
        console.log('❌ 构建验证失败！存在缺失的文件或引用错误。');
        return false;
    }
}

// 运行验证
const success = verifyBuild();
process.exit(success ? 0 : 1);
