#!/usr/bin/env python3
"""
检查特定任务的工作流数据
"""

import requests
import json
import sys

def check_task_workflow(task_id="4509502f-16cd-4d63-8a60-485f4ff84ac7", backend_url="http://localhost:8000"):
    """
    检查特定任务的工作流数据
    """
    try:
        print(f"🔍 检查任务工作流数据: {task_id}")
        print(f"Backend地址: {backend_url}")
        print("=" * 60)
        
        # 调用工作流API
        response = requests.get(
            f"{backend_url}/api/tasks/{task_id}/workflow",
            timeout=30
        )
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功!")
            print(f"   成功: {result.get('success')}")
            print(f"   数据源: {result.get('source')}")
            print(f"   消息: {result.get('message')}")
            
            if result.get("data"):
                data = result["data"]
                print(f"\n📊 工作流信息:")
                print(f"   工作流ID: {data.get('workflow_id')}")
                print(f"   工作流名称: {data.get('workflow_name')}")
                print(f"   工作流版本: {data.get('workflow_version')}")
                print(f"   当前步骤索引: {data.get('current_step_index')}")
                print(f"   总步骤数: {data.get('total_steps')}")
                print(f"   可恢复: {data.get('can_resume')}")
                
                steps = data.get("steps", [])
                print(f"\n📋 步骤详情 ({len(steps)}个步骤):")
                
                for i, step in enumerate(steps):
                    status_icon = {
                        'pending': '⏳',
                        'running': '🔄',
                        'completed': '✅',
                        'failed': '❌',
                        'paused': '⏸️',
                        'skipped': '⏭️'
                    }.get(step.get('status'), '❓')
                    
                    step_name = step.get('name', '')
                    step_desc = step.get('description', '')
                    step_status = step.get('status', '')
                    step_progress = step.get('progress', 0)
                    
                    print(f"   {i+1}. {status_icon} {step_name}")
                    print(f"      状态: {step_status} ({step_progress}%)")
                    if step_desc:
                        print(f"      描述: {step_desc}")
                    if step.get('error_message'):
                        print(f"      错误: {step.get('error_message')}")
                    if step.get('start_time'):
                        print(f"      开始时间: {step.get('start_time')}")
                    if step.get('end_time'):
                        print(f"      结束时间: {step.get('end_time')}")
                    print()
                
                # 分析数据源
                source = result.get('source', '')
                print(f"🔍 数据源分析:")
                print(f"   数据源: {source}")
                
                if source == "core_real_status":
                    print("   ✅ 这是来自Core服务的真实执行状态")
                elif source == "redis_real_execution_data":
                    print("   ✅ 这是来自Redis的真实执行数据")
                elif source == "real_config_with_final_status":
                    print("   ⚠️  这是基于真实工作流配置的最终状态")
                elif source == "default_completed_status":
                    print("   ❌ 这是使用默认步骤的已完成状态（可能是假数据）")
                elif source == "generated_basic_workflow":
                    print("   ❌ 这是生成的基础工作流（假数据）")
                else:
                    print(f"   ❓ 未知数据源: {source}")
                
                # 检查是否为假数据
                fake_indicators = []
                for step in steps:
                    step_name = step.get('name', '')
                    if step_name in ["准备任务", "连接设备", "启动应用", "执行操作", "完成任务"]:
                        fake_indicators.append(step_name)
                
                if fake_indicators:
                    print(f"\n⚠️  检测到可能的假数据指标:")
                    for indicator in fake_indicators:
                        print(f"     - 默认步骤名称: {indicator}")
                    
                    if len(fake_indicators) >= 3:
                        print("   ❌ 这很可能是假数据！")
                    else:
                        print("   ⚠️  可能包含部分假数据")
                else:
                    print("   ✅ 未检测到明显的假数据指标")
                
                # 上下文数据
                context_data = data.get("context_data", {})
                if context_data:
                    print(f"\n📋 上下文数据:")
                    for key, value in context_data.items():
                        print(f"   {key}: {value}")
            
            return True
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 检查过程异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("任务工作流数据检查工具")
    print("=" * 60)
    
    # 解析命令行参数
    task_id = "4509502f-16cd-4d63-8a60-485f4ff84ac7"
    backend_url = "http://localhost:8000"
    
    if len(sys.argv) > 1:
        task_id = sys.argv[1]
    if len(sys.argv) > 2:
        backend_url = sys.argv[2]
    
    # 执行检查
    success = check_task_workflow(task_id, backend_url)
    
    if success:
        print("\n🎉 任务工作流数据检查完成!")
    else:
        print("\n💥 检查失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
