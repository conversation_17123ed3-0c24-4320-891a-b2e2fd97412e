"""
性能监控API
提供系统性能指标和优化建议
"""

import asyncio
import logging
import time
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# 可选依赖：psutil
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil 未安装，系统监控功能将受限")

from app.services.task_queue_manager import task_queue_manager
from app.services.connection_pool_manager import connection_pool_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/performance", tags=["性能监控"])


class PerformanceMetrics(BaseModel):
    """性能指标响应模型"""
    timestamp: float
    system_metrics: Dict[str, Any]
    task_queue_metrics: Dict[str, Any]
    connection_pool_metrics: Dict[str, Any]
    recommendations: List[str]


class SystemHealth(BaseModel):
    """系统健康状态响应模型"""
    overall_healthy: bool
    components: Dict[str, Any]
    issues: List[str]


@router.get("/metrics", response_model=PerformanceMetrics)
async def get_performance_metrics():
    """获取系统性能指标"""
    try:
        timestamp = time.time()
        
        # 系统指标
        if PSUTIL_AVAILABLE:
            system_metrics = {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage_percent': psutil.disk_usage('/').percent,
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0],
                'process_count': len(psutil.pids()),
                'network_io': dict(psutil.net_io_counters()._asdict()) if psutil.net_io_counters() else {}
            }
        else:
            system_metrics = {
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_usage_percent': 0,
                'load_average': [0, 0, 0],
                'process_count': 0,
                'network_io': {},
                'psutil_unavailable': True
            }
        
        # 任务队列指标
        task_queue_metrics = task_queue_manager.get_stats()
        
        # 连接池指标
        connection_pool_metrics = await connection_pool_manager.get_connection_stats()
        
        # 生成优化建议
        recommendations = _generate_recommendations(system_metrics, task_queue_metrics)
        
        return PerformanceMetrics(
            timestamp=timestamp,
            system_metrics=system_metrics,
            task_queue_metrics=task_queue_metrics,
            connection_pool_metrics=connection_pool_metrics,
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"获取性能指标失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")


@router.get("/health", response_model=SystemHealth)
async def get_system_health():
    """获取系统健康状态"""
    try:
        issues = []
        components = {}
        
        # 检查系统资源
        if PSUTIL_AVAILABLE:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('/').percent
        else:
            cpu_percent = 0
            memory_percent = 0
            disk_percent = 0
        
        components['system'] = {
            'cpu_healthy': cpu_percent < 80,
            'memory_healthy': memory_percent < 85,
            'disk_healthy': disk_percent < 90,
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'disk_percent': disk_percent
        }
        
        if cpu_percent >= 80:
            issues.append(f"CPU使用率过高: {cpu_percent:.1f}%")
        if memory_percent >= 85:
            issues.append(f"内存使用率过高: {memory_percent:.1f}%")
        if disk_percent >= 90:
            issues.append(f"磁盘使用率过高: {disk_percent:.1f}%")
        
        # 检查任务队列
        task_stats = task_queue_manager.get_stats()
        queue_healthy = task_stats['is_running'] and task_stats['worker_count'] > 0
        components['task_queue'] = {
            'healthy': queue_healthy,
            'running': task_stats['is_running'],
            'worker_count': task_stats['worker_count'],
            'running_tasks': task_stats['running_tasks_count']
        }
        
        if not queue_healthy:
            issues.append("任务队列服务异常")
        
        # 检查连接池
        pool_health = await connection_pool_manager.health_check()
        components['connection_pools'] = pool_health
        
        if not pool_health['overall_healthy']:
            issues.append("连接池健康检查失败")
        
        overall_healthy = len(issues) == 0
        
        return SystemHealth(
            overall_healthy=overall_healthy,
            components=components,
            issues=issues
        )
        
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取系统健康状态失败: {str(e)}")


@router.post("/optimize")
async def optimize_system():
    """执行系统优化操作"""
    try:
        optimization_results = []
        
        # 清理完成的任务
        task_stats = task_queue_manager.get_stats()
        optimization_results.append({
            'action': 'task_cleanup',
            'description': '清理已完成的任务',
            'before': task_stats['completed_tasks'],
            'status': 'completed'
        })
        
        # 检查连接池状态并优化
        pool_stats = await connection_pool_manager.get_connection_stats()
        optimization_results.append({
            'action': 'connection_pool_check',
            'description': '检查连接池状态',
            'redis_pools': pool_stats['redis_pools'],
            'mongo_clients': pool_stats['mongo_clients'],
            'status': 'completed'
        })
        
        # 内存清理建议
        if PSUTIL_AVAILABLE:
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 80:
                optimization_results.append({
                    'action': 'memory_warning',
                    'description': f'内存使用率较高: {memory_percent:.1f}%',
                    'recommendation': '考虑重启服务或增加内存',
                    'status': 'warning'
                })
        else:
            optimization_results.append({
                'action': 'psutil_unavailable',
                'description': 'psutil 未安装，无法进行系统监控',
                'recommendation': '运行: pip install psutil==6.1.0',
                'status': 'warning'
            })
        
        return {
            'success': True,
            'message': '系统优化完成',
            'results': optimization_results,
            'timestamp': time.time()
        }
        
    except Exception as e:
        logger.error(f"系统优化失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"系统优化失败: {str(e)}")


@router.get("/task-queue/stats")
async def get_task_queue_stats():
    """获取任务队列详细统计"""
    try:
        stats = task_queue_manager.get_stats()
        
        # 添加队列详细信息
        queue_details = {}
        for priority, queue in task_queue_manager.task_queues.items():
            queue_details[priority.name] = {
                'size': queue.qsize(),
                'maxsize': queue.maxsize,
                'full': queue.full(),
                'empty': queue.empty()
            }
        
        stats['queue_details'] = queue_details
        stats['semaphore_value'] = task_queue_manager.semaphore._value
        
        return {
            'success': True,
            'data': stats,
            'timestamp': time.time()
        }
        
    except Exception as e:
        logger.error(f"获取任务队列统计失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务队列统计失败: {str(e)}")


@router.get("/connection-pools/stats")
async def get_connection_pool_stats():
    """获取连接池详细统计"""
    try:
        stats = await connection_pool_manager.get_connection_stats()
        health = await connection_pool_manager.health_check()
        
        return {
            'success': True,
            'data': {
                'stats': stats,
                'health': health
            },
            'timestamp': time.time()
        }
        
    except Exception as e:
        logger.error(f"获取连接池统计失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取连接池统计失败: {str(e)}")


def _generate_recommendations(system_metrics: Dict[str, Any], 
                            task_metrics: Dict[str, Any]) -> List[str]:
    """生成优化建议"""
    recommendations = []
    
    # CPU建议
    cpu_percent = system_metrics.get('cpu_percent', 0)
    if cpu_percent > 80:
        recommendations.append(f"CPU使用率过高({cpu_percent:.1f}%)，建议增加worker数量或优化任务处理逻辑")
    elif cpu_percent < 20:
        recommendations.append("CPU使用率较低，可以考虑增加并发任务数量")
    
    # 内存建议
    memory_percent = system_metrics.get('memory_percent', 0)
    if memory_percent > 85:
        recommendations.append(f"内存使用率过高({memory_percent:.1f}%)，建议优化内存使用或增加内存")
    
    # 任务队列建议
    running_tasks = task_metrics.get('running_tasks_count', 0)
    total_tasks = task_metrics.get('total_tasks', 0)
    failed_tasks = task_metrics.get('failed_tasks', 0)
    
    if failed_tasks > 0 and total_tasks > 0:
        failure_rate = (failed_tasks / total_tasks) * 100
        if failure_rate > 10:
            recommendations.append(f"任务失败率较高({failure_rate:.1f}%)，建议检查任务逻辑和错误处理")
    
    if running_tasks == 0 and total_tasks > 0:
        recommendations.append("当前没有运行中的任务，检查任务队列是否正常工作")
    
    # 队列大小建议
    for priority_name, queue_size in task_metrics.get('queue_sizes', {}).items():
        if queue_size > 100:
            recommendations.append(f"{priority_name}优先级队列积压较多({queue_size}个任务)，建议增加worker数量")
    
    if not recommendations:
        recommendations.append("系统运行状态良好，无需特别优化")
    
    return recommendations
