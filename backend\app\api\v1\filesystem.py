# TODO: 架构优化 - 文件操作应通过Core服务处理
# 根据架构文档，文件系统操作应该通过Core服务的gRPC接口处理，而不是在Backend中直接操作
# 当前实现为了保持功能可用性，采用了降级策略：优先尝试Core服务，失败时降级到本地处理
# 需要在Core服务中完善以下接口：
# 1. ListDirectory - 支持MD5计算和媒体信息获取
# 2. CalculateFileMD5 - 批量计算文件MD5
# 3. GetMediaInfo - 获取媒体文件信息
# 完善后可以移除本地文件操作逻辑

import os
import logging
import hashlib
import json
import subprocess
import asyncio
import uuid
import time
import random
import shutil
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, BackgroundTasks
from pydantic import BaseModel
try:
    from pymongo.database import Database
except ImportError:
    Database = None

from app.core.security import get_current_user

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/filesystem",
    tags=["filesystem"]
    # 临时禁用认证用于测试
    # dependencies=[Depends(get_current_user)]
)

# 定义数据模型
class FileInfo(BaseModel):
    name: str
    path: str
    size: int
    is_directory: bool
    extension: Optional[str] = None
    last_modified: Optional[str] = None
    md5_hash: Optional[str] = None
    is_uploaded: Optional[bool] = None
    media_info: Optional[Dict[str, Any]] = None

class BatchRenameRequest(BaseModel):
    folder_path: str
    file_names: List[str]  # 新文件名列表（不带扩展名）
    selected_files: Optional[List[str]] = None  # 可选：指定要重命名的文件列表

class ArchivePublishedFilesRequest(BaseModel):
    folder_path: str
    archive_folder_name: Optional[str] = "已发布"  # 归档文件夹名称
    platforms: Optional[List[str]] = None  # 可选：指定平台列表，如果不提供则检查所有平台

class BatchRenameResponse(BaseModel):
    success: bool
    renamed_count: int
    subtitle_renamed_count: int = 0  # 重命名的字幕文件数量
    errors: List[str]

class FilenamesWithSubtitlesRequest(BaseModel):
    folder_path: str

class FilenamesWithSubtitlesResponse(BaseModel):
    success: bool
    data: List[str]  # 文件名+字幕内容的列表
    error: Optional[str] = None

class ArchivePublishedFilesResponse(BaseModel):
    success: bool
    archived_count: int
    skipped_count: int
    archive_folder: str
    archived_files: List[str]
    errors: List[str]

class PrePublishRequest(BaseModel):
    """预发布请求模型"""
    file_paths: List[str]  # 要预发布的文件路径列表
    current_folder: str  # 当前文件夹路径，用于确定根目录

class PrePublishResponse(BaseModel):
    """预发布响应模型"""
    success: bool
    moved_count: int
    total_count: int
    publishing_folder: str  # publishing文件夹路径
    moved_files: List[str]  # 成功移动的文件列表
    errors: List[str]  # 错误信息列表

class FolderListResponse(BaseModel):
    path: str
    files: List[FileInfo]
    parent_path: Optional[str] = None

class FolderPathRequest(BaseModel):
    path: str

class VideoMergeRequest(BaseModel):
    folder_path: str
    target_duration_min: int = 60  # 目标时长最小值（秒）
    target_duration_max: int = 180  # 目标时长最大值（秒）
    enable_transitions: bool = True  # 是否启用转场特效
    output_quality: str = "high"  # 输出质量：high, medium, low
    max_videos_per_merge: int = 10  # 每个合并视频最多包含的原视频数量

class TripleVideoMergeRequest(BaseModel):
    folder_path: str
    output_quality: str = "high"  # 输出质量：high, medium, low
    video_duration_per_segment: int = 10  # 每个视频片段播放时长（秒）
    transition_duration: float = 0.5  # 转场时长（秒）
    enable_preview: bool = True  # 是否生成预览

class VideoMergeResponse(BaseModel):
    success: bool
    task_id: str
    message: str
    output_folder: str
    estimated_time: int  # 预计完成时间（秒）

class VideoMergeProgress(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: int  # 0-100
    current_step: str
    total_videos: int
    processed_videos: int
    output_files: List[str]
    error_message: Optional[str] = None

class TripleVideoMergeResponse(BaseModel):
    success: bool
    task_id: str
    message: str
    output_folder: str
    estimated_time: int  # 预计完成时间（秒）
    preview_url: Optional[str] = None  # 预览视频URL

class TripleVideoMergeProgress(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: int  # 0-100
    current_step: str
    total_videos: int
    processed_videos: int
    output_files: List[str]
    preview_file: Optional[str] = None
    error_message: Optional[str] = None

# 平台发布记录模型
class PlatformPublishRecord(BaseModel):
    platform: str  # 平台名称：youtube, tiktok, instagram, douyin, kuaishou, xiaohongshu, weibo
    is_published: bool = False
    publish_date: Optional[str] = None
    publish_account: Optional[str] = None
    video_id: Optional[str] = None  # 平台上的视频ID
    video_url: Optional[str] = None  # 视频链接
    notes: Optional[str] = None

# 视频MD5记录相关模型
class VideoMD5Record(BaseModel):
    file_path: str
    file_name: str
    md5_hash: str
    file_size: int
    duration: Optional[int] = None
    resolution: Optional[str] = None
    platform_records: List[PlatformPublishRecord] = []  # 各平台发布记录
    notes: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class VideoMD5RecordCreate(BaseModel):
    file_path: str
    file_name: str
    md5_hash: str
    file_size: int
    duration: Optional[int] = None
    resolution: Optional[str] = None
    notes: Optional[str] = None

class VideoMD5RecordUpdate(BaseModel):
    notes: Optional[str] = None

class PlatformPublishUpdate(BaseModel):
    platform: str
    is_published: bool
    publish_date: Optional[str] = None
    publish_account: Optional[str] = None
    video_id: Optional[str] = None
    video_url: Optional[str] = None
    notes: Optional[str] = None

class VideoMD5RecordListResponse(BaseModel):
    records: List[VideoMD5Record]
    total: int
    page: int
    limit: int

class MD5CompareRequest(BaseModel):
    folder_path: str
    platform: Optional[str] = None  # 指定平台进行比较，如果为空则与所有已发布视频比较

class DuplicateInfo(BaseModel):
    platform: Optional[str] = None
    publish_date: Optional[str] = None
    publish_account: Optional[str] = None
    video_id: Optional[str] = None
    video_url: Optional[str] = None
    notes: Optional[str] = None
    published_platforms: Optional[List[Dict]] = None
    total_published: Optional[int] = None

class MD5CompareResult(BaseModel):
    file_name: str
    file_path: str
    md5_hash: str
    is_duplicate: bool
    existing_record: Optional[VideoMD5Record] = None
    duplicate_info: Optional[DuplicateInfo] = None

class MD5CompareResponse(BaseModel):
    results: List[MD5CompareResult]
    total_files: int
    duplicate_count: int

class BatchDeleteRequest(BaseModel):
    file_paths: List[str]

# 视频旋转相关模型
class VideoRotationRequest(BaseModel):
    video_paths: List[str]  # 要旋转的视频文件路径列表
    rotation_angle: int  # 旋转角度：90(向右90度), -90(向左90度), 180(180度)
    output_quality: str = 'medium'  # 输出质量：high, medium, low
    overwrite_original: bool = False  # 是否覆盖原文件
    output_suffix: str = '_rotated'  # 输出文件名后缀

# 视频加速相关模型
class VideoAccelerationRequest(BaseModel):
    video_paths: List[str]  # 要加速的视频文件路径列表
    target_duration: int = 59  # 目标时长（秒）
    output_quality: str = 'medium'  # 输出质量：high, medium, low
    overwrite_original: bool = False  # 是否覆盖原文件
    output_suffix: str = '_accelerated'  # 输出文件名后缀
    background_processing: Optional[bool] = False  # 是否后台处理

# 视频片头片尾处理相关模型
class VideoIntroOutroRequest(BaseModel):
    folder_path: str  # 视频文件夹路径
    selected_files: Optional[List[str]] = None  # 选中的文件列表（可选，如果不提供则处理整个文件夹）
    intro_path: Optional[str] = None  # 片头文件路径（可选）
    outro_path: Optional[str] = None  # 片尾文件路径（可选）
    output_folder: Optional[str] = None  # 输出文件夹路径（可选）
    transition_effect: str = 'fade'  # 转场效果：fade, dissolve, wipe, slide, zoom, none
    transition_duration: float = 1.0  # 转场时长（秒）
    output_quality: str = 'medium'  # 输出质量：high, medium, low
    max_concurrent: int = 3  # 最大并发处理数
    overwrite_original: bool = False  # 是否覆盖原文件
    output_suffix: str = '_with_intro_outro'  # 输出文件名后缀

class VideoRotationResult(BaseModel):
    original_path: str
    output_path: str
    success: bool
    error_message: Optional[str] = None
    processing_time_ms: int
    original_file_size: int
    output_file_size: int

class VideoRotationResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    results: List[VideoRotationResult]
    successful_count: int
    failed_count: int
    total_processing_time_ms: int

class VideoAccelerationResult(BaseModel):
    original_path: str
    output_path: str
    success: bool
    error_message: Optional[str] = None
    processing_time_ms: int
    original_file_size: int
    output_file_size: int
    original_duration: float
    output_duration: float
    speed_factor: float

class VideoAccelerationResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    results: Optional[List[VideoAccelerationResult]] = None
    successful_count: Optional[int] = 0
    failed_count: Optional[int] = 0
    total_processing_time_ms: Optional[int] = 0
    # 后台处理相关字段
    task_id: Optional[str] = None  # 后台任务ID
    message: Optional[str] = None  # 提示信息

class VideoIntroOutroResult(BaseModel):
    input_path: str
    output_path: str
    success: bool
    error_message: Optional[str] = None
    processing_time_ms: int
    output_size: int

class VideoIntroOutroResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    results: List[VideoIntroOutroResult]
    successful_count: int
    failed_count: int
    total_count: int
    total_processing_time_ms: int
    output_folder: str
    total_output_size: int

# 音频处理相关模型
class GenerateSubtitlesRequest(BaseModel):
    folder_path: str  # 文件夹路径
    output_format: str = 'srt'  # 输出格式 (srt, vtt, txt)
    language: str = 'auto'  # 语言代码 (auto, zh, en, etc.)
    model_size: str = 'base'  # Whisper模型大小 (tiny, base, small, medium, large)
    max_concurrent: int = 2  # 最大并发处理数

class ExtractAudioRequest(BaseModel):
    folder_path: str  # 文件夹路径
    output_format: str = 'wav'  # 输出音频格式 (wav, mp3, aac)
    quality: str = 'high'  # 音频质量 (high, medium, low)
    max_concurrent: int = 3  # 最大并发处理数

class SeparateVocalsRequest(BaseModel):
    folder_path: str  # 文件夹路径
    selected_files: Optional[List[str]] = None  # 选中的文件列表（可选，如果不提供则处理整个文件夹）
    output_type: str = 'vocals'  # 输出类型 (vocals, instrumental, both)
    separation_method: str = 'ffmpeg'  # 分离方法 (ffmpeg: 传统方法, librosa: AI深度学习方法)
    output_quality: str = 'high'  # 输出质量 (high, medium, low)
    max_concurrent: int = 2  # 最大并发处理数

class ReplaceAudioRequest(BaseModel):
    folder_path: str  # 视频文件夹路径
    new_audio_path: str  # 新音频文件路径
    audio_volume: float = 1.0  # 音频音量 (0.0-2.0)
    fade_duration: float = 0.5  # 淡入淡出时长（秒）
    max_concurrent: int = 3  # 最大并发处理数

class AudioProcessingResult(BaseModel):
    success: bool
    input_file: str
    output_file: str
    error_message: Optional[str] = None
    processing_time_ms: int
    input_file_size: int
    output_file_size: int
    message: str

class VocalSeparationResult(BaseModel):
    success: bool
    input_file: str
    vocals_file: str
    instrumental_file: str
    error_message: Optional[str] = None
    processing_time_ms: int
    input_file_size: int
    vocals_file_size: int
    instrumental_file_size: int
    message: str

class AudioProcessingResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    processed_files: int
    successful_count: int
    failed_count: int
    output_directory: str
    results: List[AudioProcessingResult]

class VocalSeparationResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    processed_files: int
    successful_count: int
    failed_count: int
    output_directory: str
    results: List[VocalSeparationResult]

class ReplaceAudioResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    processed_files: int
    successful_count: int
    failed_count: int
    output_directory: str
    new_audio_file: str
    results: List[AudioProcessingResult]

# 获取数据库连接
def get_db(request: Request) -> Database:
    """获取数据库连接"""
    return request.app.state.mongo_db

# MD5记录管理功能
def get_video_md5_collection(db: Database):
    """获取视频MD5记录集合"""
    return db.video_md5_records

async def save_video_md5_record(db: Database, record: VideoMD5RecordCreate) -> str:
    """保存视频MD5记录到数据库"""
    try:
        collection = get_video_md5_collection(db)

        # 检查是否已存在相同MD5的记录
        existing = await collection.find_one({"md5_hash": record.md5_hash})
        if existing:
            logger.warning(f"MD5记录已存在: {record.md5_hash}")
            return str(existing["_id"])

        # 创建新记录
        record_dict = record.dict()
        record_dict["created_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
        record_dict["updated_at"] = time.strftime("%Y-%m-%d %H:%M:%S")

        result = await collection.insert_one(record_dict)
        logger.info(f"保存MD5记录成功: {record.file_name} -> {record.md5_hash}")
        return str(result.inserted_id)

    except Exception as e:
        logger.error(f"保存MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存MD5记录失败: {str(e)}")

async def update_video_md5_record(db: Database, md5_hash: str, update_data: VideoMD5RecordUpdate) -> bool:
    """更新视频MD5记录"""
    try:
        collection = get_video_md5_collection(db)

        # 构建更新数据
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        update_dict["updated_at"] = time.strftime("%Y-%m-%d %H:%M:%S")

        result = await collection.update_one(
            {"md5_hash": md5_hash},
            {"$set": update_dict}
        )

        if result.matched_count > 0:
            logger.info(f"更新MD5记录成功: {md5_hash}")
            return True
        else:
            logger.warning(f"未找到MD5记录: {md5_hash}")
            return False

    except Exception as e:
        logger.error(f"更新MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新MD5记录失败: {str(e)}")

async def update_platform_publish_status(db: Database, md5_hash: str, platform_data: PlatformPublishUpdate) -> bool:
    """更新特定平台的发布状态"""
    try:
        collection = get_video_md5_collection(db)

        # 查找现有记录
        record = await collection.find_one({"md5_hash": md5_hash})
        if not record:
            logger.warning(f"未找到MD5记录: {md5_hash}")
            return False

        # 获取现有的平台记录
        platform_records = record.get("platform_records", [])

        # 查找是否已存在该平台的记录
        platform_found = False
        for i, platform_record in enumerate(platform_records):
            if platform_record.get("platform") == platform_data.platform:
                # 更新现有平台记录
                platform_records[i] = platform_data.dict()
                platform_found = True
                break

        # 如果没有找到该平台的记录，添加新记录
        if not platform_found:
            platform_records.append(platform_data.dict())

        # 更新数据库记录
        result = await collection.update_one(
            {"md5_hash": md5_hash},
            {
                "$set": {
                    "platform_records": platform_records,
                    "updated_at": time.strftime("%Y-%m-%d %H:%M:%S")
                }
            }
        )

        if result.matched_count > 0:
            logger.info(f"更新平台发布状态成功: {md5_hash} - {platform_data.platform}")
            return True
        else:
            logger.warning(f"更新平台发布状态失败: {md5_hash}")
            return False

    except Exception as e:
        logger.error(f"更新平台发布状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新平台发布状态失败: {str(e)}")

async def get_platform_publish_status(db: Database, md5_hash: str, platform: str) -> Optional[Dict]:
    """获取特定平台的发布状态"""
    try:
        collection = get_video_md5_collection(db)
        record = await collection.find_one({"md5_hash": md5_hash})

        if not record:
            return None

        platform_records = record.get("platform_records", [])
        for platform_record in platform_records:
            if platform_record.get("platform") == platform:
                return platform_record

        return None

    except Exception as e:
        logger.error(f"获取平台发布状态失败: {str(e)}")
        return None

def check_platform_published(db: Database, md5_hash: str, platform: str) -> bool:
    """检查视频是否在特定平台已发布"""
    platform_status = get_platform_publish_status(db, md5_hash, platform)
    return platform_status and platform_status.get("is_published", False)

async def get_published_videos_by_platform(db: Database, platform: str, page: int = 1, limit: int = 20) -> Dict:
    """获取特定平台已发布的视频MD5记录列表"""
    try:
        collection = get_video_md5_collection(db)

        # 构建查询条件：查找包含指定平台且已发布的记录
        query = {
            "platform_records": {
                "$elemMatch": {
                    "platform": platform,
                    "is_published": True
                }
            }
        }

        # 计算总数
        total = await collection.count_documents(query)

        # 分页查询
        skip = (page - 1) * limit
        cursor = collection.find(query).skip(skip).limit(limit).sort("updated_at", -1)

        records = []
        async for record in cursor:
            record["_id"] = str(record["_id"])

            # 只返回指定平台的发布信息
            platform_records = record.get("platform_records", [])
            filtered_platform_records = [
                p for p in platform_records
                if p.get("platform") == platform and p.get("is_published", False)
            ]
            record["platform_records"] = filtered_platform_records

            records.append(record)

        return {
            "records": records,
            "total": total,
            "page": page,
            "limit": limit,
            "platform": platform
        }

    except Exception as e:
        logger.error(f"获取平台已发布视频失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取平台已发布视频失败: {str(e)}")

async def get_platform_published_md5_list(db: Database, platform: str) -> List[str]:
    """获取特定平台已发布视频的MD5哈希值列表"""
    try:
        collection = get_video_md5_collection(db)

        # 构建查询条件
        query = {
            "platform_records": {
                "$elemMatch": {
                    "platform": platform,
                    "is_published": True
                }
            }
        }

        # 只返回MD5哈希值
        cursor = collection.find(query, {"md5_hash": 1, "_id": 0})

        md5_list = []
        async for record in cursor:
            md5_list.append(record["md5_hash"])

        return md5_list

    except Exception as e:
        logger.error(f"获取平台已发布MD5列表失败: {str(e)}")
        return []

async def get_video_md5_record_by_hash(db: Database, md5_hash: str) -> Optional[Dict]:
    """根据MD5哈希值获取记录"""
    try:
        collection = get_video_md5_collection(db)
        record = await collection.find_one({"md5_hash": md5_hash})
        return record
    except Exception as e:
        logger.error(f"查询MD5记录失败: {str(e)}")
        return None

async def get_video_md5_records(db: Database, page: int = 1, limit: int = 20,
                         folder_path: str = None) -> Dict:
    """获取视频MD5记录列表"""
    try:
        collection = get_video_md5_collection(db)

        # 构建查询条件
        query = {}
        if folder_path:
            # 规范化路径分隔符
            normalized_path = folder_path.replace('/', '\\')
            # 确保路径以反斜杠结尾，以匹配该文件夹下的文件
            if not normalized_path.endswith('\\'):
                normalized_path += '\\'

            # 使用更安全的正则表达式转义方法
            import re
            escaped_path = re.escape(normalized_path)

            # 修改正则表达式：只匹配当前目录下的文件，不包括子文件夹
            # ^{escaped_path}[^\\]+$ 表示：以指定路径开头，后面跟着非反斜杠的字符（文件名），然后结束
            query["file_path"] = {"$regex": f"^{escaped_path}[^\\\\]+$"}

            logger.info(f"查询MD5记录，文件夹路径: {folder_path}")
            logger.info(f"规范化路径: {normalized_path}")
            logger.info(f"转义后路径: {escaped_path}")
            logger.info(f"查询条件: {query}")

        # 计算总数
        total = await collection.count_documents(query)
        logger.info(f"找到 {total} 条MD5记录")

        # 分页查询
        skip = (page - 1) * limit
        cursor = collection.find(query).sort("created_at", -1).skip(skip).limit(limit)

        records = []
        # 使用 to_list 方法获取所有记录
        cursor_records = await cursor.to_list(length=limit)
        for record in cursor_records:
            record["_id"] = str(record["_id"])
            records.append(record)
            # 调试输出
            logger.info(f"找到MD5记录: {record.get('file_name')} -> {record.get('file_path')}")

        logger.info(f"查询完成，返回 {len(records)} 条记录")

        return {
            "records": records,
            "total": total,
            "page": page,
            "limit": limit
        }

    except Exception as e:
        logger.error(f"获取MD5记录列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MD5记录列表失败: {str(e)}")

async def compare_folder_md5_with_records(db: Database, folder_path: str, platform: Optional[str] = None) -> Dict:
    """比对文件夹中的视频文件MD5与数据库记录

    Args:
        db: 数据库连接
        folder_path: 文件夹路径
        platform: 指定平台名称，如果提供则只与该平台已发布的视频进行比较
    """
    try:
        # 获取文件夹中的视频文件
        video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        files = get_folder_contents(folder_path, video_extensions, include_md5=True)

        results = []
        duplicate_count = 0

        for file in files:
            if file.is_directory or not file.md5_hash:
                continue

            # 查询数据库中是否存在相同MD5的记录
            existing_record = await get_video_md5_record_by_hash(db, file.md5_hash)
            is_duplicate = False
            duplicate_info = None

            if existing_record:
                if platform:
                    # 检查指定平台是否已发布
                    platform_records = existing_record.get("platform_records", [])
                    for platform_record in platform_records:
                        if (platform_record.get("platform") == platform and
                            platform_record.get("is_published", False)):
                            is_duplicate = True
                            duplicate_info = {
                                "platform": platform,
                                "publish_date": platform_record.get("publish_date"),
                                "publish_account": platform_record.get("publish_account"),
                                "video_id": platform_record.get("video_id"),
                                "video_url": platform_record.get("video_url"),
                                "notes": platform_record.get("notes")
                            }
                            break
                else:
                    # 检查是否在任何平台已发布
                    platform_records = existing_record.get("platform_records", [])
                    published_platforms = []
                    for platform_record in platform_records:
                        if platform_record.get("is_published", False):
                            published_platforms.append({
                                "platform": platform_record.get("platform"),
                                "publish_date": platform_record.get("publish_date"),
                                "publish_account": platform_record.get("publish_account"),
                                "video_id": platform_record.get("video_id"),
                                "video_url": platform_record.get("video_url"),
                                "notes": platform_record.get("notes")
                            })

                    if published_platforms:
                        is_duplicate = True
                        duplicate_info = {
                            "published_platforms": published_platforms,
                            "total_published": len(published_platforms)
                        }

            if is_duplicate:
                duplicate_count += 1

            results.append({
                "file_name": file.name,
                "file_path": file.path,
                "md5_hash": file.md5_hash,
                "is_duplicate": is_duplicate,
                "existing_record": existing_record,
                "duplicate_info": duplicate_info
            })

        return {
            "results": results,
            "total_files": len(results),
            "duplicate_count": duplicate_count
        }

    except Exception as e:
        logger.error(f"MD5比对失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"MD5比对失败: {str(e)}")

def batch_delete_files(file_paths: List[str]) -> Dict:
    """批量删除文件或文件夹"""
    try:
        deleted_count = 0
        errors = []

        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    if os.path.isfile(file_path):
                        # 删除文件
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"删除文件成功: {file_path}")
                    elif os.path.isdir(file_path):
                        # 删除文件夹（包括所有内容）
                        import shutil
                        shutil.rmtree(file_path)
                        deleted_count += 1
                        logger.info(f"删除文件夹成功: {file_path}")
                    else:
                        errors.append(f"路径既不是文件也不是文件夹: {file_path}")
                else:
                    errors.append(f"路径不存在: {file_path}")
            except Exception as e:
                error_msg = f"删除失败 {file_path}: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)

        return {
            "success": deleted_count > 0,
            "deleted_count": deleted_count,
            "total_count": len(file_paths),
            "errors": errors
        }

    except Exception as e:
        logger.error(f"批量删除文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除文件失败: {str(e)}")

# 获取文件扩展名
def get_file_extension(filename: str) -> Optional[str]:
    if '.' in filename:
        return filename.split('.')[-1].lower()
    return None

def find_subtitle_content(folder_path: str, filename_without_ext: str) -> Optional[str]:
    """查找并读取字幕文件内容（优先txt格式）

    Args:
        folder_path: 文件夹路径
        filename_without_ext: 文件名（不含扩展名）

    Returns:
        Optional[str]: 字幕内容，如果没有找到则返回None
    """
    # 支持的字幕文件扩展名，优先txt格式
    subtitle_extensions = ['txt', 'srt', 'vtt', 'ass', 'ssa', 'sub']

    # 可能的字幕文件位置
    subtitle_locations = [
        folder_path,  # 与视频文件同目录
        os.path.join(folder_path, "subtitles"),  # subtitles子目录
        os.path.join(folder_path, "字幕"),  # 中文字幕目录
    ]

    for location in subtitle_locations:
        if not os.path.exists(location):
            continue

        for subtitle_ext in subtitle_extensions:
            subtitle_filename = f"{filename_without_ext}.{subtitle_ext}"
            subtitle_path = os.path.join(location, subtitle_filename)

            if os.path.exists(subtitle_path):
                try:
                    with open(subtitle_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()

                    # 如果是srt或vtt格式，提取纯文本内容
                    if subtitle_ext in ['srt', 'vtt']:
                        content = extract_text_from_subtitle(content, subtitle_ext)

                    if content:  # 只返回非空内容
                        return content

                except Exception as e:
                    logger.warning(f"读取字幕文件失败 {subtitle_path}: {str(e)}")
                    continue

    return None

def extract_text_from_subtitle(content: str, format_type: str) -> str:
    """从字幕文件中提取纯文本内容"""
    lines = content.split('\n')
    text_lines = []

    if format_type == 'srt':
        # SRT格式：跳过序号和时间戳行
        for line in lines:
            line = line.strip()
            if not line:
                continue
            # 跳过纯数字行（序号）
            if line.isdigit():
                continue
            # 跳过时间戳行
            if '-->' in line:
                continue
            text_lines.append(line)

    elif format_type == 'vtt':
        # VTT格式：跳过WEBVTT头和时间戳行
        for line in lines:
            line = line.strip()
            if not line or line == 'WEBVTT':
                continue
            # 跳过时间戳行
            if '-->' in line:
                continue
            text_lines.append(line)

    return ' '.join(text_lines)

def rename_associated_subtitle_files(folder_path: str, old_filename: str, new_filename_without_ext: str, errors: list) -> int:
    """重命名与视频文件关联的字幕文件

    Args:
        folder_path: 文件夹路径
        old_filename: 原始文件名（包含扩展名）
        new_filename_without_ext: 新文件名（不包含扩展名）
        errors: 错误列表

    Returns:
        int: 成功重命名的字幕文件数量
    """
    renamed_count = 0

    # 获取原始文件名（不含扩展名）
    old_name_without_ext = old_filename
    if '.' in old_filename:
        old_name_without_ext = old_filename.rsplit('.', 1)[0]

    # 支持的字幕文件扩展名
    subtitle_extensions = ['srt', 'vtt', 'txt', 'ass', 'ssa', 'sub']

    # 可能的字幕文件位置
    subtitle_locations = [
        folder_path,  # 与视频文件同目录
        os.path.join(folder_path, "subtitles"),  # subtitles子目录
        os.path.join(folder_path, "字幕"),  # 中文字幕目录
    ]

    for location in subtitle_locations:
        if not os.path.exists(location):
            continue

        try:
            for subtitle_ext in subtitle_extensions:
                # 查找匹配的字幕文件
                old_subtitle_filename = f"{old_name_without_ext}.{subtitle_ext}"
                old_subtitle_path = os.path.join(location, old_subtitle_filename)

                if os.path.exists(old_subtitle_path):
                    # 构建新的字幕文件路径
                    new_subtitle_filename = f"{new_filename_without_ext}.{subtitle_ext}"
                    new_subtitle_path = os.path.join(location, new_subtitle_filename)

                    # 检查新字幕文件名是否已存在
                    if os.path.exists(new_subtitle_path) and old_subtitle_path != new_subtitle_path:
                        errors.append(f"字幕文件 {new_subtitle_filename} 已存在，跳过重命名 {old_subtitle_filename}")
                        continue

                    # 执行字幕文件重命名
                    os.rename(old_subtitle_path, new_subtitle_path)
                    renamed_count += 1
                    logger.info(f"字幕文件重命名成功: {old_subtitle_filename} -> {new_subtitle_filename}")

        except Exception as e:
            error_msg = f"重命名字幕文件失败 {location}: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg)

    return renamed_count

# 计算文件MD5哈希值
def calculate_file_md5(file_path: str) -> Optional[str]:
    """计算文件的MD5哈希值"""
    try:
        logger.debug(f"🔍 检查文件是否存在: {file_path}")
        if not os.path.exists(file_path):
            logger.error(f"❌ 文件不存在: {file_path}")
            return None

        logger.debug(f"📊 开始计算MD5哈希值: {file_path}")
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)

        result = hash_md5.hexdigest()
        logger.info(f"✅ MD5计算成功: {file_path} -> {result}")
        return result
    except Exception as e:
        logger.error(f"❌ 计算MD5失败 {file_path}: {str(e)}")
        return None



# 获取媒体文件信息
def get_media_info(file_path: str) -> Optional[Dict[str, Any]]:
    """使用ffprobe获取媒体文件信息"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return None

        # 检查文件扩展名是否为媒体文件
        extension = get_file_extension(file_path)
        if not extension or extension not in ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'mp3', 'wav', 'aac']:
            return None

        # 检查ffprobe是否可用
        try:
            subprocess.run(['ffprobe', '-version'], capture_output=True, check=True, timeout=5)
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            logger.warning("ffprobe不可用，跳过媒体信息获取")
            return None

        # 使用ffprobe获取媒体信息
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams', file_path
        ]

        # 设置环境变量以处理编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            encoding='utf-8',
            errors='ignore',  # 忽略编码错误
            env=env
        )

        if result.returncode != 0:
            logger.warning(f"ffprobe执行失败 {file_path}: {result.stderr}")
            return None

        # 检查输出是否为空
        if not result.stdout or result.stdout.strip() == '':
            logger.warning(f"ffprobe返回空结果 {file_path}")
            return None

        try:
            info = json.loads(result.stdout)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败 {file_path}: {e}")
            logger.debug(f"ffprobe输出: {result.stdout[:500]}...")  # 只记录前500字符
            return None

        # 提取关键信息
        media_info = {}

        # 格式信息
        if 'format' in info:
            format_info = info['format']
            media_info['duration'] = float(format_info.get('duration', 0))
            media_info['size'] = int(format_info.get('size', 0))
            media_info['bit_rate'] = int(format_info.get('bit_rate', 0))
            media_info['format_name'] = format_info.get('format_name', '')

        # 流信息
        if 'streams' in info:
            video_streams = [s for s in info['streams'] if s.get('codec_type') == 'video']
            audio_streams = [s for s in info['streams'] if s.get('codec_type') == 'audio']

            if video_streams:
                video = video_streams[0]
                media_info['video'] = {
                    'codec': video.get('codec_name', ''),
                    'width': video.get('width', 0),
                    'height': video.get('height', 0),
                    'fps': eval(video.get('r_frame_rate', '0/1')) if video.get('r_frame_rate') else 0
                }

            if audio_streams:
                audio = audio_streams[0]
                media_info['audio'] = {
                    'codec': audio.get('codec_name', ''),
                    'sample_rate': audio.get('sample_rate', 0),
                    'channels': audio.get('channels', 0)
                }

        return media_info

    except Exception as e:
        logger.error(f"获取媒体信息失败 {file_path}: {str(e)}")
        return None

# 检查文件是否已上传
def check_file_uploaded(file_path: str, db: Database) -> bool:
    """检查文件是否已经上传过"""
    try:
        # 计算文件MD5
        md5_hash = calculate_file_md5(file_path)
        if not md5_hash:
            return False

        # 在数据库中查找相同MD5的文件
        # 这里假设有一个uploads集合存储上传记录
        uploads_collection = db.uploads
        existing = uploads_collection.find_one({"file_md5": md5_hash})

        return existing is not None

    except Exception as e:
        logger.error(f"检查上传状态失败 {file_path}: {str(e)}")
        return False

# 获取文件夹内容（简化版本）
def get_folder_contents(folder_path: str, filter_extensions: List[str] = None,
                       include_md5: bool = False, include_media_info: bool = False,
                       db: Database = None) -> List[FileInfo]:
    try:
        contents = []
        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)
            is_directory = os.path.isdir(item_path)

            # 获取文件扩展名
            extension = None if is_directory else get_file_extension(item)

            # 如果指定了扩展名过滤，且当前文件不是目录且扩展名不在过滤列表中，则跳过
            if filter_extensions and not is_directory and extension not in filter_extensions:
                continue

            try:
                size = 0 if is_directory else os.path.getsize(item_path)
                last_modified = os.path.getmtime(item_path)

                # 计算MD5和媒体信息（仅对文件）
                md5_hash = None
                is_uploaded = None
                media_info = None

                if not is_directory:
                    if include_md5:
                        md5_hash = calculate_file_md5(item_path)
                        if db is not None and md5_hash:
                            is_uploaded = check_file_uploaded(item_path, db)

                    if include_media_info:
                        media_info = get_media_info(item_path)

                contents.append(FileInfo(
                    name=item,
                    path=item_path,
                    size=size,
                    is_directory=is_directory,
                    extension=extension,
                    last_modified=str(last_modified),
                    md5_hash=md5_hash,
                    is_uploaded=is_uploaded,
                    media_info=media_info
                ))
            except (PermissionError, OSError) as e:
                logger.warning(f"无法获取文件信息 {item_path}: {str(e)}")
                # 添加文件但标记为无法访问
                contents.append(FileInfo(
                    name=item,
                    path=item_path,
                    size=-1,  # 表示无法获取大小
                    is_directory=is_directory,
                    extension=extension,
                    last_modified=None,
                    md5_hash=None,
                    is_uploaded=None,
                    media_info=None
                ))

        # 排序：先文件夹，再文件，按名称排序
        contents.sort(key=lambda x: (not x.is_directory, x.name.lower()))
        return contents
    except PermissionError:
        logger.error(f"无权限访问文件夹: {folder_path}")
        raise HTTPException(status_code=403, detail="无权限访问该文件夹")
    except FileNotFoundError:
        logger.error(f"文件夹不存在: {folder_path}")
        raise HTTPException(status_code=404, detail="文件夹不存在")
    except Exception as e:
        logger.error(f"获取文件夹内容失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取文件夹内容失败: {str(e)}")

# API端点：获取文件夹内容（简化版本）
@router.post("/list", response_model=FolderListResponse)
async def list_folder_contents(
    request: FolderPathRequest,
    db_request: Request,
    filter_extensions: List[str] = Query(None, description="过滤特定扩展名的文件，如 mp4, avi"),
    include_md5: bool = Query(False, description="是否计算文件MD5哈希值"),
    include_media_info: bool = Query(False, description="是否获取媒体文件信息"),
    core_service_id: str = Query(None, description="指定Core服务ID")
):
    """
    获取指定文件夹的内容，包括子文件夹和文件

    - **path**: 文件夹路径
    - **filter_extensions**: 可选，过滤特定扩展名的文件
    - **include_md5**: 是否计算文件MD5哈希值
    - **include_media_info**: 是否获取媒体文件信息
    """
    logger.info(f"获取文件夹内容: {request.path}, 过滤扩展名: {filter_extensions}, MD5: {include_md5}, 媒体信息: {include_media_info}, Core服务ID: {core_service_id}")

    # 获取数据库连接
    db = None
    try:
        db = get_db(db_request)
    except Exception as e:
        logger.warning(f"无法获取数据库连接: {str(e)}")

    # 尝试通过Core服务获取文件夹内容
    logger.info(f"=== 开始处理Core服务选择，参数: '{core_service_id}' ===")
    from app.core.grpc_client import get_core_client_by_service_id
    core_client = get_core_client_by_service_id(core_service_id)

    if core_client is None:
        logger.warning("Core服务不可用，使用本地文件系统处理")
        # 降级到本地处理
        # 验证路径
        if not os.path.exists(request.path):
            raise HTTPException(status_code=404, detail="文件夹不存在")
        if not os.path.isdir(request.path):
            raise HTTPException(status_code=400, detail="指定路径不是文件夹")

        contents = get_folder_contents(request.path, filter_extensions, include_md5, include_media_info, db)
    else:
        logger.info(f"通过Core服务获取文件夹内容: {request.path}")
        try:
            # 调用Core服务的ListDirectory接口
            result = await core_client.list_directory(
                path=request.path,
                filter_extensions=filter_extensions,
                include_md5=include_md5,
                include_media_info=include_media_info
            )

            if not result.get("success", False):
                error_msg = result.get("error", "未知错误")
                logger.error(f"Core服务调用失败: {error_msg}")
                raise HTTPException(status_code=500, detail=f"获取文件夹内容失败: {error_msg}")

            # 转换Core服务返回的数据格式为Backend期望的格式
            contents = []
            for file_data in result.get("files", []):
                # 获取文件扩展名
                extension = None
                if not file_data["is_directory"]:
                    extension = get_file_extension(file_data["name"])

                file_item = FileInfo(
                    name=file_data["name"],
                    path=file_data["path"],
                    is_directory=file_data["is_directory"],
                    size=file_data["size"],
                    extension=extension,
                    last_modified=str(file_data["modified_time"]) if file_data["modified_time"] else None,
                    md5_hash=file_data.get("md5_hash"),
                    media_info=file_data.get("media_info")
                )
                contents.append(file_item)

            logger.info(f"Core服务返回 {len(contents)} 个项目")

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"Core服务调用失败，降级到本地处理: {str(e)}")
            # 验证路径
            if not os.path.exists(request.path):
                raise HTTPException(status_code=404, detail="文件夹不存在")
            if not os.path.isdir(request.path):
                raise HTTPException(status_code=400, detail="指定路径不是文件夹")

            contents = get_folder_contents(request.path, filter_extensions, include_md5, include_media_info, db)

    # 获取父文件夹路径
    parent_path = os.path.dirname(request.path) if request.path != os.path.dirname(request.path) else None

    logger.info(f"文件夹内容获取完成，返回 {len(contents)} 个项目")

    return FolderListResponse(
        path=request.path,
        files=contents,
        parent_path=parent_path
    )




# API端点：获取驱动器列表（Windows）
@router.get("/drives", response_model=List[str])
async def list_drives():
    """获取Windows系统上的驱动器列表"""
    try:
        import string
        from ctypes import windll
        
        drives = []
        bitmask = windll.kernel32.GetLogicalDrives()
        
        for letter in string.ascii_uppercase:
            if bitmask & 1:
                drive_path = f"{letter}:\\"
                drives.append(drive_path)
            bitmask >>= 1
            
        return drives
    except Exception as e:
        logger.error(f"获取驱动器列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取驱动器列表失败: {str(e)}")

# API端点：验证文件夹中的视频文件
@router.post("/validate-video-folder", response_model=Dict[str, Any])
async def validate_video_folder(request: FolderPathRequest):
    """
    验证指定文件夹中的视频文件
    
    - **path**: 文件夹路径
    """
    logger.info(f"验证视频文件夹: {request.path}")
    
    # 验证路径
    if not os.path.exists(request.path):
        return {"valid": False, "message": "文件夹不存在", "files": []}
    
    if not os.path.isdir(request.path):
        return {"valid": False, "message": "指定路径不是文件夹", "files": []}
    
    # 视频文件扩展名
    video_extensions = ["mp4", "avi", "mov", "wmv", "flv", "mkv"]
    
    try:
        # 获取文件夹内容
        all_contents = get_folder_contents(request.path)
        
        # 过滤出视频文件
        video_files = [
            {
                "name": file.name,
                "path": file.path,
                "size": file.size,
                "extension": file.extension,
                "valid": file.extension in video_extensions and file.size > 0
            }
            for file in all_contents
            if not file.is_directory and file.extension in video_extensions
        ]
        
        # 检查是否有有效的视频文件
        valid = len(video_files) > 0
        message = f"找到 {len(video_files)} 个视频文件" if valid else "文件夹中没有视频文件"
        
        return {
            "valid": valid,
            "message": message,
            "files": video_files
        }
    except Exception as e:
        logger.error(f"验证视频文件夹失败: {str(e)}", exc_info=True)
        return {
            "valid": False,
            "message": f"验证失败: {str(e)}",
            "files": []
        }

# API端点：获取文件名和字幕内容（用于AI辅助命名）
@router.post("/filenames-with-subtitles", response_model=FilenamesWithSubtitlesResponse)
async def get_filenames_with_subtitles(request: FilenamesWithSubtitlesRequest):
    """
    获取文件夹中文件名和对应的字幕内容，用于AI辅助命名

    - **folder_path**: 文件夹路径

    返回格式：每行包含"文件名 | 字幕内容"
    """
    logger.info(f"获取文件名和字幕内容: {request.folder_path}")

    try:
        # 验证路径
        if not os.path.exists(request.folder_path):
            return FilenamesWithSubtitlesResponse(
                success=False,
                data=[],
                error="文件夹不存在"
            )

        if not os.path.isdir(request.folder_path):
            return FilenamesWithSubtitlesResponse(
                success=False,
                data=[],
                error="指定路径不是文件夹"
            )

        # 获取文件夹中的所有文件
        files = []
        for filename in os.listdir(request.folder_path):
            file_path = os.path.join(request.folder_path, filename)
            if os.path.isfile(file_path):
                # 过滤掉字幕文件本身
                extension = get_file_extension(filename)
                if extension not in ['srt', 'vtt', 'txt', 'ass', 'ssa', 'sub']:
                    files.append(filename)

        # 按文件名排序
        files.sort()

        # 为每个文件生成"文件名 | 字幕内容"格式
        result_lines = []
        for filename in files:
            # 获取不含扩展名的文件名
            name_without_ext = filename
            if '.' in filename:
                name_without_ext = filename.rsplit('.', 1)[0]

            # 查找对应的字幕内容
            subtitle_content = find_subtitle_content(request.folder_path, name_without_ext)

            if subtitle_content:
                # 限制字幕内容长度，避免过长
                if len(subtitle_content) > 200:
                    subtitle_content = subtitle_content[:200] + "..."
                result_line = f"{name_without_ext} | {subtitle_content}"
            else:
                result_line = name_without_ext

            result_lines.append(result_line)

        return FilenamesWithSubtitlesResponse(
            success=True,
            data=result_lines
        )

    except Exception as e:
        logger.error(f"获取文件名和字幕内容失败: {str(e)}", exc_info=True)
        return FilenamesWithSubtitlesResponse(
            success=False,
            data=[],
            error=f"获取失败: {str(e)}"
        )

# API端点：批量重命名文件
@router.post("/batch-rename", response_model=BatchRenameResponse)
async def batch_rename_files(request: BatchRenameRequest):
    """
    批量重命名文件夹中的文件

    - **folder_path**: 文件夹路径
    - **file_names**: 新文件名列表（不包含扩展名）
    - **selected_files**: 可选，指定要重命名的文件列表。如果不提供，则重命名文件夹中的所有文件
    """
    logger.info(f"批量重命名文件: {request.folder_path}, 文件数量: {len(request.file_names)}, 选中文件: {request.selected_files}")

    # 验证路径
    if not os.path.exists(request.folder_path):
        raise HTTPException(status_code=404, detail="文件夹不存在")

    if not os.path.isdir(request.folder_path):
        raise HTTPException(status_code=400, detail="指定路径不是文件夹")

    try:
        # 确定要重命名的文件列表
        if request.selected_files:
            # 使用指定的文件列表
            target_files = []
            for filename in request.selected_files:
                file_path = os.path.join(request.folder_path, filename)
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    target_files.append(filename)
                else:
                    logger.warning(f"指定的文件不存在或不是文件: {filename}")

            # 按文件名排序，确保重命名顺序一致
            target_files.sort()
        else:
            # 获取文件夹中的所有文件（排除目录）
            target_files = []
            for item in os.listdir(request.folder_path):
                item_path = os.path.join(request.folder_path, item)
                if os.path.isfile(item_path):
                    target_files.append(item)

            # 按文件名排序，确保重命名顺序一致
            target_files.sort()

        # 检查文件数量是否匹配
        if len(target_files) != len(request.file_names):
            raise HTTPException(
                status_code=400,
                detail=f"文件数量不匹配：要重命名的文件有 {len(target_files)} 个，但提供了 {len(request.file_names)} 个新名称"
            )

        renamed_count = 0
        total_subtitle_renamed_count = 0
        errors = []

        # 执行重命名
        for i, old_filename in enumerate(target_files):
            try:
                # 获取原文件的扩展名
                old_path = os.path.join(request.folder_path, old_filename)
                extension = get_file_extension(old_filename)

                # 构建新文件名
                new_filename = request.file_names[i]
                if extension:
                    new_filename += f".{extension}"

                new_path = os.path.join(request.folder_path, new_filename)

                # 检查新文件名是否已存在
                if os.path.exists(new_path) and old_path != new_path:
                    errors.append(f"文件 {new_filename} 已存在，跳过重命名 {old_filename}")
                    continue

                # 执行主文件重命名
                os.rename(old_path, new_path)
                renamed_count += 1
                logger.info(f"重命名成功: {old_filename} -> {new_filename}")

                # 检查并重命名对应的字幕文件
                subtitle_renamed_count = rename_associated_subtitle_files(
                    request.folder_path, old_filename, request.file_names[i], errors
                )
                total_subtitle_renamed_count += subtitle_renamed_count
                if subtitle_renamed_count > 0:
                    logger.info(f"同时重命名了 {subtitle_renamed_count} 个字幕文件")

            except Exception as e:
                error_msg = f"重命名失败 {old_filename}: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)

        return BatchRenameResponse(
            success=renamed_count > 0,
            renamed_count=renamed_count,
            subtitle_renamed_count=total_subtitle_renamed_count,
            errors=errors
        )

    except Exception as e:
        logger.error(f"批量重命名失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量重命名失败: {str(e)}")

# API端点：获取文件名列表（用于复制）
@router.post("/get-filenames", response_model=List[str])
async def get_filenames_for_copy(request: FolderPathRequest):
    """
    获取文件夹中所有文件的文件名（不含扩展名），用于复制操作

    - **path**: 文件夹路径
    """
    logger.info(f"获取文件名列表: {request.path}")

    # 验证路径
    if not os.path.exists(request.path):
        raise HTTPException(status_code=404, detail="文件夹不存在")

    if not os.path.isdir(request.path):
        raise HTTPException(status_code=400, detail="指定路径不是文件夹")

    try:
        filenames = []
        for item in os.listdir(request.path):
            item_path = os.path.join(request.path, item)
            if os.path.isfile(item_path):
                # 获取不含扩展名的文件名
                name_without_ext = os.path.splitext(item)[0]
                filenames.append(name_without_ext)

        # 排序确保顺序一致
        filenames.sort()

        return filenames

    except Exception as e:
        logger.error(f"获取文件名列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取文件名列表失败: {str(e)}")


# 检查文件是否已发布的辅助函数
async def check_file_published_status(db, md5_hash: str, platforms: Optional[List[str]] = None) -> bool:
    """检查文件是否在指定平台已发布"""
    try:
        collection = get_video_md5_collection(db)
        record = await collection.find_one({"md5_hash": md5_hash})

        if not record:
            return False

        platform_records = record.get("platform_records", [])
        if not platform_records:
            return False

        # 如果没有指定平台，检查是否在任何平台发布
        if not platforms:
            for platform_record in platform_records:
                if platform_record.get("is_published", False):
                    return True
            return False

        # 检查指定平台
        for platform in platforms:
            for platform_record in platform_records:
                if (platform_record.get("platform") == platform and
                    platform_record.get("is_published", False)):
                    return True

        return False

    except Exception as e:
        logger.error(f"检查文件发布状态失败: {str(e)}")
        return False


@router.post("/archive-published", response_model=ArchivePublishedFilesResponse)
async def archive_published_files(
    request: ArchivePublishedFilesRequest,
    db_request: Request,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    一键归档已发布的文件到子文件夹

    - **folder_path**: 文件夹路径
    - **archive_folder_name**: 归档文件夹名称（默认为"已发布"）
    - **platforms**: 可选，指定要检查的平台列表。如果不提供，则检查所有平台
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    logger.info(f"归档已发布文件: {request.folder_path}, 归档文件夹: {request.archive_folder_name}")

    try:
        db = get_db(db_request)

        # 获取文件夹中的所有文件并检查发布状态
        published_status = {}

        if os.path.exists(request.folder_path) and os.path.isdir(request.folder_path):
            for item in os.listdir(request.folder_path):
                item_path = os.path.join(request.folder_path, item)
                if os.path.isfile(item_path) and item != request.archive_folder_name:
                    # 计算文件MD5
                    md5_hash = calculate_file_md5(item_path)
                    if md5_hash:
                        # 检查文件是否已发布
                        is_published = await check_file_published_status(db, md5_hash, request.platforms)
                        published_status[md5_hash] = is_published
                        logger.debug(f"文件 {item} (MD5: {md5_hash[:8]}...) 发布状态: {is_published}")

        # 调用Core服务进行归档操作
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务进行归档: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务进行归档")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务归档文件，发布状态映射: {len(published_status)} 个文件")

        # 调用Core服务
        try:
            grpc_response = await core_client.archive_published_files(
                folder_path=request.folder_path,
                archive_folder_name=request.archive_folder_name or "已发布",
                platforms=request.platforms,
                published_status=published_status
            )
        except Exception as e:
            logger.error(f"调用Core服务归档文件失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        return ArchivePublishedFilesResponse(
            success=grpc_response["success"],
            archived_count=grpc_response["archived_count"],
            skipped_count=grpc_response["skipped_count"],
            archive_folder=grpc_response["archive_folder"],
            archived_files=grpc_response["archived_files"],
            errors=grpc_response["errors"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"归档已发布文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"归档已发布文件失败: {str(e)}")


# 视频合并任务状态存储
video_merge_tasks = {}

# 三拼视频任务状态存储
triple_video_tasks = {}

class VideoMergeService:
    """视频合并服务类"""

    def __init__(self):
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        self.transition_effects = [
            'fade',
            'wipeleft',
            'wiperight',
            'wipeup',
            'wipedown',
            'slideleft',
            'slideright',
            'slideup',
            'slidedown',
            'circlecrop',
            'rectcrop',
            'distance',
            'fadeblack',
            'fadewhite',
            'radial',
            'smoothleft',
            'smoothright',
            'smoothup',
            'smoothdown'
        ]



    def get_video_duration(self, video_path: str) -> float:
        """获取视频时长（秒）"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', video_path
            ]

            # 设置环境变量以处理编码问题
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                env=env
            )

            if result.returncode != 0:
                logger.warning(f"无法获取视频时长: {video_path}")
                return 0.0

            if not result.stdout or result.stdout.strip() == '':
                logger.warning(f"ffprobe返回空结果: {video_path}")
                return 0.0

            data = json.loads(result.stdout)
            duration = float(data.get('format', {}).get('duration', 0))
            return duration
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败 {video_path}: {str(e)}")
            return 0.0
        except Exception as e:
            logger.error(f"获取视频时长失败 {video_path}: {str(e)}")
            return 0.0

    def scan_video_files(self, folder_path: str) -> List[Dict[str, Any]]:
        """扫描文件夹中的视频文件"""
        video_files = []
        try:
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isfile(item_path):
                    extension = item.split('.')[-1].lower() if '.' in item else ''
                    if extension in self.supported_video_extensions:
                        duration = self.get_video_duration(item_path)
                        if duration > 0:  # 只包含有效的视频文件
                            video_files.append({
                                'name': item,
                                'path': item_path,
                                'duration': duration,
                                'size': os.path.getsize(item_path)
                            })

            # 按时长排序，短视频优先
            video_files.sort(key=lambda x: x['duration'])
            logger.info(f"扫描到 {len(video_files)} 个视频文件")
            return video_files
        except Exception as e:
            logger.error(f"扫描视频文件失败: {str(e)}")
            return []

    def group_videos_by_duration(self, video_files: List[Dict[str, Any]],
                               target_min: int, target_max: int,
                               max_videos_per_group: int) -> List[List[Dict[str, Any]]]:
        """根据目标时长将视频分组"""
        groups = []
        current_group = []
        current_duration = 0

        for video in video_files:
            video_duration = video['duration']

            # 如果当前组为空，直接添加
            if not current_group:
                current_group.append(video)
                current_duration = video_duration
                continue

            # 检查添加当前视频后是否超过目标时长或最大视频数量
            if (current_duration + video_duration <= target_max and
                len(current_group) < max_videos_per_group):
                current_group.append(video)
                current_duration += video_duration
            else:
                # 如果当前组达到最小时长要求，保存当前组
                if current_duration >= target_min:
                    groups.append(current_group)
                else:
                    # 如果当前组时长不够，尝试添加当前视频
                    if current_duration + video_duration <= target_max * 1.2:  # 允许20%的超出
                        current_group.append(video)
                        current_duration += video_duration
                    groups.append(current_group)

                # 开始新组
                current_group = [video]
                current_duration = video_duration

        # 处理最后一组
        if current_group:
            if current_duration >= target_min:
                groups.append(current_group)
            elif groups:
                # 如果最后一组时长不够，合并到前一组
                groups[-1].extend(current_group)
            else:
                # 如果只有一组且时长不够，仍然保留
                groups.append(current_group)

        logger.info(f"视频分组完成，共 {len(groups)} 组")
        return groups

    async def merge_video_group(self, video_group: List[Dict[str, Any]],
                              output_path: str, group_index: int,
                              enable_transitions: bool, output_quality: str) -> bool:
        """合并一组视频"""
        try:
            logger.info(f"开始合并第 {group_index + 1} 组视频，包含 {len(video_group)} 个文件")

            # 创建临时文件列表
            temp_dir = os.path.join(os.path.dirname(output_path), 'temp_merge')
            os.makedirs(temp_dir, exist_ok=True)

            # 如果只有一个视频，直接复制
            if len(video_group) == 1:
                import shutil
                shutil.copy2(video_group[0]['path'], output_path)
                logger.info(f"单个视频直接复制: {output_path}")
                return True

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y']  # -y 覆盖输出文件

            # 添加输入文件
            for video in video_group:
                cmd.extend(['-i', video['path']])

            # 构建复杂滤镜
            filter_complex = []

            if enable_transitions and len(video_group) > 1:
                # 使用转场特效
                for i in range(len(video_group)):
                    # 缩放和格式化每个输入
                    filter_complex.append(f"[{i}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1,fps=30[v{i}]")
                    filter_complex.append(f"[{i}:a]aresample=44100,aformat=sample_fmts=fltp:channel_layouts=stereo[a{i}]")

                # 添加转场效果
                current_video = "v0"
                current_audio = "a0"

                for i in range(1, len(video_group)):
                    transition_effect = random.choice(self.transition_effects)
                    transition_duration = 1.0  # 1秒转场

                    # 视频转场
                    filter_complex.append(
                        f"[{current_video}][v{i}]xfade=transition={transition_effect}:duration={transition_duration}:offset=0[v{i}_merged]"
                    )

                    # 音频交叉淡入淡出
                    filter_complex.append(
                        f"[{current_audio}][a{i}]acrossfade=d={transition_duration}[a{i}_merged]"
                    )

                    current_video = f"v{i}_merged"
                    current_audio = f"a{i}_merged"

                # 最终输出
                filter_complex.append(f"[{current_video}][{current_audio}]")

            else:
                # 简单拼接，不使用转场
                video_inputs = []
                audio_inputs = []

                for i in range(len(video_group)):
                    filter_complex.append(f"[{i}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1,fps=30[v{i}]")
                    filter_complex.append(f"[{i}:a]aresample=44100,aformat=sample_fmts=fltp:channel_layouts=stereo[a{i}]")
                    video_inputs.append(f"v{i}")
                    audio_inputs.append(f"a{i}")

                # 拼接视频和音频
                filter_complex.append(f"{''.join([f'[{v}]' for v in video_inputs])}concat=n={len(video_inputs)}:v=1:a=0[outv]")
                filter_complex.append(f"{''.join([f'[{a}]' for a in audio_inputs])}concat=n={len(audio_inputs)}:v=0:a=1[outa]")

            # 添加滤镜复杂参数
            if filter_complex:
                cmd.extend(['-filter_complex', ';'.join(filter_complex)])
                if enable_transitions and len(video_group) > 1:
                    # 转场模式：直接映射最终输出
                    pass  # 最终输出已在filter_complex中定义
                else:
                    # 拼接模式：映射拼接后的输出
                    cmd.extend(['-map', '[outv]', '-map', '[outa]'])

            # 设置输出质量
            if output_quality == "high":
                cmd.extend(['-c:v', 'libx264', '-preset', 'medium', '-crf', '18'])
            elif output_quality == "medium":
                cmd.extend(['-c:v', 'libx264', '-preset', 'fast', '-crf', '23'])
            else:  # low
                cmd.extend(['-c:v', 'libx264', '-preset', 'fast', '-crf', '28'])

            cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
            cmd.append(output_path)

            logger.info(f"执行ffmpeg命令: {' '.join(cmd[:10])}...")  # 只记录前10个参数

            # 执行ffmpeg命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"视频合并成功: {output_path}")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"视频合并失败: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"合并视频组异常: {str(e)}", exc_info=True)
            return False

# 创建视频合并服务实例
video_merge_service = VideoMergeService()

async def process_triple_video_task(task_id: str, request: TripleVideoMergeRequest, core_service_id: Optional[str] = None):
    """后台处理三拼视频任务"""
    try:
        # 更新任务状态
        triple_video_tasks[task_id] = TripleVideoMergeProgress(
            task_id=task_id,
            status="processing",
            progress=0,
            current_step="准备创建三拼视频",
            total_videos=0,
            processed_videos=0,
            output_files=[]
        )

        logger.info(f"开始处理三拼视频任务: {task_id}")

        # 1. 创建输出文件夹
        output_folder = os.path.join(request.folder_path, 'triple_output')
        os.makedirs(output_folder, exist_ok=True)

        # 2. 生成输出文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_filename = f"三拼视频_{timestamp}.mp4"
        output_path = os.path.join(output_folder, output_filename)

        triple_video_tasks[task_id].progress = 20
        triple_video_tasks[task_id].current_step = "正在通过Core服务创建三拼视频..."

        # 3. 通过gRPC调用Core服务创建三拼视频
        from app.core.grpc_client import get_core_client_by_service_id

        try:
            # 根据core_service_id选择Core服务
            if core_service_id:
                core_client = get_core_client_by_service_id(core_service_id)
                logger.info(f"三拼视频任务使用指定的Core服务: {core_service_id}")
            else:
                from app.core.grpc_client import get_core_client
                core_client = get_core_client()
                logger.info("三拼视频任务使用默认Core服务")

            if not core_client:
                raise Exception("Core服务不可用")

            # 调用Core服务的三拼视频功能
            result = await core_client.create_triple_video(
                folder_path=request.folder_path,
                output_path=output_path,
                video_duration_per_segment=request.video_duration_per_segment,
                transition_duration=request.transition_duration,
                output_quality=request.output_quality
            )
        except Exception as e:
            triple_video_tasks[task_id].status = "failed"
            triple_video_tasks[task_id].error_message = f"连接Core服务失败: {str(e)}"
            logger.error(f"连接Core服务失败: {str(e)}")
            return

        if result.get("success"):
            triple_video_tasks[task_id].progress = 90
            triple_video_tasks[task_id].current_step = "三拼视频创建完成"
            triple_video_tasks[task_id].output_files.append(output_filename)
            triple_video_tasks[task_id].processed_videos = result.get("processed_videos", 0)
            triple_video_tasks[task_id].total_videos = result.get("processed_videos", 0)

            # 4. 生成预览（如果启用）
            if request.enable_preview:
                triple_video_tasks[task_id].current_step = "正在生成预览..."
                preview_filename = f"预览_{output_filename}"
                preview_path = os.path.join(output_folder, preview_filename)

                try:
                    # 通过Core服务创建预览
                    preview_result = await core_client.create_triple_video(
                        folder_path=request.folder_path,
                        output_path=preview_path,
                        video_duration_per_segment=min(5, request.video_duration_per_segment),  # 预览用较短时长
                        transition_duration=request.transition_duration,
                        output_quality="low"  # 预览用低质量
                    )

                    if preview_result.get("success"):
                        triple_video_tasks[task_id].preview_file = preview_filename
                except Exception as e:
                    logger.warning(f"生成预览失败: {str(e)}")
                    # 预览失败不影响主任务

            triple_video_tasks[task_id].progress = 100
            triple_video_tasks[task_id].status = "completed"
            triple_video_tasks[task_id].current_step = "任务完成"
            logger.info(f"三拼视频任务完成: {output_filename}")

        else:
            triple_video_tasks[task_id].status = "failed"
            triple_video_tasks[task_id].error_message = result.get("error", "Core服务创建三拼视频失败")
            logger.error(f"三拼视频任务失败: {task_id}, 错误: {result.get('error')}")

    except Exception as e:
        logger.error(f"处理三拼视频任务异常: {str(e)}", exc_info=True)
        triple_video_tasks[task_id].status = "failed"
        triple_video_tasks[task_id].error_message = f"任务处理异常: {str(e)}"



async def process_video_merge_task(task_id: str, request: VideoMergeRequest, core_service_id: Optional[str] = None):
    """后台处理视频合并任务"""
    try:
        # 更新任务状态
        video_merge_tasks[task_id] = VideoMergeProgress(
            task_id=task_id,
            status="processing",
            progress=0,
            current_step="扫描视频文件",
            total_videos=0,
            processed_videos=0,
            output_files=[]
        )

        logger.info(f"开始处理视频合并任务: {task_id}")
        if core_service_id:
            logger.info(f"使用指定的Core服务: {core_service_id}")
        else:
            logger.info("使用默认Core服务")

        # 1. 扫描视频文件
        video_files = video_merge_service.scan_video_files(request.folder_path)
        if not video_files:
            video_merge_tasks[task_id].status = "failed"
            video_merge_tasks[task_id].error_message = "未找到有效的视频文件"
            return

        video_merge_tasks[task_id].total_videos = len(video_files)
        video_merge_tasks[task_id].progress = 10
        video_merge_tasks[task_id].current_step = f"找到 {len(video_files)} 个视频文件，正在分组"

        # 2. 视频分组
        video_groups = video_merge_service.group_videos_by_duration(
            video_files,
            request.target_duration_min,
            request.target_duration_max,
            request.max_videos_per_merge
        )

        if not video_groups:
            video_merge_tasks[task_id].status = "failed"
            video_merge_tasks[task_id].error_message = "无法创建有效的视频分组"
            return

        video_merge_tasks[task_id].progress = 20
        video_merge_tasks[task_id].current_step = f"创建了 {len(video_groups)} 个视频组，开始合并"

        # 3. 创建输出目录
        output_folder = os.path.join(request.folder_path, 'output')
        os.makedirs(output_folder, exist_ok=True)

        # 4. 通过Core服务执行视频合并
        video_merge_tasks[task_id].progress = 50
        video_merge_tasks[task_id].current_step = "正在通过Core服务合并视频..."

        # 根据core_service_id选择Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"视频合并任务使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("视频合并任务使用默认Core服务")

        if not core_client:
            raise Exception("Core服务不可用")

        try:
            # 调用Core服务的视频合并功能
            result = await core_client.merge_videos(
                folder_path=request.folder_path,
                target_duration_min=request.target_duration_min,
                target_duration_max=request.target_duration_max,
                enable_transitions=request.enable_transitions,
                output_quality=request.output_quality,
                max_videos_per_merge=request.max_videos_per_merge
            )

            if result.get("success", False):
                # 更新任务状态
                video_merge_tasks[task_id].progress = 100
                video_merge_tasks[task_id].processed_videos = result.get("processed_videos", 0)
                video_merge_tasks[task_id].output_files.extend(result.get("output_files", []))
                video_merge_tasks[task_id].status = "completed"
                successful_merges = result.get("successful_merges", 0)
                video_merge_tasks[task_id].current_step = f"合并完成！成功创建 {successful_merges} 个视频文件"
                logger.info(f"视频合并任务完成: {task_id}, 成功合并 {successful_merges} 组")
            else:
                error_msg = result.get("error", "Core服务合并失败")
                video_merge_tasks[task_id].status = "failed"
                video_merge_tasks[task_id].error_message = error_msg
                logger.error(f"视频合并任务失败: {task_id}, 错误: {error_msg}")

        except Exception as e:
            video_merge_tasks[task_id].status = "failed"
            video_merge_tasks[task_id].error_message = f"连接Core服务失败: {str(e)}"
            logger.error(f"连接Core服务失败: {str(e)}")

    except Exception as e:
        logger.error(f"视频合并任务异常: {task_id}, {str(e)}", exc_info=True)
        video_merge_tasks[task_id].status = "failed"
        video_merge_tasks[task_id].error_message = f"任务执行异常: {str(e)}"

# API端点：创建视频合并任务
@router.post("/merge-videos", response_model=VideoMergeResponse)
async def create_video_merge_task(
    request: VideoMergeRequest,
    background_tasks: BackgroundTasks,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    创建批量视频合并任务

    - **folder_path**: 视频文件夹路径
    - **target_duration_min**: 目标时长最小值（秒）
    - **target_duration_max**: 目标时长最大值（秒）
    - **enable_transitions**: 是否启用转场特效
    - **output_quality**: 输出质量（high/medium/low）
    - **max_videos_per_merge**: 每个合并视频最多包含的原视频数量
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    logger.info(f"创建视频合并任务: {request.folder_path}")

    # 验证路径
    if not os.path.exists(request.folder_path):
        raise HTTPException(status_code=404, detail="文件夹不存在")

    if not os.path.isdir(request.folder_path):
        raise HTTPException(status_code=400, detail="指定路径不是文件夹")

    # 验证参数
    if request.target_duration_min >= request.target_duration_max:
        raise HTTPException(status_code=400, detail="最小时长必须小于最大时长")

    if request.target_duration_min < 10:
        raise HTTPException(status_code=400, detail="最小时长不能少于10秒")

    if request.max_videos_per_merge < 1:
        raise HTTPException(status_code=400, detail="每组最大视频数量必须大于0")

    # 检查ffmpeg是否可用
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True, timeout=5)
    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
        raise HTTPException(status_code=500, detail="ffmpeg不可用，无法进行视频合并")

    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 创建输出文件夹
    output_folder = os.path.join(request.folder_path, 'output')

    # 初始化任务状态
    video_merge_tasks[task_id] = VideoMergeProgress(
        task_id=task_id,
        status="pending",
        progress=0,
        current_step="任务已创建，等待处理",
        total_videos=0,
        processed_videos=0,
        output_files=[]
    )

    # 添加后台任务
    background_tasks.add_task(process_video_merge_task, task_id, request, core_service_id)

    # 估算完成时间（基于文件夹大小）
    try:
        folder_size = sum(os.path.getsize(os.path.join(request.folder_path, f))
                         for f in os.listdir(request.folder_path)
                         if os.path.isfile(os.path.join(request.folder_path, f)))
        # 估算：每GB大约需要60秒处理时间
        estimated_time = max(60, int(folder_size / (1024 * 1024 * 1024) * 60))
    except:
        estimated_time = 300  # 默认5分钟

    return VideoMergeResponse(
        success=True,
        task_id=task_id,
        message="视频合并任务已创建，正在后台处理",
        output_folder=output_folder,
        estimated_time=estimated_time
    )

# API端点：获取视频合并任务进度
@router.get("/merge-videos/{task_id}/progress", response_model=VideoMergeProgress)
async def get_video_merge_progress(task_id: str):
    """
    获取视频合并任务进度

    - **task_id**: 任务ID
    """
    if task_id not in video_merge_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return video_merge_tasks[task_id]

# API端点：取消视频合并任务
@router.delete("/merge-videos/{task_id}")
async def cancel_video_merge_task(task_id: str):
    """
    取消视频合并任务

    - **task_id**: 任务ID
    """
    if task_id not in video_merge_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = video_merge_tasks[task_id]
    if task.status in ["completed", "failed"]:
        raise HTTPException(status_code=400, detail="任务已完成，无法取消")

    # 标记任务为已取消
    task.status = "cancelled"
    task.current_step = "任务已取消"

    logger.info(f"视频合并任务已取消: {task_id}")

    return {"success": True, "message": "任务已取消"}

# API端点：获取所有视频合并任务
@router.get("/merge-videos", response_model=List[VideoMergeProgress])
async def list_video_merge_tasks():
    """获取所有视频合并任务列表"""
    return list(video_merge_tasks.values())

# ==================== MD5记录管理API端点 ====================

# API端点：保存视频MD5记录
@router.post("/md5-records", response_model=Dict[str, Any])
async def create_video_md5_record(
    record: VideoMD5RecordCreate,
    db_request: Request
):
    """
    保存视频文件的MD5记录到数据库

    - **file_path**: 文件路径
    - **file_name**: 文件名
    - **md5_hash**: MD5哈希值
    - **file_size**: 文件大小
    - **duration**: 视频时长（可选）
    - **resolution**: 分辨率（可选）
    - **notes**: 备注（可选）
    """
    try:
        db = get_db(db_request)
        record_id = save_video_md5_record(db, record)
        return {
            "success": True,
            "record_id": record_id,
            "message": "MD5记录保存成功"
        }
    except Exception as e:
        logger.error(f"保存MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存MD5记录失败: {str(e)}")

# API端点：更新视频MD5记录
@router.put("/md5-records/{md5_hash}", response_model=Dict[str, Any])
async def update_video_md5_record_api(
    md5_hash: str,
    update_data: VideoMD5RecordUpdate,
    db_request: Request
):
    """
    更新视频MD5记录的发布状态等信息

    - **md5_hash**: MD5哈希值
    - **is_published**: 是否已发布（可选）
    - **publish_date**: 发布日期（可选）
    - **publish_platform**: 发布平台（可选）
    - **publish_account**: 发布账号（可选）
    - **notes**: 备注（可选）
    """
    try:
        db = get_db(db_request)
        success = await update_video_md5_record(db, md5_hash, update_data)

        if success:
            return {
                "success": True,
                "message": "MD5记录更新成功"
            }
        else:
            raise HTTPException(status_code=404, detail="未找到指定的MD5记录")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新MD5记录失败: {str(e)}")

# API端点：获取视频MD5记录列表
@router.get("/md5-records", response_model=VideoMD5RecordListResponse)
async def get_video_md5_records_api(
    db_request: Request,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=2000, description="每页数量"),
    folder_path: str = Query(None, description="文件夹路径过滤")
):
    """
    获取视频MD5记录列表

    - **page**: 页码
    - **limit**: 每页数量
    - **folder_path**: 文件夹路径过滤（可选）
    """
    try:
        db = get_db(db_request)
        result = await get_video_md5_records(db, page, limit, folder_path)
        return VideoMD5RecordListResponse(**result)

    except Exception as e:
        logger.error(f"获取MD5记录列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MD5记录列表失败: {str(e)}")

# API端点：批量保存文件夹中视频的MD5记录
@router.post("/md5-records/batch-save", response_model=Dict[str, Any])
async def batch_save_folder_md5_records(
    request: FolderPathRequest,
    db_request: Request
):
    """
    批量保存文件夹中所有视频文件的MD5记录

    - **path**: 文件夹路径
    """
    try:
        db = get_db(db_request)

        # 通过Core服务获取文件夹中的视频文件
        from app.core.grpc_client import get_core_client
        core_client = get_core_client()

        video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']

        if core_client is None:
            logger.warning("Core服务不可用，使用本地文件系统处理")
            # 降级到本地处理
            files = get_folder_contents(request.path, video_extensions, include_md5=True, include_media_info=True)
        else:
            logger.info(f"通过Core服务获取文件夹内容: {request.path}")
            try:
                # 调用Core服务的ListDirectory接口
                result = await core_client.list_directory(
                    path=request.path,
                    filter_extensions=video_extensions,
                    include_md5=True,
                    include_media_info=True
                )

                if not result.get("success", False):
                    error_msg = result.get("error", "未知错误")
                    logger.error(f"Core服务调用失败: {error_msg}")
                    raise Exception(f"Core服务调用失败: {error_msg}")

                # 转换Core服务返回的数据格式为Backend期望的格式
                files = []
                for file_data in result.get("files", []):
                    file_item = FileInfo(
                        name=file_data["name"],
                        path=file_data["path"],
                        is_directory=file_data["is_directory"],
                        size=file_data["size"],
                        last_modified=str(file_data["modified_time"]) if file_data["modified_time"] else None,
                        md5_hash=file_data.get("md5_hash"),
                        media_info=file_data.get("media_info")
                    )
                    files.append(file_item)

                logger.info(f"Core服务返回 {len(files)} 个视频文件")

                # 调试：检查前几个文件的MD5情况
                for i, file_info in enumerate(files[:3]):
                    logger.info(f"文件 {i+1}: {file_info.name}")
                    logger.info(f"  路径: {file_info.path}")
                    logger.info(f"  MD5: {file_info.md5_hash}")
                    logger.info(f"  大小: {file_info.size}")

            except Exception as e:
                logger.error(f"Core服务调用失败，降级到本地处理: {str(e)}")
                files = get_folder_contents(request.path, video_extensions, include_md5=True, include_media_info=True)

        saved_count = 0
        skipped_count = 0
        errors = []

        for file in files:
            if file.is_directory:
                logger.debug(f"跳过目录: {file.name}")
                continue

            if not file.md5_hash:
                logger.warning(f"文件MD5为空，跳过: {file.name}")
                continue

            try:
                # 检查是否已存在
                existing = await get_video_md5_record_by_hash(db, file.md5_hash)
                if existing:
                    skipped_count += 1
                    logger.info(f"MD5已存在，跳过: {file.name} -> {file.md5_hash}")
                    continue

                # 创建记录
                duration = None
                resolution = None
                if file.media_info:
                    duration = int(file.media_info.get('duration', 0))
                    if file.media_info.get('video'):
                        width = file.media_info['video'].get('width', 0)
                        height = file.media_info['video'].get('height', 0)
                        if width and height:
                            resolution = f"{width}x{height}"

                record = VideoMD5RecordCreate(
                    file_path=file.path,
                    file_name=file.name,
                    md5_hash=file.md5_hash,
                    file_size=file.size,
                    duration=duration,
                    resolution=resolution
                )

                # 直接插入新记录，不再检查重复（因为上面已经检查过了）
                collection = get_video_md5_collection(db)
                record_dict = record.dict()
                record_dict["created_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
                record_dict["updated_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
                result = await collection.insert_one(record_dict)

                saved_count += 1
                logger.info(f"保存MD5记录成功: {file.name} -> {file.md5_hash}")

            except Exception as e:
                error_msg = f"保存文件 {file.name} 的MD5记录失败: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)

        # 统计信息
        total_files = len(files)
        video_files = len([f for f in files if not f.is_directory])
        files_with_md5 = len([f for f in files if not f.is_directory and f.md5_hash])
        files_without_md5 = video_files - files_with_md5

        logger.info(f"批量保存统计:")
        logger.info(f"  总文件数: {total_files}")
        logger.info(f"  视频文件数: {video_files}")
        logger.info(f"  有MD5的文件: {files_with_md5}")
        logger.info(f"  无MD5的文件: {files_without_md5}")
        logger.info(f"  新增记录: {saved_count}")
        logger.info(f"  跳过记录: {skipped_count}")

        return {
            "success": saved_count > 0,
            "saved_count": saved_count,
            "skipped_count": skipped_count,
            "total_files": files_with_md5,
            "files_without_md5": files_without_md5,
            "errors": errors,
            "message": f"批量保存完成：新增 {saved_count} 条记录，跳过 {skipped_count} 条已存在记录"
        }

    except Exception as e:
        logger.error(f"批量保存MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量保存MD5记录失败: {str(e)}")

# API端点：MD5比对
@router.post("/md5-records/compare", response_model=MD5CompareResponse)
async def compare_folder_md5_records(
    request: MD5CompareRequest,
    db_request: Request
):
    """
    比对文件夹中的视频文件MD5与数据库记录，找出重复文件

    - **folder_path**: 文件夹路径
    - **platform**: 可选，指定平台名称。如果提供，则只与该平台已发布的视频进行比较；如果不提供，则与所有已发布的视频进行比较
    """
    try:
        db = get_db(db_request)
        result = await compare_folder_md5_with_records(db, request.folder_path, request.platform)
        return MD5CompareResponse(**result)

    except Exception as e:
        logger.error(f"MD5比对失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"MD5比对失败: {str(e)}")

# API端点：批量删除重复文件
@router.post("/md5-records/batch-delete", response_model=Dict[str, Any])
async def batch_delete_duplicate_files(
    request: BatchDeleteRequest
):
    """
    批量删除指定的文件或文件夹

    - **file_paths**: 要删除的文件或文件夹路径列表
    """
    try:
        result = batch_delete_files(request.file_paths)
        return result

    except Exception as e:
        logger.error(f"批量删除文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除文件失败: {str(e)}")

# API端点：删除文件（不删除MD5记录）
@router.post("/delete-files", response_model=Dict[str, Any])
async def delete_files_only(
    request: BatchDeleteRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    删除指定的文件或文件夹（物理删除，不删除MD5记录）

    - **file_paths**: 要删除的文件或文件夹路径列表
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务删除
    """
    try:
        logger.info(f"删除文件请求，文件数量: {len(request.file_paths)}")
        logger.info(f"使用Core服务ID: {core_service_id}")
        for file_path in request.file_paths:
            logger.info(f"准备删除文件: {file_path}")

        # 尝试通过Core服务删除
        if core_service_id:
            logger.info(f"通过Core服务删除文件: {core_service_id}")
            from app.core.grpc_client import get_core_client_by_service_id

            core_client = get_core_client_by_service_id(core_service_id)
            if core_client:
                try:
                    # 调用Core服务的删除接口
                    result = await core_client.delete_files(request.file_paths)
                    logger.info(f"Core服务删除完成: {result}")
                    return result
                except Exception as e:
                    logger.error(f"Core服务删除失败: {str(e)}")
                    # 如果Core服务删除失败，降级到本地删除
                    logger.warning("Core服务删除失败，降级到本地删除")
            else:
                logger.warning(f"无法连接到Core服务 {core_service_id}，降级到本地删除")

        # 本地删除（降级方案）
        logger.info("使用本地删除方式")
        import os
        deleted_count = 0
        errors = []

        for file_path in request.file_paths:
            try:
                if os.path.exists(file_path):
                    if os.path.isfile(file_path):
                        # 删除文件
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"文件删除成功: {file_path}")
                    elif os.path.isdir(file_path):
                        # 删除文件夹（包括所有内容）
                        import shutil
                        shutil.rmtree(file_path)
                        deleted_count += 1
                        logger.info(f"文件夹删除成功: {file_path}")
                    else:
                        errors.append(f"路径既不是文件也不是文件夹: {file_path}")
                else:
                    errors.append(f"路径不存在: {file_path}")
            except Exception as e:
                errors.append(f"删除失败 {file_path}: {str(e)}")
                logger.error(f"删除失败 {file_path}: {str(e)}")

        result = {
            "success": deleted_count > 0,
            "deleted_count": deleted_count,
            "total_count": len(request.file_paths),
            "errors": errors
        }

        logger.info(f"删除结果: 成功删除 {result['deleted_count']} 个项目（文件/文件夹）")
        return result

    except Exception as e:
        logger.error(f"删除文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

# API端点：根据MD5哈希值获取记录
@router.get("/md5-records/{md5_hash}", response_model=Dict[str, Any])
async def get_video_md5_record_by_hash_api(
    md5_hash: str,
    db_request: Request
):
    """
    根据MD5哈希值获取视频记录

    - **md5_hash**: MD5哈希值
    """
    try:
        db = get_db(db_request)
        record = await get_video_md5_record_by_hash(db, md5_hash)

        if record:
            record["_id"] = str(record["_id"])
            return {
                "success": True,
                "record": record
            }
        else:
            return {
                "success": False,
                "record": None,
                "message": "未找到指定的MD5记录"
            }

    except Exception as e:
        logger.error(f"查询MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询MD5记录失败: {str(e)}")

# API端点：删除MD5记录
@router.delete("/md5-records/{md5_hash}", response_model=Dict[str, Any])
async def delete_video_md5_record(
    md5_hash: str,
    db_request: Request
):
    """
    删除指定的MD5记录

    - **md5_hash**: MD5哈希值
    """
    try:
        db = get_db(db_request)
        collection = get_video_md5_collection(db)

        result = collection.delete_one({"md5_hash": md5_hash})

        if result.deleted_count > 0:
            return {
                "success": True,
                "message": "MD5记录删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="未找到指定的MD5记录")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除MD5记录失败: {str(e)}")

# ==================== 调试API端点 ====================

# API端点：调试MD5记录查询
@router.get("/debug/md5-records", response_model=Dict[str, Any])
async def debug_md5_records(
    db_request: Request,
    folder_path: str = Query(None, description="文件夹路径"),
    md5_hash: str = Query(None, description="特定MD5哈希值")
):
    """
    调试MD5记录查询，显示详细的查询过程和结果
    """
    try:
        db = get_db(db_request)
        collection = get_video_md5_collection(db)

        debug_info = {
            "input_folder_path": folder_path,
            "input_md5_hash": md5_hash,
            "total_records_in_db": collection.count_documents({}),
            "query_results": [],
            "all_file_paths": [],
            "query_details": {}
        }

        # 获取数据库中所有记录的文件路径
        all_records = collection.find({}, {"file_path": 1, "file_name": 1, "md5_hash": 1})
        for record in all_records:
            debug_info["all_file_paths"].append({
                "file_name": record.get("file_name"),
                "file_path": record.get("file_path"),
                "md5_hash": record.get("md5_hash")
            })

        # 如果指定了MD5哈希值，查询特定记录
        if md5_hash:
            specific_record = collection.find_one({"md5_hash": md5_hash})
            debug_info["specific_md5_record"] = specific_record

        # 如果指定了文件夹路径，测试不同的查询方式
        if folder_path:
            import re

            # 方式1：安全的正则表达式查询
            normalized_path = folder_path.replace('/', '\\')
            if not normalized_path.endswith('\\'):
                normalized_path += '\\'
            escaped_path = re.escape(normalized_path)
            query1 = {"file_path": {"$regex": f"^{escaped_path}"}}

            # 方式2：简单前缀匹配（也需要转义）
            escaped_folder_path = re.escape(folder_path)
            query2 = {"file_path": {"$regex": f"^{escaped_folder_path}"}}

            # 方式3：包含匹配（也需要转义）
            query3 = {"file_path": {"$regex": escaped_folder_path}}

            debug_info["query_details"] = {
                "original_path": folder_path,
                "normalized_path": normalized_path,
                "escaped_path": escaped_path,
                "query1": query1,
                "query2": query2,
                "query3": query3
            }

            # 执行不同的查询
            for i, query in enumerate([query1, query2, query3], 1):
                results = list(collection.find(query))
                debug_info["query_results"].append({
                    f"query_{i}": query,
                    f"results_count": len(results),
                    f"results": [
                        {
                            "file_name": r.get("file_name"),
                            "file_path": r.get("file_path"),
                            "md5_hash": r.get("md5_hash")
                        } for r in results
                    ]
                })

        return debug_info

    except Exception as e:
        logger.error(f"调试MD5记录查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"调试查询失败: {str(e)}")

# ==================== 平台发布状态管理API端点 ====================

# API端点：更新平台发布状态
@router.put("/md5-records/{md5_hash}/platform/{platform}", response_model=Dict[str, Any])
async def update_platform_publish_status_api(
    md5_hash: str,
    platform: str,
    platform_data: PlatformPublishUpdate,
    db_request: Request
):
    """
    更新视频在特定平台的发布状态

    - **md5_hash**: MD5哈希值
    - **platform**: 平台名称
    - **platform_data**: 平台发布信息
    """
    try:
        db = get_db(db_request)

        # 确保平台参数一致
        platform_data.platform = platform

        success = await update_platform_publish_status(db, md5_hash, platform_data)

        if success:
            return {
                "success": True,
                "message": f"平台 {platform} 的发布状态更新成功"
            }
        else:
            raise HTTPException(status_code=404, detail="未找到指定的MD5记录")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新平台发布状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新平台发布状态失败: {str(e)}")

# API端点：获取平台发布状态
@router.get("/md5-records/{md5_hash}/platform/{platform}", response_model=Dict[str, Any])
async def get_platform_publish_status_api(
    md5_hash: str,
    platform: str,
    db_request: Request
):
    """
    获取视频在特定平台的发布状态

    - **md5_hash**: MD5哈希值
    - **platform**: 平台名称
    """
    try:
        db = get_db(db_request)
        platform_status = get_platform_publish_status(db, md5_hash, platform)

        if platform_status:
            return {
                "success": True,
                "platform_status": platform_status
            }
        else:
            return {
                "success": False,
                "platform_status": None,
                "message": f"未找到平台 {platform} 的发布记录"
            }

    except Exception as e:
        logger.error(f"获取平台发布状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取平台发布状态失败: {str(e)}")

# API端点：检查平台是否已发布
@router.get("/md5-records/{md5_hash}/platform/{platform}/published", response_model=Dict[str, Any])
async def check_platform_published_api(
    md5_hash: str,
    platform: str,
    db_request: Request
):
    """
    检查视频是否在特定平台已发布

    - **md5_hash**: MD5哈希值
    - **platform**: 平台名称
    """
    try:
        db = get_db(db_request)
        is_published = check_platform_published(db, md5_hash, platform)

        return {
            "success": True,
            "is_published": is_published,
            "platform": platform,
            "md5_hash": md5_hash
        }

    except Exception as e:
        logger.error(f"检查平台发布状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检查平台发布状态失败: {str(e)}")

# API端点：获取所有平台的发布状态
@router.get("/md5-records/{md5_hash}/platforms", response_model=Dict[str, Any])
async def get_all_platform_publish_status_api(
    md5_hash: str,
    db_request: Request
):
    """
    获取视频在所有平台的发布状态

    - **md5_hash**: MD5哈希值
    """
    try:
        db = get_db(db_request)
        record = await get_video_md5_record_by_hash(db, md5_hash)

        if not record:
            raise HTTPException(status_code=404, detail="未找到指定的MD5记录")

        platform_records = record.get("platform_records", [])

        return {
            "success": True,
            "md5_hash": md5_hash,
            "platform_records": platform_records,
            "total_platforms": len(platform_records),
            "published_platforms": len([p for p in platform_records if p.get("is_published", False)])
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取所有平台发布状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取所有平台发布状态失败: {str(e)}")

# API端点：获取特定平台已发布的视频列表
@router.get("/md5-records/platform/{platform}/published", response_model=Dict[str, Any])
async def get_published_videos_by_platform_api(
    platform: str,
    db_request: Request,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=2000, description="每页数量")
):
    """
    获取特定平台已发布的视频MD5记录列表

    - **platform**: 平台名称
    - **page**: 页码（默认1）
    - **limit**: 每页数量（默认20，最大100）
    """
    try:
        db = get_db(db_request)
        result = await get_published_videos_by_platform(db, platform, page, limit)
        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        logger.error(f"获取平台已发布视频失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取平台已发布视频失败: {str(e)}")

# API端点：获取特定平台已发布视频的MD5列表
@router.get("/md5-records/platform/{platform}/published-md5-list", response_model=Dict[str, Any])
async def get_platform_published_md5_list_api(
    platform: str,
    db_request: Request
):
    """
    获取特定平台已发布视频的MD5哈希值列表

    - **platform**: 平台名称
    """
    try:
        db = get_db(db_request)
        md5_list = await get_platform_published_md5_list(db, platform)
        return {
            "success": True,
            "platform": platform,
            "md5_list": md5_list,
            "count": len(md5_list)
        }

    except Exception as e:
        logger.error(f"获取平台已发布MD5列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取平台已发布MD5列表失败: {str(e)}")

# API端点：批量检查多个平台的发布状态
@router.post("/md5-records/batch-check-platforms", response_model=Dict[str, Any])
async def batch_check_platform_published_api(
    request: Dict[str, Any],
    db_request: Request
):
    """
    批量检查多个视频在指定平台的发布状态

    - **md5_hashes**: MD5哈希值列表
    - **platforms**: 平台名称列表
    """
    try:
        db = get_db(db_request)
        md5_hashes = request.get("md5_hashes", [])
        platforms = request.get("platforms", [])

        results = []

        for md5_hash in md5_hashes:
            record_result = {
                "md5_hash": md5_hash,
                "platforms": {}
            }

            for platform in platforms:
                is_published = check_platform_published(db, md5_hash, platform)
                platform_status = get_platform_publish_status(db, md5_hash, platform)

                record_result["platforms"][platform] = {
                    "is_published": is_published,
                    "status": platform_status
                }

            results.append(record_result)

        return {
            "success": True,
            "results": results,
            "total_records": len(results),
            "checked_platforms": platforms
        }

    except Exception as e:
        logger.error(f"批量检查平台发布状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量检查平台发布状态失败: {str(e)}")

# API端点：批量获取MD5记录
@router.post("/md5-records/batch-get", response_model=Dict[str, Any])
async def batch_get_md5_records(
    request: Dict[str, Any],
    db_request: Request
):
    """
    批量获取MD5记录（通过MD5哈希值列表）

    - **md5_hashes**: MD5哈希值列表
    """
    try:
        db = get_db(db_request)
        md5_hashes = request.get("md5_hashes", [])

        if not md5_hashes:
            return {
                "success": True,
                "records": [],
                "found_count": 0,
                "total_requested": 0
            }

        logger.info(f"批量查询MD5记录，数量: {len(md5_hashes)}")

        collection = get_video_md5_collection(db)

        # 使用 $in 操作符批量查询
        query = {"md5_hash": {"$in": md5_hashes}}
        cursor = collection.find(query)

        records = []
        found_md5_hashes = set()

        # 异步处理游标
        async for record in cursor:
            record["_id"] = str(record["_id"])
            records.append(record)
            found_md5_hashes.add(record["md5_hash"])
            logger.info(f"找到MD5记录: {record['md5_hash']} -> {record['file_name']}")

        # 记录未找到的MD5哈希值
        missing_md5_hashes = set(md5_hashes) - found_md5_hashes
        if missing_md5_hashes:
            logger.info(f"未找到的MD5记录: {list(missing_md5_hashes)}")

        return {
            "success": True,
            "records": records,
            "found_count": len(records),
            "total_requested": len(md5_hashes),
            "missing_md5_hashes": list(missing_md5_hashes)
        }

    except Exception as e:
        logger.error(f"批量获取MD5记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量获取MD5记录失败: {str(e)}")

# ==================== 三拼视频API端点 ====================

# API端点：创建三拼视频任务
@router.post("/create-triple-video", response_model=TripleVideoMergeResponse)
async def create_triple_video_task(
    request: TripleVideoMergeRequest,
    background_tasks: BackgroundTasks,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    创建三拼视频任务

    - **folder_path**: 竖版视频文件夹路径
    - **output_quality**: 输出质量（high/medium/low）
    - **video_duration_per_segment**: 每个视频片段播放时长（秒）
    - **transition_duration**: 转场时长（秒）
    - **enable_preview**: 是否生成预览
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    logger.info(f"创建三拼视频任务: {request.folder_path}")

    # 验证路径
    if not os.path.exists(request.folder_path):
        raise HTTPException(status_code=404, detail="文件夹不存在")

    if not os.path.isdir(request.folder_path):
        raise HTTPException(status_code=400, detail="指定路径不是文件夹")

    # 注意：现在使用视频实际时长，video_duration_per_segment参数将被忽略
    logger.info(f"三拼视频请求: 使用视频实际时长，忽略video_duration_per_segment参数")

    # 检查ffmpeg是否可用
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True, timeout=5)
    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
        raise HTTPException(status_code=500, detail="ffmpeg不可用，无法进行视频处理")

    # 预检查文件夹是否存在视频文件（简化检查）
    try:
        video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        video_count = 0
        for filename in os.listdir(request.folder_path):
            file_ext = os.path.splitext(filename)[1][1:].lower()
            if file_ext in video_extensions:
                video_count += 1

        if video_count < 3:
            raise HTTPException(
                status_code=400,
                detail=f"至少需要3个视频文件才能创建三拼视频，当前只有{video_count}个"
            )
    except OSError as e:
        raise HTTPException(status_code=400, detail=f"无法访问文件夹: {str(e)}")

    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 创建输出文件夹
    output_folder = os.path.join(request.folder_path, 'triple_output')

    # 初始化任务状态
    triple_video_tasks[task_id] = TripleVideoMergeProgress(
        task_id=task_id,
        status="pending",
        progress=0,
        current_step="任务已创建，等待处理",
        total_videos=video_count,
        processed_videos=0,
        output_files=[]
    )

    # 添加后台任务
    background_tasks.add_task(process_triple_video_task, task_id, request, core_service_id)

    # 估算完成时间（基于视频数量）
    estimated_time = max(120, video_count * 10)  # 每个视频大约10秒处理时间

    return TripleVideoMergeResponse(
        success=True,
        task_id=task_id,
        message=f"三拼视频任务已创建，将处理{video_count}个视频文件",
        output_folder=output_folder,
        estimated_time=estimated_time
    )

# API端点：获取三拼视频任务进度
@router.get("/triple-video/{task_id}/progress", response_model=TripleVideoMergeProgress)
async def get_triple_video_progress(task_id: str):
    """
    获取三拼视频任务进度

    - **task_id**: 任务ID
    """
    if task_id not in triple_video_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return triple_video_tasks[task_id]

# API端点：取消三拼视频任务
@router.delete("/triple-video/{task_id}")
async def cancel_triple_video_task(task_id: str):
    """
    取消三拼视频任务

    - **task_id**: 任务ID
    """
    if task_id not in triple_video_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = triple_video_tasks[task_id]
    if task.status in ["completed", "failed"]:
        raise HTTPException(status_code=400, detail="任务已完成，无法取消")

    # 标记任务为已取消
    task.status = "cancelled"
    task.current_step = "任务已取消"

    logger.info(f"三拼视频任务已取消: {task_id}")

    return {"success": True, "message": "任务已取消"}

# API端点：获取所有三拼视频任务
@router.get("/triple-video", response_model=List[TripleVideoMergeProgress])
async def list_triple_video_tasks():
    """获取所有三拼视频任务列表"""
    return list(triple_video_tasks.values())


# ==================== 视频水印处理相关模型和API ====================

# 水印检测请求模型
class WatermarkDetectionRequest(BaseModel):
    video_path: str
    detection_mode: str = "auto"  # auto, template, region
    template_path: Optional[str] = None
    detection_region: Optional[str] = None  # x,y,width,height
    sensitivity: float = 0.7  # 0.0-1.0
    save_detection_result: bool = False

# 水印信息模型
class WatermarkInfo(BaseModel):
    watermark_type: str  # logo, text, timestamp, platform_mark
    position: str  # x,y,width,height
    confidence: float  # 0.0-1.0
    description: str
    time_range: Optional[str] = None  # start_time,end_time

# 水印检测响应模型
class WatermarkDetectionResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    watermark_detected: bool
    watermarks: List[WatermarkInfo] = []
    detection_result_path: Optional[str] = None
    detection_time_ms: int

# 水印清除请求模型
class WatermarkRemovalRequest(BaseModel):
    input_video_path: str
    output_video_path: str
    removal_mode: str = "auto"  # auto, manual, inpaint
    watermark_regions: List[str] = []  # x,y,width,height
    inpaint_method: str = "blur"  # blur, median, inpaint
    output_quality: str = "medium"  # high, medium, low
    preserve_encoding: bool = False

# 水印清除响应模型
class WatermarkRemovalResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    output_file_path: str
    processing_time_ms: int
    original_file_size: int
    output_file_size: int
    removed_watermarks_count: int

# 批量水印处理请求模型
class BatchWatermarkProcessRequest(BaseModel):
    input_folder_path: str
    output_folder_path: str
    process_mode: str = "detect_and_remove"  # detect_only, remove_only, detect_and_remove
    file_filters: List[str] = ["*.mp4"]
    recursive: bool = False
    max_concurrent: int = 3
    detection_config: Optional[WatermarkDetectionRequest] = None
    removal_config: Optional[WatermarkRemovalRequest] = None

# 批量处理结果项模型
class BatchProcessResultItem(BaseModel):
    file_path: str
    status: str  # success, failed, skipped
    error_message: Optional[str] = None
    detection_result: Optional[WatermarkDetectionResponse] = None
    removal_result: Optional[WatermarkRemovalResponse] = None
    processing_time_ms: int

# 批量水印处理响应模型
class BatchWatermarkProcessResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    results: List[BatchProcessResultItem] = []
    total_files: int
    successful_files: int
    failed_files: int
    total_processing_time_ms: int

# 水印处理任务进度模型
class WatermarkTaskProgress(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: int  # 0-100
    current_step: str
    total_files: int
    processed_files: int
    successful_files: int
    failed_files: int
    error_message: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None

# 全局任务存储
watermark_tasks: Dict[str, WatermarkTaskProgress] = {}

# API端点：检测视频水印
@router.post("/watermark/detect", response_model=WatermarkDetectionResponse)
async def detect_video_watermark(
    request: WatermarkDetectionRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    检测视频中的水印

    - **video_path**: 视频文件路径
    - **detection_mode**: 检测模式 (auto, template, region)
    - **template_path**: 水印模板路径（模板匹配模式使用）
    - **detection_region**: 检测区域 (x,y,width,height)
    - **sensitivity**: 检测敏感度 (0.0-1.0)
    - **save_detection_result**: 是否保存检测结果图片
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到水印检测请求: {request.video_path}")

        # 检查文件是否存在
        if not os.path.exists(request.video_path):
            raise HTTPException(status_code=404, detail=f"视频文件不存在: {request.video_path}")

        # 调用Core服务进行水印检测
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务检测水印: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务检测水印")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        result = await core_client.detect_watermark(
            video_path=request.video_path,
            detection_mode=request.detection_mode,
            template_path=request.template_path or "",
            detection_region=request.detection_region or "",
            sensitivity=request.sensitivity,
            save_detection_result=request.save_detection_result
        )

        await core_client.close()

        # 转换水印信息
        watermarks = []
        for wm in result.get("watermarks", []):
            watermarks.append(WatermarkInfo(
                watermark_type=wm.get("watermark_type", ""),
                position=wm.get("position", ""),
                confidence=wm.get("confidence", 0.0),
                description=wm.get("description", ""),
                time_range=wm.get("time_range", "")
            ))

        response = WatermarkDetectionResponse(
            success=result.get("success", False),
            error=result.get("error"),
            watermark_detected=result.get("watermark_detected", False),
            watermarks=watermarks,
            detection_result_path=result.get("detection_result_path"),
            detection_time_ms=result.get("detection_time_ms", 0)
        )

        logger.info(f"水印检测完成: 检测到 {len(watermarks)} 个水印")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"水印检测失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"水印检测失败: {str(e)}")

# API端点：清除视频水印
@router.post("/watermark/remove", response_model=WatermarkRemovalResponse)
async def remove_video_watermark(
    request: WatermarkRemovalRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    清除视频中的水印

    - **input_video_path**: 输入视频文件路径
    - **output_video_path**: 输出视频文件路径
    - **removal_mode**: 清除模式 (auto, manual, inpaint)
    - **watermark_regions**: 水印区域列表 (x,y,width,height)
    - **inpaint_method**: 修复算法 (blur, median, inpaint)
    - **output_quality**: 输出质量 (high, medium, low)
    - **preserve_encoding**: 是否保持原始编码
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到水印清除请求: {request.input_video_path} -> {request.output_video_path}")

        # 检查输入文件是否存在
        if not os.path.exists(request.input_video_path):
            raise HTTPException(status_code=404, detail=f"输入视频文件不存在: {request.input_video_path}")

        # 确保输出目录存在
        output_dir = os.path.dirname(request.output_video_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # 调用Core服务进行水印清除
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务清除水印: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务清除水印")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        result = await core_client.remove_watermark(
            input_video_path=request.input_video_path,
            output_video_path=request.output_video_path,
            removal_mode=request.removal_mode,
            watermark_regions=request.watermark_regions,
            inpaint_method=request.inpaint_method,
            output_quality=request.output_quality,
            preserve_encoding=request.preserve_encoding
        )

        await core_client.close()

        response = WatermarkRemovalResponse(
            success=result.get("success", False),
            error=result.get("error"),
            output_file_path=result.get("output_file_path", ""),
            processing_time_ms=result.get("processing_time_ms", 0),
            original_file_size=result.get("original_file_size", 0),
            output_file_size=result.get("output_file_size", 0),
            removed_watermarks_count=result.get("removed_watermarks_count", 0)
        )

        logger.info(f"水印清除完成: {response.output_file_path}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"水印清除失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"水印清除失败: {str(e)}")

# API端点：批量处理视频水印
@router.post("/watermark/batch", response_model=Dict[str, Any])
async def batch_process_watermark(
    request: BatchWatermarkProcessRequest,
    background_tasks: BackgroundTasks,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    批量处理视频水印（异步任务）

    - **input_folder_path**: 输入文件夹路径
    - **output_folder_path**: 输出文件夹路径
    - **process_mode**: 处理模式 (detect_only, remove_only, detect_and_remove)
    - **file_filters**: 文件过滤器
    - **recursive**: 是否递归处理子目录
    - **max_concurrent**: 最大并发处理数
    - **detection_config**: 检测配置
    - **removal_config**: 清除配置
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到批量水印处理请求: {request.input_folder_path}")

        # 检查输入文件夹是否存在
        if not os.path.exists(request.input_folder_path):
            raise HTTPException(status_code=404, detail=f"输入文件夹不存在: {request.input_folder_path}")

        # 确保输出文件夹存在
        if not os.path.exists(request.output_folder_path):
            os.makedirs(request.output_folder_path, exist_ok=True)

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 创建任务进度记录
        task_progress = WatermarkTaskProgress(
            task_id=task_id,
            status="pending",
            progress=0,
            current_step="准备开始批量处理",
            total_files=0,
            processed_files=0,
            successful_files=0,
            failed_files=0,
            start_time=time.strftime("%Y-%m-%d %H:%M:%S")
        )

        watermark_tasks[task_id] = task_progress

        # 启动后台任务
        background_tasks.add_task(
            execute_batch_watermark_processing,
            task_id,
            request,
            core_service_id
        )

        logger.info(f"批量水印处理任务已创建: {task_id}")

        return {
            "success": True,
            "task_id": task_id,
            "message": "批量水印处理任务已启动",
            "estimated_time": "根据文件数量和大小而定"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建批量水印处理任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建批量水印处理任务失败: {str(e)}")

async def execute_batch_watermark_processing(task_id: str, request: BatchWatermarkProcessRequest, core_service_id: Optional[str] = None):
    """执行批量水印处理任务"""
    try:
        task = watermark_tasks[task_id]
        task.status = "processing"
        task.current_step = "正在处理批量水印任务"

        logger.info(f"开始执行批量水印处理任务: {task_id}")
        if core_service_id:
            logger.info(f"使用指定的Core服务: {core_service_id}")
        else:
            logger.info("使用默认Core服务")

        # 构建检测配置
        detection_config = None
        if request.detection_config:
            detection_config = {
                'detection_mode': request.detection_config.detection_mode,
                'template_path': request.detection_config.template_path or '',
                'detection_region': request.detection_config.detection_region or '',
                'sensitivity': request.detection_config.sensitivity,
                'save_detection_result': request.detection_config.save_detection_result
            }

        # 构建清除配置
        removal_config = None
        if request.removal_config:
            removal_config = {
                'removal_mode': request.removal_config.removal_mode,
                'watermark_regions': request.removal_config.watermark_regions,
                'inpaint_method': request.removal_config.inpaint_method,
                'output_quality': request.removal_config.output_quality,
                'preserve_encoding': request.removal_config.preserve_encoding
            }

        # 调用Core服务进行批量处理
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"批量水印处理使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("批量水印处理使用默认Core服务")

        if not core_client:
            raise Exception("Core服务不可用")

        result = await core_client.batch_process_watermark(
            input_folder_path=request.input_folder_path,
            output_folder_path=request.output_folder_path,
            process_mode=request.process_mode,
            file_filters=request.file_filters,
            recursive=request.recursive,
            max_concurrent=request.max_concurrent,
            detection_config=detection_config,
            removal_config=removal_config
        )

        await core_client.close()

        # 更新任务状态
        if result.get("success", False):
            task.status = "completed"
            task.progress = 100
            task.current_step = "批量处理完成"
            task.total_files = result.get("total_files", 0)
            task.successful_files = result.get("successful_files", 0)
            task.failed_files = result.get("failed_files", 0)
            task.processed_files = task.total_files
        else:
            task.status = "failed"
            task.error_message = result.get("error", "未知错误")
            task.current_step = "批量处理失败"

        task.end_time = time.strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"批量水印处理任务完成: {task_id}, 状态: {task.status}")

    except Exception as e:
        logger.error(f"执行批量水印处理任务异常: {str(e)}", exc_info=True)
        task = watermark_tasks.get(task_id)
        if task:
            task.status = "failed"
            task.error_message = f"执行异常: {str(e)}"
            task.current_step = "任务执行异常"
            task.end_time = time.strftime("%Y-%m-%d %H:%M:%S")

# API端点：获取批量水印处理任务进度
@router.get("/watermark/batch/{task_id}", response_model=WatermarkTaskProgress)
async def get_watermark_task_progress(task_id: str):
    """
    获取批量水印处理任务进度

    - **task_id**: 任务ID
    """
    if task_id not in watermark_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return watermark_tasks[task_id]

# API端点：取消批量水印处理任务
@router.delete("/watermark/batch/{task_id}")
async def cancel_watermark_task(task_id: str):
    """
    取消批量水印处理任务

    - **task_id**: 任务ID
    """
    if task_id not in watermark_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = watermark_tasks[task_id]
    if task.status in ["completed", "failed"]:
        raise HTTPException(status_code=400, detail="任务已完成，无法取消")

    # 标记任务为已取消
    task.status = "cancelled"
    task.current_step = "任务已取消"
    task.end_time = time.strftime("%Y-%m-%d %H:%M:%S")

    logger.info(f"批量水印处理任务已取消: {task_id}")

    return {"success": True, "message": "任务已取消"}

# API端点：获取所有批量水印处理任务
@router.get("/watermark/batch", response_model=List[WatermarkTaskProgress])
async def list_watermark_tasks():
    """获取所有批量水印处理任务列表"""
    return list(watermark_tasks.values())

# API端点：旋转视频
@router.post("/rotate-videos", response_model=VideoRotationResponse)
async def rotate_videos(
    request: VideoRotationRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    旋转视频文件

    - **video_paths**: 要旋转的视频文件路径列表
    - **rotation_angle**: 旋转角度（90: 向右90度, -90: 向左90度, 180: 180度）
    - **output_quality**: 输出质量（high, medium, low）
    - **overwrite_original**: 是否覆盖原文件
    - **output_suffix**: 输出文件名后缀（当不覆盖原文件时使用）
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到视频旋转请求: {len(request.video_paths)} 个文件, 角度: {request.rotation_angle}°")
        logger.info(f"使用Core服务ID: {core_service_id}")

        # 验证旋转角度
        if request.rotation_angle not in [90, -90, 180]:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的旋转角度: {request.rotation_angle}。支持的角度: 90, -90, 180"
            )

        # 验证视频文件路径
        if not request.video_paths:
            raise HTTPException(status_code=400, detail="视频文件路径列表不能为空")

        # 检查文件是否存在
        missing_files = []
        for video_path in request.video_paths:
            if not os.path.exists(video_path):
                missing_files.append(video_path)

        if missing_files:
            raise HTTPException(
                status_code=404,
                detail=f"以下文件不存在: {', '.join(missing_files)}"
            )

        # 调用Core服务进行视频旋转
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务旋转视频")

        try:
            grpc_response = await core_client.rotate_videos(
                video_paths=request.video_paths,
                rotation_angle=request.rotation_angle,
                output_quality=request.output_quality,
                overwrite_original=request.overwrite_original,
                output_suffix=request.output_suffix
            )
        except Exception as e:
            logger.error(f"调用Core服务旋转视频失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for result in grpc_response.get("results", []):
            results.append(VideoRotationResult(
                original_path=result.get("original_path", ""),
                output_path=result.get("output_path", ""),
                success=result.get("success", False),
                error_message=result.get("error_message", ""),
                processing_time_ms=result.get("processing_time_ms", 0),
                original_file_size=result.get("original_file_size", 0),
                output_file_size=result.get("output_file_size", 0)
            ))

        response = VideoRotationResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            results=results,
            successful_count=grpc_response.get("successful_count", 0),
            failed_count=grpc_response.get("failed_count", 0),
            total_processing_time_ms=grpc_response.get("total_processing_time_ms", 0)
        )

        if response.success:
            logger.info(f"视频旋转完成: 成功 {response.successful_count} 个，失败 {response.failed_count} 个")
        else:
            logger.error(f"视频旋转失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"旋转视频异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"旋转视频失败: {str(e)}")


# API端点：批量视频加速
@router.post("/accelerate-videos", response_model=VideoAccelerationResponse)
async def accelerate_videos(
    request: VideoAccelerationRequest,
    background_tasks: BackgroundTasks,
    raw_request: Request,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    批量视频加速到指定时长

    - **video_paths**: 要加速的视频文件路径列表
    - **target_duration**: 目标时长（秒），默认59秒
    - **output_quality**: 输出质量（high, medium, low）
    - **overwrite_original**: 是否覆盖原文件（否则输出到accelerated_videos文件夹）
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        # 记录原始请求数据用于调试
        if raw_request:
            body = await raw_request.body()
            logger.info(f"原始请求体: {body.decode('utf-8')}")

        logger.info(f"收到批量视频加速请求:")
        logger.info(f"  - 视频文件数量: {len(request.video_paths)}")
        logger.info(f"  - 视频文件路径: {request.video_paths}")
        logger.info(f"  - 目标时长: {request.target_duration}秒")
        logger.info(f"  - 输出质量: {request.output_quality}")
        logger.info(f"  - 覆盖原文件: {request.overwrite_original}")
        if not request.overwrite_original:
            logger.info(f"  - 输出到 accelerated_videos 文件夹，保持原文件名")

        # 验证目标时长
        if request.target_duration <= 0:
            raise HTTPException(
                status_code=400,
                detail=f"目标时长必须大于0秒，当前值: {request.target_duration}"
            )

        # 验证视频文件路径
        if not request.video_paths:
            raise HTTPException(status_code=400, detail="视频文件路径列表不能为空")

        # 检查文件是否存在
        missing_files = []
        for video_path in request.video_paths:
            if not os.path.exists(video_path):
                missing_files.append(video_path)

        if missing_files:
            raise HTTPException(
                status_code=404,
                detail=f"以下文件不存在: {', '.join(missing_files)}"
            )

        # 检查是否需要后台处理
        if request.background_processing:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 创建任务记录
            from app.core.schemas.social_repository import SocialDatabaseService
            db = raw_request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            task_data = {
                "task_id": task_id,
                "task_name": f"视频加速任务 - {len(request.video_paths)}个文件",
                "task_type": "video_acceleration",
                "status": "pending",
                "progress": 0,
                "created_at": time.time(),
                "updated_at": time.time(),
                "params": {
                    "video_paths": request.video_paths,
                    "target_duration": request.target_duration,
                    "output_quality": request.output_quality,
                    "overwrite_original": request.overwrite_original,
                    "output_suffix": request.output_suffix,
                    "core_service_id": core_service_id  # 添加core服务ID
                }
            }

            # 保存任务到数据库
            await db_service.create_task(task_data)

            # 添加到后台任务队列
            background_tasks.add_task(execute_video_acceleration_task, task_id, request, db)

            logger.info(f"视频加速任务已提交到后台处理: {task_id}")

            return VideoAccelerationResponse(
                success=True,
                error="",
                task_id=task_id,
                message=f"视频加速任务已提交到后台处理，任务ID: {task_id}"
            )

        # 同步处理模式
        # 调用Core服务进行视频加速
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务加速视频")

        try:
            grpc_response = await core_client.accelerate_videos(
                video_paths=request.video_paths,
                target_duration=request.target_duration,
                output_quality=request.output_quality,
                overwrite_original=request.overwrite_original,
                output_suffix=request.output_suffix
            )
        except Exception as e:
            logger.error(f"调用Core服务加速视频失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for result in grpc_response.get("results", []):
            results.append(VideoAccelerationResult(
                original_path=result.get("original_path", ""),
                output_path=result.get("output_path", ""),
                success=result.get("success", False),
                error_message=result.get("error_message", ""),
                processing_time_ms=result.get("processing_time_ms", 0),
                original_file_size=result.get("original_file_size", 0),
                output_file_size=result.get("output_file_size", 0),
                original_duration=result.get("original_duration", 0.0),
                output_duration=result.get("output_duration", 0.0),
                speed_factor=result.get("speed_factor", 0.0)
            ))

        response = VideoAccelerationResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            results=results,
            successful_count=grpc_response.get("successful_count", 0),
            failed_count=grpc_response.get("failed_count", 0),
            total_processing_time_ms=grpc_response.get("total_processing_time_ms", 0)
        )

        if response.success:
            logger.info(f"视频加速完成: 成功 {response.successful_count} 个，失败 {response.failed_count} 个")
        else:
            logger.error(f"视频加速失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"加速视频异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"加速视频失败: {str(e)}")


@router.get("/accelerate-videos/task/{task_id}")
async def get_video_acceleration_task_status(task_id: str, request: Request):
    """获取视频加速任务状态"""
    try:
        from app.core.schemas.social_repository import SocialDatabaseService
        db = request.app.state.mongo_db
        db_service = SocialDatabaseService(db)

        # 查询任务状态
        task = await db_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        return {
            "success": True,
            "task_id": task_id,
            "status": task.get("status", "unknown"),
            "progress": task.get("progress", 0),
            "message": task.get("message", ""),
            "created_at": task.get("created_at"),
            "updated_at": task.get("updated_at"),
            "result": task.get("result")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")


# ==================== 视频预览相关API ====================

class VideoThumbnailRequest(BaseModel):
    """生成视频缩略图请求"""
    video_path: str
    thumbnail_path: Optional[str] = None
    timestamp: Optional[float] = None
    max_width: Optional[int] = 320
    max_height: Optional[int] = 180
    quality: Optional[int] = 85
    force_regenerate: Optional[bool] = False

class VideoThumbnailResponse(BaseModel):
    """生成视频缩略图响应"""
    success: bool
    error: Optional[str] = None
    thumbnail_path: Optional[str] = None
    thumbnail_url: Optional[str] = None
    thumbnail_size: Optional[int] = None
    actual_width: Optional[int] = None
    actual_height: Optional[int] = None
    generation_time_ms: Optional[int] = None
    from_cache: Optional[bool] = None

class VideoPreviewInfoRequest(BaseModel):
    """获取视频预览信息请求"""
    video_path: str
    include_thumbnail: Optional[bool] = True
    include_detailed_metadata: Optional[bool] = False

class VideoPreviewInfoResponse(BaseModel):
    """获取视频预览信息响应"""
    success: bool
    error: Optional[str] = None
    media_info: Optional[Dict[str, Any]] = None
    thumbnail_info: Optional[Dict[str, Any]] = None
    detailed_metadata: Optional[Dict[str, Any]] = None

class VideoPreviewClipRequest(BaseModel):
    """生成视频预览片段请求"""
    video_path: str
    preview_clip_path: Optional[str] = None
    start_time: Optional[float] = 0
    duration: Optional[float] = 30
    output_quality: Optional[str] = "medium"
    force_regenerate: Optional[bool] = False

class VideoPreviewClipResponse(BaseModel):
    """生成视频预览片段响应"""
    success: bool
    error: Optional[str] = None
    preview_clip_path: Optional[str] = None
    preview_clip_size: Optional[int] = None
    generation_time_ms: Optional[int] = None
    from_cache: Optional[bool] = None

@router.post("/video/thumbnail", response_model=VideoThumbnailResponse)
async def generate_video_thumbnail(
    request: VideoThumbnailRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """生成视频缩略图

    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到生成视频缩略图请求: {request.video_path}")

        # 验证文件存在
        if not os.path.exists(request.video_path):
            raise HTTPException(status_code=404, detail=f"视频文件不存在: {request.video_path}")

        # 验证是否为视频文件
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', '.webm', '.3gp', '.ts'}
        file_ext = os.path.splitext(request.video_path.lower())[1]
        if file_ext not in video_extensions:
            raise HTTPException(status_code=400, detail=f"不支持的视频格式: {file_ext}")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务生成视频缩略图: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务生成视频缩略图")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务生成视频缩略图")

        try:
            grpc_response = await core_client.generate_video_thumbnail(
                video_path=request.video_path,
                thumbnail_path=request.thumbnail_path,
                timestamp=request.timestamp,
                width=request.max_width,
                height=request.max_height,
                quality=request.quality,
                force_regenerate=request.force_regenerate
            )
        except Exception as e:
            logger.error(f"调用Core服务生成视频缩略图失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        response = VideoThumbnailResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            thumbnail_path=grpc_response.get("thumbnail_path", ""),
            thumbnail_url=grpc_response.get("thumbnail_url", ""),
            thumbnail_size=grpc_response.get("thumbnail_size", 0),
            actual_width=grpc_response.get("actual_width", 0),
            actual_height=grpc_response.get("actual_height", 0),
            generation_time_ms=grpc_response.get("generation_time_ms", 0),
            from_cache=grpc_response.get("from_cache", False)
        )

        if response.success:
            logger.info(f"视频缩略图生成成功: {response.thumbnail_path}")
        else:
            logger.error(f"视频缩略图生成失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成视频缩略图异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"生成视频缩略图失败: {str(e)}")

@router.post("/video/preview-info", response_model=VideoPreviewInfoResponse)
async def get_video_preview_info(
    request: VideoPreviewInfoRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """获取视频预览信息

    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到获取视频预览信息请求: {request.video_path}")

        # 验证文件存在
        if not os.path.exists(request.video_path):
            raise HTTPException(status_code=404, detail=f"视频文件不存在: {request.video_path}")

        # 验证是否为视频文件
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', '.webm', '.3gp', '.ts'}
        file_ext = os.path.splitext(request.video_path.lower())[1]
        if file_ext not in video_extensions:
            raise HTTPException(status_code=400, detail=f"不支持的视频格式: {file_ext}")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务获取视频预览信息: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务获取视频预览信息")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务获取视频预览信息")

        try:
            grpc_response = await core_client.get_video_preview_info(
                video_path=request.video_path,
                include_thumbnail=request.include_thumbnail,
                include_detailed_metadata=request.include_detailed_metadata
            )
        except Exception as e:
            logger.error(f"调用Core服务获取视频预览信息失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        response = VideoPreviewInfoResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            media_info=grpc_response.get("media_info", {}),
            thumbnail_info=grpc_response.get("thumbnail_info", {}),
            detailed_metadata=grpc_response.get("detailed_metadata", {})
        )

        if response.success:
            logger.info(f"视频预览信息获取成功: {request.video_path}")
        else:
            logger.error(f"视频预览信息获取失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取视频预览信息异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取视频预览信息失败: {str(e)}")

@router.post("/video/preview-clip", response_model=VideoPreviewClipResponse)
async def generate_video_preview_clip(
    request: VideoPreviewClipRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """生成视频预览片段

    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到生成视频预览片段请求: {request.video_path}")

        # 验证文件存在
        if not os.path.exists(request.video_path):
            raise HTTPException(status_code=404, detail=f"视频文件不存在: {request.video_path}")

        # 验证是否为视频文件
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', '.webm', '.3gp', '.ts'}
        file_ext = os.path.splitext(request.video_path.lower())[1]
        if file_ext not in video_extensions:
            raise HTTPException(status_code=400, detail=f"不支持的视频格式: {file_ext}")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务生成视频预览片段: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务生成视频预览片段")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务生成视频预览片段")

        try:
            grpc_response = await core_client.generate_video_preview_clip(
                video_path=request.video_path,
                preview_clip_path=request.preview_clip_path,
                start_time=request.start_time,
                duration=request.duration,
                output_quality=request.output_quality,
                force_regenerate=request.force_regenerate
            )
        except Exception as e:
            logger.error(f"调用Core服务生成视频预览片段失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        response = VideoPreviewClipResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            preview_clip_path=grpc_response.get("preview_clip_path", ""),
            preview_clip_size=grpc_response.get("preview_clip_size", 0),
            generation_time_ms=grpc_response.get("generation_time_ms", 0),
            from_cache=grpc_response.get("from_cache", False)
        )

        if response.success:
            logger.info(f"视频预览片段生成成功: {response.preview_clip_path}")
        else:
            logger.error(f"视频预览片段生成失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成视频预览片段异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"生成视频预览片段失败: {str(e)}")

# 批量视频预览信息获取
class BatchVideoPreviewRequest(BaseModel):
    """批量获取视频预览信息请求"""
    video_paths: List[str]
    include_thumbnail: Optional[bool] = True
    include_detailed_metadata: Optional[bool] = False

class BatchVideoPreviewResponse(BaseModel):
    """批量获取视频预览信息响应"""
    success: bool
    error: Optional[str] = None
    results: List[Dict[str, Any]] = []
    successful_count: int = 0
    failed_count: int = 0

@router.post("/video/batch-preview-info", response_model=BatchVideoPreviewResponse)
async def get_batch_video_preview_info(request: BatchVideoPreviewRequest):
    """批量获取视频预览信息"""
    try:
        logger.info(f"收到批量获取视频预览信息请求: {len(request.video_paths)} 个文件")

        if not request.video_paths:
            raise HTTPException(status_code=400, detail="视频路径列表不能为空")

        # 调用Core服务
        from app.core.grpc_client import get_core_client

        core_client = get_core_client()
        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        results = []
        successful_count = 0
        failed_count = 0

        # 逐个处理视频文件
        for video_path in request.video_paths:
            try:
                # 验证文件存在
                if not os.path.exists(video_path):
                    results.append({
                        "video_path": video_path,
                        "success": False,
                        "error": f"视频文件不存在: {video_path}"
                    })
                    failed_count += 1
                    continue

                # 验证是否为视频文件
                video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', '.webm', '.3gp', '.ts'}
                file_ext = os.path.splitext(video_path.lower())[1]
                if file_ext not in video_extensions:
                    results.append({
                        "video_path": video_path,
                        "success": False,
                        "error": f"不支持的视频格式: {file_ext}"
                    })
                    failed_count += 1
                    continue

                # 调用Core服务获取预览信息
                grpc_response = await core_client.get_video_preview_info(
                    video_path=video_path,
                    include_thumbnail=request.include_thumbnail,
                    include_detailed_metadata=request.include_detailed_metadata
                )

                if grpc_response.get("success", False):
                    results.append({
                        "video_path": video_path,
                        "success": True,
                        "media_info": grpc_response.get("media_info", {}),
                        "thumbnail_info": grpc_response.get("thumbnail_info", {}),
                        "detailed_metadata": grpc_response.get("detailed_metadata", {})
                    })
                    successful_count += 1
                else:
                    results.append({
                        "video_path": video_path,
                        "success": False,
                        "error": grpc_response.get("error", "未知错误")
                    })
                    failed_count += 1

            except Exception as e:
                logger.error(f"处理视频文件失败 {video_path}: {str(e)}")
                results.append({
                    "video_path": video_path,
                    "success": False,
                    "error": f"处理失败: {str(e)}"
                })
                failed_count += 1

        # 构建响应
        response = BatchVideoPreviewResponse(
            success=True,
            results=results,
            successful_count=successful_count,
            failed_count=failed_count
        )

        logger.info(f"批量获取视频预览信息完成: 成功 {successful_count} 个，失败 {failed_count} 个")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量获取视频预览信息异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量获取视频预览信息失败: {str(e)}")


# 音频处理相关API端点

@router.post("/generate-subtitles", response_model=AudioProcessingResponse)
async def generate_subtitles_batch(
    request: GenerateSubtitlesRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    批量生成字幕

    - **folder_path**: 文件夹路径
    - **output_format**: 输出格式 (srt, vtt, txt)
    - **language**: 语言代码 (auto, zh, en, etc.)
    - **model_size**: Whisper模型大小 (tiny, base, small, medium, large)
    - **max_concurrent**: 最大并发处理数
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到批量生成字幕请求: {request.folder_path}")

        # 验证文件夹路径
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail="文件夹不存在")

        if not os.path.isdir(request.folder_path):
            raise HTTPException(status_code=400, detail="指定路径不是文件夹")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务生成字幕: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务生成字幕")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务生成字幕")

        try:
            grpc_response = await core_client.generate_subtitles_batch(
                folder_path=request.folder_path,
                output_format=request.output_format,
                language=request.language,
                model_size=request.model_size,
                max_concurrent=request.max_concurrent
            )
        except Exception as e:
            logger.error(f"调用Core服务生成字幕失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for item in grpc_response.get('results', []):
            results.append(AudioProcessingResult(
                success=item.get('success', False),
                input_file=item.get('input_file', ''),
                output_file=item.get('output_file', ''),
                error_message=item.get('error_message', ''),
                processing_time_ms=item.get('processing_time_ms', 0),
                input_file_size=item.get('input_file_size', 0),
                output_file_size=item.get('output_file_size', 0),
                message=item.get('message', '')
            ))

        response = AudioProcessingResponse(
            success=grpc_response.get('success', False),
            error=grpc_response.get('error', ''),
            processed_files=grpc_response.get('processed_files', 0),
            successful_count=grpc_response.get('successful_count', 0),
            failed_count=grpc_response.get('failed_count', 0),
            output_directory=grpc_response.get('output_directory', ''),
            results=results
        )

        if response.success:
            logger.info(f"批量生成字幕成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
        else:
            logger.error(f"批量生成字幕失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量生成字幕异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量生成字幕失败: {str(e)}")

@router.post("/extract-audio", response_model=AudioProcessingResponse)
async def extract_audio_batch(
    request: ExtractAudioRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    批量分离音频

    - **folder_path**: 文件夹路径
    - **output_format**: 输出音频格式 (wav, mp3, aac)
    - **quality**: 音频质量 (high, medium, low)
    - **max_concurrent**: 最大并发处理数
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到批量分离音频请求: {request.folder_path}")

        # 验证文件夹路径
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail="文件夹不存在")

        if not os.path.isdir(request.folder_path):
            raise HTTPException(status_code=400, detail="指定路径不是文件夹")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务分离音频: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务分离音频")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务分离音频")

        try:
            grpc_response = await core_client.extract_audio_batch(
                folder_path=request.folder_path,
                output_format=request.output_format,
                quality=request.quality,
                max_concurrent=request.max_concurrent
            )
        except Exception as e:
            logger.error(f"调用Core服务分离音频失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for item in grpc_response.get('results', []):
            results.append(AudioProcessingResult(
                success=item.get('success', False),
                input_file=item.get('input_file', ''),
                output_file=item.get('output_file', ''),
                error_message=item.get('error_message', ''),
                processing_time_ms=item.get('processing_time_ms', 0),
                input_file_size=item.get('input_file_size', 0),
                output_file_size=item.get('output_file_size', 0),
                message=item.get('message', '')
            ))

        response = AudioProcessingResponse(
            success=grpc_response.get('success', False),
            error=grpc_response.get('error', ''),
            processed_files=grpc_response.get('processed_files', 0),
            successful_count=grpc_response.get('successful_count', 0),
            failed_count=grpc_response.get('failed_count', 0),
            output_directory=grpc_response.get('output_directory', ''),
            results=results
        )

        if response.success:
            logger.info(f"批量分离音频成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
        else:
            logger.error(f"批量分离音频失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量分离音频异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量分离音频失败: {str(e)}")

@router.post("/separate-vocals", response_model=VocalSeparationResponse)
async def separate_vocals_batch(
    request: SeparateVocalsRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    批量人声分离

    - **folder_path**: 文件夹路径
    - **selected_files**: 选中的文件列表（可选，如果不提供则处理整个文件夹）
    - **separation_method**: 分离方法 (ffmpeg, librosa)
    - **output_quality**: 输出质量 (high, medium, low)
    - **max_concurrent**: 最大并发处理数
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到批量人声分离请求: {request.folder_path}")
        if request.selected_files:
            logger.info(f"选中文件数量: {len(request.selected_files)}")
        else:
            logger.info("处理整个文件夹")

        # 验证文件夹路径
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail="文件夹不存在")

        if not os.path.isdir(request.folder_path):
            raise HTTPException(status_code=400, detail="指定路径不是文件夹")

        # 验证选中的文件（如果提供了）
        if request.selected_files and len(request.selected_files) > 0:
            for file_name in request.selected_files:
                file_path = os.path.join(request.folder_path, file_name)
                if not os.path.exists(file_path):
                    raise HTTPException(status_code=404, detail=f"选中的文件不存在: {file_name}")
                if not os.path.isfile(file_path):
                    raise HTTPException(status_code=400, detail=f"选中的项目不是文件: {file_name}")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务人声分离: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务人声分离")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务人声分离")

        try:
            # 准备选中文件列表
            selected_files_list = None
            if request.selected_files and len(request.selected_files) > 0:
                selected_files_list = list(request.selected_files)
                logger.info(f"传递给Core服务的选中文件: {selected_files_list}")
            else:
                logger.info("没有选中文件，Core服务将处理整个文件夹")

            grpc_response = await core_client.separate_vocals_batch(
                folder_path=request.folder_path,
                output_type=request.output_type,
                separation_method=request.separation_method,
                output_quality=request.output_quality,
                max_concurrent=request.max_concurrent,
                selected_files=selected_files_list
            )
        except Exception as e:
            logger.error(f"调用Core服务人声分离失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for item in grpc_response.get('results', []):
            results.append(VocalSeparationResult(
                success=item.get('success', False),
                input_file=item.get('input_file', ''),
                vocals_file=item.get('vocals_file', ''),
                instrumental_file=item.get('instrumental_file', ''),
                error_message=item.get('error_message', ''),
                processing_time_ms=item.get('processing_time_ms', 0),
                input_file_size=item.get('input_file_size', 0),
                vocals_file_size=item.get('vocals_file_size', 0),
                instrumental_file_size=item.get('instrumental_file_size', 0),
                message=item.get('message', '')
            ))

        response = VocalSeparationResponse(
            success=grpc_response.get('success', False),
            error=grpc_response.get('error', ''),
            processed_files=grpc_response.get('processed_files', 0),
            successful_count=grpc_response.get('successful_count', 0),
            failed_count=grpc_response.get('failed_count', 0),
            output_directory=grpc_response.get('output_directory', ''),
            results=results
        )

        if response.success:
            logger.info(f"批量人声分离成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
        else:
            logger.error(f"批量人声分离失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量人声分离异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量人声分离失败: {str(e)}")

@router.post("/replace-audio", response_model=ReplaceAudioResponse)
async def replace_audio_batch(
    request: ReplaceAudioRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    批量替换音频

    - **folder_path**: 视频文件夹路径
    - **new_audio_path**: 新音频文件路径
    - **audio_volume**: 音频音量 (0.0-2.0)
    - **fade_duration**: 淡入淡出时长（秒）
    - **max_concurrent**: 最大并发处理数
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到批量替换音频请求: {request.folder_path}")

        # 验证文件夹路径
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail="文件夹不存在")

        if not os.path.isdir(request.folder_path):
            raise HTTPException(status_code=400, detail="指定路径不是文件夹")

        # 验证新音频文件
        if not os.path.exists(request.new_audio_path):
            raise HTTPException(status_code=404, detail="新音频文件不存在")

        if not os.path.isfile(request.new_audio_path):
            raise HTTPException(status_code=400, detail="指定路径不是文件")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务替换音频: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务替换音频")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务替换音频")

        try:
            grpc_response = await core_client.replace_audio_batch(
                folder_path=request.folder_path,
                new_audio_path=request.new_audio_path,
                audio_volume=request.audio_volume,
                fade_duration=request.fade_duration,
                max_concurrent=request.max_concurrent
            )
        except Exception as e:
            logger.error(f"调用Core服务替换音频失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for item in grpc_response.get('results', []):
            results.append(AudioProcessingResult(
                success=item.get('success', False),
                input_file=item.get('input_file', ''),
                output_file=item.get('output_file', ''),
                error_message=item.get('error_message', ''),
                processing_time_ms=item.get('processing_time_ms', 0),
                input_file_size=item.get('input_file_size', 0),
                output_file_size=item.get('output_file_size', 0),
                message=item.get('message', '')
            ))

        response = ReplaceAudioResponse(
            success=grpc_response.get('success', False),
            error=grpc_response.get('error', ''),
            processed_files=grpc_response.get('processed_files', 0),
            successful_count=grpc_response.get('successful_count', 0),
            failed_count=grpc_response.get('failed_count', 0),
            output_directory=grpc_response.get('output_directory', ''),
            new_audio_file=grpc_response.get('new_audio_file', ''),
            results=results
        )

        if response.success:
            logger.info(f"批量替换音频成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
        else:
            logger.error(f"批量替换音频失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量替换音频异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量替换音频失败: {str(e)}")


@router.post("/pre-publish", response_model=PrePublishResponse)
async def pre_publish_files(
    request: PrePublishRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    预发布功能：将选中的视频文件剪切到根目录下的publishing文件夹

    - **file_paths**: 要预发布的文件路径列表
    - **current_folder**: 当前文件夹路径，用于确定根目录
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    logger.info(f"预发布文件请求: {len(request.file_paths)} 个文件")
    logger.info(f"当前文件夹: {request.current_folder}")

    try:
        # 验证文件路径（在测试模式下跳过文件存在性检查）
        missing_files = []
        test_mode = any("test" in path.lower() or "demo" in path.lower() for path in request.file_paths)

        if not test_mode:
            for file_path in request.file_paths:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
                    logger.warning(f"文件不存在: {file_path}")

            if missing_files:
                raise HTTPException(
                    status_code=404,
                    detail=f"以下文件不存在: {', '.join(missing_files)}"
                )
        else:
            logger.info("检测到测试模式，跳过文件存在性检查")

        # 确定账号根目录
        # 路径结构：H:\PublishSystem\{platform_id}\{account_name}\{可能的子文件夹}
        # 目标：找到账号根目录 H:\PublishSystem\{platform_id}\{account_name}
        current_path = os.path.abspath(request.current_folder)
        path_parts = current_path.split(os.sep)

        logger.info(f"当前路径: {current_path}")
        logger.info(f"路径分割: {path_parts}")

        account_root_dir = None

        # 查找包含"PublishSystem"的路径部分
        publishsystem_index = -1
        for i, part in enumerate(path_parts):
            if "PublishSystem" in part:
                publishsystem_index = i
                break

        if publishsystem_index >= 0 and len(path_parts) > publishsystem_index + 2:
            # 路径结构：[盘符, ..., PublishSystem, platform, account, ...]
            # 账号根目录应该是：盘符/.../PublishSystem/platform/account
            account_root_dir = os.sep.join(path_parts[:publishsystem_index + 3])
            logger.info(f"找到账号根目录: {account_root_dir}")
        else:
            # 如果路径结构不符合预期，尝试其他方法
            # 假设当前路径就是账号根目录或其子目录
            # 向上查找直到找到合适的账号根目录
            temp_path = current_path
            while temp_path and temp_path != os.path.dirname(temp_path):
                # 检查是否是账号根目录（包含平台名称的上级目录）
                parent_dir = os.path.dirname(temp_path)
                if parent_dir and any(platform in os.path.basename(parent_dir).lower()
                                    for platform in ['youtube', 'tiktok', 'douyin', 'kuaishou', 'xiaohongshu']):
                    account_root_dir = temp_path
                    logger.info(f"通过平台名称推断账号根目录: {account_root_dir}")
                    break
                temp_path = parent_dir

            # 如果还是找不到，使用当前路径
            if not account_root_dir:
                account_root_dir = current_path
                logger.warning(f"无法确定账号根目录，使用当前路径: {account_root_dir}")

        logger.info(f"确定的账号根目录: {account_root_dir}")

        # 创建publishing文件夹路径（在账号根目录下）
        publishing_folder = os.path.join(account_root_dir, "publishing")
        logger.info(f"Publishing文件夹路径: {publishing_folder}")

        # 准备文件移动操作
        operations = []
        for file_path in request.file_paths:
            file_name = os.path.basename(file_path)
            target_path = os.path.join(publishing_folder, file_name)

            # 如果目标文件已存在，添加时间戳后缀
            if os.path.exists(target_path):
                name, ext = os.path.splitext(file_name)
                timestamp = int(time.time())
                target_path = os.path.join(publishing_folder, f"{name}_{timestamp}{ext}")
                logger.info(f"目标文件已存在，重命名为: {os.path.basename(target_path)}")

            operations.append({
                "source_path": file_path,
                "target_path": target_path
            })

        # 调用Core服务进行文件移动
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务进行预发布: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务进行预发布")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务移动文件到publishing文件夹")

        try:
            grpc_response = await core_client.move_files(operations)
            logger.info(f"Core服务响应: {grpc_response}")
        except Exception as e:
            logger.error(f"调用Core服务移动文件失败: {str(e)}")
            # 如果Core服务不可用，尝试本地文件移动作为降级方案
            logger.info("尝试本地文件移动作为降级方案")
            moved_count = 0
            errors = []

            # 确保publishing目录存在
            if not os.path.exists(publishing_folder):
                os.makedirs(publishing_folder, exist_ok=True)
                logger.info(f"创建publishing目录: {publishing_folder}")

            for operation in operations:
                try:
                    source_path = operation["source_path"]
                    target_path = operation["target_path"]

                    if test_mode:
                        # 测试模式：模拟文件移动
                        logger.info(f"测试模式 - 模拟移动: {source_path} -> {target_path}")
                        moved_count += 1
                    elif os.path.exists(source_path):
                        # 使用shutil.move进行文件移动
                        import shutil
                        shutil.move(source_path, target_path)
                        moved_count += 1
                        logger.info(f"本地移动成功: {source_path} -> {target_path}")
                    else:
                        error_msg = f"源文件不存在: {source_path}"
                        errors.append(error_msg)
                        logger.warning(error_msg)

                except Exception as move_error:
                    error_msg = f"移动文件失败 {operation['source_path']}: {str(move_error)}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            # 构建降级响应
            grpc_response = {
                "success": moved_count > 0,
                "moved_count": moved_count,
                "total_count": len(operations),
                "errors": errors
            }
            logger.info(f"本地移动完成: {grpc_response}")

        # 构建成功移动的文件列表
        moved_files = []
        if grpc_response.get("success", False):
            if test_mode:
                # 测试模式：直接使用操作列表构建moved_files
                moved_files = [os.path.basename(op["target_path"]) for op in operations]
                logger.info(f"测试模式 - 模拟移动的文件: {moved_files}")
            else:
                # 检查哪些文件成功移动了
                for operation in operations:
                    target_path = operation["target_path"]
                    source_path = operation["source_path"]
                    # 如果目标文件存在且源文件不存在，说明移动成功
                    if os.path.exists(target_path) and not os.path.exists(source_path):
                        moved_files.append(os.path.basename(target_path))
                    elif os.path.exists(target_path):
                        # 如果目标文件存在，也认为是成功的（可能是复制而非移动）
                        moved_files.append(os.path.basename(target_path))

        response = PrePublishResponse(
            success=grpc_response.get("success", False),
            moved_count=grpc_response.get("moved_count", 0),
            total_count=grpc_response.get("total_count", 0),
            publishing_folder=publishing_folder,
            moved_files=moved_files,
            errors=grpc_response.get("errors", [])
        )

        if response.success:
            logger.info(f"预发布成功: 移动了 {response.moved_count}/{response.total_count} 个文件到 {publishing_folder}")
        else:
            logger.error(f"预发布失败: {response.errors}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预发布异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"预发布失败: {str(e)}")


# API端点：批量添加视频片头片尾
@router.post("/add-intro-outro", response_model=VideoIntroOutroResponse)
async def add_intro_outro_batch(request: VideoIntroOutroRequest, raw_request: Request):
    """
    批量为视频添加片头片尾

    - **folder_path**: 视频文件夹路径
    - **intro_path**: 片头文件路径（可选）
    - **outro_path**: 片尾文件路径（可选）
    - **output_folder**: 输出文件夹路径（可选）
    - **transition_effect**: 转场效果（fade, dissolve, wipe, slide, zoom, none）
    - **transition_duration**: 转场时长（秒）
    - **output_quality**: 输出质量（high/medium/low）
    - **max_concurrent**: 最大并发处理数
    - **overwrite_original**: 是否覆盖原文件
    - **output_suffix**: 输出文件名后缀
    """
    try:
        logger.info(f"收到批量添加片头片尾请求:")
        logger.info(f"  - 文件夹路径: {request.folder_path}")
        logger.info(f"  - request.selected_files: {request.selected_files}")
        logger.info(f"  - selected_files 类型: {type(request.selected_files)}")

        if request.selected_files:
            logger.info(f"  - 选中文件数量: {len(request.selected_files)}")
            logger.info(f"  - 选中文件列表: {list(request.selected_files)}")
        else:
            logger.info(f"  - 处理模式: 整个文件夹")

        # 验证输入参数
        if not request.intro_path and not request.outro_path:
            raise HTTPException(status_code=400, detail="必须指定片头或片尾文件")

        # 验证文件夹路径
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail="视频文件夹不存在")

        if not os.path.isdir(request.folder_path):
            raise HTTPException(status_code=400, detail="指定路径不是文件夹")

        # 验证选中的文件（如果提供了）
        if request.selected_files and len(request.selected_files) > 0:
            for file_name in request.selected_files:
                file_path = os.path.join(request.folder_path, file_name)
                if not os.path.exists(file_path):
                    raise HTTPException(status_code=404, detail=f"选中的文件不存在: {file_name}")
                if not os.path.isfile(file_path):
                    raise HTTPException(status_code=400, detail=f"选中的项目不是文件: {file_name}")

        # 验证片头文件
        if request.intro_path and not os.path.exists(request.intro_path):
            raise HTTPException(status_code=404, detail=f"片头文件不存在: {request.intro_path}")

        # 验证片尾文件
        if request.outro_path and not os.path.exists(request.outro_path):
            raise HTTPException(status_code=404, detail=f"片尾文件不存在: {request.outro_path}")

        # 调用Core服务
        from app.core.grpc_client import get_core_client

        core_client = get_core_client()
        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务批量添加片头片尾")

        try:
            # 获取Core服务客户端
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()

            if not core_client:
                raise HTTPException(status_code=503, detail="无法连接到Core服务")

            # 准备选中文件列表
            selected_files_list = None
            if request.selected_files and len(request.selected_files) > 0:
                selected_files_list = list(request.selected_files)
                logger.info(f"传递给Core服务的选中文件: {selected_files_list}")
            else:
                logger.info("没有选中文件，Core服务将处理整个文件夹")

            # 调用Core服务的片头片尾处理功能
            grpc_response = await core_client.batch_add_intro_outro(
                folder_path=request.folder_path,
                selected_files=selected_files_list,
                intro_path=request.intro_path,
                outro_path=request.outro_path,
                output_folder=request.output_folder,
                transition_effect=request.transition_effect,
                transition_duration=request.transition_duration,
                output_quality=request.output_quality,
                max_concurrent=request.max_concurrent,
                overwrite_original=request.overwrite_original,
                output_suffix=request.output_suffix
            )

            logger.info(f"批量添加片头片尾完成: 处理了 {grpc_response.get('total_count', 0)} 个文件，成功 {grpc_response.get('successful_count', 0)} 个")

        except Exception as e:
            logger.error(f"调用Core服务批量添加片头片尾失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for item in grpc_response.get("results", []):
            result = VideoIntroOutroResult(
                input_path=item.get("input_path", ""),
                output_path=item.get("output_path", ""),
                success=item.get("success", False),
                error_message=item.get("error_message", ""),
                processing_time_ms=item.get("processing_time_ms", 0),
                output_size=item.get("output_size", 0)
            )
            results.append(result)

        response = VideoIntroOutroResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            results=results,
            successful_count=grpc_response.get("successful_count", 0),
            failed_count=grpc_response.get("failed_count", 0),
            total_count=grpc_response.get("total_count", 0),
            total_processing_time_ms=grpc_response.get("total_processing_time_ms", 0),
            output_folder=grpc_response.get("output_folder", ""),
            total_output_size=grpc_response.get("total_output_size", 0)
        )

        if response.success:
            logger.info(f"批量添加片头片尾成功: 处理了 {response.total_count} 个文件，成功 {response.successful_count} 个")
        else:
            logger.error(f"批量添加片头片尾失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量添加片头片尾异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量添加片头片尾失败: {str(e)}")


# 视频裁剪相关数据模型
class VideoClipRequest(BaseModel):
    """视频裁剪请求"""
    video_paths: List[str]
    clip_mode: str  # "segments" 或 "duration"
    segment_count: Optional[int] = 0  # 段数（segments模式）
    segment_duration: Optional[float] = 0.0  # 每段时长（duration模式）
    buffer_duration: Optional[float] = 2.0  # 裁剪点检测缓冲区
    output_quality: Optional[str] = "medium"  # 输出质量
    output_folder: Optional[str] = ""  # 输出文件夹
    filename_template: Optional[str] = ""  # 文件名模板
    preserve_audio_quality: Optional[bool] = True  # 保留原始音频质量
    max_concurrent: Optional[int] = 3  # 最大并发数
    volume_sensitivity: Optional[float] = 0.5  # 音量检测敏感度
    min_segment_duration: Optional[float] = 5.0  # 最小片段时长
    background_processing: Optional[bool] = False  # 是否后台处理

class VideoSegmentInfo(BaseModel):
    """视频片段信息"""
    file_path: str
    segment_index: int
    start_time: float
    end_time: float
    duration: float
    file_size: int
    start_volume: float
    end_volume: float

class VideoClipResult(BaseModel):
    """单个视频裁剪结果"""
    input_path: str
    success: bool
    error_message: str
    processing_time_ms: int
    segments: List[VideoSegmentInfo]
    original_duration: float
    volume_analysis_data: str

class VideoClipResponse(BaseModel):
    """视频裁剪响应"""
    success: bool
    error: str
    results: Optional[List[VideoClipResult]] = None
    total_processing_time_ms: Optional[int] = 0
    successful_count: Optional[int] = 0
    failed_count: Optional[int] = 0
    total_count: Optional[int] = 0
    output_folder: Optional[str] = ""
    total_output_size: Optional[int] = 0
    total_segments: Optional[int] = 0
    # 后台处理相关字段
    task_id: Optional[str] = None  # 后台任务ID
    message: Optional[str] = None  # 提示信息


# ==================== 高清处理相关模型 ====================

class VideoEnhanceRequest(BaseModel):
    """视频高清处理请求"""
    file_paths: List[str]  # 要处理的文件路径列表（支持视频和图片）
    enhance_mode: str = 'ai'  # 处理模式：ai, traditional
    scale_factor: int = 2  # 放大倍数：2, 3, 4
    output_quality: str = 'high'  # 输出质量：high, medium, low
    denoise_level: str = 'medium'  # 降噪级别：none, low, medium, high
    sharpen_level: str = 'medium'  # 锐化级别：none, low, medium, high
    overwrite_original: bool = False  # 是否覆盖原文件
    output_suffix: str = '_enhanced'  # 输出文件名后缀
    max_concurrent: int = 2  # 最大并发处理数
    background_processing: Optional[bool] = False  # 是否后台处理

class VideoEnhanceResult(BaseModel):
    """单个文件高清处理结果"""
    original_path: str
    output_path: str
    success: bool
    error_message: str
    processing_time_ms: int
    original_file_size: int
    output_file_size: int
    original_resolution: Optional[str] = None
    output_resolution: Optional[str] = None
    scale_factor: int

class VideoEnhanceResponse(BaseModel):
    """高清处理响应"""
    success: bool
    error: str
    results: Optional[List[VideoEnhanceResult]] = None
    total_processing_time_ms: Optional[int] = 0
    successful_count: Optional[int] = 0
    failed_count: Optional[int] = 0
    total_count: Optional[int] = 0
    output_folder: Optional[str] = ""
    total_output_size: Optional[int] = 0
    # 后台处理相关字段
    task_id: Optional[str] = None  # 后台任务ID
    message: Optional[str] = None  # 提示信息


async def execute_video_clip_task(task_id: str, request: VideoClipRequest, db):
    """后台执行视频裁剪任务"""
    try:
        logger.info(f"开始执行后台视频裁剪任务: {task_id}")

        # 获取数据库服务
        from app.core.schemas.social_repository import SocialDatabaseService

        # 创建数据库服务实例
        db_service = SocialDatabaseService(db)

        # 更新任务状态为执行中
        await db_service.update_task(task_id, {
            "status": "running",
            "progress": 0,
            "updated_at": time.time(),
            "message": "开始视频裁剪处理"
        })

        # 获取任务参数中的core_service_id
        task = await db_service.get_task(task_id)
        core_service_id = task.get("params", {}).get("core_service_id") if task else None

        # 调用Core服务进行视频裁剪
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"后台任务使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("后台任务使用默认Core服务")

        if not core_client:
            raise Exception("Core服务不可用")

        # 更新进度
        await db_service.update_task(task_id, {
            "progress": 10,
            "message": "正在调用Core服务进行视频裁剪"
        })

        grpc_response = await core_client.clip_videos(
            video_paths=request.video_paths,
            clip_mode=request.clip_mode,
            segment_count=request.segment_count or 0,
            segment_duration=request.segment_duration or 0.0,
            buffer_duration=request.buffer_duration or 2.0,
            output_quality=request.output_quality or "medium",
            output_folder=request.output_folder or "",
            filename_template=request.filename_template or "",
            preserve_audio_quality=request.preserve_audio_quality if request.preserve_audio_quality is not None else True,
            max_concurrent=request.max_concurrent or 3,
            volume_sensitivity=request.volume_sensitivity or 0.5,
            min_segment_duration=request.min_segment_duration or 5.0
        )

        # 更新任务状态为完成
        await db_service.update_task(task_id, {
            "status": "completed",
            "progress": 100,
            "updated_at": time.time(),
            "message": "视频裁剪任务完成",
            "result": {
                "success": grpc_response.get("success", False),
                "error": grpc_response.get("error", ""),
                "total_count": grpc_response.get("total_count", 0),
                "successful_count": grpc_response.get("successful_count", 0),
                "failed_count": grpc_response.get("failed_count", 0),
                "output_folder": grpc_response.get("output_folder", ""),
                "total_segments": grpc_response.get("total_segments", 0)
            }
        })

        logger.info(f"后台视频裁剪任务完成: {task_id}")

    except Exception as e:
        logger.error(f"后台视频裁剪任务失败: {task_id}, 错误: {str(e)}")

        # 更新任务状态为失败
        try:
            from app.core.schemas.social_repository import SocialDatabaseService
            db_service = SocialDatabaseService(db)
            await db_service.update_task(task_id, {
                "status": "failed",
                "progress": 0,
                "updated_at": time.time(),
                "message": f"视频裁剪任务失败: {str(e)}"
            })
        except:
            pass


async def execute_video_acceleration_task(task_id: str, request: VideoAccelerationRequest, db):
    """后台执行视频加速任务"""
    try:
        logger.info(f"开始执行后台视频加速任务: {task_id}")

        # 获取数据库服务
        from app.core.schemas.social_repository import SocialDatabaseService

        # 创建数据库服务实例
        db_service = SocialDatabaseService(db)

        # 更新任务状态为执行中
        await db_service.update_task(task_id, {
            "status": "running",
            "progress": 0,
            "updated_at": time.time(),
            "message": "开始视频加速处理"
        })

        # 获取任务参数中的core_service_id
        task = await db_service.get_task(task_id)
        core_service_id = task.get("params", {}).get("core_service_id") if task else None

        # 调用Core服务进行视频加速
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"后台任务使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("后台任务使用默认Core服务")

        if not core_client:
            raise Exception("Core服务不可用")

        # 更新进度
        await db_service.update_task(task_id, {
            "progress": 10,
            "message": "正在调用Core服务进行视频加速"
        })

        grpc_response = await core_client.accelerate_videos(
            video_paths=request.video_paths,
            target_duration=request.target_duration,
            output_quality=request.output_quality,
            overwrite_original=request.overwrite_original,
            output_suffix=request.output_suffix
        )

        # 更新任务状态为完成
        await db_service.update_task(task_id, {
            "status": "completed",
            "progress": 100,
            "updated_at": time.time(),
            "message": "视频加速任务完成",
            "result": {
                "success": grpc_response.get("success", False),
                "error": grpc_response.get("error", ""),
                "successful_count": grpc_response.get("successful_count", 0),
                "failed_count": grpc_response.get("failed_count", 0),
                "total_processing_time_ms": grpc_response.get("total_processing_time_ms", 0)
            }
        })

        logger.info(f"后台视频加速任务完成: {task_id}")

    except Exception as e:
        logger.error(f"后台视频加速任务失败: {task_id}, 错误: {str(e)}")

        # 更新任务状态为失败
        try:
            from app.core.schemas.social_repository import SocialDatabaseService
            db_service = SocialDatabaseService(db)
            await db_service.update_task(task_id, {
                "status": "failed",
                "progress": 0,
                "updated_at": time.time(),
                "message": f"视频加速任务失败: {str(e)}"
            })
        except:
            pass


@router.post("/clip-videos", response_model=VideoClipResponse)
async def clip_videos(
    request: VideoClipRequest,
    background_tasks: BackgroundTasks,
    raw_request: Request,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    智能视频裁剪

    支持两种裁剪模式：
    1. segments模式：按指定段数裁剪
    2. duration模式：按指定时长裁剪

    裁剪算法会在目标时间点附近寻找音量最小的位置进行裁剪，
    以确保裁剪点在语句或内容的自然停顿处。

    支持后台处理：设置 background_processing=true 可以将任务提交到后台处理

    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    logger.info(f"收到视频裁剪请求: {len(request.video_paths)} 个文件, 模式: {request.clip_mode}")
    logger.info(f"使用Core服务ID: {core_service_id}")

    try:
        # 验证裁剪模式
        if request.clip_mode not in ["segments", "duration"]:
            raise HTTPException(status_code=400, detail="裁剪模式必须是 'segments' 或 'duration'")

        if request.clip_mode == "segments" and (not request.segment_count or request.segment_count <= 0):
            raise HTTPException(status_code=400, detail="segments模式下段数必须大于0")

        if request.clip_mode == "duration" and (not request.segment_duration or request.segment_duration <= 0):
            raise HTTPException(status_code=400, detail="duration模式下每段时长必须大于0")

        # 验证文件路径
        if not request.video_paths:
            raise HTTPException(status_code=400, detail="视频文件路径列表不能为空")

        # 验证文件存在性
        missing_files = []
        for video_path in request.video_paths:
            if not os.path.exists(video_path):
                missing_files.append(video_path)

        if missing_files:
            raise HTTPException(
                status_code=404,
                detail=f"以下文件不存在: {', '.join(missing_files)}"
            )

        # 检查是否需要后台处理
        if request.background_processing:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 创建任务记录
            from app.core.schemas.social_repository import SocialDatabaseService
            db = raw_request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            task_data = {
                "task_id": task_id,
                "task_name": f"视频裁剪任务 - {len(request.video_paths)}个文件",
                "task_type": "video_clip",
                "status": "pending",
                "progress": 0,
                "created_at": time.time(),
                "updated_at": time.time(),
                "params": {
                    "video_paths": request.video_paths,
                    "clip_mode": request.clip_mode,
                    "segment_count": request.segment_count,
                    "segment_duration": request.segment_duration,
                    "buffer_duration": request.buffer_duration,
                    "output_quality": request.output_quality,
                    "output_folder": request.output_folder,
                    "filename_template": request.filename_template,
                    "preserve_audio_quality": request.preserve_audio_quality,
                    "max_concurrent": request.max_concurrent,
                    "volume_sensitivity": request.volume_sensitivity,
                    "min_segment_duration": request.min_segment_duration,
                    "core_service_id": core_service_id  # 添加core服务ID
                }
            }

            # 保存任务到数据库
            await db_service.create_task(task_data)

            # 添加到后台任务队列
            background_tasks.add_task(execute_video_clip_task, task_id, request, db)

            logger.info(f"视频裁剪任务已提交到后台处理: {task_id}")

            return VideoClipResponse(
                success=True,
                error="",
                task_id=task_id,
                message=f"视频裁剪任务已提交到后台处理，任务ID: {task_id}"
            )

        # 同步处理模式
        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务进行视频裁剪")

        try:
            grpc_response = await core_client.clip_videos(
                video_paths=request.video_paths,
                clip_mode=request.clip_mode,
                segment_count=request.segment_count or 0,
                segment_duration=request.segment_duration or 0.0,
                buffer_duration=request.buffer_duration or 2.0,
                output_quality=request.output_quality or "medium",
                output_folder=request.output_folder or "",
                filename_template=request.filename_template or "",
                preserve_audio_quality=request.preserve_audio_quality if request.preserve_audio_quality is not None else True,
                max_concurrent=request.max_concurrent or 3,
                volume_sensitivity=request.volume_sensitivity or 0.5,
                min_segment_duration=request.min_segment_duration or 5.0
            )
        except Exception as e:
            logger.error(f"调用Core服务裁剪视频失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        results = []
        for result_item in grpc_response.get("results", []):
            segments = []
            for segment in result_item.get("segments", []):
                segments.append(VideoSegmentInfo(
                    file_path=segment["file_path"],
                    segment_index=segment["segment_index"],
                    start_time=segment["start_time"],
                    end_time=segment["end_time"],
                    duration=segment["duration"],
                    file_size=segment["file_size"],
                    start_volume=segment["start_volume"],
                    end_volume=segment["end_volume"]
                ))

            results.append(VideoClipResult(
                input_path=result_item["input_path"],
                success=result_item["success"],
                error_message=result_item["error_message"],
                processing_time_ms=result_item["processing_time_ms"],
                segments=segments,
                original_duration=result_item["original_duration"],
                volume_analysis_data=result_item["volume_analysis_data"]
            ))

        response = VideoClipResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            results=results,
            total_processing_time_ms=grpc_response.get("total_processing_time_ms", 0),
            successful_count=grpc_response.get("successful_count", 0),
            failed_count=grpc_response.get("failed_count", 0),
            total_count=grpc_response.get("total_count", 0),
            output_folder=grpc_response.get("output_folder", ""),
            total_output_size=grpc_response.get("total_output_size", 0),
            total_segments=grpc_response.get("total_segments", 0)
        )

        if response.success:
            logger.info(f"视频裁剪成功: 处理了 {response.total_count} 个文件，成功 {response.successful_count} 个，生成 {response.total_segments} 个片段")
        else:
            logger.error(f"视频裁剪失败: {response.error}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"视频裁剪异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"视频裁剪失败: {str(e)}")


@router.get("/clip-videos/task/{task_id}")
async def get_video_clip_task_status(task_id: str, request: Request):
    """获取视频裁剪任务状态"""
    try:
        from app.core.schemas.social_repository import SocialDatabaseService
        db = request.app.state.mongo_db
        db_service = SocialDatabaseService(db)

        # 查询任务状态
        task = await db_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        return {
            "success": True,
            "task_id": task_id,
            "status": task.get("status", "unknown"),
            "progress": task.get("progress", 0),
            "message": task.get("message", ""),
            "created_at": task.get("created_at"),
            "updated_at": task.get("updated_at"),
            "result": task.get("result")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")


# ==================== 高清处理相关API ====================

@router.post("/enhance-files", response_model=VideoEnhanceResponse)
async def enhance_files(
    request: VideoEnhanceRequest,
    background_tasks: BackgroundTasks,
    raw_request: Request,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    批量高清处理文件（支持视频和图片）

    - **file_paths**: 文件路径列表
    - **enhance_mode**: 处理模式 (ai, traditional)
    - **scale_factor**: 放大倍数 (2, 3, 4)
    - **output_quality**: 输出质量 (high, medium, low)
    - **denoise_level**: 降噪级别 (none, low, medium, high)
    - **sharpen_level**: 锐化级别 (none, low, medium, high)
    - **overwrite_original**: 是否覆盖原文件
    - **output_suffix**: 输出文件名后缀
    - **max_concurrent**: 最大并发处理数
    - **background_processing**: 是否后台处理
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到高清处理请求: {len(request.file_paths)} 个文件")

        # 验证文件路径
        for file_path in request.file_paths:
            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail=f"文件不存在: {file_path}")

        # 如果启用后台处理，创建后台任务
        if request.background_processing:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 保存任务到数据库
            try:
                from app.core.schemas.social_repository import SocialDatabaseService
                db = raw_request.app.state.mongo_db
                db_service = SocialDatabaseService(db)

                await db_service.create_task({
                    "task_id": task_id,
                    "task_type": "enhance_files",
                    "status": "pending",
                    "progress": 0,
                    "created_at": time.time(),
                    "updated_at": time.time(),
                    "params": {
                        "file_paths": request.file_paths,
                        "enhance_mode": request.enhance_mode,
                        "scale_factor": request.scale_factor,
                        "output_quality": request.output_quality,
                        "denoise_level": request.denoise_level,
                        "sharpen_level": request.sharpen_level,
                        "overwrite_original": request.overwrite_original,
                        "output_suffix": request.output_suffix,
                        "max_concurrent": request.max_concurrent,
                        "core_service_id": core_service_id
                    },
                    "message": "高清处理任务已创建"
                })

                logger.info(f"高清处理后台任务已创建: {task_id}")

                # 添加后台任务
                background_tasks.add_task(execute_video_enhance_task, task_id, request, raw_request.app.state.mongo_db)

                return VideoEnhanceResponse(
                    success=True,
                    error="",
                    task_id=task_id,
                    message="高清处理任务已创建，正在后台处理..."
                )

            except Exception as e:
                logger.error(f"创建高清处理后台任务失败: {str(e)}")
                # 降级到同步处理
                logger.warning("降级到同步高清处理")

        # 同步处理模式
        logger.info(f"开始同步高清处理")

        # 调用Core服务进行高清处理
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务进行高清处理: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务进行高清处理")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        try:
            # 调用Core服务的高清处理功能
            grpc_response = await core_client.enhance_files(
                file_paths=request.file_paths,
                enhance_mode=request.enhance_mode,
                scale_factor=request.scale_factor,
                output_quality=request.output_quality,
                denoise_level=request.denoise_level,
                sharpen_level=request.sharpen_level,
                overwrite_original=request.overwrite_original,
                output_suffix=request.output_suffix,
                max_concurrent=request.max_concurrent
            )

            logger.info(f"高清处理完成: 处理了 {grpc_response.get('total_count', 0)} 个文件，成功 {grpc_response.get('successful_count', 0)} 个")

        except Exception as e:
            logger.error(f"调用Core服务高清处理失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        # 构建响应
        return VideoEnhanceResponse(
            success=grpc_response.get("success", False),
            error=grpc_response.get("error", ""),
            results=grpc_response.get("results", []),
            total_processing_time_ms=grpc_response.get("total_processing_time_ms", 0),
            successful_count=grpc_response.get("successful_count", 0),
            failed_count=grpc_response.get("failed_count", 0),
            total_count=grpc_response.get("total_count", 0),
            output_folder=grpc_response.get("output_folder", ""),
            total_output_size=grpc_response.get("total_output_size", 0)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"高清处理异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"高清处理失败: {str(e)}")


async def execute_video_enhance_task(task_id: str, request: VideoEnhanceRequest, db):
    """后台执行高清处理任务"""
    try:
        logger.info(f"开始执行后台高清处理任务: {task_id}")

        # 获取数据库服务
        from app.core.schemas.social_repository import SocialDatabaseService

        # 创建数据库服务实例
        db_service = SocialDatabaseService(db)

        # 更新任务状态为执行中
        await db_service.update_task(task_id, {
            "status": "running",
            "progress": 0,
            "updated_at": time.time(),
            "message": "开始高清处理"
        })

        # 获取任务参数中的core_service_id
        task = await db_service.get_task(task_id)
        core_service_id = task.get("params", {}).get("core_service_id") if task else None

        # 调用Core服务进行高清处理
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"后台任务使用指定的Core服务: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("后台任务使用默认Core服务")

        if not core_client:
            raise Exception("Core服务不可用")

        grpc_response = await core_client.enhance_files(
            file_paths=request.file_paths,
            enhance_mode=request.enhance_mode,
            scale_factor=request.scale_factor,
            output_quality=request.output_quality,
            denoise_level=request.denoise_level,
            sharpen_level=request.sharpen_level,
            overwrite_original=request.overwrite_original,
            output_suffix=request.output_suffix,
            max_concurrent=request.max_concurrent
        )

        # 更新任务状态为完成
        await db_service.update_task(task_id, {
            "status": "completed",
            "progress": 100,
            "updated_at": time.time(),
            "message": "高清处理任务完成",
            "result": {
                "success": grpc_response.get("success", False),
                "error": grpc_response.get("error", ""),
                "successful_count": grpc_response.get("successful_count", 0),
                "failed_count": grpc_response.get("failed_count", 0),
                "total_processing_time_ms": grpc_response.get("total_processing_time_ms", 0)
            }
        })

        logger.info(f"后台高清处理任务完成: {task_id}")

    except Exception as e:
        logger.error(f"后台高清处理任务失败: {task_id}, {str(e)}")
        try:
            from app.core.schemas.social_repository import SocialDatabaseService
            db_service = SocialDatabaseService(db)
            await db_service.update_task(task_id, {
                "status": "failed",
                "progress": 0,
                "updated_at": time.time(),
                "message": f"高清处理任务失败: {str(e)}"
            })
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {str(update_error)}")


@router.get("/enhance-files/task/{task_id}")
async def get_video_enhance_task_status(task_id: str, request: Request):
    """查询高清处理任务状态"""
    try:
        from app.core.schemas.social_repository import SocialDatabaseService
        db = request.app.state.mongo_db
        db_service = SocialDatabaseService(db)

        # 查询任务状态
        task = await db_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        return {
            "success": True,
            "task_id": task_id,
            "status": task.get("status", "unknown"),
            "progress": task.get("progress", 0),
            "message": task.get("message", ""),
            "created_at": task.get("created_at"),
            "updated_at": task.get("updated_at"),
            "result": task.get("result")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询高清处理任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")


# AI换脸相关接口
class FaceSwapRequest(BaseModel):
    """AI换脸请求模型"""
    folder_path: str  # 文件夹路径
    target_faces: Optional[List[str]] = None  # 目标人脸列表（可选，自动检测）
    source_face: Optional[str] = None  # 源人脸（可选，自动检测）
    max_concurrent: int = 2  # 最大并发处理数
    quality: str = 'medium'  # 处理质量 (high, medium, low)
    overwrite_original: bool = False  # 是否覆盖原文件

class FaceSwapResponse(BaseModel):
    """AI换脸响应模型"""
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    results: List[Dict[str, Any]] = []
    total_processing_time_ms: int = 0
    successful_count: int = 0
    failed_count: int = 0
    total_count: int = 0
    output_folder: Optional[str] = None
    face_templates_used: Optional[Dict[str, Any]] = None

class FaceTemplatesInfoRequest(BaseModel):
    """人脸模板信息请求模型"""
    folder_path: str

class FaceTemplatesInfoResponse(BaseModel):
    """人脸模板信息响应模型"""
    success: bool
    message: str
    templates_folder: str
    target_faces: List[str] = []
    source_face: Optional[str] = None
    total_target_faces: int = 0
    has_source_face: bool = False

class DetectVideoFacesRequest(BaseModel):
    """视频人脸检测请求模型"""
    folder_path: str
    max_faces_per_video: int = 5
    sample_frames: int = 10

class DetectedFace(BaseModel):
    """检测到的人脸信息"""
    id: str
    filename: str
    path: str
    video_name: str
    video_path: str
    frame_index: int
    face_location: List[int]
    confidence: float
    encoding: List[float]

class DetectVideoFacesResponse(BaseModel):
    """视频人脸检测响应模型"""
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    detected_faces: List[DetectedFace] = []
    video_count: int = 0
    total_faces_detected: int = 0
    faces_output_dir: Optional[str] = None

class FaceSwapWithSelectionRequest(BaseModel):
    """带人脸选择的换脸请求模型"""
    folder_path: str
    selected_target_faces: List[str]  # 用户选择的要被替换的人脸ID列表
    source_face_path: str  # 源人脸图片路径
    max_concurrent: int = 2
    quality: str = 'medium'
    overwrite_original: bool = False


@router.post("/face-swap", response_model=FaceSwapResponse)
async def batch_face_swap(
    request: FaceSwapRequest,
    core_service_id: Optional[str] = Query(None, description="可选的Core服务ID")
):
    """
    批量AI换脸处理

    ## 功能说明

    对指定文件夹中的所有视频文件进行AI换脸处理。

    ## 使用方法

    1. **准备人脸模板**：
       - 在频道文件夹下创建 `face_templates` 目录
       - 放入人脸图片：
         - `source_face.jpg` (要替换成的脸)
         - `target_face_1.jpg` (要被替换的脸1)
         - `target_face_2.jpg` (要被替换的脸2)

    2. **调用接口**：
       - 指定文件夹路径
       - 系统自动检测人脸模板
       - 批量处理所有视频文件

    ## 参数说明

    - **folder_path**: 视频文件夹路径（如：H:\\PublishSystem\\youtube\\A-HK-0-1-00-88-屠妞学院）
    - **target_faces**: 目标人脸列表（可选，留空则自动检测）
    - **source_face**: 源人脸（可选，留空则自动检测）
    - **max_concurrent**: 最大并发处理数（建议2-4）
    - **quality**: 处理质量 (high, medium, low)
    - **overwrite_original**: 是否覆盖原文件（false则输出到face_swapped文件夹）
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
    try:
        logger.info(f"收到AI换脸请求: {request.folder_path}")

        # 检查文件夹是否存在
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail=f"文件夹不存在: {request.folder_path}")

        # 调用Core服务进行换脸处理
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务进行换脸: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务进行换脸")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务进行AI换脸处理")

        try:
            grpc_response = await core_client.batch_face_swap(
                folder_path=request.folder_path,
                target_faces=request.target_faces,
                source_face=request.source_face,
                max_concurrent=request.max_concurrent,
                quality=request.quality,
                overwrite_original=request.overwrite_original
            )
        except Exception as e:
            logger.error(f"调用Core服务换脸失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        logger.info(f"AI换脸处理完成: 成功 {grpc_response.get('successful_count', 0)} 个，失败 {grpc_response.get('failed_count', 0)} 个")

        return FaceSwapResponse(**grpc_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI换脸处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI换脸处理失败: {str(e)}")


@router.post("/face-templates-info", response_model=FaceTemplatesInfoResponse)
async def get_face_templates_info(
    request: FaceTemplatesInfoRequest,
    core_service_id: Optional[str] = Query(None, description="可选的Core服务ID")
):
    """
    获取文件夹中的人脸模板信息

    ## 功能说明

    检查指定文件夹中的人脸模板配置情况，帮助用户了解当前的换脸配置。

    ## 参数说明

    - **folder_path**: 文件夹路径
    - **core_service_id**: 可选的Core服务ID
    """
    try:
        logger.info(f"获取人脸模板信息: {request.folder_path}")

        # 检查文件夹是否存在
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail=f"文件夹不存在: {request.folder_path}")

        # 调用Core服务
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务获取模板信息: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务获取模板信息")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        try:
            grpc_response = await core_client.get_face_templates_info(
                folder_path=request.folder_path
            )
        except Exception as e:
            logger.error(f"调用Core服务获取模板信息失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        return FaceTemplatesInfoResponse(**grpc_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取人脸模板信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取人脸模板信息失败: {str(e)}")


@router.post("/detect-video-faces", response_model=DetectVideoFacesResponse)
async def detect_video_faces(
    request: DetectVideoFacesRequest,
    core_service_id: Optional[str] = Query(None, description="可选的Core服务ID")
):
    """
    从视频中检测人脸，供用户选择

    ## 功能说明

    自动分析指定文件夹中的视频文件，检测并提取其中的人脸，供用户选择哪些人脸需要被替换。

    ## 工作流程

    1. **视频采样**：从每个视频中采样指定数量的帧
    2. **人脸检测**：使用AI技术检测每帧中的人脸
    3. **人脸提取**：提取检测到的人脸图片
    4. **去重处理**：移除相似的重复人脸
    5. **结果返回**：返回所有检测到的不同人脸

    ## 参数说明

    - **folder_path**: 视频文件夹路径
    - **max_faces_per_video**: 每个视频最多检测的人脸数（默认5个）
    - **sample_frames**: 采样帧数（默认10帧）
    - **core_service_id**: 可选的Core服务ID

    ## 返回结果

    返回检测到的人脸列表，每个人脸包含：
    - 人脸图片文件路径
    - 来源视频信息
    - 人脸位置坐标
    - 人脸特征编码
    """
    try:
        logger.info(f"收到视频人脸检测请求: {request.folder_path}")

        # 检查文件夹是否存在
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail=f"文件夹不存在: {request.folder_path}")

        # 调用Core服务进行人脸检测
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务进行人脸检测: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务进行人脸检测")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务进行视频人脸检测")

        try:
            grpc_response = await core_client.detect_faces_from_videos(
                folder_path=request.folder_path,
                max_faces_per_video=request.max_faces_per_video,
                sample_frames=request.sample_frames
            )
        except Exception as e:
            logger.error(f"调用Core服务人脸检测失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        if grpc_response.get('success'):
            detected_count = len(grpc_response.get('detected_faces', []))
            logger.info(f"视频人脸检测完成: 检测到 {detected_count} 个不同的人脸")
        else:
            logger.warning(f"视频人脸检测失败: {grpc_response.get('error', '未知错误')}")

        return DetectVideoFacesResponse(**grpc_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"视频人脸检测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"视频人脸检测失败: {str(e)}")


@router.post("/face-swap-with-selection", response_model=FaceSwapResponse)
async def face_swap_with_selection(
    request: FaceSwapWithSelectionRequest,
    core_service_id: Optional[str] = Query(None, description="可选的Core服务ID")
):
    """
    基于用户选择的人脸进行换脸处理

    ## 功能说明

    根据用户选择的目标人脸和源人脸，对指定文件夹中的视频进行AI换脸处理。

    ## 参数说明

    - **folder_path**: 视频文件夹路径
    - **selected_target_faces**: 用户选择的要被替换的人脸ID列表
    - **source_face_path**: 源人脸图片路径（要替换成的脸）
    - **max_concurrent**: 最大并发处理数
    - **quality**: 处理质量 (high, medium, low)
    - **overwrite_original**: 是否覆盖原文件
    - **core_service_id**: 可选的Core服务ID
    """
    try:
        logger.info(f"收到基于选择的AI换脸请求: {request.folder_path}")

        # 检查文件夹是否存在
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=404, detail=f"文件夹不存在: {request.folder_path}")

        # 检查源人脸文件是否存在
        if not os.path.exists(request.source_face_path):
            raise HTTPException(status_code=404, detail=f"源人脸文件不存在: {request.source_face_path}")

        # 调用Core服务进行换脸处理
        from app.core.grpc_client import get_core_client_by_service_id

        # 根据core_service_id选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务进行换脸: {core_service_id}")
        else:
            from app.core.grpc_client import get_core_client
            core_client = get_core_client()
            logger.info("使用默认Core服务进行换脸")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        logger.info(f"调用Core服务进行基于选择的AI换脸处理")

        try:
            grpc_response = await core_client.face_swap_with_selection(
                folder_path=request.folder_path,
                selected_target_faces=request.selected_target_faces,
                source_face_path=request.source_face_path,
                max_concurrent=request.max_concurrent,
                quality=request.quality,
                overwrite_original=request.overwrite_original
            )
        except Exception as e:
            logger.error(f"调用Core服务换脸失败: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Core服务调用失败: {str(e)}")

        logger.info(f"基于选择的AI换脸处理完成: 成功 {grpc_response.get('successful_count', 0)} 个，失败 {grpc_response.get('failed_count', 0)} 个")

        return FaceSwapResponse(**grpc_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"基于选择的AI换脸处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"基于选择的AI换脸处理失败: {str(e)}")


# ==================== 人脸复用相关API ====================

class FacePresetRequest(BaseModel):
    """人脸预设请求"""
    name: str
    description: Optional[str] = ""
    source_face_path: str
    target_faces: List[Dict[str, Any]]
    folder_path: str  # 添加文件夹路径，用于按项目保存

class FacePresetResponse(BaseModel):
    """人脸预设响应"""
    id: str
    name: str
    description: str
    source_face_path: str
    target_faces: List[Dict[str, Any]]
    created_at: str
    updated_at: str

@router.post("/face-presets")
async def save_face_preset(
    request: FacePresetRequest,
    current_user = Depends(get_current_user)
):
    """保存人脸预设"""
    try:
        logger.info(f"保存人脸预设: {request.name}")

        # 生成预设ID
        preset_id = str(uuid.uuid4())

        # 构建预设数据
        preset_data = {
            "id": preset_id,
            "name": request.name,
            "description": request.description,
            "source_face_path": request.source_face_path,
            "target_faces": request.target_faces,
            "folder_path": request.folder_path,
            "created_at": time.time(),
            "updated_at": time.time(),
            "created_by": getattr(current_user, 'username', 'unknown')
        }

        # 按文件夹保存预设
        # 使用文件夹路径的哈希值作为子目录名，避免路径过长和特殊字符问题
        import hashlib
        folder_hash = hashlib.md5(request.folder_path.encode('utf-8')).hexdigest()[:8]
        presets_dir = os.path.join(os.getcwd(), "data", "face_presets", folder_hash)
        os.makedirs(presets_dir, exist_ok=True)

        # 同时保存文件夹路径映射
        mapping_file = os.path.join(os.getcwd(), "data", "face_presets", "folder_mapping.json")
        try:
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    folder_mapping = json.load(f)
            else:
                folder_mapping = {}

            folder_mapping[folder_hash] = {
                "folder_path": request.folder_path,
                "updated_at": time.time()
            }

            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(folder_mapping, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"保存文件夹映射失败: {str(e)}")

        preset_file = os.path.join(presets_dir, f"{preset_id}.json")
        with open(preset_file, 'w', encoding='utf-8') as f:
            json.dump(preset_data, f, ensure_ascii=False, indent=2)

        logger.info(f"人脸预设保存成功: {preset_file}")

        return {
            "success": True,
            "message": "人脸预设保存成功",
            "preset_id": preset_id
        }

    except Exception as e:
        logger.error(f"保存人脸预设失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存人脸预设失败: {str(e)}")

@router.get("/face-presets")
async def get_face_presets(
    folder_path: str = Query(..., description="文件夹路径"),
    current_user = Depends(get_current_user)
):
    """获取指定文件夹的人脸预设列表"""
    try:
        logger.info(f"获取人脸预设列表: {folder_path}")

        # 计算文件夹哈希
        import hashlib
        folder_hash = hashlib.md5(folder_path.encode('utf-8')).hexdigest()[:8]
        presets_dir = os.path.join(os.getcwd(), "data", "face_presets", folder_hash)

        if not os.path.exists(presets_dir):
            return {"success": True, "presets": []}

        presets = []
        for filename in os.listdir(presets_dir):
            if filename.endswith('.json'):
                preset_file = os.path.join(presets_dir, filename)
                try:
                    with open(preset_file, 'r', encoding='utf-8') as f:
                        preset_data = json.load(f)
                    # 只返回属于当前文件夹的预设
                    if preset_data.get('folder_path') == folder_path:
                        presets.append(preset_data)
                except Exception as e:
                    logger.warning(f"读取预设文件失败: {preset_file}, {str(e)}")

        # 按创建时间排序
        presets.sort(key=lambda x: x.get('created_at', 0), reverse=True)

        return {
            "success": True,
            "presets": presets
        }

    except Exception as e:
        logger.error(f"获取人脸预设列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取人脸预设列表失败: {str(e)}")

@router.get("/face-presets/{preset_id}")
async def get_face_preset(
    preset_id: str,
    folder_path: str = Query(..., description="文件夹路径"),
    current_user = Depends(get_current_user)
):
    """获取特定人脸预设"""
    try:
        logger.info(f"获取人脸预设: {preset_id}")

        # 计算文件夹哈希
        import hashlib
        folder_hash = hashlib.md5(folder_path.encode('utf-8')).hexdigest()[:8]
        preset_file = os.path.join(os.getcwd(), "data", "face_presets", folder_hash, f"{preset_id}.json")

        if not os.path.exists(preset_file):
            raise HTTPException(status_code=404, detail="人脸预设不存在")

        with open(preset_file, 'r', encoding='utf-8') as f:
            preset_data = json.load(f)

        # 验证预设属于当前文件夹
        if preset_data.get('folder_path') != folder_path:
            raise HTTPException(status_code=404, detail="人脸预设不存在")

        return {
            "success": True,
            "preset": preset_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取人脸预设失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取人脸预设失败: {str(e)}")

@router.delete("/face-presets/{preset_id}")
async def delete_face_preset(
    preset_id: str,
    folder_path: str = Query(..., description="文件夹路径"),
    current_user = Depends(get_current_user)
):
    """删除人脸预设"""
    try:
        logger.info(f"删除人脸预设: {preset_id}")

        # 计算文件夹哈希
        import hashlib
        folder_hash = hashlib.md5(folder_path.encode('utf-8')).hexdigest()[:8]
        preset_file = os.path.join(os.getcwd(), "data", "face_presets", folder_hash, f"{preset_id}.json")

        if not os.path.exists(preset_file):
            raise HTTPException(status_code=404, detail="人脸预设不存在")

        # 验证预设属于当前文件夹
        with open(preset_file, 'r', encoding='utf-8') as f:
            preset_data = json.load(f)

        if preset_data.get('folder_path') != folder_path:
            raise HTTPException(status_code=404, detail="人脸预设不存在")

        os.remove(preset_file)

        return {
            "success": True,
            "message": "人脸预设删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除人脸预设失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除人脸预设失败: {str(e)}")
