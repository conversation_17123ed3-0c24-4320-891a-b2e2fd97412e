# ThunderHub 前端环境搭建指南

## 依赖安装

### 1. 基础依赖安装

```bash
cd frontend
npm install
```

### 2. 安装 Sass 预处理器（可选）

如果你想使用 SCSS 语法编写样式，需要安装 sass 依赖：

```bash
npm install -D sass
```

或者使用 yarn：

```bash
yarn add -D sass
```

### 3. 验证安装

安装完成后，可以通过以下命令验证：

```bash
npm list sass
```

## 样式开发说明

### 当前状态

目前视频编辑器组件已经将样式从 SCSS 改为普通 CSS，以避免依赖问题。所有组件都可以正常运行。

### 如果要使用 SCSS

如果你安装了 sass 依赖，可以将组件的样式标签改回：

```vue
<!-- 从 -->
<style scoped>

<!-- 改为 -->
<style scoped lang="scss">
```

然后就可以使用 SCSS 的嵌套语法：

```scss
.parent {
  color: red;
  
  .child {
    color: blue;
    
    &:hover {
      color: green;
    }
  }
}
```

### SCSS 语法优势

1. **嵌套语法**: 更清晰的层级结构
2. **变量支持**: 可以定义和复用变量
3. **混入 (Mixins)**: 可复用的样式块
4. **函数**: 动态计算样式值

## 启动开发服务器

```bash
cd frontend
npm run dev
```

## 构建生产版本

```bash
cd frontend
npm run build
```

## 常见问题

### 1. Sass 依赖问题

**错误信息**:
```
Preprocessor dependency "sass" not found. Did you install it?
```

**解决方案**:
```bash
npm install -D sass
```

### 2. 样式不生效

**可能原因**:
- CSS 选择器优先级问题
- 样式被其他组件覆盖
- 深度选择器使用不当

**解决方案**:
- 使用 `:deep()` 选择器穿透组件边界
- 增加选择器特异性
- 检查样式加载顺序

### 3. 响应式样式问题

**建议**:
- 使用相对单位 (rem, em, %)
- 合理设置断点
- 测试不同屏幕尺寸

## 样式规范

### 1. 命名规范

- 使用 kebab-case 命名类名
- 组件样式使用 scoped
- 全局样式谨慎使用

```css
/* 推荐 */
.video-editor-container { }
.timeline-track { }
.media-library-item { }

/* 不推荐 */
.videoEditorContainer { }
.TimelineTrack { }
.media_library_item { }
```

### 2. 组织结构

```scss
// 1. 组件根元素
.component-root {
  // 基础样式
}

// 2. 子元素
.component-root .child-element {
  // 子元素样式
}

// 3. 状态样式
.component-root.active {
  // 激活状态
}

// 4. 响应式样式
@media (max-width: 768px) {
  .component-root {
    // 移动端样式
  }
}
```

### 3. 颜色变量

建议在项目中定义统一的颜色变量：

```scss
// colors.scss
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

$bg-dark: #1a1a1a;
$bg-medium: #2a2a2a;
$bg-light: #3a3a3a;

$text-primary: #ffffff;
$text-secondary: #cccccc;
$text-disabled: #999999;
```

## 开发工具推荐

### 1. VS Code 插件

- **Vetur**: Vue 语法高亮和智能提示
- **Sass**: SCSS 语法支持
- **Auto Rename Tag**: 自动重命名标签
- **Bracket Pair Colorizer**: 括号配对着色

### 2. 浏览器开发工具

- **Vue DevTools**: Vue 组件调试
- **Chrome DevTools**: 样式调试和性能分析

## 性能优化建议

### 1. 样式优化

- 避免深层嵌套选择器
- 使用 CSS 变量减少重复
- 合理使用 scoped 样式

### 2. 组件优化

- 按需加载组件
- 使用 v-show 而不是 v-if（频繁切换）
- 合理使用 computed 和 watch

### 3. 构建优化

- 启用 CSS 压缩
- 使用 Tree Shaking
- 代码分割和懒加载

## 总结

ThunderHub 前端项目已经配置了完整的开发环境，支持现代化的前端开发工作流。通过合理的样式组织和规范，可以高效地开发和维护复杂的用户界面。
