#!/usr/bin/env python3
"""
测试字幕文件重命名功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.api.v1.filesystem import rename_associated_subtitle_files

def create_test_files(test_dir):
    """创建测试文件"""
    # 创建视频文件（空文件用于测试）
    video_file = os.path.join(test_dir, "测试视频.mp4")
    with open(video_file, 'w') as f:
        f.write("")
    
    # 创建字幕文件
    subtitle_files = [
        os.path.join(test_dir, "测试视频.srt"),
        os.path.join(test_dir, "测试视频.vtt"),
    ]
    
    for subtitle_file in subtitle_files:
        with open(subtitle_file, 'w', encoding='utf-8') as f:
            f.write("1\n00:00:00,000 --> 00:00:05,000\n测试字幕内容\n\n")
    
    # 在subtitles子目录中创建字幕文件
    subtitles_dir = os.path.join(test_dir, "subtitles")
    os.makedirs(subtitles_dir, exist_ok=True)
    
    subtitle_in_subdir = os.path.join(subtitles_dir, "测试视频.txt")
    with open(subtitle_in_subdir, 'w', encoding='utf-8') as f:
        f.write("测试字幕文本内容")
    
    return video_file, subtitle_files + [subtitle_in_subdir]

def test_subtitle_rename():
    """测试字幕文件重命名功能"""
    print("🧪 开始测试字幕文件重命名功能...")
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 测试目录: {temp_dir}")
        
        # 创建测试文件
        video_file, subtitle_files = create_test_files(temp_dir)
        
        print("📄 创建的测试文件:")
        for file_path in [video_file] + subtitle_files:
            print(f"  - {os.path.basename(file_path)}")
        
        # 测试重命名功能
        errors = []
        old_filename = "测试视频.mp4"
        new_filename_without_ext = "新的视频名称"
        
        print(f"\n🔄 执行重命名: {old_filename} -> {new_filename_without_ext}")
        
        renamed_count = rename_associated_subtitle_files(
            temp_dir, old_filename, new_filename_without_ext, errors
        )
        
        print(f"✅ 重命名了 {renamed_count} 个字幕文件")
        
        if errors:
            print("❌ 重命名过程中的错误:")
            for error in errors:
                print(f"  - {error}")
        
        # 检查重命名结果
        print("\n📋 重命名后的文件:")
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, temp_dir)
                print(f"  - {relative_path}")
        
        # 验证预期的文件是否存在
        expected_files = [
            "新的视频名称.srt",
            "新的视频名称.vtt",
            os.path.join("subtitles", "新的视频名称.txt")
        ]
        
        print("\n🔍 验证预期文件:")
        all_success = True
        for expected_file in expected_files:
            expected_path = os.path.join(temp_dir, expected_file)
            if os.path.exists(expected_path):
                print(f"  ✅ {expected_file}")
            else:
                print(f"  ❌ {expected_file} (未找到)")
                all_success = False
        
        if all_success:
            print("\n🎉 所有测试通过！字幕文件重命名功能正常工作。")
        else:
            print("\n❌ 部分测试失败，请检查功能实现。")
        
        return all_success

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 测试没有字幕文件的情况
        video_file = os.path.join(temp_dir, "无字幕视频.mp4")
        with open(video_file, 'w') as f:
            f.write("")
        
        errors = []
        renamed_count = rename_associated_subtitle_files(
            temp_dir, "无字幕视频.mp4", "重命名的无字幕视频", errors
        )
        
        print(f"📄 无字幕文件测试: 重命名了 {renamed_count} 个字幕文件 (预期: 0)")
        
        # 测试文件名冲突的情况
        os.makedirs(os.path.join(temp_dir, "subtitles"), exist_ok=True)
        
        # 创建原始字幕文件
        with open(os.path.join(temp_dir, "原始视频.srt"), 'w') as f:
            f.write("原始字幕")
        
        # 创建目标文件名已存在的字幕文件
        with open(os.path.join(temp_dir, "目标视频.srt"), 'w') as f:
            f.write("已存在的字幕")
        
        errors = []
        renamed_count = rename_associated_subtitle_files(
            temp_dir, "原始视频.mp4", "目标视频", errors
        )
        
        print(f"📄 文件冲突测试: 重命名了 {renamed_count} 个字幕文件，错误数: {len(errors)}")
        if errors:
            print(f"  错误信息: {errors[0]}")

if __name__ == "__main__":
    print("=" * 60)
    print("🎬 ThunderHub 字幕文件重命名功能测试")
    print("=" * 60)
    
    try:
        # 基本功能测试
        success = test_subtitle_rename()
        
        # 边界情况测试
        test_edge_cases()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 测试完成！字幕文件重命名功能可以正常使用。")
        else:
            print("❌ 测试发现问题，请检查实现。")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
