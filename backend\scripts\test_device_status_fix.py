#!/usr/bin/env python3
"""
测试设备状态查询修复
验证Backend服务能否正确读取Core服务写入的设备状态
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.redis_sync_service import RedisSyncService
from app.core.database import get_database
import redis.asyncio as redis
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_device_status_query():
    """测试设备状态查询功能"""
    print("🔍 测试设备状态查询修复...")
    
    try:
        # 初始化Redis连接
        redis_client = redis.from_url("redis://localhost:6379/0", decode_responses=True)
        
        # 初始化MongoDB连接
        db_service = await get_database()
        mongo_db = db_service.db
        
        # 创建RedisSyncService实例
        redis_sync_service = RedisSyncService(
            redis_url="redis://localhost:6379/0",
            mongo_url="mongodb://localhost:27017",
            db_name="thunderhub"
        )
        await redis_sync_service.start()
        
        print("\n📋 测试环境准备完成")
        
        # 测试设备ID
        test_device_id = "33"
        
        print(f"\n🔍 测试设备{test_device_id}的状态查询...")
        
        # 1. 直接测试新的辅助方法
        print("\n1️⃣ 测试 _get_device_status_from_redis 方法:")
        status, source_key = await redis_sync_service._get_device_status_from_redis(test_device_id)
        if status:
            print(f"   ✅ 找到设备状态: {status} (来源: {source_key})")
        else:
            print(f"   ❌ 未找到设备状态")
        
        # 2. 测试完整的设备可用性检查
        print("\n2️⃣ 测试 _is_device_available 方法:")
        is_available = await redis_sync_service._is_device_available(test_device_id)
        print(f"   设备{test_device_id}可用性: {is_available}")
        
        # 3. 手动检查Redis中的键
        print("\n3️⃣ 手动检查Redis中的设备状态键:")
        
        # 检查新格式的键
        pattern = f"device:*:{test_device_id}:state"
        cursor = 0
        found_keys = []
        while True:
            cursor, keys = await redis_client.scan(cursor, match=pattern, count=100)
            found_keys.extend(keys)
            if cursor == 0:
                break
        
        if found_keys:
            print(f"   找到新格式键: {found_keys}")
            for key in found_keys:
                value = await redis_client.get(key)
                print(f"   键 {key} 的值: {value}")
        else:
            print(f"   未找到新格式键 (模式: {pattern})")
        
        # 检查旧格式的键
        old_key = f"device:{test_device_id}:status"
        old_value = await redis_client.get(old_key)
        if old_value:
            print(f"   找到旧格式键 {old_key}: {old_value}")
        else:
            print(f"   未找到旧格式键: {old_key}")
        
        # 4. 检查MongoDB中的设备状态
        print("\n4️⃣ 检查MongoDB中的设备状态:")
        device = mongo_db.devices.find_one({"device_id": test_device_id})
        if device:
            print(f"   MongoDB中设备{test_device_id}状态: {device.get('status', 'unknown')}")
            print(f"   更新时间: {device.get('updated_at', 'unknown')}")
        else:
            print(f"   MongoDB中未找到设备{test_device_id}")
        
        # 5. 模拟Core服务写入设备状态（用于测试）
        print("\n5️⃣ 模拟Core服务写入设备状态:")
        test_core_id = "test_core"
        test_status_data = {
            "status": "running",
            "device_id": test_device_id,
            "core_id": test_core_id,
            "timestamp": time.time(),
            "updated_at": datetime.now().isoformat()
        }
        
        # 写入新格式的键
        new_key = f"device:{test_core_id}:{test_device_id}:state"
        await redis_client.set(new_key, json.dumps(test_status_data), ex=3600)
        print(f"   写入测试数据到键: {new_key}")
        
        # 再次测试设备状态查询
        print("\n6️⃣ 重新测试设备状态查询:")
        status, source_key = await redis_sync_service._get_device_status_from_redis(test_device_id)
        if status:
            print(f"   ✅ 找到设备状态: {status} (来源: {source_key})")
        else:
            print(f"   ❌ 仍未找到设备状态")
        
        is_available = await redis_sync_service._is_device_available(test_device_id)
        print(f"   设备{test_device_id}可用性: {is_available}")
        
        # 清理测试数据
        await redis_client.delete(new_key)
        print(f"\n🧹 清理测试数据: {new_key}")
        
        print("\n✅ 测试完成!")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        print(f"\n❌ 测试失败: {str(e)}")
    
    finally:
        # 关闭连接
        try:
            await redis_client.close()
            if 'redis_sync_service' in locals():
                await redis_sync_service.stop()
        except Exception as e:
            logger.error(f"关闭连接失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_device_status_query())
