<template>
  <div class="workflow-demo">
    <div class="demo-header">
      <h1>🔧 工作流步骤展示演示</h1>
      <p>这个页面演示了工作流步骤的详细展示、状态跟踪和断点续传功能</p>
    </div>

    <el-card class="demo-controls">
      <template #header>
        <span>演示控制</span>
      </template>
      
      <div class="control-buttons">
        <el-button type="primary" @click="createDemoTask" :loading="creating">
          创建演示任务
        </el-button>
        <el-button type="success" @click="startDemoWorkflow" :disabled="!demoTaskId" :loading="starting">
          启动工作流
        </el-button>
        <el-button type="warning" @click="simulateError" :disabled="!demoTaskId">
          模拟错误
        </el-button>
        <el-button type="info" @click="resetDemo" :loading="resetting">
          重置演示
        </el-button>
      </div>
      
      <div class="demo-info" v-if="demoTaskId">
        <el-alert 
          title="演示任务已创建" 
          type="success" 
          :closable="false"
          show-icon
        >
          <template #default>
            <p>任务ID: <code>{{ demoTaskId }}</code></p>
            <p>您可以在下方查看工作流的详细执行步骤</p>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 工作流步骤展示 -->
    <el-card class="workflow-display" v-if="demoTaskId">
      <template #header>
        <span>工作流步骤详情</span>
      </template>
      
      <WorkflowSteps 
        :task-id="demoTaskId"
        :show-controls="true"
        :auto-refresh="true"
        @workflow-updated="handleWorkflowUpdated"
        @step-completed="handleStepCompleted"
        @workflow-completed="handleWorkflowCompleted"
        @workflow-failed="handleWorkflowFailed"
      />
    </el-card>

    <!-- 功能说明 -->
    <el-card class="feature-description">
      <template #header>
        <span>功能特性</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="feature-item">
            <el-icon class="feature-icon" color="#409eff"><View /></el-icon>
            <h3>步骤可视化</h3>
            <p>清晰展示每个工作流步骤的执行状态、进度和详细信息</p>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="feature-item">
            <el-icon class="feature-icon" color="#67c23a"><Refresh /></el-icon>
            <h3>断点续传</h3>
            <p>支持从任意步骤重新开始执行，失败后可以从断点继续</p>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="feature-item">
            <el-icon class="feature-icon" color="#e6a23c"><Setting /></el-icon>
            <h3>灵活控制</h3>
            <p>支持暂停、继续、重试、跳过等多种工作流控制操作</p>
          </div>
        </el-col>
      </el-row>
      
      <el-divider />
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="feature-item">
            <el-icon class="feature-icon" color="#f56c6c"><Document /></el-icon>
            <h3>详细日志</h3>
            <p>每个步骤都有详细的执行日志，便于问题排查和调试</p>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="feature-item">
            <el-icon class="feature-icon" color="#909399"><Timer /></el-icon>
            <h3>实时监控</h3>
            <p>实时更新步骤状态和进度，提供准确的执行时间统计</p>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="feature-item">
            <el-icon class="feature-icon" color="#606266"><DataAnalysis /></el-icon>
            <h3>状态持久化</h3>
            <p>工作流状态自动保存，系统重启后可以恢复执行</p>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  View, 
  Refresh, 
  Setting, 
  Document, 
  Timer, 
  DataAnalysis 
} from '@element-plus/icons-vue'
import WorkflowSteps from '@/components/WorkflowSteps.vue'

// 响应式数据
const demoTaskId = ref<string>('')
const creating = ref(false)
const starting = ref(false)
const resetting = ref(false)

// 方法
const createDemoTask = async () => {
  creating.value = true
  try {
    // 模拟创建演示任务
    const taskId = `demo_task_${Date.now()}`
    
    // 这里应该调用实际的API创建任务
    // const response = await createTask({
    //   task_type: 'demo_workflow',
    //   workflow_name: '演示视频上传工作流',
    //   ...
    // })
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    demoTaskId.value = taskId
    ElMessage.success('演示任务创建成功')
    
    // 创建模拟的工作流上下文
    await createMockWorkflowContext(taskId)
    
  } catch (error) {
    console.error('创建演示任务失败:', error)
    ElMessage.error('创建演示任务失败')
  } finally {
    creating.value = false
  }
}

const startDemoWorkflow = async () => {
  starting.value = true
  try {
    // 这里应该调用实际的API启动工作流
    // await startTask(demoTaskId.value)
    
    // 模拟启动工作流
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('演示工作流已启动')
    
    // 模拟工作流步骤执行
    simulateWorkflowExecution()
    
  } catch (error) {
    console.error('启动工作流失败:', error)
    ElMessage.error('启动工作流失败')
  } finally {
    starting.value = false
  }
}

const simulateError = () => {
  ElMessage.warning('模拟工作流执行错误')
  // 这里可以模拟某个步骤失败的情况
}

const resetDemo = async () => {
  resetting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    demoTaskId.value = ''
    ElMessage.success('演示已重置')
  } finally {
    resetting.value = false
  }
}

// 模拟创建工作流上下文
const createMockWorkflowContext = async (taskId: string) => {
  // 这里应该调用后端API保存工作流上下文
  console.log('创建模拟工作流上下文:', taskId)
}

// 模拟工作流执行
const simulateWorkflowExecution = () => {
  // 这里可以模拟工作流步骤的执行过程
  console.log('开始模拟工作流执行')
}

// 工作流事件处理
const handleWorkflowUpdated = (context: any) => {
  console.log('工作流状态更新:', context)
}

const handleStepCompleted = (stepId: string) => {
  console.log('步骤完成:', stepId)
  ElMessage.success(`步骤 ${stepId} 执行完成`)
}

const handleWorkflowCompleted = () => {
  console.log('工作流完成')
  ElMessage.success('🎉 演示工作流执行完成！')
}

const handleWorkflowFailed = (error: string) => {
  console.log('工作流失败:', error)
  ElMessage.error(`工作流执行失败: ${error}`)
}
</script>

<style scoped>
.workflow-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.demo-header p {
  color: #606266;
  font-size: 16px;
}

.demo-controls {
  margin-bottom: 30px;
}

.control-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.demo-info {
  margin-top: 20px;
}

.demo-info code {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.workflow-display {
  margin-bottom: 30px;
}

.feature-description {
  margin-bottom: 30px;
}

.feature-item {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.feature-item h3 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 18px;
}

.feature-item p {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}
</style>
