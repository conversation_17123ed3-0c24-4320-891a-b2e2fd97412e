"""
工作流状态管理服务
负责工作流执行状态的持久化存储和管理
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from bson import ObjectId

from app.core.schemas.content_models import WorkflowContext, WorkflowStep

logger = logging.getLogger(__name__)

class WorkflowStateService:
    """工作流状态管理服务"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.collection = db.workflow_states
        
    async def save_workflow_context(self, task_id: str, context: WorkflowContext) -> bool:
        """保存工作流上下文
        
        Args:
            task_id: 任务ID
            context: 工作流上下文
            
        Returns:
            bool: 是否保存成功
        """
        try:
            context.updated_at = datetime.now()
            
            # 转换为字典格式
            context_dict = context.dict()
            context_dict['task_id'] = task_id
            
            # 使用upsert操作
            result = await self.collection.update_one(
                {"task_id": task_id},
                {"$set": context_dict},
                upsert=True
            )
            
            logger.info(f"保存工作流上下文成功: task_id={task_id}, workflow_id={context.workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存工作流上下文失败: task_id={task_id}, error={str(e)}")
            return False
    
    async def get_workflow_context(self, task_id: str) -> Optional[WorkflowContext]:
        """获取工作流上下文
        
        Args:
            task_id: 任务ID
            
        Returns:
            WorkflowContext: 工作流上下文，如果不存在返回None
        """
        try:
            doc = await self.collection.find_one({"task_id": task_id})
            if not doc:
                return None
                
            # 移除MongoDB的_id字段
            doc.pop('_id', None)
            doc.pop('task_id', None)
            
            return WorkflowContext(**doc)
            
        except Exception as e:
            logger.error(f"获取工作流上下文失败: task_id={task_id}, error={str(e)}")
            return None
    
    async def update_step_status(self, task_id: str, step_id: str, status: str, 
                               progress: int = None, error_message: str = None) -> bool:
        """更新步骤状态
        
        Args:
            task_id: 任务ID
            step_id: 步骤ID
            status: 新状态
            progress: 进度
            error_message: 错误信息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            update_data = {
                "steps.$.status": status,
                "updated_at": datetime.now()
            }
            
            if progress is not None:
                update_data["steps.$.progress"] = progress
                
            if error_message:
                update_data["steps.$.error_message"] = error_message
                
            if status == "running":
                update_data["steps.$.start_time"] = datetime.now()
            elif status in ["completed", "failed", "skipped"]:
                update_data["steps.$.end_time"] = datetime.now()
                
            result = await self.collection.update_one(
                {"task_id": task_id, "steps.id": step_id},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                logger.info(f"更新步骤状态成功: task_id={task_id}, step_id={step_id}, status={status}")
                return True
            else:
                logger.warning(f"未找到要更新的步骤: task_id={task_id}, step_id={step_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新步骤状态失败: task_id={task_id}, step_id={step_id}, error={str(e)}")
            return False
    
    async def update_current_step(self, task_id: str, step_index: int) -> bool:
        """更新当前步骤索引
        
        Args:
            task_id: 任务ID
            step_index: 步骤索引
            
        Returns:
            bool: 是否更新成功
        """
        try:
            result = await self.collection.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "current_step_index": step_index,
                        "updated_at": datetime.now()
                    }
                }
            )
            
            if result.modified_count > 0:
                logger.info(f"更新当前步骤成功: task_id={task_id}, step_index={step_index}")
                return True
            else:
                logger.warning(f"未找到要更新的工作流: task_id={task_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新当前步骤失败: task_id={task_id}, error={str(e)}")
            return False
    
    async def add_step_log(self, task_id: str, step_id: str, log_message: str) -> bool:
        """添加步骤日志
        
        Args:
            task_id: 任务ID
            step_id: 步骤ID
            log_message: 日志消息
            
        Returns:
            bool: 是否添加成功
        """
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {log_message}"
            
            result = await self.collection.update_one(
                {"task_id": task_id, "steps.id": step_id},
                {
                    "$push": {"steps.$.logs": log_entry},
                    "$set": {"updated_at": datetime.now()}
                }
            )
            
            if result.modified_count > 0:
                logger.debug(f"添加步骤日志成功: task_id={task_id}, step_id={step_id}")
                return True
            else:
                logger.warning(f"未找到要添加日志的步骤: task_id={task_id}, step_id={step_id}")
                return False
                
        except Exception as e:
            logger.error(f"添加步骤日志失败: task_id={task_id}, step_id={step_id}, error={str(e)}")
            return False
    
    async def increment_step_retry(self, task_id: str, step_id: str) -> bool:
        """增加步骤重试次数
        
        Args:
            task_id: 任务ID
            step_id: 步骤ID
            
        Returns:
            bool: 是否更新成功
        """
        try:
            result = await self.collection.update_one(
                {"task_id": task_id, "steps.id": step_id},
                {
                    "$inc": {"steps.$.retry_count": 1},
                    "$set": {"updated_at": datetime.now()}
                }
            )
            
            if result.modified_count > 0:
                logger.info(f"增加步骤重试次数成功: task_id={task_id}, step_id={step_id}")
                return True
            else:
                logger.warning(f"未找到要更新的步骤: task_id={task_id}, step_id={step_id}")
                return False
                
        except Exception as e:
            logger.error(f"增加步骤重试次数失败: task_id={task_id}, step_id={step_id}, error={str(e)}")
            return False
    
    async def save_checkpoint(self, task_id: str, checkpoint_data: Dict[str, Any]) -> bool:
        """保存检查点数据
        
        Args:
            task_id: 任务ID
            checkpoint_data: 检查点数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            result = await self.collection.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "checkpoint_data": checkpoint_data,
                        "updated_at": datetime.now()
                    }
                }
            )
            
            if result.modified_count > 0:
                logger.info(f"保存检查点数据成功: task_id={task_id}")
                return True
            else:
                logger.warning(f"未找到要更新的工作流: task_id={task_id}")
                return False
                
        except Exception as e:
            logger.error(f"保存检查点数据失败: task_id={task_id}, error={str(e)}")
            return False
    
    async def set_resume_point(self, task_id: str, step_index: int) -> bool:
        """设置恢复点
        
        Args:
            task_id: 任务ID
            step_index: 恢复步骤索引
            
        Returns:
            bool: 是否设置成功
        """
        try:
            result = await self.collection.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "resume_from_step": step_index,
                        "can_resume": True,
                        "updated_at": datetime.now()
                    }
                }
            )
            
            if result.modified_count > 0:
                logger.info(f"设置恢复点成功: task_id={task_id}, step_index={step_index}")
                return True
            else:
                logger.warning(f"未找到要更新的工作流: task_id={task_id}")
                return False
                
        except Exception as e:
            logger.error(f"设置恢复点失败: task_id={task_id}, error={str(e)}")
            return False
    
    async def delete_workflow_context(self, task_id: str) -> bool:
        """删除工作流上下文
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            result = await self.collection.delete_one({"task_id": task_id})
            
            if result.deleted_count > 0:
                logger.info(f"删除工作流上下文成功: task_id={task_id}")
                return True
            else:
                logger.warning(f"未找到要删除的工作流上下文: task_id={task_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除工作流上下文失败: task_id={task_id}, error={str(e)}")
            return False
    
    async def get_workflow_statistics(self, workflow_id: str = None) -> Dict[str, Any]:
        """获取工作流统计信息
        
        Args:
            workflow_id: 工作流ID，如果为None则统计所有工作流
            
        Returns:
            Dict: 统计信息
        """
        try:
            match_filter = {}
            if workflow_id:
                match_filter["workflow_id"] = workflow_id
                
            pipeline = [
                {"$match": match_filter},
                {
                    "$group": {
                        "_id": "$workflow_id",
                        "total_executions": {"$sum": 1},
                        "avg_steps": {"$avg": "$total_steps"},
                        "success_rate": {
                            "$avg": {
                                "$cond": [
                                    {"$eq": [{"$size": {"$filter": {"input": "$steps", "cond": {"$eq": ["$$this.status", "completed"]}}}}, "$total_steps"]},
                                    1, 0
                                ]
                            }
                        }
                    }
                }
            ]
            
            cursor = self.collection.aggregate(pipeline)
            results = await cursor.to_list(length=None)
            
            return {
                "workflows": results,
                "total_workflows": len(results)
            }
            
        except Exception as e:
            logger.error(f"获取工作流统计信息失败: error={str(e)}")
            return {"workflows": [], "total_workflows": 0}
