#!/usr/bin/env python3
"""
测试设备分配功能
验证 RedisSyncService 中的设备分配逻辑是否正常工作
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_device_allocation():
    """测试设备分配功能"""
    
    try:
        # 导入必要的模块
        from app.services.redis_sync_service import RedisSyncService
        from pymongo import MongoClient
        import redis.asyncio as redis
        
        # 配置
        redis_url = "redis://***************:6379/1"
        mongo_url = "mongodb://localhost:27017"
        db_name = "thunderhub"
        
        print("🔍 设备分配功能测试")
        print("=" * 50)
        
        # 创建MongoDB连接
        mongo_client = MongoClient(mongo_url)
        mongo_db = mongo_client[db_name]
        
        # 创建Redis同步服务实例
        redis_sync_service = RedisSyncService(
            redis_url=redis_url,
            socketio=None,  # 测试时不需要socketio
            mongo_db=mongo_db
        )
        
        # 启动服务
        await redis_sync_service.start()
        print("✅ Redis同步服务已启动")
        
        # 测试数据
        test_task = {
            "task_id": f"test_task_{int(datetime.now().timestamp())}",
            "account_id": "test_account_123",
            "platform_id": "test_platform_456",
            "parent_task_id": "test_parent_task"
        }
        
        print(f"\n📋 测试任务数据:")
        print(f"  - 任务ID: {test_task['task_id']}")
        print(f"  - 账号ID: {test_task['account_id']}")
        print(f"  - 平台ID: {test_task['platform_id']}")
        
        # 测试设备可用性检查
        print(f"\n🔍 测试设备可用性检查...")
        
        # 查询一些设备进行测试
        devices = list(mongo_db.devices.find().limit(3))
        if devices:
            for device in devices:
                device_id = device.get("device_id")
                if device_id:
                    is_available = await redis_sync_service._is_device_available(device_id)
                    status = device.get("status", "unknown")
                    print(f"  - 设备 {device_id}: 状态={status}, 可用={is_available}")
        else:
            print("  ⚠️  数据库中没有找到设备数据")
        
        # 测试设备分配
        print(f"\n🎯 测试设备分配...")
        allocated_device = await redis_sync_service._allocate_device_for_task(test_task)
        
        if allocated_device:
            print(f"✅ 成功分配设备: {allocated_device}")
            
            # 测试设备占用标记
            print(f"🔒 测试设备占用标记...")
            await redis_sync_service._mark_device_occupied(allocated_device, test_task['task_id'])
            
            # 再次检查设备可用性
            is_available_after = await redis_sync_service._is_device_available(allocated_device)
            print(f"设备 {allocated_device} 占用后可用性: {is_available_after}")
            
            # 测试设备锁释放
            print(f"🔓 测试设备锁释放...")
            await redis_sync_service._release_device_lock(allocated_device, test_task['task_id'])
            
            # 再次检查设备可用性
            is_available_final = await redis_sync_service._is_device_available(allocated_device)
            print(f"设备 {allocated_device} 释放后可用性: {is_available_final}")
            
        else:
            print("❌ 设备分配失败")
        
        # 测试查找可用设备
        print(f"\n🔍 测试查找可用设备...")
        available_device = await redis_sync_service._find_available_device()
        if available_device:
            print(f"✅ 找到可用设备: {available_device}")
        else:
            print("❌ 没有找到可用设备")
        
        # 关闭服务
        await redis_sync_service.stop()
        mongo_client.close()
        
        print(f"\n📊 测试总结:")
        print(f"  - 设备分配测试: {'✅ 通过' if allocated_device else '❌ 失败'}")
        print(f"  - 设备查找测试: {'✅ 通过' if available_device else '❌ 失败'}")
        print(f"✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_device_methods_only():
    """仅测试设备相关方法（不需要完整的服务启动）"""
    
    try:
        from app.services.redis_sync_service import RedisSyncService
        from pymongo import MongoClient
        
        # 配置
        redis_url = "redis://***************:6379/1"
        mongo_url = "mongodb://localhost:27017"
        db_name = "thunderhub"
        
        print("🔧 设备方法单元测试")
        print("=" * 50)
        
        # 创建MongoDB连接
        mongo_client = MongoClient(mongo_url)
        mongo_db = mongo_client[db_name]
        
        # 创建Redis同步服务实例（不启动完整服务）
        redis_sync_service = RedisSyncService(
            redis_url=redis_url,
            socketio=None,
            mongo_db=mongo_db
        )
        
        # 手动初始化Redis客户端
        import redis.asyncio as aioredis
        redis_sync_service.redis_client = aioredis.Redis.from_url(redis_url)
        
        # 测试Redis连接
        await redis_sync_service.redis_client.ping()
        print("✅ Redis连接成功")
        
        # 测试各个方法
        print(f"\n🔍 测试 _find_available_device 方法...")
        device = await redis_sync_service._find_available_device()
        print(f"结果: {device}")
        
        if device:
            print(f"\n🔍 测试 _is_device_available 方法...")
            is_available = await redis_sync_service._is_device_available(device)
            print(f"设备 {device} 可用性: {is_available}")
            
            print(f"\n🔒 测试 _mark_device_occupied 方法...")
            await redis_sync_service._mark_device_occupied(device, "test_task_123")
            
            print(f"\n🔍 再次测试设备可用性...")
            is_available_after = await redis_sync_service._is_device_available(device)
            print(f"设备 {device} 占用后可用性: {is_available_after}")
            
            print(f"\n🔓 测试 _release_device_lock 方法...")
            await redis_sync_service._release_device_lock(device, "test_task_123")
            
            print(f"\n🔍 最终测试设备可用性...")
            is_available_final = await redis_sync_service._is_device_available(device)
            print(f"设备 {device} 释放后可用性: {is_available_final}")
        
        # 关闭连接
        await redis_sync_service.redis_client.close()
        mongo_client.close()
        
        print(f"\n✅ 方法测试完成")
        
    except Exception as e:
        print(f"❌ 方法测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("选择测试模式:")
    print("1. 完整设备分配测试（需要启动完整服务）")
    print("2. 设备方法单元测试（仅测试方法）")
    
    try:
        choice = input("请选择 (1 或 2): ").strip()
        
        if choice == "1":
            await test_device_allocation()
        elif choice == "2":
            await test_device_methods_only()
        else:
            print("无效选择，默认运行方法单元测试")
            await test_device_methods_only()
            
    except KeyboardInterrupt:
        print("\n用户取消测试")
    except Exception as e:
        print(f"测试异常: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
