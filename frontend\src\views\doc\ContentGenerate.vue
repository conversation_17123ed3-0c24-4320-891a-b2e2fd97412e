<template>
  <div class="content-generate">
    <!-- 顶部工具栏 (升级版) -->
    <div class="toolbar-wrapper">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-dropdown>
            <el-button type="primary">ComfyUI<el-icon class="el-icon--right"><arrow-down /></el-icon></el-button>
            <template #dropdown><el-dropdown-menu/></template>
          </el-dropdown>
          <el-dropdown>
            <el-button type="primary">新建任务<el-icon class="el-icon--right"><arrow-down /></el-icon></el-button>
            <template #dropdown><el-dropdown-menu/></template>
          </el-dropdown>
          <el-button @click="loadTask">加载任务</el-button>
          <el-button @click="deleteTask">删除任务</el-button>
          <el-button @click="copyTask">复制任务</el-button>
          <el-button @click="oneClickMovie">一键成片</el-button>
        </div>
        <div class="toolbar-right">
          <el-button color="#E6A23C">云端部署</el-button>
          <el-button color="#E6A23C">销毁镜像</el-button>
          <el-button color="#E6A23C">打开文件</el-button>
          <el-button color="#F56C6C">停止运行</el-button>
        </div>
      </div>
      <div class="toolbar secondary-toolbar">
        <el-button @click="addScene">增加分镜</el-button>
        <el-button @click="setScene">设置分镜</el-button>
        <el-button @click="batchInference">批量推理</el-button>
        <el-button @click="batchVoice">批量语音</el-button>
        <el-button @click="batchDraw">批量绘图</el-button>
        <el-button @click="batchUpscale">批量放大</el-button>
        <el-button @click="videoInference">视频推理</el-button>
        <el-button @click="batchAnimation">批量动画</el-button>
        <el-button @click="composeVideo">合成视频</el-button>
      </div>
    </div>

    <div class="main-container">
      <!-- 左侧任务列表 -->
      <div class="left-panel">
        <div class="task-list-header">
          <span>任务列表</span>
          <el-badge :value="taskList.length" class="task-count" />
        </div>

        <div class="task-list">
          <div
            v-for="task in filteredTasks"
            :key="task.id"
            class="task-item"
            :class="{ active: selectedTask?.id === task.id }"
            @click="selectTask(task)"
          >
            <div class="task-content">
              <div class="task-title">{{ task.title }}</div>
              <div class="task-description">{{ task.description }}</div>
              <div class="task-meta">
                <el-tag :type="getTaskStatusType(task.status)" size="small">
                  {{ getTaskStatusText(task.status) }}
                </el-tag>
                <span class="task-time">{{ formatTime(task.created_at) }}</span>
              </div>
            </div>
            <div class="task-actions">
              <el-button
                size="small"
                type="primary"
                @click.stop="compressTask(task)"
              >
                压缩
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域：分镜表格风格（升级版） -->
      <div class="right-panel">
        <div class="content-table-container">
          <el-table :data="filteredVideos" border style="width: 100%" height="100%">
            <el-table-column label="编号" type="index" width="60" />
            <el-table-column label="文案" min-width="180">
              <template #default="{ row }">
                <el-input v-model="row.title_cn" type="textarea" autosize />
              </template>
            </el-table-column>
            <el-table-column label="分镜" min-width="80">
              <template #default="{ row }">
                <el-select v-model="row.sceneType" placeholder="选择分镜类型" size="small" style="width:80px">
                  <el-option label="感内" value="感内" />
                  <el-option label="感外" value="感外" />
                  <el-option label="动作" value="动作" />
                  <el-option label="特写" value="特写" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="提示词(中)" min-width="160">
              <template #default="{ row }">
                <el-input v-model="row.prompt_cn" type="textarea" autosize />
              </template>
            </el-table-column>
            <el-table-column label="提示词(EN)" min-width="160">
              <template #default="{ row }">
                <el-input v-model="row.title_en" type="textarea" autosize />
              </template>
            </el-table-column>
            <el-table-column label="视频提示词" min-width="120">
              <template #default="{ row }">
                <el-input v-model="row.video_prompt" type="textarea" autosize />
              </template>
            </el-table-column>
            <el-table-column label="新图" min-width="120">
              <template #default="{ row }">
                <img :src="row.mainImage || row.thumbnail" :alt="row.title_cn" style="width:100px;height:56px;border-radius:6px;object-fit:cover;" />
              </template>
            </el-table-column>
            <el-table-column label="语音" min-width="100">
              <template #default="{ row }">
                <el-radio-group v-model="row.voiceType" size="small">
                  <el-radio-button label="旁白" />
                  <el-radio-button label="李靖" />
                  <el-radio-button label="小雅" />
                </el-radio-group>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="260">
              <template #default="{ row, $index }">
                <el-button-group>
                  <el-button size="small" @click.stop="moveUp($index)" :disabled="$index === 0">向上合并</el-button>
                  <el-button size="small" @click.stop="pushKeywords(row)">推关键词</el-button>
                  <el-button size="small" @click.stop="redraw(row)">重新绘图</el-button>
                  <el-button size="small" @click.stop="playVoice(row)">语音试听</el-button>
                  <el-button size="small" @click.stop="makeAnimation(row)">制作动画</el-button>
                  <el-button size="small" @click.stop="openSegmentDialog(row)">分镜设置</el-button>
                </el-button-group>
              </template>
            </el-table-column>
            <el-table-column label="备选图片" min-width="160">
              <template #default="{ row }">
                <div style="display:flex;gap:6px;">
                  <img v-for="(img, n) in row.candidates || [row.thumbnail]" :key="n" :src="img" style="width:48px;height:32px;border-radius:4px;object-fit:cover;border:2px solid" :style="{borderColor: row.mainImage === img ? '#409eff' : '#eee'}" @click="setMainImage(row, img)" />
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[12, 24, 48, 96]"
              :total="totalVideos"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
        <!-- 分镜设置弹窗 -->
        <el-dialog v-model="showSegmentDialog" title="分镜设置" width="600px">
          <el-form :model="currentSegment" label-width="100px">
            <el-form-item label="分镜类型">
              <el-select v-model="currentSegment.sceneType" placeholder="选择分镜类型">
                <el-option label="感内" value="感内" />
                <el-option label="感外" value="感外" />
                <el-option label="动作" value="动作" />
                <el-option label="特写" value="特写" />
              </el-select>
            </el-form-item>
            <el-form-item label="文案">
              <el-input v-model="currentSegment.title_cn" type="textarea" autosize />
            </el-form-item>
            <el-form-item label="提示词(中)">
              <el-input v-model="currentSegment.prompt_cn" type="textarea" autosize />
            </el-form-item>
            <el-form-item label="提示词(EN)">
              <el-input v-model="currentSegment.title_en" type="textarea" autosize />
            </el-form-item>
            <el-form-item label="视频提示词">
              <el-input v-model="currentSegment.video_prompt" type="textarea" autosize />
            </el-form-item>
            <el-form-item label="语音类型">
              <el-radio-group v-model="currentSegment.voiceType">
                <el-radio-button label="旁白" />
                <el-radio-button label="李靖" />
                <el-radio-button label="小雅" />
              </el-radio-group>
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="showSegmentDialog = false">取消</el-button>
            <el-button type="primary" @click="saveSegmentDialog">保存</el-button>
          </template>
        </el-dialog>
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateTaskDialog"
      title="创建AI内容生成任务"
      width="700px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="120px">
        <el-form-item label="任务名称" prop="title">
          <el-input v-model="createForm.title" placeholder="请输入任务名称" />
        </el-form-item>

        <el-form-item label="内容主题" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请详细描述您想要生成的视频内容主题、风格和要求"
          />
        </el-form-item>

        <el-form-item label="生成类型" prop="type">
          <el-radio-group v-model="createForm.type">
            <el-radio label="video">视频生成</el-radio>
            <el-radio label="image_sequence">图片序列</el-radio>
            <el-radio label="storyboard">分镜脚本</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="视频风格">
          <el-select v-model="createForm.style" placeholder="选择视频风格" style="width: 100%">
            <el-option label="真实风格" value="realistic" />
            <el-option label="动漫风格" value="anime" />
            <el-option label="卡通风格" value="cartoon" />
            <el-option label="科幻风格" value="sci-fi" />
            <el-option label="古典风格" value="classical" />
          </el-select>
        </el-form-item>

        <el-form-item label="视频时长">
          <el-input-number
            v-model="createForm.duration"
            :min="5"
            :max="300"
            :step="5"
            controls-position="right"
          />
          <span style="margin-left: 8px; color: #909399;">秒</span>
        </el-form-item>

        <el-form-item label="分辨率">
          <el-select v-model="createForm.resolution" style="width: 100%">
            <el-option label="1920x1080 (1080p)" value="1920x1080" />
            <el-option label="1280x720 (720p)" value="1280x720" />
            <el-option label="1080x1920 (竖屏)" value="1080x1920" />
            <el-option label="720x1280 (竖屏)" value="720x1280" />
          </el-select>
        </el-form-item>

        <el-form-item label="生成数量">
          <el-input-number v-model="createForm.count" :min="1" :max="5" />
        </el-form-item>

        <el-form-item label="高级设置">
          <el-collapse>
            <el-collapse-item title="ComfyUI工作流" name="workflow">
              <el-select v-model="createForm.workflow" placeholder="选择工作流模板" style="width: 100%">
                <el-option label="默认视频生成" value="default_video" />
                <el-option label="高质量渲染" value="high_quality" />
                <el-option label="快速生成" value="fast_generation" />
              </el-select>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateTaskDialog = false">取消</el-button>
        <el-button type="primary" @click="createTask" :loading="creating">
          <el-icon><MagicStick /></el-icon>
          开始生成
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import {
  Plus, Refresh, Search, VideoCamera, VideoPlay, MagicStick, Edit, Delete, ArrowDown
} from '@element-plus/icons-vue'
import {
  getGenerationTasks,
  createGenerationTask,
  getGenerationTask,
  startGenerationTask,
  pauseGenerationTask,
  stopGenerationTask,
  getTaskProgress,
  deleteGenerationTask,
  testComfyUIConnection
} from '@/api/content-generate'
import type { GenerationTask } from '@/api/content-generate'

// 类型定义
interface GenerationTask {
  id: string
  title: string
  type: 'video' | 'image_sequence' | 'storyboard'
  description: string
  style?: string
  duration?: number
  resolution?: string
  count: number
  workflow?: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'paused'
  progress: number
  current_step?: string
  created_at: string
  completed_at?: string
}

interface VideoContent {
  id: string
  title_en: string
  title_cn: string
  thumbnail: string
  duration: string
  uploadable: boolean
  task_id: string
  file_path?: string
  file_size?: number
}

// 响应式数据
const taskList = ref<GenerationTask[]>([])
const videoList = ref<VideoContent[]>([])
const selectedTask = ref<GenerationTask | null>(null)
const selectedVideos = ref<string[]>([])
const showCreateTaskDialog = ref(false)
const creating = ref(false)

// 搜索和筛选
const searchKeyword = ref('')
const filterStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const totalVideos = ref(0)

// 表单数据
const createForm = reactive({
  title: '',
  type: 'video',
  description: '',
  style: 'realistic',
  duration: 30,
  resolution: '1920x1080',
  count: 1,
  workflow: 'default_video'
})

const createRules = {
  title: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择生成类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入内容主题', trigger: 'blur' }]
}

const createFormRef = ref()

// 计算属性
const filteredTasks = computed(() => {
  return taskList.value.filter(task => {
    if (searchKeyword.value && !task.title.includes(searchKeyword.value)) {
      return false
    }
    if (filterStatus.value && task.status !== filterStatus.value) {
      return false
    }
    return true
  })
})

const filteredVideos = computed(() => {
  let videos = videoList.value

  if (searchKeyword.value) {
    videos = videos.filter(video =>
      video.title_en.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      video.title_cn.includes(searchKeyword.value)
    )
  }

  if (filterStatus.value) {
    if (filterStatus.value === 'uploadable') {
      videos = videos.filter(video => video.uploadable)
    }
  }

  totalVideos.value = videos.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return videos.slice(start, end)
})

// 方法
const selectTask = (task: GenerationTask) => {
  selectedTask.value = task
  // 加载该任务的视频内容
  loadTaskVideos(task.id)
}

const toggleVideoSelection = (videoId: string) => {
  const index = selectedVideos.value.indexOf(videoId)
  if (index > -1) {
    selectedVideos.value.splice(index, 1)
  } else {
    selectedVideos.value.push(videoId)
  }
}

const getTaskStatusType = (status: string) => {
  const statusMap = {
    pending: '',
    processing: 'warning',
    completed: 'success',
    failed: 'danger',
    paused: 'info'
  }
  return statusMap[status] || ''
}

const getTaskStatusText = (status: string) => {
  const statusMap = {
    pending: '待处理',
    processing: '生成中',
    completed: '已完成',
    failed: '失败',
    paused: '已暂停'
  }
  return statusMap[status] || status
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const refreshTasks = () => {
  loadTasks()
  ElMessage.success('刷新成功')
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const createTask = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    creating.value = true

    // 调用API创建任务
    const taskData = {
      title: createForm.title,
      type: createForm.type,
      description: createForm.description,
      style: createForm.style,
      count: createForm.count,
      config: {
        duration: createForm.duration,
        resolution: createForm.resolution,
        comfyui_workflow: createForm.workflow
      }
    }

    const response = await createGenerationTask(taskData)

    // 添加到任务列表
    taskList.value.unshift(response)
    showCreateTaskDialog.value = false
    ElMessage.success('AI内容生成任务创建成功，正在准备生成环境...')

    // 开始监控任务进度
    startTaskMonitoring(response.id)

  } catch (error) {
    console.error('创建任务失败:', error)
    ElMessage.error('创建任务失败')
  } finally {
    creating.value = false
  }
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    title: '',
    type: 'video',
    description: '',
    style: 'realistic',
    duration: 30,
    resolution: '1920x1080',
    count: 1,
    workflow: 'default_video'
  })
  createFormRef.value?.clearValidate()
}

const loadTasks = async () => {
  try {
    loading.value = true
    const response = await getGenerationTasks({
      page: currentPage.value,
      page_size: pageSize.value,
      status: statusFilter.value || undefined,
      type: typeFilter.value || undefined
    })

    taskList.value = response.tasks
    total.value = response.total

    ElMessage.success('任务列表加载成功')
  } catch (error) {
    console.error('加载任务列表失败:', error)
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadTaskVideos = async (taskId: string) => {
  try {
    const task = await getGenerationTask(taskId)
    if (task.results && task.results.length > 0) {
      // 将结果转换为视频格式
      const videos = task.results.map(result => ({
        id: result.id,
        title_en: result.title,
        title_cn: result.title,
        thumbnail: result.preview_url || 'https://picsum.photos/320/180',
        duration: '30s',
        uploadable: true,
        task_id: taskId,
        file_size: Math.floor(Math.random() * 50 + 10)
      }))

      // 更新视频列表
      videoList.value = videos
    }
  } catch (error) {
    console.error('加载任务视频失败:', error)
    ElMessage.error('加载任务视频失败')
  }
}

const startGeneration = async (task: GenerationTask) => {
  try {
    await startGenerationTask(task.id)
    ElMessage.success('任务已启动')

    // 开始监控任务进度
    startTaskMonitoring(task.id)

  } catch (error) {
    console.error('启动任务失败:', error)
    ElMessage.error('启动任务失败')
  }
}

// 任务监控相关
const taskMonitoringIntervals = new Map<string, number>()

const startTaskMonitoring = (taskId: string) => {
  // 清除已存在的监控
  if (taskMonitoringIntervals.has(taskId)) {
    clearInterval(taskMonitoringIntervals.get(taskId))
  }

  // 开始新的监控
  const interval = setInterval(async () => {
    try {
      const progress = await getTaskProgress(taskId)

      // 更新任务列表中的任务状态
      const taskIndex = taskList.value.findIndex(t => t.id === taskId)
      if (taskIndex !== -1) {
        taskList.value[taskIndex].status = progress.status
        taskList.value[taskIndex].progress = progress.progress
        taskList.value[taskIndex].current_step = progress.current_step

        if (progress.error_message) {
          taskList.value[taskIndex].error_message = progress.error_message
        }

        // 如果任务完成或失败，停止监控
        if (progress.status === 'completed' || progress.status === 'failed') {
          clearInterval(interval)
          taskMonitoringIntervals.delete(taskId)

          if (progress.status === 'completed') {
            ElMessage.success(`任务 "${taskList.value[taskIndex].title}" 生成完成！`)
            // 加载生成的内容
            loadTaskVideos(taskId)
          } else {
            ElMessage.error(`任务 "${taskList.value[taskIndex].title}" 生成失败`)
          }
        }
      }

    } catch (error) {
      console.error('监控任务进度失败:', error)
      clearInterval(interval)
      taskMonitoringIntervals.delete(taskId)
    }
  }, 3000) // 每3秒检查一次

  taskMonitoringIntervals.set(taskId, interval)
}

const stopTaskMonitoring = (taskId: string) => {
  if (taskMonitoringIntervals.has(taskId)) {
    clearInterval(taskMonitoringIntervals.get(taskId))
    taskMonitoringIntervals.delete(taskId)
  }
}

const generateMockVideos = (task: GenerationTask) => {
  const mockVideos: VideoContent[] = []

  for (let i = 0; i < task.count; i++) {
    mockVideos.push({
      id: `${task.id}-video-${i + 1}`,
      title_en: `A ${task.style} style video about ${task.description.substring(0, 50)}...`,
      title_cn: `${task.title} - 生成视频 ${i + 1}`,
      thumbnail: `https://picsum.photos/320/180?random=${Date.now() + i}`,
      duration: `${task.duration || 30}s`,
      uploadable: true,
      task_id: task.id,
      file_size: Math.floor(Math.random() * 50 + 10) // 10-60MB
    })
  }

  // 添加到视频列表
  videoList.value.push(...mockVideos)
}

const compressTask = (task: GenerationTask) => {
  ElMessage.info(`开始压缩任务: ${task.title}`)
  // TODO: 实现任务压缩逻辑
}

const batchCompress = () => {
  if (selectedVideos.value.length === 0) {
    ElMessage.warning('请先选择要压缩的视频')
    return
  }
  ElMessage.info(`开始批量压缩 ${selectedVideos.value.length} 个视频`)
  // TODO: 实现批量压缩逻辑
}

const playVideo = (video: VideoContent) => {
  ElMessage.info(`播放视频: ${video.title_cn}`)
  // TODO: 实现视频播放逻辑
}

const editVideo = (video: VideoContent) => {
  ElMessage.info(`编辑视频: ${video.title_cn}`)
  // TODO: 实现视频编辑逻辑
}

const compressVideo = (video: VideoContent) => {
  ElMessage.info(`压缩视频: ${video.title_cn}`)
  // TODO: 实现视频压缩逻辑
}

const uploadVideo = (video: VideoContent) => {
  if (!video.uploadable) {
    ElMessage.warning('视频还未准备好上传')
    return
  }
  ElMessage.success(`开始上传视频: ${video.title_cn}`)
  // TODO: 实现视频上传逻辑
}

// 分镜表格相关方法和数据
const showSegmentDialog = ref(false)
const currentSegment = ref<any>({})

const setMainImage = (row: any, img: string) => {
  row.mainImage = img
}
const openSegmentDialog = (row: any) => {
  currentSegment.value = { ...row }
  showSegmentDialog.value = true
}
const saveSegmentDialog = () => {
  // 保存弹窗内容到原表格行
  const idx = filteredVideos.value.findIndex(v => v.id === currentSegment.value.id)
  if (idx > -1) {
    Object.assign(filteredVideos.value[idx], currentSegment.value)
  }
  showSegmentDialog.value = false
}
const moveUp = (index: number) => {
  if (index > 0) {
    const arr = filteredVideos.value
    const temp = arr[index - 1]
    arr[index - 1] = arr[index]
    arr[index] = temp
  }
}
const pushKeywords = (row: any) => ElMessage.success('推关键词')
const redraw = (row: any) => ElMessage.success('重新绘图')
const playVoice = (row: any) => ElMessage.success('语音试听')
const makeAnimation = (row: any) => ElMessage.success('制作动画')

// 顶部工具栏方法
const loadTask = () => ElMessage.info('加载任务')
const deleteTask = () => ElMessage.info('删除任务')
const copyTask = () => ElMessage.info('复制任务')
const oneClickMovie = () => ElMessage.info('一键成片')
const addScene = () => ElMessage.info('增加分镜')
const setScene = () => ElMessage.info('设置分镜')
const batchInference = () => ElMessage.info('批量推理')
const batchVoice = () => ElMessage.info('批量语音')
const batchDraw = () => ElMessage.info('批量绘图')
const batchUpscale = () => ElMessage.info('批量放大')
const videoInference = () => ElMessage.info('视频推理')
const batchAnimation = () => ElMessage.info('批量动画')
const composeVideo = () => ElMessage.info('合成视频')

// 初始化
onMounted(async () => {
  // 加载任务列表
  await loadTasks()

  // 测试ComfyUI连接
  try {
    const connectionTest = await testComfyUIConnection()
    if (connectionTest.status === 'connected') {
      ElMessage.success('ComfyUI服务连接正常')
    } else {
      ElMessage.warning('ComfyUI服务连接失败，部分功能可能不可用')
    }
  } catch (error) {
    console.error('测试ComfyUI连接失败:', error)
  }

  // 如果没有任务，显示模拟数据
  if (taskList.value.length === 0) {
    taskList.value = [
    {
      id: '1',
      title: '美食制作教程视频',
      type: 'video',
      description: '生成关于家常菜制作的教学视频，包含详细的制作步骤和技巧说明',
      style: 'realistic',
      duration: 60,
      resolution: '1920x1080',
      count: 3,
      workflow: 'high_quality',
      status: 'completed',
      progress: 1,
      created_at: '2024-01-15T10:00:00Z',
      completed_at: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      title: '旅游风景短视频',
      type: 'video',
      description: '生成日本樱花季旅游风景视频，展现美丽的自然景色',
      style: 'realistic',
      duration: 30,
      resolution: '1080x1920',
      count: 2,
      workflow: 'default_video',
      status: 'processing',
      progress: 0.6,
      current_step: '正在渲染视频帧...',
      created_at: '2024-01-15T11:00:00Z'
    },
    {
      id: '3',
      title: '科技产品宣传片',
      type: 'video',
      description: '为新款智能手机制作产品宣传视频，突出科技感和创新特性',
      style: 'sci-fi',
      duration: 45,
      resolution: '1920x1080',
      count: 1,
      workflow: 'high_quality',
      status: 'pending',
      progress: 0,
      created_at: '2024-01-15T12:00:00Z'
    }
  ]

  // 模拟已完成任务的视频内容
  videoList.value = [
    {
      id: '1-video-1',
      title_en: 'A school girl and charming American woman cooking 30 year-old girl with long hair, wearing a white apron, standing in a modern kitchen, smiling while preparing ingredients for cooking, natural lighting, high quality, realistic style',
      title_cn: '美食制作教程 - 家常菜制作',
      thumbnail: 'https://picsum.photos/320/180?random=1',
      duration: '60s',
      uploadable: true,
      task_id: '1',
      sceneType: '动作',
      prompt_cn: '展示一个年轻女孩在厨房里准备食材的场景',
      title_en: 'A young girl preparing ingredients in a modern kitchen, smiling while cooking, high quality, realistic style',
      video_prompt: 'A young girl preparing ingredients in a modern kitchen, smiling while cooking, high quality, realistic style',
      voiceType: '旁白',
      mainImage: 'https://picsum.photos/320/180?random=1',
      candidates: [
        'https://picsum.photos/320/180?random=1',
        'https://picsum.photos/320/180?random=2',
        'https://picsum.photos/320/180?random=3'
      ]
    },
    {
      id: '1-video-2',
      title_en: 'A beautiful cooking scene with fresh ingredients and traditional Chinese cooking techniques, warm kitchen atmosphere, detailed food preparation process',
      title_cn: '传统中式烹饪技巧',
      thumbnail: 'https://picsum.photos/320/180?random=2',
      duration: '60s',
      uploadable: true,
      task_id: '1',
      sceneType: '感内',
      prompt_cn: '展示一个传统中式厨房的温馨氛围',
      title_en: 'A warm kitchen atmosphere with fresh ingredients and traditional Chinese cooking techniques, high quality, realistic style',
      video_prompt: 'A warm kitchen atmosphere with fresh ingredients and traditional Chinese cooking techniques, high quality, realistic style',
      voiceType: '李靖',
      mainImage: 'https://picsum.photos/320/180?random=2',
      candidates: [
        'https://picsum.photos/320/180?random=2',
        'https://picsum.photos/320/180?random=3',
        'https://picsum.photos/320/180?random=4'
      ]
    },
    {
      id: '1-video-3',
      title_en: 'Professional chef demonstrating advanced cooking methods, modern kitchen equipment, step-by-step cooking tutorial, high-definition video quality',
      title_cn: '专业烹饪技法展示',
      thumbnail: 'https://picsum.photos/320/180?random=3',
      duration: '60s',
      uploadable: false,
      task_id: '1',
      sceneType: '动作',
      prompt_cn: '展示一个专业厨师演示高级烹饪技巧的场景',
      title_en: 'A professional chef demonstrating advanced cooking methods, high quality, realistic style',
      video_prompt: 'A professional chef demonstrating advanced cooking methods, high quality, realistic style',
      voiceType: '小雅',
      mainImage: 'https://picsum.photos/320/180?random=3',
      candidates: [
        'https://picsum.photos/320/180?random=3',
        'https://picsum.photos/320/180?random=4',
        'https://picsum.photos/320/180?random=5'
      ]
    }
  ]

  // 默认选择第一个任务
  if (taskList.value.length > 0) {
    selectedTask.value = taskList.value[0]
  }

  totalVideos.value = videoList.value.length
  }
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理所有任务监控
  taskMonitoringIntervals.forEach((interval) => {
    clearInterval(interval)
  })
  taskMonitoringIntervals.clear()
})
</script>

<style scoped>
.content-generate {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa; /* 浅色背景 */
  color: #303133; /* 深色文字 */
}

.toolbar-wrapper {
  padding: 16px 20px;
  background: #fff; /* 工具栏背景 */
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.secondary-toolbar {
  margin-top: 12px;
  justify-content: flex-start;
  gap: 12px;
}

.toolbar .el-button, .secondary-toolbar .el-button {
  background-color: #fff;
  color: #303133;
  border-color: #e4e7ed;
}

.toolbar .el-button:hover, .secondary-toolbar .el-button:hover {
  background-color: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.toolbar-left, .toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.toolbar-left .el-dropdown {
  width: 120px; /* Adjust width for dropdown */
}

.toolbar-left .el-dropdown .el-button {
  width: 100%;
  justify-content: space-between;
}

.toolbar-left .el-dropdown .el-button .el-icon {
  margin-left: 8px;
}

.toolbar-right .el-button {
  background-color: #E6A23C; /* Orange button */
  border-color: #E6A23C;
  color: white;
}

.toolbar-right .el-button:hover {
  background-color: #D39A33;
  border-color: #D39A33;
}

.toolbar-right .el-button:active {
  background-color: #C0902D;
  border-color: #C0902D;
}

.toolbar-right .el-button:focus {
  background-color: #E6A23C;
  border-color: #E6A23C;
}

.toolbar-right .el-button.is-disabled {
  background-color: #E6A23C;
  border-color: #E6A23C;
  opacity: 0.6;
}

.toolbar-right .el-button.is-disabled:hover {
  background-color: #D39A33;
  border-color: #D39A33;
}

.toolbar-right .el-button.is-disabled:active {
  background-color: #C0902D;
  border-color: #C0902D;
}

.toolbar-right .el-button.is-disabled:focus {
  background-color: #E6A23C;
  border-color: #E6A23C;
}

.toolbar-right .el-button.is-disabled .el-icon {
  color: #E5EAF3;
}

.main-container {
  display: flex;
  flex: 1;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.left-panel {
  width: 320px;
  flex-shrink: 0;
  background: #fff; /* 面板背景 */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
  font-size: 16px;
  color: #303133;
}

.task-count {
  margin-left: 8px;
}

.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.task-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.task-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.task-item.active {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.task-content {
  margin-bottom: 12px;
}

.task-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
  color: #303133;
}

.task-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #909399;
}

.task-time {
  font-size: 11px;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.right-panel {
  flex: 1;
  min-width: 0;
  background: #fff; /* 面板背景 */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
}

.content-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>

<style>
/* el-table 浅色主题覆盖 */
.right-panel .el-table {
  --el-table-bg-color: #fff;
  --el-table-tr-bg-color: #fff;
  --el-table-header-bg-color: #f5f7fa;
  --el-table-border-color: #e4e7ed;
  --el-table-text-color: #303133;
  --el-table-header-text-color: #303133;
  --el-table-row-hover-bg-color: #f0f9ff;
}
.right-panel .el-table th, .right-panel .el-table td {
  background-color: transparent;
}
.right-panel .el-input__wrapper, .right-panel .el-textarea__inner, .right-panel .el-select__wrapper {
  background-color: #fff;
  box-shadow: none;
  border: 1px solid #e4e7ed;
  color: #303133;
}
.right-panel .el-radio-button__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}
.right-panel .el-radio-button__original-radio:checked+.el-radio-button__inner {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}
/* el-dialog 浅色主题覆盖 */
.el-dialog {
  background-color: #fff;
}
.el-dialog__title {
  color: #303133;
}
.el-dialog__body {
  color: #303133;
}
.el-form-item__label {
  color: #606266;
}
</style>
