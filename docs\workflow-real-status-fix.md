# 工作流真实状态显示修复

## 问题描述

用户反馈工作流显示存在严重问题：
- **只显示静态模板**：不管任务是成功还是失败，都只显示工作流配置模板
- **缺少真实状态**：没有显示任务的实际执行进度和步骤状态
- **状态不同步**：工作流步骤状态与任务实际执行状态不一致

## 问题根本原因

### 1. API设计问题

**文件**: `backend/app/api/v1/workflow.py`

原有的`get_task_workflow` API只做了以下事情：
1. 从Redis查找缓存的工作流状态（通常为空）
2. 从Core服务获取静态的工作流配置模板
3. 返回静态模板，没有真实的执行状态

**问题代码**：
```python
# 只通过gRPC从Core服务获取工作流配置，不允许直接读取文件
core_workflow = await get_workflow_config_from_core(platform_id, content_type, core_service_id)

if core_workflow:
    # 构建工作流数据 - 这里只是静态模板！
    workflow_data = {
        "workflow_id": core_workflow.get("workflow_id", f"workflow_{task_id}"),
        "workflow_name": core_workflow.get("workflow_name", "未知工作流"),
        "current_step_index": 0,  # ❌ 总是0，不是真实进度
        "steps": core_workflow.get("steps", []),  # ❌ 静态步骤，没有执行状态
    }
```

### 2. 缺少真实状态获取

Core服务提供了`GetTaskStatus` gRPC接口，可以获取：
- 任务真实执行状态
- 当前执行步骤
- 每个步骤的进度和状态
- 执行日志和错误信息

但是Backend没有调用这个接口来获取真实状态。

## 修复方案

### 1. 优先获取真实状态

**修改**: `backend/app/api/v1/workflow.py`

```python
@router.get("/{task_id}/workflow")
async def get_task_workflow(task_id: str, workflow_service: WorkflowStateService = Depends(get_workflow_service)):
    """获取任务工作流详情"""
    try:
        logger.info(f"获取任务工作流详情: {task_id}")

        # 🔧 重要修复：优先从Core服务获取真实的任务执行状态
        from app.core.grpc_client import get_task_client
        
        # 1. 首先从数据库获取任务信息，确定使用哪个Core服务
        task = await db.social_tasks.find_one({"task_id": task_id})
        core_service_id = task.get("core_service_id")
        
        # 2. 从Core服务获取真实的任务执行状态
        task_client = await get_task_client(core_service_id)
        if task_client:
            task_status = await task_client.get_task_status(task_id)
            
            if task_status and task_status.get("task_id") == task_id:
                # 构建真实的工作流状态
                workflow_data = await build_real_workflow_from_task_status(task_status, task)
                if workflow_data:
                    return {
                        "success": True,
                        "data": workflow_data,
                        "source": "core_real_status"  # ✅ 真实状态
                    }
```

### 2. 构建真实工作流状态

**新增函数**: `build_real_workflow_from_task_status`

```python
async def build_real_workflow_from_task_status(task_status: Dict[str, Any], task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """从Core服务的任务状态构建真实的工作流数据"""
    
    # 获取任务基本信息
    task_id = task_status.get("task_id")
    status = task_status.get("status", "unknown")  # running/completed/failed/paused
    progress = task_status.get("progress", 0)      # 0-100
    current_step = task_status.get("current_step", 1)  # 当前步骤
    
    # 构建真实的工作流步骤
    workflow_steps = []
    for i, step_data in enumerate(steps_data):
        step_status = "pending"
        step_progress = 0
        
        # 根据任务状态和当前步骤确定每个步骤的状态
        if i + 1 < current_step:
            step_status = "completed"  # ✅ 已完成
            step_progress = 100
        elif i + 1 == current_step:
            if status == "running":
                step_status = "running"  # 🔄 执行中
                step_progress = progress
            elif status == "completed":
                step_status = "completed"  # ✅ 已完成
                step_progress = 100
            elif status == "failed":
                step_status = "failed"  # ❌ 失败
                step_progress = progress
            elif status == "paused":
                step_status = "paused"  # ⏸️ 暂停
                step_progress = progress
        else:
            step_status = "pending"  # ⏳ 等待中
            step_progress = 0
        
        workflow_step = {
            "id": f"step_{i+1}",
            "name": step_data.get("name", f"步骤 {i+1}"),
            "status": step_status,  # ✅ 真实状态
            "progress": step_progress,  # ✅ 真实进度
            "start_time": step_data.get("start_time", ""),
            "end_time": step_data.get("end_time", ""),
            "error_message": step_data.get("error_message", ""),
            # ... 其他字段
        }
        
        workflow_steps.append(workflow_step)
    
    # 构建完整的工作流数据
    workflow_data = {
        "workflow_id": f"workflow_{task_id}",
        "workflow_name": get_workflow_name_from_task(task),
        "current_step_index": current_step - 1,  # ✅ 真实的当前步骤
        "total_steps": len(workflow_steps),
        "steps": workflow_steps,  # ✅ 包含真实状态的步骤
        "can_resume": status in ["paused", "failed"],  # ✅ 真实的恢复能力
        # ... 其他字段
    }
    
    return workflow_data
```

### 3. 降级处理策略

修复后的API采用多层降级策略：

1. **优先级1**: 从Core服务获取真实任务状态 ✅
2. **优先级2**: 从Redis获取缓存的工作流状态
3. **优先级3**: 从Core服务获取静态工作流配置模板
4. **优先级4**: 返回默认的工作流结构

## 修复效果

### 修复前的显示

```json
{
  "workflow_name": "YouTube短视频上传",
  "current_step_index": 0,  // ❌ 总是0
  "steps": [
    {
      "name": "准备任务",
      "status": "pending",  // ❌ 总是pending
      "progress": 0         // ❌ 总是0
    },
    {
      "name": "连接设备", 
      "status": "pending",  // ❌ 不管任务实际状态
      "progress": 0
    }
    // ... 所有步骤都是pending状态
  ],
  "source": "core_grpc"  // 静态模板
}
```

### 修复后的显示

**运行中任务**：
```json
{
  "workflow_name": "YouTube短视频上传",
  "current_step_index": 2,  // ✅ 真实的当前步骤
  "steps": [
    {
      "name": "准备任务",
      "status": "completed",  // ✅ 已完成
      "progress": 100
    },
    {
      "name": "连接设备",
      "status": "completed",  // ✅ 已完成
      "progress": 100
    },
    {
      "name": "启动应用",
      "status": "running",    // ✅ 正在执行
      "progress": 65          // ✅ 真实进度
    },
    {
      "name": "执行操作",
      "status": "pending",    // ✅ 等待中
      "progress": 0
    }
  ],
  "source": "core_real_status"  // ✅ 真实状态
}
```

**失败任务**：
```json
{
  "workflow_name": "YouTube短视频上传",
  "current_step_index": 3,
  "steps": [
    {
      "name": "准备任务",
      "status": "completed",
      "progress": 100
    },
    {
      "name": "连接设备",
      "status": "completed", 
      "progress": 100
    },
    {
      "name": "启动应用",
      "status": "completed",
      "progress": 100
    },
    {
      "name": "执行操作",
      "status": "failed",     // ✅ 显示失败状态
      "progress": 45,
      "error_message": "元素定位失败"  // ✅ 显示错误信息
    }
  ],
  "can_resume": true  // ✅ 可以恢复执行
}
```

## 数据流

### 修复前的数据流
```
前端请求 → Backend API → Core服务(GetWorkflowConfig) → 静态模板 → 前端显示
                                    ↓
                            只有配置，没有状态 ❌
```

### 修复后的数据流
```
前端请求 → Backend API → Core服务(GetTaskStatus) → 真实状态 → 构建工作流 → 前端显示
                                    ↓                    ↓
                            包含执行状态和进度 ✅    动态步骤状态 ✅
```

## 用户体验改进

### 1. 真实状态显示
- ✅ **执行中任务**：显示当前正在执行的步骤，实时进度
- ✅ **已完成任务**：显示所有步骤为完成状态
- ✅ **失败任务**：显示失败的具体步骤和错误信息
- ✅ **暂停任务**：显示暂停在哪个步骤

### 2. 进度可视化
- ✅ **步骤进度条**：显示每个步骤的真实执行进度
- ✅ **整体进度**：基于真实步骤状态计算总体进度
- ✅ **时间信息**：显示步骤的开始时间、结束时间、执行时长

### 3. 错误诊断
- ✅ **失败定位**：准确显示在哪个步骤失败
- ✅ **错误信息**：显示具体的错误原因
- ✅ **恢复建议**：根据失败步骤提供恢复操作

### 4. 操作控制
- ✅ **智能恢复**：只有真正可恢复的任务才显示恢复按钮
- ✅ **步骤控制**：可以从失败的步骤重新开始
- ✅ **实时更新**：工作流状态实时反映任务执行情况

## 兼容性

- ✅ **向后兼容**：如果Core服务不可用，降级到原有逻辑
- ✅ **多Core服务**：正确使用任务指定的Core服务ID
- ✅ **错误处理**：各种异常情况都有相应的降级处理
- ✅ **性能优化**：优先使用真实状态，避免不必要的模板获取

## 总结

通过这次修复，工作流显示现在能够：

1. **显示真实状态**：不再是静态模板，而是任务的真实执行状态
2. **实时更新进度**：每个步骤的进度都反映真实的执行情况
3. **准确错误定位**：失败任务能准确显示失败的步骤和原因
4. **智能操作控制**：根据真实状态提供合适的操作选项

这个修复解决了用户反馈的核心问题，大大提升了任务监控和调试的体验。
