"""
结果自动分发服务
实现生成结果自动发布到指定的社交媒体账号
"""

import logging
import asyncio
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

logger = logging.getLogger(__name__)

class DistributionStatus(Enum):
    """分发状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SCHEDULED = "scheduled"

class DistributionPlatform(Enum):
    """分发平台"""
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"
    DOUYIN = "douyin"
    BILIBILI = "bilibili"
    INSTAGRAM = "instagram"
    TWITTER = "twitter"
    FACEBOOK = "facebook"

@dataclass
class DistributionConfig:
    """分发配置"""
    platform: DistributionPlatform
    account_id: str
    title: str
    description: str = ""
    tags: List[str] = None
    privacy: str = "public"  # public, private, unlisted
    schedule_time: Optional[str] = None
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.custom_params is None:
            self.custom_params = {}

@dataclass
class DistributionJob:
    """分发任务"""
    id: str
    result_id: str
    config: DistributionConfig
    status: DistributionStatus = DistributionStatus.PENDING
    progress: float = 0.0
    platform_post_id: Optional[str] = None
    platform_url: Optional[str] = None
    created_at: str = ""
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

class ResultDistributionService:
    """结果分发服务"""
    
    def __init__(self, storage_service=None):
        self.storage_service = storage_service
        self.distribution_jobs: Dict[str, DistributionJob] = {}
        self.platform_adapters: Dict[DistributionPlatform, Any] = {}
        self.scheduled_jobs: List[DistributionJob] = []
        self._init_platform_adapters()
    
    def _init_platform_adapters(self):
        """初始化平台适配器"""
        try:
            # 这里应该导入实际的平台适配器
            # 暂时使用模拟适配器
            
            self.platform_adapters = {
                DistributionPlatform.YOUTUBE: MockYouTubeAdapter(),
                DistributionPlatform.TIKTOK: MockTikTokAdapter(),
                DistributionPlatform.DOUYIN: MockDouyinAdapter(),
                DistributionPlatform.BILIBILI: MockBilibiliAdapter(),
                DistributionPlatform.INSTAGRAM: MockInstagramAdapter(),
                DistributionPlatform.TWITTER: MockTwitterAdapter(),
                DistributionPlatform.FACEBOOK: MockFacebookAdapter()
            }
            
            logger.info("平台适配器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化平台适配器失败: {str(e)}")
    
    def create_distribution_job(self, result_id: str, config: DistributionConfig) -> str:
        """创建分发任务"""
        try:
            job_id = str(uuid.uuid4())
            
            job = DistributionJob(
                id=job_id,
                result_id=result_id,
                config=config
            )
            
            # 如果设置了调度时间，标记为已调度
            if config.schedule_time:
                job.status = DistributionStatus.SCHEDULED
                self.scheduled_jobs.append(job)
            
            self.distribution_jobs[job_id] = job
            
            # 如果没有调度时间，立即执行
            if not config.schedule_time:
                asyncio.create_task(self._execute_distribution_job(job_id))
            
            logger.info(f"创建分发任务: {job_id} -> {config.platform.value}")
            return job_id
            
        except Exception as e:
            logger.error(f"创建分发任务失败: {str(e)}")
            raise
    
    async def _execute_distribution_job(self, job_id: str):
        """执行分发任务"""
        job = self.distribution_jobs.get(job_id)
        if not job:
            return
        
        try:
            job.status = DistributionStatus.PROCESSING
            job.started_at = datetime.now().isoformat()
            job.progress = 0.1
            
            # 获取结果文件
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            result = self.storage_service.get_result(job.result_id)
            if not result:
                raise ValueError(f"结果不存在: {job.result_id}")
            
            job.progress = 0.2
            
            # 获取平台适配器
            adapter = self.platform_adapters.get(job.config.platform)
            if not adapter:
                raise ValueError(f"不支持的平台: {job.config.platform.value}")
            
            job.progress = 0.3
            
            # 准备发布数据
            publish_data = {
                "file_path": result.file_path,
                "title": job.config.title,
                "description": job.config.description,
                "tags": job.config.tags,
                "privacy": job.config.privacy,
                "account_id": job.config.account_id,
                "custom_params": job.config.custom_params
            }
            
            job.progress = 0.5
            
            # 执行发布
            publish_result = await adapter.publish(publish_data)
            
            if publish_result.get("success"):
                job.status = DistributionStatus.COMPLETED
                job.platform_post_id = publish_result.get("post_id")
                job.platform_url = publish_result.get("url")
                job.progress = 1.0
                job.completed_at = datetime.now().isoformat()
                
                logger.info(f"分发任务完成: {job_id} -> {job.platform_url}")
            else:
                raise Exception(publish_result.get("error", "发布失败"))
                
        except Exception as e:
            job.status = DistributionStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.now().isoformat()
            
            # 检查是否需要重试
            if job.retry_count < job.max_retries:
                job.retry_count += 1
                job.status = DistributionStatus.PENDING
                
                # 延迟重试
                retry_delay = min(60 * (2 ** job.retry_count), 3600)  # 最大1小时
                await asyncio.sleep(retry_delay)
                await self._execute_distribution_job(job_id)
            else:
                logger.error(f"分发任务最终失败: {job_id} - {str(e)}")
    
    def get_distribution_job(self, job_id: str) -> Optional[DistributionJob]:
        """获取分发任务"""
        return self.distribution_jobs.get(job_id)
    
    def get_distribution_jobs(self, result_id: str = None, platform: DistributionPlatform = None,
                            status: DistributionStatus = None) -> List[DistributionJob]:
        """获取分发任务列表"""
        jobs = list(self.distribution_jobs.values())
        
        if result_id:
            jobs = [job for job in jobs if job.result_id == result_id]
        
        if platform:
            jobs = [job for job in jobs if job.config.platform == platform]
        
        if status:
            jobs = [job for job in jobs if job.status == status]
        
        # 按创建时间倒序排序
        jobs.sort(key=lambda j: j.created_at, reverse=True)
        return jobs
    
    def cancel_distribution_job(self, job_id: str) -> bool:
        """取消分发任务"""
        job = self.distribution_jobs.get(job_id)
        if not job:
            return False
        
        if job.status in [DistributionStatus.PENDING, DistributionStatus.SCHEDULED]:
            job.status = DistributionStatus.CANCELLED
            job.completed_at = datetime.now().isoformat()
            
            # 从调度列表中移除
            self.scheduled_jobs = [j for j in self.scheduled_jobs if j.id != job_id]
            
            logger.info(f"取消分发任务: {job_id}")
            return True
        
        return False
    
    async def process_scheduled_jobs(self):
        """处理调度任务"""
        try:
            current_time = datetime.now()
            
            jobs_to_execute = []
            for job in self.scheduled_jobs[:]:
                if job.config.schedule_time:
                    schedule_time = datetime.fromisoformat(job.config.schedule_time)
                    if current_time >= schedule_time:
                        jobs_to_execute.append(job)
                        self.scheduled_jobs.remove(job)
            
            # 执行到期的调度任务
            for job in jobs_to_execute:
                job.status = DistributionStatus.PENDING
                asyncio.create_task(self._execute_distribution_job(job.id))
                logger.info(f"执行调度分发任务: {job.id}")
                
        except Exception as e:
            logger.error(f"处理调度任务失败: {str(e)}")
    
    def get_distribution_statistics(self) -> Dict[str, Any]:
        """获取分发统计"""
        stats = {
            "total_jobs": len(self.distribution_jobs),
            "by_status": {},
            "by_platform": {},
            "success_rate": 0.0,
            "scheduled_jobs": len(self.scheduled_jobs)
        }
        
        for job in self.distribution_jobs.values():
            # 按状态统计
            status = job.status.value
            stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
            
            # 按平台统计
            platform = job.config.platform.value
            stats["by_platform"][platform] = stats["by_platform"].get(platform, 0) + 1
        
        # 计算成功率
        completed = stats["by_status"].get("completed", 0)
        total_finished = completed + stats["by_status"].get("failed", 0)
        if total_finished > 0:
            stats["success_rate"] = completed / total_finished
        
        return stats

# 模拟平台适配器
class MockPlatformAdapter:
    """模拟平台适配器基类"""
    
    def __init__(self, platform_name: str):
        self.platform_name = platform_name
    
    async def publish(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """发布内容"""
        try:
            # 模拟发布过程
            await asyncio.sleep(2)  # 模拟网络延迟
            
            # 模拟成功发布
            post_id = f"{self.platform_name}_{uuid.uuid4().hex[:8]}"
            url = f"https://{self.platform_name}.com/post/{post_id}"
            
            logger.info(f"模拟发布到 {self.platform_name}: {data['title']}")
            
            return {
                "success": True,
                "post_id": post_id,
                "url": url
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

class MockYouTubeAdapter(MockPlatformAdapter):
    def __init__(self):
        super().__init__("youtube")

class MockTikTokAdapter(MockPlatformAdapter):
    def __init__(self):
        super().__init__("tiktok")

class MockDouyinAdapter(MockPlatformAdapter):
    def __init__(self):
        super().__init__("douyin")

class MockBilibiliAdapter(MockPlatformAdapter):
    def __init__(self):
        super().__init__("bilibili")

class MockInstagramAdapter(MockPlatformAdapter):
    def __init__(self):
        super().__init__("instagram")

class MockTwitterAdapter(MockPlatformAdapter):
    def __init__(self):
        super().__init__("twitter")

class MockFacebookAdapter(MockPlatformAdapter):
    def __init__(self):
        super().__init__("facebook")

# 全局分发服务实例
_distribution_service = None

def get_distribution_service() -> ResultDistributionService:
    """获取分发服务实例"""
    global _distribution_service
    
    if _distribution_service is None:
        _distribution_service = ResultDistributionService()
    
    return _distribution_service

async def initialize_distribution_service():
    """初始化分发服务"""
    service = get_distribution_service()
    
    # 启动调度任务处理循环
    async def schedule_loop():
        while True:
            try:
                await service.process_scheduled_jobs()
                await asyncio.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度循环异常: {str(e)}")
                await asyncio.sleep(60)
    
    asyncio.create_task(schedule_loop())
    logger.info("分发服务初始化完成")
