# 视频相关API的Core服务选择支持

## 概述

为了与文件管理功能的Core服务选择保持一致，我们为所有视频相关的API添加了Core服务选择支持。

## 修改的API

### 1. 视频缩略图生成API

**后端修改**：`backend/app/api/v1/filesystem.py`

```python
@router.post("/video/thumbnail", response_model=VideoThumbnailResponse)
async def generate_video_thumbnail(
    request: VideoThumbnailRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """生成视频缩略图
    
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
```

**Core服务选择逻辑**：
```python
# 根据core_service_id选择Core服务
if core_service_id:
    core_client = get_core_client_by_service_id(core_service_id)
    logger.info(f"使用指定的Core服务生成视频缩略图: {core_service_id}")
else:
    from app.core.grpc_client import get_core_client
    core_client = get_core_client()
    logger.info("使用默认Core服务生成视频缩略图")
```

**前端修改**：`frontend/src/api/social.ts`
```typescript
export const generateVideoThumbnail = (data: VideoThumbnailRequest, coreServiceId?: string) => {
  return request<VideoThumbnailResponse>({
    url: '/api/v1/filesystem/video/thumbnail',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

**文件管理页面集成**：`frontend/src/views/doc/Manager.vue`
```typescript
const response = await generateVideoThumbnail({
  video_path: file.path,
  max_width: 120,
  max_height: 68,
  quality: 75
}, selectedCoreService.value || undefined)
```

### 2. 视频预览信息API

**后端修改**：
```python
@router.post("/video/preview-info", response_model=VideoPreviewInfoResponse)
async def get_video_preview_info(
    request: VideoPreviewInfoRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """获取视频预览信息
    
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
```

**前端修改**：
```typescript
export const getVideoPreviewInfo = (data: VideoPreviewInfoRequest, coreServiceId?: string) => {
  return request<VideoPreviewInfoResponse>({
    url: '/api/v1/filesystem/video/preview-info',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

### 3. 视频预览片段生成API

**后端修改**：
```python
@router.post("/video/preview-clip", response_model=VideoPreviewClipResponse)
async def generate_video_preview_clip(
    request: VideoPreviewClipRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """生成视频预览片段
    
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
```

**前端修改**：
```typescript
export const generateVideoPreviewClip = (data: VideoPreviewClipRequest, coreServiceId?: string) => {
  return request<VideoPreviewClipResponse>({
    url: '/api/v1/filesystem/video/preview-clip',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

## 技术实现细节

### Core服务选择逻辑

所有视频API都遵循相同的Core服务选择模式：

1. **参数检查**：检查是否提供了 `core_service_id` 参数
2. **服务选择**：
   - 如果提供了 `core_service_id`，使用 `get_core_client_by_service_id()` 获取指定服务
   - 如果没有提供，使用 `get_core_client()` 获取默认服务
3. **日志记录**：记录使用的Core服务信息
4. **错误处理**：如果无法获取Core服务，返回503错误

### 前端集成

1. **API函数更新**：所有视频相关的API函数都添加了可选的 `coreServiceId` 参数
2. **参数传递**：通过查询参数 `core_service_id` 传递给后端
3. **文件管理集成**：缩略图生成已经集成到文件管理页面，使用用户选择的Core服务

## 使用方式

### 在文件管理页面

用户选择Core服务后，所有视频操作（包括缩略图生成）都会使用选择的Core服务：

1. 用户在下拉菜单中选择Core服务
2. 系统自动将选择的服务ID传递给所有视频相关的API
3. 缩略图生成、视频预览等功能都使用选择的Core服务

### 在其他页面

如果需要在其他页面使用这些API，可以手动传递Core服务ID：

```typescript
// 使用指定的Core服务
const thumbnail = await generateVideoThumbnail(request, 'core-service-123')

// 使用默认Core服务
const thumbnail = await generateVideoThumbnail(request)
```

## 向后兼容性

所有修改都保持向后兼容：

- `core_service_id` 参数都是可选的
- 如果不提供该参数，系统使用默认Core服务
- 现有的API调用不需要修改即可继续工作

## 日志记录

所有API都添加了详细的日志记录：

- 记录使用的Core服务ID
- 记录Core服务选择结果
- 记录API调用状态

## 测试建议

1. **缩略图生成**：测试选择不同Core服务时的缩略图生成
2. **视频预览**：测试视频预览信息获取功能
3. **预览片段**：测试视频预览片段生成功能
4. **错误处理**：测试Core服务不可用时的错误处理
5. **默认行为**：测试不指定Core服务时的默认行为

## 影响范围

这次修改主要影响：

1. **文件管理页面**：缩略图生成现在使用选择的Core服务
2. **视频预览功能**：所有视频预览相关功能支持Core服务选择
3. **API一致性**：视频相关API与其他文件操作API保持一致的Core服务选择机制

## 后续改进

可以考虑为以下功能添加Core服务选择支持：

1. 音频处理相关API
2. 文件上传和下载API
3. 其他媒体处理功能

这样可以确保整个系统的Core服务选择机制保持一致。
