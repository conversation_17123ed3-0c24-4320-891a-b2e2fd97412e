"""
实时进度监控服务
通过WebSocket推送任务和批处理的实时进度更新
"""

import logging
import asyncio
import json
import time
from typing import Dict, Any, Set, Optional, List
from datetime import datetime
import websockets
from websockets.server import WebSocketServerProtocol
from dataclasses import asdict

logger = logging.getLogger(__name__)

class ProgressEvent:
    """进度事件"""
    
    def __init__(self, event_type: str, data: Dict[str, Any]):
        self.event_type = event_type
        self.data = data
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": self.event_type,
            "data": self.data,
            "timestamp": self.timestamp
        }

class ProgressMonitorService:
    """进度监控服务"""
    
    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.clients: Set[WebSocketServerProtocol] = set()
        self.subscriptions: Dict[str, Set[WebSocketServerProtocol]] = {}  # 订阅关系
        self.server = None
        self.is_running = False
        
        # 事件缓存，用于新连接的客户端获取历史状态
        self.event_cache: Dict[str, ProgressEvent] = {}
        self.max_cache_size = 1000
    
    async def start_server(self):
        """启动WebSocket服务器"""
        try:
            self.server = await websockets.serve(
                self.handle_client,
                self.host,
                self.port,
                ping_interval=20,
                ping_timeout=10
            )
            self.is_running = True
            logger.info(f"进度监控WebSocket服务器启动: ws://{self.host}:{self.port}")
        except Exception as e:
            logger.error(f"启动WebSocket服务器失败: {str(e)}")
    
    async def stop_server(self):
        """停止WebSocket服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            self.is_running = False
            logger.info("进度监控WebSocket服务器停止")
    
    async def handle_client(self, websocket: WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"客户端连接: {client_id}")
        
        self.clients.add(websocket)
        
        try:
            # 发送欢迎消息
            await self.send_to_client(websocket, {
                "type": "welcome",
                "data": {
                    "client_id": client_id,
                    "server_time": time.time()
                }
            })
            
            # 处理客户端消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_client_message(websocket, data)
                except json.JSONDecodeError:
                    await self.send_error(websocket, "无效的JSON格式")
                except Exception as e:
                    logger.error(f"处理客户端消息失败: {str(e)}")
                    await self.send_error(websocket, str(e))
        
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端断开连接: {client_id}")
        except Exception as e:
            logger.error(f"客户端连接异常: {str(e)}")
        finally:
            self.clients.discard(websocket)
            # 清理订阅
            for subscription_set in self.subscriptions.values():
                subscription_set.discard(websocket)
    
    async def handle_client_message(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """处理客户端消息"""
        message_type = data.get("type")
        
        if message_type == "subscribe":
            # 订阅特定任务或批处理的进度
            target_id = data.get("target_id")
            target_type = data.get("target_type", "task")  # task 或 batch
            
            if target_id:
                subscription_key = f"{target_type}:{target_id}"
                if subscription_key not in self.subscriptions:
                    self.subscriptions[subscription_key] = set()
                self.subscriptions[subscription_key].add(websocket)
                
                # 发送当前状态（如果有缓存）
                if subscription_key in self.event_cache:
                    event = self.event_cache[subscription_key]
                    await self.send_to_client(websocket, event.to_dict())
                
                await self.send_to_client(websocket, {
                    "type": "subscribed",
                    "data": {
                        "target_id": target_id,
                        "target_type": target_type
                    }
                })
        
        elif message_type == "unsubscribe":
            # 取消订阅
            target_id = data.get("target_id")
            target_type = data.get("target_type", "task")
            
            if target_id:
                subscription_key = f"{target_type}:{target_id}"
                if subscription_key in self.subscriptions:
                    self.subscriptions[subscription_key].discard(websocket)
                
                await self.send_to_client(websocket, {
                    "type": "unsubscribed",
                    "data": {
                        "target_id": target_id,
                        "target_type": target_type
                    }
                })
        
        elif message_type == "ping":
            # 心跳响应
            await self.send_to_client(websocket, {
                "type": "pong",
                "data": {"timestamp": time.time()}
            })
    
    async def send_to_client(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """发送消息给客户端"""
        try:
            await websocket.send(json.dumps(data))
        except Exception as e:
            logger.error(f"发送消息失败: {str(e)}")
    
    async def send_error(self, websocket: WebSocketServerProtocol, error_message: str):
        """发送错误消息"""
        await self.send_to_client(websocket, {
            "type": "error",
            "data": {"message": error_message}
        })
    
    async def broadcast_task_progress(self, task_id: str, progress_data: Dict[str, Any]):
        """广播任务进度更新"""
        event = ProgressEvent("task_progress", {
            "task_id": task_id,
            **progress_data
        })
        
        subscription_key = f"task:{task_id}"
        await self._broadcast_to_subscribers(subscription_key, event)
        
        # 缓存事件
        self._cache_event(subscription_key, event)
    
    async def broadcast_batch_progress(self, batch_id: str, progress_data: Dict[str, Any]):
        """广播批处理进度更新"""
        event = ProgressEvent("batch_progress", {
            "batch_id": batch_id,
            **progress_data
        })
        
        subscription_key = f"batch:{batch_id}"
        await self._broadcast_to_subscribers(subscription_key, event)
        
        # 缓存事件
        self._cache_event(subscription_key, event)
    
    async def broadcast_task_status_change(self, task_id: str, old_status: str, new_status: str, 
                                         additional_data: Dict[str, Any] = None):
        """广播任务状态变化"""
        event = ProgressEvent("task_status_change", {
            "task_id": task_id,
            "old_status": old_status,
            "new_status": new_status,
            **(additional_data or {})
        })
        
        subscription_key = f"task:{task_id}"
        await self._broadcast_to_subscribers(subscription_key, event)
        
        # 缓存事件
        self._cache_event(subscription_key, event)
    
    async def broadcast_batch_status_change(self, batch_id: str, old_status: str, new_status: str,
                                          additional_data: Dict[str, Any] = None):
        """广播批处理状态变化"""
        event = ProgressEvent("batch_status_change", {
            "batch_id": batch_id,
            "old_status": old_status,
            "new_status": new_status,
            **(additional_data or {})
        })
        
        subscription_key = f"batch:{batch_id}"
        await self._broadcast_to_subscribers(subscription_key, event)
        
        # 缓存事件
        self._cache_event(subscription_key, event)
    
    async def _broadcast_to_subscribers(self, subscription_key: str, event: ProgressEvent):
        """向订阅者广播事件"""
        if subscription_key not in self.subscriptions:
            return
        
        subscribers = self.subscriptions[subscription_key].copy()
        if not subscribers:
            return
        
        message = event.to_dict()
        
        # 并发发送给所有订阅者
        tasks = []
        for websocket in subscribers:
            if websocket in self.clients:  # 确保连接仍然有效
                tasks.append(self.send_to_client(websocket, message))
            else:
                # 清理无效连接
                self.subscriptions[subscription_key].discard(websocket)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def _cache_event(self, key: str, event: ProgressEvent):
        """缓存事件"""
        self.event_cache[key] = event
        
        # 限制缓存大小
        if len(self.event_cache) > self.max_cache_size:
            # 删除最旧的事件
            oldest_key = min(self.event_cache.keys(), 
                           key=lambda k: self.event_cache[k].timestamp)
            del self.event_cache[oldest_key]
    
    def get_client_count(self) -> int:
        """获取连接的客户端数量"""
        return len(self.clients)
    
    def get_subscription_count(self) -> int:
        """获取订阅数量"""
        return sum(len(subscribers) for subscribers in self.subscriptions.values())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "is_running": self.is_running,
            "client_count": self.get_client_count(),
            "subscription_count": self.get_subscription_count(),
            "cached_events": len(self.event_cache),
            "subscriptions": {
                key: len(subscribers) 
                for key, subscribers in self.subscriptions.items()
            }
        }

# 全局进度监控服务实例
_progress_monitor_service = None

def get_progress_monitor_service() -> ProgressMonitorService:
    """获取进度监控服务实例"""
    global _progress_monitor_service
    
    if _progress_monitor_service is None:
        _progress_monitor_service = ProgressMonitorService()
    
    return _progress_monitor_service

async def initialize_progress_monitor():
    """初始化进度监控服务"""
    service = get_progress_monitor_service()
    await service.start_server()
    logger.info("进度监控服务初始化完成")
