# AI人声分离功能说明

## 概述

ThunderHub现在支持两种人声分离方法：

1. **传统方法 (ffmpeg)**: 基于声道操作的简单分离，速度快但效果有限
2. **AI方法 (librosa)**: 基于深度学习的高质量分离，效果显著但需要更多计算资源

## 安装AI依赖

在使用AI人声分离功能之前，需要先安装相关依赖。

### 重要：使用正确的Python环境

如果您的ThunderHub Core服务运行在虚拟环境中，请确保在**相同的虚拟环境**中安装依赖：

```bash
# 方法1: 激活虚拟环境后安装
cd core
# 激活虚拟环境（Windows）
venv\Scripts\activate
# 或者激活虚拟环境（Linux/Mac）
source venv/bin/activate

# 然后安装依赖
python install_ai_vocal_separation.py
```

```bash
# 方法2: 直接使用虚拟环境的Python
cd core
# Windows
venv\Scripts\python.exe install_ai_vocal_separation.py
# Linux/Mac
venv/bin/python install_ai_vocal_separation.py
```

### 验证安装

安装完成后，脚本会自动验证Demucs是否在当前Python环境中可用。您应该看到类似的输出：

```
✅ Demucs模块导入成功
✅ Demucs命令行工具测试成功!
🎉 AI人声分离依赖安装成功!
```

## 使用方法

### API调用

```python
# 使用传统方法（默认）
result = await audio_service.separate_vocals_batch(
    folder_path="/path/to/videos",
    separation_method="ffmpeg",  # 传统方法
    output_type="both"
)

# 使用AI方法（推荐）
result = await audio_service.separate_vocals_batch(
    folder_path="/path/to/videos", 
    separation_method="librosa",  # AI方法
    output_type="both"
)
```

### 前端界面

在人声分离界面中：
- **分离方法**: 选择 "librosa" 启用AI分离
- **输出类型**: 
  - `vocals`: 只输出人声版本
  - `instrumental`: 只输出伴奏版本  
  - `both`: 同时输出人声和伴奏版本

## 技术原理

### 传统方法 (FFmpeg)
- 基于左右声道的数学运算
- 假设人声在中央声道，伴奏在左右声道
- 优点：速度快，资源消耗少
- 缺点：效果有限，无法处理复杂混音

### AI方法 (Demucs)
- 使用Facebook开源的Demucs深度学习模型
- 基于混合变换器架构 (Hybrid Transformer)
- 能够智能识别和分离不同音频源
- 优点：分离效果显著，适用于各种音频
- 缺点：需要更多计算资源和时间

## 性能对比

| 方法 | 分离质量 | 处理速度 | 资源消耗 | 适用场景 |
|------|----------|----------|----------|----------|
| FFmpeg | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐ | 快速处理，要求不高 |
| Demucs AI | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ | 高质量分离，专业用途 |

## 故障排除

### 1. "Demucs不可用"错误

这通常是因为Demucs安装在了错误的Python环境中。解决方法：

```bash
# 检查当前Python环境
python -c "import sys; print('Python路径:', sys.executable)"

# 检查Demucs是否在当前环境中
python -c "import demucs; print('Demucs可用')"

# 如果失败，在当前环境中安装Demucs
pip install demucs torch torchaudio librosa soundfile
```

### 2. 虚拟环境问题

确保ThunderHub Core服务和Demucs使用相同的Python环境：

```bash
# 查看ThunderHub Core使用的Python
# 在Core服务日志中查找类似信息：
# "使用Python可执行文件: /path/to/python"

# 确保在相同环境中安装Demucs
/path/to/python -m pip install demucs
```

### 3. Demucs安装失败
```bash
# 手动安装完整依赖
pip install demucs torch torchaudio librosa soundfile
```

### 2. GPU加速（可选）
如果有NVIDIA GPU，可以安装CUDA版本的PyTorch以加速处理：
```bash
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 3. 内存不足
- 减少并发处理数量 (`max_concurrent`)
- 使用较小的Demucs模型
- 增加系统虚拟内存

## 注意事项

1. **首次使用**: AI方法首次运行时会下载模型文件（约100MB）
2. **处理时间**: AI方法比传统方法慢3-10倍，但效果显著提升
3. **存储空间**: AI分离会产生更多临时文件，确保有足够磁盘空间
4. **回退机制**: 如果AI分离失败，系统会自动回退到传统方法

## 更新日志

- **v1.3**: 修复Demucs输出路径检测问题
  - 智能查找Demucs输出文件
  - 支持多种模型输出结构
  - 修复base_name不一致问题
  - 增强调试信息
- **v1.2**: 修复虚拟环境Python路径问题
  - 使用sys.executable确保正确的Python环境
  - 改进环境检测和错误提示
- **v1.1**: 添加自动回退机制和错误处理
- **v1.0**: 实现基于Demucs的AI人声分离
