"""
AI模型管理服务
提供模型的上传、管理、版本控制和元数据管理功能
"""

import logging
import json
import os
import hashlib
import shutil
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class ModelType(Enum):
    """模型类型枚举"""
    CHECKPOINT = "checkpoint"
    LORA = "lora"
    VAE = "vae"
    CONTROLNET = "controlnet"
    UPSCALER = "upscaler"
    EMBEDDING = "embedding"

class ModelStatus(Enum):
    """模型状态枚举"""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

@dataclass
class ModelInfo:
    """模型信息"""
    id: str
    name: str
    type: ModelType
    version: str
    description: str = ""
    author: str = "unknown"
    file_path: str = ""
    file_size: int = 0
    file_hash: str = ""
    status: ModelStatus = ModelStatus.UPLOADING
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    created_at: str = ""
    updated_at: str = ""
    download_count: int = 0
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = self.created_at

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['type'] = self.type.value
        result['status'] = self.status.value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelInfo':
        """从字典创建实例"""
        data = data.copy()
        data['type'] = ModelType(data['type'])
        data['status'] = ModelStatus(data['status'])
        return cls(**data)

class ModelManagementService:
    """模型管理服务"""
    
    def __init__(self, models_dir: str = None):
        self.models_dir = models_dir or os.getenv("MODELS_DIR", "data/models")
        self.metadata_file = os.path.join(self.models_dir, "models_metadata.json")
        self._ensure_directories()
        self._load_metadata()
    
    def _ensure_directories(self):
        """确保模型目录存在"""
        for model_type in ModelType:
            type_dir = os.path.join(self.models_dir, model_type.value)
            os.makedirs(type_dir, exist_ok=True)
    
    def _load_metadata(self):
        """加载模型元数据"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.models = {
                        model_id: ModelInfo.from_dict(model_data)
                        for model_id, model_data in data.items()
                    }
            else:
                self.models = {}
                self._scan_existing_models()
        except Exception as e:
            logger.error(f"加载模型元数据失败: {str(e)}")
            self.models = {}
    
    def _save_metadata(self):
        """保存模型元数据"""
        try:
            data = {
                model_id: model.to_dict()
                for model_id, model in self.models.items()
            }
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存模型元数据失败: {str(e)}")
    
    def _scan_existing_models(self):
        """扫描现有模型文件"""
        """扫描模型目录中的现有文件并创建元数据"""
        for model_type in ModelType:
            type_dir = os.path.join(self.models_dir, model_type.value)
            if os.path.exists(type_dir):
                for file_path in Path(type_dir).glob("*.safetensors"):
                    if not any(m.file_path == str(file_path) for m in self.models.values()):
                        # 创建新的模型记录
                        model_info = self._create_model_from_file(file_path, model_type)
                        if model_info:
                            self.models[model_info.id] = model_info
        
        self._save_metadata()
    
    def _create_model_from_file(self, file_path: Path, model_type: ModelType) -> Optional[ModelInfo]:
        """从文件创建模型信息"""
        try:
            file_stats = file_path.stat()
            file_hash = self._calculate_file_hash(str(file_path))
            
            model_info = ModelInfo(
                id=f"{model_type.value}_{file_path.stem}_{file_hash[:8]}",
                name=file_path.stem,
                type=model_type,
                version="1.0.0",
                description=f"从文件扫描导入: {file_path.name}",
                file_path=str(file_path),
                file_size=file_stats.st_size,
                file_hash=file_hash,
                status=ModelStatus.ACTIVE
            )
            
            return model_info
        except Exception as e:
            logger.error(f"从文件创建模型信息失败: {str(e)}")
            return None
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {str(e)}")
            return ""
    
    def upload_model(self, file_path: str, model_type: ModelType, name: str, 
                    version: str = "1.0.0", description: str = "", 
                    author: str = "user", tags: List[str] = None) -> Optional[ModelInfo]:
        """上传模型文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 计算文件哈希
            file_hash = self._calculate_file_hash(file_path)
            file_size = os.path.getsize(file_path)
            
            # 检查是否已存在相同哈希的模型
            existing_model = self._find_model_by_hash(file_hash)
            if existing_model:
                logger.warning(f"模型已存在: {existing_model.name}")
                return existing_model
            
            # 生成模型ID
            model_id = f"{model_type.value}_{name}_{file_hash[:8]}"
            
            # 确定目标路径
            target_dir = os.path.join(self.models_dir, model_type.value)
            file_extension = Path(file_path).suffix
            target_path = os.path.join(target_dir, f"{name}{file_extension}")
            
            # 复制文件
            shutil.copy2(file_path, target_path)
            
            # 创建模型信息
            model_info = ModelInfo(
                id=model_id,
                name=name,
                type=model_type,
                version=version,
                description=description,
                author=author,
                file_path=target_path,
                file_size=file_size,
                file_hash=file_hash,
                status=ModelStatus.ACTIVE,
                tags=tags or []
            )
            
            # 保存到内存和文件
            self.models[model_id] = model_info
            self._save_metadata()
            
            logger.info(f"模型上传成功: {name} ({model_id})")
            return model_info
            
        except Exception as e:
            logger.error(f"模型上传失败: {str(e)}")
            return None
    
    def _find_model_by_hash(self, file_hash: str) -> Optional[ModelInfo]:
        """根据哈希查找模型"""
        for model in self.models.values():
            if model.file_hash == file_hash:
                return model
        return None
    
    def get_model(self, model_id: str) -> Optional[ModelInfo]:
        """获取模型信息"""
        return self.models.get(model_id)
    
    def get_models(self, model_type: ModelType = None, status: ModelStatus = None,
                  tags: List[str] = None) -> List[ModelInfo]:
        """获取模型列表"""
        models = list(self.models.values())
        
        if model_type:
            models = [m for m in models if m.type == model_type]
        
        if status:
            models = [m for m in models if m.status == status]
        
        if tags:
            models = [m for m in models if any(tag in m.tags for tag in tags)]
        
        return sorted(models, key=lambda m: m.updated_at, reverse=True)
    
    def update_model(self, model_id: str, **kwargs) -> Optional[ModelInfo]:
        """更新模型信息"""
        model = self.models.get(model_id)
        if not model:
            return None
        
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(model, key) and key not in ['id', 'file_hash', 'created_at']:
                setattr(model, key, value)
        
        model.updated_at = datetime.now().isoformat()
        self._save_metadata()
        
        logger.info(f"模型信息更新: {model.name} ({model_id})")
        return model
    
    def delete_model(self, model_id: str, remove_file: bool = False) -> bool:
        """删除模型"""
        model = self.models.get(model_id)
        if not model:
            return False
        
        try:
            # 删除文件（如果需要）
            if remove_file and os.path.exists(model.file_path):
                os.remove(model.file_path)
            
            # 从内存中删除
            del self.models[model_id]
            self._save_metadata()
            
            logger.info(f"模型删除成功: {model.name} ({model_id})")
            return True
            
        except Exception as e:
            logger.error(f"删除模型失败: {str(e)}")
            return False
    
    def get_model_statistics(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        stats = {
            "total_models": len(self.models),
            "by_type": {},
            "by_status": {},
            "total_size": 0
        }
        
        for model in self.models.values():
            # 按类型统计
            type_name = model.type.value
            stats["by_type"][type_name] = stats["by_type"].get(type_name, 0) + 1
            
            # 按状态统计
            status_name = model.status.value
            stats["by_status"][status_name] = stats["by_status"].get(status_name, 0) + 1
            
            # 总大小
            stats["total_size"] += model.file_size
        
        return stats

# 全局模型管理服务实例
_model_service = None

def get_model_service() -> ModelManagementService:
    """获取模型管理服务实例"""
    global _model_service
    
    if _model_service is None:
        _model_service = ModelManagementService()
    
    return _model_service
