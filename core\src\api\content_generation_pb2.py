# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: src/api/content_generation.proto
# Protobuf Python Version: 6.30.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    30,
    0,
    '',
    'src/api/content_generation.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n src/api/content_generation.proto\x12\x12\x63ontent_generation\"\x81\x03\n\x10GenerationConfig\x12\x18\n\x10\x63omfyui_workflow\x18\x01 \x01(\t\x12O\n\x0emodel_settings\x18\x02 \x03(\x0b\x32\x37.content_generation.GenerationConfig.ModelSettingsEntry\x12Q\n\x0foutput_settings\x18\x03 \x03(\x0b\x32\x38.content_generation.GenerationConfig.OutputSettingsEntry\x12\x10\n\x08\x64uration\x18\x04 \x01(\x05\x12\x12\n\nresolution\x18\x05 \x01(\t\x12\x0b\n\x03\x66ps\x18\x06 \x01(\x05\x12\x0f\n\x07quality\x18\x07 \x01(\t\x1a\x34\n\x12ModelSettingsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x35\n\x13OutputSettingsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xcc\x01\n\x1b\x43reateGenerationTaskRequest\x12\x12\n\naccount_id\x18\x01 \x01(\t\x12\x13\n\x0bplatform_id\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\r\n\x05style\x18\x06 \x01(\t\x12\r\n\x05\x63ount\x18\x07 \x01(\x05\x12\x34\n\x06\x63onfig\x18\x08 \x01(\x0b\x32$.content_generation.GenerationConfig\"O\n\x1c\x43reateGenerationTaskResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07task_id\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"A\n\x1aStartGenerationTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\t\"=\n\x1bStartGenerationTaskResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"E\n\x1eGetGenerationTaskStatusRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\t\"\xb4\x01\n\x14GenerationTaskStatus\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x10\n\x08progress\x18\x03 \x01(\x02\x12\x14\n\x0c\x63urrent_step\x18\x04 \x01(\t\x12\x15\n\rerror_message\x18\x05 \x01(\t\x12\x12\n\ncreated_at\x18\x06 \x01(\t\x12\x12\n\nupdated_at\x18\x07 \x01(\t\x12\x14\n\x0c\x63ompleted_at\x18\x08 \x01(\t\"{\n\x1fGetGenerationTaskStatusResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x38\n\x06status\x18\x02 \x01(\x0b\x32(.content_generation.GenerationTaskStatus\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"B\n\x1b\x43\x61ncelGenerationTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\t\">\n\x1c\x43\x61ncelGenerationTaskResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"\x9b\x02\n\x10GenerationResult\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x11\n\tfile_path\x18\x04 \x01(\t\x12\x11\n\tfile_size\x18\x05 \x01(\x03\x12\x13\n\x0bpreview_url\x18\x06 \x01(\t\x12\x14\n\x0c\x64ownload_url\x18\x07 \x01(\t\x12\x16\n\x0ethumbnail_path\x18\x08 \x01(\t\x12\x44\n\x08metadata\x18\t \x03(\x0b\x32\x32.content_generation.GenerationResult.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"B\n\x1bGetGenerationResultsRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\t\"u\n\x1cGetGenerationResultsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x35\n\x07results\x18\x02 \x03(\x0b\x32$.content_generation.GenerationResult\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"E\n\x1c\x44ownloadGeneratedFileRequest\x12\x11\n\tresult_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\t\"z\n\x1d\x44ownloadGeneratedFileResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x11\n\tfile_data\x18\x02 \x01(\x0c\x12\x10\n\x08\x66ilename\x18\x03 \x01(\t\x12\x14\n\x0c\x63ontent_type\x18\x04 \x01(\t\x12\r\n\x05\x65rror\x18\x05 \x01(\t\"\x1e\n\x1cTestComfyUIConnectionRequest\"\x8a\x01\n\x1dTestComfyUIConnectionResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\x12\x12\n\nqueue_size\x18\x04 \x01(\x05\x12\x14\n\x0c\x61\x63tive_tasks\x18\x05 \x01(\x05\x12\r\n\x05\x65rror\x18\x06 \x01(\t\"H\n\tModelInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\"\x1b\n\x19GetAvailableModelsRequest\"\xda\x01\n\x1aGetAvailableModelsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x32\n\x0btext_models\x18\x02 \x03(\x0b\x32\x1d.content_generation.ModelInfo\x12\x33\n\x0cimage_models\x18\x03 \x03(\x0b\x32\x1d.content_generation.ModelInfo\x12\x33\n\x0cvideo_models\x18\x04 \x03(\x0b\x32\x1d.content_generation.ModelInfo\x12\r\n\x05\x65rror\x18\x05 \x01(\t2\xf9\x07\n\x18\x43ontentGenerationService\x12y\n\x14\x43reateGenerationTask\x12/.content_generation.CreateGenerationTaskRequest\x1a\x30.content_generation.CreateGenerationTaskResponse\x12v\n\x13StartGenerationTask\x12..content_generation.StartGenerationTaskRequest\x1a/.content_generation.StartGenerationTaskResponse\x12\x82\x01\n\x17GetGenerationTaskStatus\x12\x32.content_generation.GetGenerationTaskStatusRequest\x1a\x33.content_generation.GetGenerationTaskStatusResponse\x12y\n\x14\x43\x61ncelGenerationTask\x12/.content_generation.CancelGenerationTaskRequest\x1a\x30.content_generation.CancelGenerationTaskResponse\x12y\n\x14GetGenerationResults\x12/.content_generation.GetGenerationResultsRequest\x1a\x30.content_generation.GetGenerationResultsResponse\x12|\n\x15\x44ownloadGeneratedFile\x12\x30.content_generation.DownloadGeneratedFileRequest\x1a\x31.content_generation.DownloadGeneratedFileResponse\x12|\n\x15TestComfyUIConnection\x12\x30.content_generation.TestComfyUIConnectionRequest\x1a\x31.content_generation.TestComfyUIConnectionResponse\x12s\n\x12GetAvailableModels\x12-.content_generation.GetAvailableModelsRequest\x1a..content_generation.GetAvailableModelsResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'src.api.content_generation_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_GENERATIONCONFIG_MODELSETTINGSENTRY']._loaded_options = None
  _globals['_GENERATIONCONFIG_MODELSETTINGSENTRY']._serialized_options = b'8\001'
  _globals['_GENERATIONCONFIG_OUTPUTSETTINGSENTRY']._loaded_options = None
  _globals['_GENERATIONCONFIG_OUTPUTSETTINGSENTRY']._serialized_options = b'8\001'
  _globals['_GENERATIONRESULT_METADATAENTRY']._loaded_options = None
  _globals['_GENERATIONRESULT_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_GENERATIONCONFIG']._serialized_start=57
  _globals['_GENERATIONCONFIG']._serialized_end=442
  _globals['_GENERATIONCONFIG_MODELSETTINGSENTRY']._serialized_start=335
  _globals['_GENERATIONCONFIG_MODELSETTINGSENTRY']._serialized_end=387
  _globals['_GENERATIONCONFIG_OUTPUTSETTINGSENTRY']._serialized_start=389
  _globals['_GENERATIONCONFIG_OUTPUTSETTINGSENTRY']._serialized_end=442
  _globals['_CREATEGENERATIONTASKREQUEST']._serialized_start=445
  _globals['_CREATEGENERATIONTASKREQUEST']._serialized_end=649
  _globals['_CREATEGENERATIONTASKRESPONSE']._serialized_start=651
  _globals['_CREATEGENERATIONTASKRESPONSE']._serialized_end=730
  _globals['_STARTGENERATIONTASKREQUEST']._serialized_start=732
  _globals['_STARTGENERATIONTASKREQUEST']._serialized_end=797
  _globals['_STARTGENERATIONTASKRESPONSE']._serialized_start=799
  _globals['_STARTGENERATIONTASKRESPONSE']._serialized_end=860
  _globals['_GETGENERATIONTASKSTATUSREQUEST']._serialized_start=862
  _globals['_GETGENERATIONTASKSTATUSREQUEST']._serialized_end=931
  _globals['_GENERATIONTASKSTATUS']._serialized_start=934
  _globals['_GENERATIONTASKSTATUS']._serialized_end=1114
  _globals['_GETGENERATIONTASKSTATUSRESPONSE']._serialized_start=1116
  _globals['_GETGENERATIONTASKSTATUSRESPONSE']._serialized_end=1239
  _globals['_CANCELGENERATIONTASKREQUEST']._serialized_start=1241
  _globals['_CANCELGENERATIONTASKREQUEST']._serialized_end=1307
  _globals['_CANCELGENERATIONTASKRESPONSE']._serialized_start=1309
  _globals['_CANCELGENERATIONTASKRESPONSE']._serialized_end=1371
  _globals['_GENERATIONRESULT']._serialized_start=1374
  _globals['_GENERATIONRESULT']._serialized_end=1657
  _globals['_GENERATIONRESULT_METADATAENTRY']._serialized_start=1610
  _globals['_GENERATIONRESULT_METADATAENTRY']._serialized_end=1657
  _globals['_GETGENERATIONRESULTSREQUEST']._serialized_start=1659
  _globals['_GETGENERATIONRESULTSREQUEST']._serialized_end=1725
  _globals['_GETGENERATIONRESULTSRESPONSE']._serialized_start=1727
  _globals['_GETGENERATIONRESULTSRESPONSE']._serialized_end=1844
  _globals['_DOWNLOADGENERATEDFILEREQUEST']._serialized_start=1846
  _globals['_DOWNLOADGENERATEDFILEREQUEST']._serialized_end=1915
  _globals['_DOWNLOADGENERATEDFILERESPONSE']._serialized_start=1917
  _globals['_DOWNLOADGENERATEDFILERESPONSE']._serialized_end=2039
  _globals['_TESTCOMFYUICONNECTIONREQUEST']._serialized_start=2041
  _globals['_TESTCOMFYUICONNECTIONREQUEST']._serialized_end=2071
  _globals['_TESTCOMFYUICONNECTIONRESPONSE']._serialized_start=2074
  _globals['_TESTCOMFYUICONNECTIONRESPONSE']._serialized_end=2212
  _globals['_MODELINFO']._serialized_start=2214
  _globals['_MODELINFO']._serialized_end=2286
  _globals['_GETAVAILABLEMODELSREQUEST']._serialized_start=2288
  _globals['_GETAVAILABLEMODELSREQUEST']._serialized_end=2315
  _globals['_GETAVAILABLEMODELSRESPONSE']._serialized_start=2318
  _globals['_GETAVAILABLEMODELSRESPONSE']._serialized_end=2536
  _globals['_CONTENTGENERATIONSERVICE']._serialized_start=2539
  _globals['_CONTENTGENERATIONSERVICE']._serialized_end=3556
# @@protoc_insertion_point(module_scope)
