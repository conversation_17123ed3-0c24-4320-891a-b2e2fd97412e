server {
    server_name _;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 主页面和路由处理
    location / {
        try_files $uri $uri/ /index.html;
        # 对于 HTML 文件，不缓存以确保获取最新版本
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
    }

    # 静态资源缓存策略
    location ~* \.(js|css)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public, immutable";
        # 如果文件不存在，返回404而不是重定向到index.html
        try_files $uri =404;
    }

    # 图片和字体文件
    location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public, immutable";
    }

    # 对于 assets 目录下的文件，如果不存在则返回404
    location /assets/ {
        try_files $uri =404;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 错误页面处理
    error_page 404 /index.html;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}