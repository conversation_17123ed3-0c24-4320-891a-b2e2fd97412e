# AI内容生成与社交账号关联功能说明

## 🎯 功能概述

现在AI内容生成功能已经与社交媒体账号直接关联，用户可以：
- 创建任务时直接选择目标社交账号
- 按账号维度筛选和查看任务
- 生成完成后自动发布到指定账号

## 📋 已实现功能

### 1. 任务模型扩展 ✅
- 在 `GenerationTask` 中添加了账号关联字段：
  - `account_id`: 目标社交账号ID
  - `platform_id`: 目标平台ID  
  - `account_name`: 账号显示名称
  - `auto_publish`: 是否自动发布

### 2. 创建任务时选择账号 ✅
- 在创建任务对话框中添加了账号选择下拉框
- 支持按平台分组显示账号
- 可选择是否自动发布

### 3. 任务列表筛选功能 ✅
- 按平台筛选任务
- 按具体账号筛选任务
- 按任务状态筛选
- 任务项显示关联的账号名称

### 4. 后端API支持 ✅
- 扩展了创建任务API，支持账号参数
- 添加了获取可用账号列表API
- 任务查询API支持账号筛选参数

## 🚀 使用方法

### 创建关联账号的任务

1. 点击"创建任务"按钮
2. 选择生成类型（图片、视频、文本、分镜脚本）
3. 填写任务基本信息（标题、描述、风格等）
4. 根据类型配置特定参数：
   - **图片生成**：尺寸、生成步数
   - **视频生成**：时长、分辨率
   - **文本生成**：长度、类型
   - **分镜脚本**：场景数量、每场景时长
5. 在"目标账号"下拉框中选择要发布的社交媒体账号
6. 可选择"生成完成后自动发布"
7. 点击"开始生成"

### 按账号筛选任务

1. 在左侧任务列表上方的筛选器中：
   - 选择特定平台（如YouTube、抖音）
   - 选择具体账号
   - 选择任务状态
2. 任务列表会实时筛选显示

### API调用示例

```javascript
// 创建关联账号的任务
const taskData = {
  title: "美食制作视频",
  type: "video",
  description: "生成家常菜制作教程",
  account_id: "youtube_account_123",
  platform_id: "youtube",
  auto_publish: true
}

const response = await createGenerationTask(taskData)
```

```javascript
// 获取可用账号列表
const accounts = await getAvailableAccounts()
// 返回格式：
// {
//   platforms: {
//     "youtube": [{ id: "123", display_name: "我的频道", ... }],
//     "douyin": [{ id: "456", display_name: "抖音号", ... }]
//   },
//   platform_info: {
//     "youtube": { name: "YouTube", icon: "youtube.png" }
//   }
// }
```

```javascript
// 按账号筛选任务
const tasks = await getGenerationTasks({
  account_id: "youtube_account_123",
  platform_id: "youtube",
  status: "completed"
})
```

## 🔄 数据流程

1. **任务创建**：用户选择账号 → 任务保存账号关联信息
2. **内容生成**：AI生成内容 → 保存生成结果
3. **自动发布**：如果启用自动发布 → 调用平台适配器发布内容
4. **任务管理**：用户可按账号维度查看和管理任务

## 📊 数据库结构

### content_generation_tasks 集合
```javascript
{
  "id": "task_123",
  "title": "任务标题",
  "type": "video",
  "description": "任务描述",
  "account_id": "youtube_account_123",  // 新增
  "platform_id": "youtube",            // 新增
  "account_name": "我的YouTube频道",     // 新增
  "auto_publish": true,                 // 新增
  "status": "completed",
  "results": [...]
}
```

## 🎨 界面展示

### 创建任务界面
- 新增"目标账号"选择器，按平台分组显示
- 新增"自动发布"复选框

### 任务列表界面  
- 新增平台和账号筛选器
- 任务项显示关联的账号标签

### 4. 账号维度的任务管理 ✅
- 支持列表视图和账号视图两种模式
- 账号视图按账号分组显示任务，包含统计信息
- 批量操作功能：支持多选、全选、批量删除
- 账号任务详情查看功能

### 5. 核心生成功能实现 ✅
- **ComfyUI真实接口对接**：替换模拟实现，实现真实的ComfyUI API调用
- **多类型内容生成**：支持图片、视频、文本、分镜脚本四种生成类型
- **工作流模板系统**：为不同类型生成提供专门的ComfyUI工作流
- **参数配置系统**：根据生成类型显示不同的配置选项

## 🔧 技术实现要点

1. **简单直接的关联**：不使用复杂的关联表，直接在任务中存储账号ID
2. **前端组件化**：账号选择器支持平台分组和搜索
3. **API扩展**：保持向后兼容，新增字段为可选
4. **实时筛选**：前端计算属性实现实时筛选效果
5. **双视图模式**：列表视图和账号分组视图满足不同使用场景
6. **批量操作**：支持复选框选择和批量删除功能

## 📈 已实现的高级功能

✅ **批量任务管理**：支持多选任务进行批量删除
✅ **账号统计视图**：显示每个账号的任务数量和状态分布
✅ **任务详情查看**：可查看特定账号的任务详情
✅ **双视图切换**：列表视图和账号分组视图
✅ **真实ComfyUI集成**：完整的ComfyUI API对接，支持实际的AI生成
✅ **多类型生成支持**：图片、视频、文本、分镜脚本四种类型
✅ **智能参数配置**：根据生成类型动态显示相关配置选项
✅ **工作流模板管理**：预设多种ComfyUI工作流模板

## 🎯 核心优势

1. **简单直观**：用户创建任务时直接选择目标账号
2. **高效管理**：按账号维度组织和查看任务
3. **自动化**：支持生成完成后自动发布
4. **灵活筛选**：多维度筛选任务列表
5. **无缝集成**：充分利用现有的社交账号管理系统
6. **批量操作**：提高任务管理效率
7. **统计分析**：直观显示账号任务分布情况
