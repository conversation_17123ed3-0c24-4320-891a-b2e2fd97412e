# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from src.api import content_generation_pb2 as src_dot_api_dot_content__generation__pb2

GRPC_GENERATED_VERSION = '1.72.0rc1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in src/api/content_generation_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ContentGenerationServiceStub(object):
    """内容生成服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateGenerationTask = channel.unary_unary(
                '/content_generation.ContentGenerationService/CreateGenerationTask',
                request_serializer=src_dot_api_dot_content__generation__pb2.CreateGenerationTaskRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.CreateGenerationTaskResponse.FromString,
                _registered_method=True)
        self.StartGenerationTask = channel.unary_unary(
                '/content_generation.ContentGenerationService/StartGenerationTask',
                request_serializer=src_dot_api_dot_content__generation__pb2.StartGenerationTaskRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.StartGenerationTaskResponse.FromString,
                _registered_method=True)
        self.GetGenerationTaskStatus = channel.unary_unary(
                '/content_generation.ContentGenerationService/GetGenerationTaskStatus',
                request_serializer=src_dot_api_dot_content__generation__pb2.GetGenerationTaskStatusRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.GetGenerationTaskStatusResponse.FromString,
                _registered_method=True)
        self.CancelGenerationTask = channel.unary_unary(
                '/content_generation.ContentGenerationService/CancelGenerationTask',
                request_serializer=src_dot_api_dot_content__generation__pb2.CancelGenerationTaskRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.CancelGenerationTaskResponse.FromString,
                _registered_method=True)
        self.GetGenerationResults = channel.unary_unary(
                '/content_generation.ContentGenerationService/GetGenerationResults',
                request_serializer=src_dot_api_dot_content__generation__pb2.GetGenerationResultsRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.GetGenerationResultsResponse.FromString,
                _registered_method=True)
        self.DownloadGeneratedFile = channel.unary_unary(
                '/content_generation.ContentGenerationService/DownloadGeneratedFile',
                request_serializer=src_dot_api_dot_content__generation__pb2.DownloadGeneratedFileRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.DownloadGeneratedFileResponse.FromString,
                _registered_method=True)
        self.TestComfyUIConnection = channel.unary_unary(
                '/content_generation.ContentGenerationService/TestComfyUIConnection',
                request_serializer=src_dot_api_dot_content__generation__pb2.TestComfyUIConnectionRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.TestComfyUIConnectionResponse.FromString,
                _registered_method=True)
        self.GetAvailableModels = channel.unary_unary(
                '/content_generation.ContentGenerationService/GetAvailableModels',
                request_serializer=src_dot_api_dot_content__generation__pb2.GetAvailableModelsRequest.SerializeToString,
                response_deserializer=src_dot_api_dot_content__generation__pb2.GetAvailableModelsResponse.FromString,
                _registered_method=True)


class ContentGenerationServiceServicer(object):
    """内容生成服务
    """

    def CreateGenerationTask(self, request, context):
        """创建内容生成任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartGenerationTask(self, request, context):
        """启动内容生成任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetGenerationTaskStatus(self, request, context):
        """获取任务状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelGenerationTask(self, request, context):
        """取消任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetGenerationResults(self, request, context):
        """获取任务结果
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DownloadGeneratedFile(self, request, context):
        """下载生成的文件
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TestComfyUIConnection(self, request, context):
        """测试ComfyUI连接
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableModels(self, request, context):
        """获取可用模型
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ContentGenerationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateGenerationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateGenerationTask,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.CreateGenerationTaskRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.CreateGenerationTaskResponse.SerializeToString,
            ),
            'StartGenerationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StartGenerationTask,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.StartGenerationTaskRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.StartGenerationTaskResponse.SerializeToString,
            ),
            'GetGenerationTaskStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGenerationTaskStatus,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.GetGenerationTaskStatusRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.GetGenerationTaskStatusResponse.SerializeToString,
            ),
            'CancelGenerationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelGenerationTask,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.CancelGenerationTaskRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.CancelGenerationTaskResponse.SerializeToString,
            ),
            'GetGenerationResults': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGenerationResults,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.GetGenerationResultsRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.GetGenerationResultsResponse.SerializeToString,
            ),
            'DownloadGeneratedFile': grpc.unary_unary_rpc_method_handler(
                    servicer.DownloadGeneratedFile,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.DownloadGeneratedFileRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.DownloadGeneratedFileResponse.SerializeToString,
            ),
            'TestComfyUIConnection': grpc.unary_unary_rpc_method_handler(
                    servicer.TestComfyUIConnection,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.TestComfyUIConnectionRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.TestComfyUIConnectionResponse.SerializeToString,
            ),
            'GetAvailableModels': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableModels,
                    request_deserializer=src_dot_api_dot_content__generation__pb2.GetAvailableModelsRequest.FromString,
                    response_serializer=src_dot_api_dot_content__generation__pb2.GetAvailableModelsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'content_generation.ContentGenerationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('content_generation.ContentGenerationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ContentGenerationService(object):
    """内容生成服务
    """

    @staticmethod
    def CreateGenerationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/CreateGenerationTask',
            src_dot_api_dot_content__generation__pb2.CreateGenerationTaskRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.CreateGenerationTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartGenerationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/StartGenerationTask',
            src_dot_api_dot_content__generation__pb2.StartGenerationTaskRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.StartGenerationTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetGenerationTaskStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/GetGenerationTaskStatus',
            src_dot_api_dot_content__generation__pb2.GetGenerationTaskStatusRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.GetGenerationTaskStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelGenerationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/CancelGenerationTask',
            src_dot_api_dot_content__generation__pb2.CancelGenerationTaskRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.CancelGenerationTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetGenerationResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/GetGenerationResults',
            src_dot_api_dot_content__generation__pb2.GetGenerationResultsRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.GetGenerationResultsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DownloadGeneratedFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/DownloadGeneratedFile',
            src_dot_api_dot_content__generation__pb2.DownloadGeneratedFileRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.DownloadGeneratedFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TestComfyUIConnection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/TestComfyUIConnection',
            src_dot_api_dot_content__generation__pb2.TestComfyUIConnectionRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.TestComfyUIConnectionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAvailableModels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/content_generation.ContentGenerationService/GetAvailableModels',
            src_dot_api_dot_content__generation__pb2.GetAvailableModelsRequest.SerializeToString,
            src_dot_api_dot_content__generation__pb2.GetAvailableModelsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
