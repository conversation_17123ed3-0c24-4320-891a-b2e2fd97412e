"""
通用元素查找器
支持配置文件驱动的元素定位和操作
"""

import asyncio
import logging
import yaml
from typing import Dict, List, Any, Optional
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from appium.webdriver.common.appiumby import AppiumBy

logger = logging.getLogger(__name__)


class ElementFinder:
    """配置驱动的元素查找器"""

    def __init__(self, config_path: str, uploader=None):
        """初始化元素查找器

        Args:
            config_path: 配置文件路径
            uploader: 上传器实例，用于UiAutomator2恢复
        """
        self.config_path = config_path
        self.uploader = uploader
        self.config = self._load_config()
        self.elements = self.config.get('elements', {})
        self.wait_times = self.config.get('wait_times', {})
        self.retry_config = self.config.get('retry_config', {})

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            logger.info(f"尝试加载配置文件: {self.config_path}")
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"✅ 成功加载元素配置: {self.config_path}")
                logger.info(f"配置文件包含的元素: {list(config.get('elements', {}).keys())}")
                return config
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {self.config_path}, 错误: {e}")
            return {}

    async def find_and_click(self, driver, element_name: str, max_retries: int = 3, retry_delay: float = 3.0, **kwargs) -> bool:
        """查找并点击元素（带重试机制）

        Args:
            driver: Appium驱动
            element_name: 元素名称
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            **kwargs: 模板参数（如filename等）

        Returns:
            bool: 是否成功点击
        """
        if element_name not in self.elements:
            logger.error(f"配置中未找到元素: {element_name}")
            return False

        element_configs = self.elements[element_name]
        logger.info(f"🔍 查找并点击元素: {element_name}")



        # 🔧 智能重试机制
        for retry in range(max_retries):
            if retry > 0:
                # 🔧 渐进式重试延迟：第一次重试短一些，后续逐渐增加
                smart_retry_delay = retry_delay + (retry - 1) * 2.0
                logger.info(f"🔄 第 {retry + 1} 次尝试查找元素: {element_name} (等待 {smart_retry_delay} 秒)")
                await asyncio.sleep(smart_retry_delay)

                # 🔧 在重试前等待UI稳定
                if element_name in ["finish_editing_button", "add_music_button"]:
                    logger.info("⏳ 等待UI稳定...")
                    await asyncio.sleep(2.0)

            for i, config in enumerate(element_configs, 1):
                try:
                    logger.info(f"🔍 方法{i}：{config.get('description', config['type'])}")

                    element = await self._find_element(driver, config, element_name, **kwargs)
                    if element:
                        # 检查是否是坐标点击（已经执行了点击操作）
                        if element == "coordinate_clicked":
                            logger.info(f"✅ 成功点击 {element_name} (方法{i}: {config['type']} - 坐标点击, 第{retry + 1}次尝试)")
                        else:
                            # 普通元素点击 - 增加详细日志和智能点击
                            try:
                                # 记录元素信息
                                element_info = f"元素类型: {type(element).__name__}"
                                if hasattr(element, 'tag_name'):
                                    element_info += f", 标签: {element.tag_name}"
                                if hasattr(element, 'get_attribute'):
                                    try:
                                        resource_id = element.get_attribute('resource-id')
                                        content_desc = element.get_attribute('content-desc')
                                        clickable = element.get_attribute('clickable')
                                        element_info += f", resource-id: {resource_id}, content-desc: {content_desc}, clickable: {clickable}"
                                    except:
                                        pass

                                logger.info(f"🎯 即将点击元素 {element_name}: {element_info}")

                                # 检查元素是否可点击
                                is_clickable = False
                                try:
                                    clickable_attr = element.get_attribute('clickable')
                                    is_clickable = clickable_attr == 'true'
                                except:
                                    is_clickable = True  # 如果无法获取属性，假设可点击

                                if not is_clickable and element_name == "privacy_button":
                                    # 对于隐私按钮，如果元素不可点击，尝试查找可点击的子元素
                                    logger.warning(f"⚠️ 元素 {element_name} 不可点击，尝试查找可点击的子元素")
                                    clickable_child = await self._find_clickable_child(element)
                                    if clickable_child:
                                        logger.info(f"✅ 找到可点击的子元素，使用子元素进行点击")
                                        element = clickable_child
                                    else:
                                        logger.warning(f"⚠️ 未找到可点击的子元素，尝试直接点击")

                                # 执行点击
                                element.click()
                                logger.info(f"✅ 成功点击 {element_name} (方法{i}: {config['type']}, 第{retry + 1}次尝试)")

                            except Exception as click_error:
                                logger.error(f"❌ 点击元素失败 {element_name}: {str(click_error)}")
                                raise click_error

                        # 🔧 智能等待：根据元素类型调整等待时间
                        wait_time = self._get_smart_wait_time(element_name)
                        logger.info(f"⏳ 点击后等待 {wait_time} 秒 (元素: {element_name})")
                        await asyncio.sleep(wait_time)

                        # 🔧 新增：验证点击效果（对于关键元素）
                        if await self._should_verify_click_effect(element_name):
                            await self._verify_click_effect(driver, element_name)

                        return True

                except Exception as e:
                    error_msg = str(e)
                    logger.warning(f"❌ 方法{i}失败 ({config['type']}): {error_msg}")

                    # 对于特定元素，提供更详细的调试信息
                    if element_name == "finish_editing_button" and "NoSuchElementError" in error_msg:
                        logger.debug(f"🔍 完成按钮查找失败详情:")
                        logger.debug(f"   - 查找方式: {config['type']}")
                        logger.debug(f"   - 查找值: {config['value']}")
                        logger.debug(f"   - 超时时间: {config.get('timeout', 'N/A')}秒")

                        # 尝试查找页面上所有的Button元素
                        try:
                            all_buttons = driver.find_elements("xpath", "//android.widget.Button")
                            logger.debug(f"   - 页面上共有 {len(all_buttons)} 个Button元素")
                            for idx, btn in enumerate(all_buttons[:5]):  # 只显示前5个
                                try:
                                    btn_text = btn.get_attribute('text') or ''
                                    btn_desc = btn.get_attribute('content-desc') or ''
                                    btn_id = btn.get_attribute('resource-id') or ''
                                    logger.debug(f"     Button{idx}: text='{btn_text}', desc='{btn_desc}', id='{btn_id}'")
                                except:
                                    pass
                        except Exception as debug_error:
                            logger.debug(f"   - 调试查找失败: {str(debug_error)}")

                    continue

            # 如果这次尝试失败，等待一下再重试
            if retry < max_retries - 1:
                logger.warning(f"⚠️ 第 {retry + 1} 次尝试失败，{retry_delay} 秒后重试...")

        logger.error(f"❌ 所有方法和重试都失败: {element_name}")
        return False

    async def _find_clickable_child(self, parent_element):
        """在父元素中查找可点击的子元素"""
        try:
            # 尝试查找所有子元素
            children = parent_element.find_elements("xpath", ".//*[@clickable='true']")
            if children:
                logger.info(f"🔍 在父元素中找到 {len(children)} 个可点击的子元素")
                # 返回第一个可点击的子元素
                return children[0]
            else:
                # 如果没有找到可点击的子元素，尝试查找所有子元素
                all_children = parent_element.find_elements("xpath", ".//*")
                logger.info(f"🔍 父元素共有 {len(all_children)} 个子元素")
                for child in all_children:
                    try:
                        clickable = child.get_attribute('clickable')
                        if clickable == 'true':
                            logger.info(f"✅ 找到可点击的子元素")
                            return child
                    except:
                        continue

            return None
        except Exception as e:
            logger.warning(f"⚠️ 查找可点击子元素失败: {str(e)}")
            return None

    async def find_element(self, driver, element_name: str, wait_for_real_element: bool = False, **kwargs):
        """仅查找元素，不执行操作

        Args:
            driver: Appium驱动
            element_name: 元素名称
            wait_for_real_element: 是否只查找真实元素（跳过坐标定位）
            **kwargs: 模板参数

        Returns:
            WebElement or None: 找到的元素
        """
        if element_name not in self.elements:
            logger.error(f"配置中未找到元素: {element_name}")
            return None

        element_configs = self.elements[element_name]
        logger.info(f"🔍 查找元素: {element_name}")

        for i, config in enumerate(element_configs, 1):
            try:
                # 如果是等待真实元素，跳过坐标定位
                if wait_for_real_element and config['type'] in ['coordinate', 'coordinate_relative']:
                    logger.debug(f"⏭️ 跳过坐标定位方法{i}：{config.get('description', config['type'])}")
                    continue

                logger.debug(f"🔍 方法{i}：{config.get('description', config['type'])}")
                element = await self._find_element(driver, config, element_name, **kwargs)
                if element and element != "coordinate_clicked":
                    # 额外检查元素是否真实可见和可点击
                    if wait_for_real_element:
                        try:
                            is_displayed = element.is_displayed()
                            is_enabled = element.is_enabled()
                            if is_displayed and is_enabled:
                                logger.info(f"✅ 找到真实可见元素 {element_name} (方法{i}: {config['type']})")
                                return element
                            else:
                                logger.debug(f"⚠️ 元素存在但不可见或不可用: displayed={is_displayed}, enabled={is_enabled}")
                                continue
                        except Exception as check_error:
                            logger.debug(f"⚠️ 检查元素可见性失败: {str(check_error)}")
                            continue
                    else:
                        logger.info(f"✅ 找到元素 {element_name} (方法{i}: {config['type']})")
                        return element

            except Exception as e:
                logger.debug(f"❌ 方法{i}失败 ({config['type']}): {str(e)}")
                continue

        logger.error(f"❌ 未找到元素: {element_name}")
        return None

    async def _find_element(self, driver, config: Dict[str, Any], element_name: str = "", **kwargs):
        """根据配置查找单个元素

        Args:
            driver: Appium驱动
            config: 元素配置
            element_name: 元素名称（用于智能策略）
            **kwargs: 模板参数

        Returns:
            WebElement or None: 找到的元素
        """
        element_type = config['type']
        value = config['value']
        timeout = config.get('timeout', 10)

        # 处理模板参数
        if isinstance(value, str) and '{' in value:
            try:
                original_value = value
                value = value.format(**kwargs)
                logger.info(f"🔧 模板替换: {original_value} -> {value}")
                logger.info(f"🔧 模板参数: {kwargs}")
            except KeyError as e:
                logger.warning(f"模板参数缺失: {e}")
                return None

        # 验证和修复XPath表达式
        if element_type in ['xpath', 'xpath_template']:
            value = self._validate_and_fix_xpath(value)

        try:
            return await self._find_element_with_recovery(driver, element_type, value, timeout)
        except Exception as e:
            error_msg = str(e)
            logger.debug(f"查找元素失败 ({element_type}): {error_msg}")

            # 🔧 检查是否是UiAutomator2崩溃
            if self._is_uiautomator2_crash_error(error_msg):
                logger.warning(f"🔧 检测到UiAutomator2崩溃，尝试恢复...")
                recovery_success = await self._attempt_uiautomator2_recovery(driver)
                if recovery_success:
                    logger.info("🔧 UiAutomator2恢复成功，重试查找元素...")
                    try:
                        # 获取恢复后的新driver实例
                        new_driver = self._get_recovered_driver()
                        if new_driver:
                            logger.info("✅ 获取到恢复后的新driver实例")
                            return await self._find_element_with_recovery(new_driver, element_type, value, timeout)
                        else:
                            logger.error("❌ 无法获取恢复后的driver实例")
                            raise Exception("UiAutomator2恢复后无法获取新的driver实例")
                    except Exception as retry_error:
                        logger.error(f"UiAutomator2恢复后重试失败: {str(retry_error)}")
                        # 恢复后重试仍然失败，抛出异常
                        raise Exception(f"UiAutomator2恢复后仍然无法操作设备: {str(retry_error)}")
                else:
                    # 恢复失败，抛出异常让上层知道设备不可用
                    raise Exception(f"UiAutomator2恢复失败，设备连接不可用: {error_msg}")

            return None

    def _is_uiautomator2_crash_error(self, error_message: str) -> bool:
        """检查错误是否是UiAutomator2崩溃

        Args:
            error_message: 错误消息

        Returns:
            bool: 是否是UiAutomator2崩溃
        """
        crash_indicators = [
            "instrumentation process is not running",
            "probably crashed",
            "UiAutomator2 server",
            "cannot be proxied to UiAutomator2 server",
            "POST /element' cannot be proxied",
            "A session is either terminated or not started",
            "NoSuchDriverError",
            "device 'emulator-", # 设备未找到错误
            "Can not convert #BOOLEAN to a NodeList", # XPath布尔值转换错误
            "XPathException"  # XPath表达式错误
        ]

        error_lower = error_message.lower()
        return any(indicator.lower() in error_lower for indicator in crash_indicators)

    async def _attempt_uiautomator2_recovery(self, driver) -> bool:
        """尝试从UiAutomator2崩溃中恢复

        Args:
            driver: 当前的Appium驱动实例

        Returns:
            bool: 是否恢复成功
        """
        try:
            if self.uploader and hasattr(self.uploader, 'recover_from_uiautomator2_crash'):
                logger.info("🔧 通过上传器实例尝试UiAutomator2恢复...")
                recovery_success = await self.uploader.recover_from_uiautomator2_crash()
                return recovery_success
            else:
                logger.warning("无法获取上传器实例，尝试简单的连接测试...")
                # 简单的连接测试，看看driver是否还能工作
                try:
                    current_activity = driver.current_activity
                    logger.info(f"Driver仍然可用，当前活动: {current_activity}")
                    return True
                except Exception as test_error:
                    logger.error(f"Driver连接测试失败: {str(test_error)}")
                    return False

        except Exception as e:
            logger.error(f"UiAutomator2恢复尝试失败: {str(e)}")
            return False

    def _get_recovered_driver(self):
        """获取恢复后的driver实例

        Returns:
            WebDriver or None: 恢复后的driver实例
        """
        try:
            if self.uploader and hasattr(self.uploader, 'get_driver'):
                new_driver = self.uploader.get_driver()
                if new_driver:
                    logger.debug("✅ 从上传器获取到恢复后的driver实例")
                    return new_driver
                else:
                    logger.warning("⚠️ 上传器中没有可用的driver实例")
            else:
                logger.warning("⚠️ 无法获取上传器实例或上传器没有get_driver方法")

            return None

        except Exception as e:
            logger.error(f"获取恢复后driver失败: {str(e)}")
            return None

    def _validate_and_fix_xpath(self, xpath: str) -> str:
        """验证和修复XPath表达式

        Args:
            xpath: 原始XPath表达式

        Returns:
            str: 修复后的XPath表达式
        """
        try:
            # 检查常见的XPath语法错误并修复
            original_xpath = xpath

            # 1. 修复 '] and @' 语法错误 - 应该在方括号内使用 and
            # 例如: //element[@attr='value'] and @clickable='true'
            # 应该是: //element[@attr='value' and @clickable='true']
            import re

            # 查找 '] and @' 模式
            pattern = r'(\])\s+and\s+(@[^=]+=[^]]+)'
            if re.search(pattern, xpath):
                logger.warning(f"检测到XPath语法错误，尝试修复: {xpath}")
                # 将 '] and @attr' 替换为 ' and @attr]'
                xpath = re.sub(r'(\])\s+and\s+(@[^=]+=[^]]+)', r' and \2]', xpath)
                logger.info(f"XPath修复结果: {xpath}")

            # 2. 检查引号匹配
            single_quotes = xpath.count("'")
            double_quotes = xpath.count('"')

            if single_quotes % 2 != 0:
                logger.warning(f"XPath表达式中单引号不匹配: {xpath}")

            if double_quotes % 2 != 0:
                logger.warning(f"XPath表达式中双引号不匹配: {xpath}")

            # 3. 检查方括号匹配
            open_brackets = xpath.count('[')
            close_brackets = xpath.count(']')

            if open_brackets != close_brackets:
                logger.warning(f"XPath表达式中方括号不匹配: {xpath} (开括号:{open_brackets}, 闭括号:{close_brackets})")

            # 4. 记录修复信息
            if xpath != original_xpath:
                logger.info(f"✅ XPath表达式已修复: {original_xpath} -> {xpath}")

            return xpath

        except Exception as e:
            logger.warning(f"XPath验证失败: {str(e)}, 使用原始表达式: {xpath}")
            return xpath

    async def _find_element_with_recovery(self, driver, element_type: str, value: str, timeout: int):
        """简化的元素查找"""
        if element_type == "id":
            return await asyncio.wait_for(
                asyncio.to_thread(
                    lambda: WebDriverWait(driver, timeout).until(
                        EC.presence_of_element_located((AppiumBy.ID, value))
                    )
                ),
                timeout=timeout + 5.0
            )
        elif element_type == "xpath" or element_type == "xpath_template":
            return await asyncio.wait_for(
                asyncio.to_thread(
                    lambda: WebDriverWait(driver, timeout).until(
                        EC.element_to_be_clickable((AppiumBy.XPATH, value))
                    )
                ),
                timeout=timeout + 5.0
            )
        elif element_type == "accessibility_id":
            return await asyncio.wait_for(
                asyncio.to_thread(
                    lambda: WebDriverWait(driver, timeout).until(
                        EC.element_to_be_clickable((AppiumBy.ACCESSIBILITY_ID, value))
                    )
                ),
                timeout=timeout + 5.0
            )
        elif element_type == "coordinate":
            # 坐标点击不需要查找元素，直接执行点击
            await asyncio.wait_for(
                asyncio.to_thread(lambda: driver.tap([value])),
                timeout=10.0
            )
            return "coordinate_clicked"  # 返回特殊标识表示坐标点击已完成
        elif element_type == "coordinate_relative":
            # 相对坐标点击
            screen_size = await asyncio.wait_for(
                asyncio.to_thread(lambda: driver.get_window_size()),
                timeout=10.0
            )
            x = int(screen_size['width'] * value[0])
            y = int(screen_size['height'] * value[1])
            await asyncio.wait_for(
                asyncio.to_thread(lambda: driver.tap([(x, y)])),
                timeout=10.0
            )
            return "coordinate_clicked"  # 返回特殊标识表示坐标点击已完成
        else:
            logger.error(f"不支持的元素类型: {element_type}")
            return None



    async def input_text(self, driver, element_name: str, text: str) -> bool:
        """在指定元素中输入文本

        Args:
            driver: Appium驱动
            element_name: 元素名称
            text: 要输入的文本

        Returns:
            bool: 是否成功输入
        """
        element = await self.find_element(driver, element_name)
        if element:
            try:
                element.clear()
                element.send_keys(text)
                logger.info(f"✅ 成功输入文本到 {element_name}: {text}")
                return True
            except Exception as e:
                logger.error(f"❌ 输入文本失败 {element_name}: {str(e)}")
                return False
        return False

    def get_wait_time(self, wait_type: str) -> float:
        """获取等待时间

        Args:
            wait_type: 等待类型

        Returns:
            float: 等待时间（秒）
        """
        return self.wait_times.get(wait_type, 2.0)

    def _get_smart_wait_time(self, element_name: str) -> float:
        """根据元素类型获取智能等待时间

        Args:
            element_name: 元素名称

        Returns:
            float: 等待时间（秒）
        """
        # 🔧 关键UI操作需要更长的等待时间
        critical_elements = {
            "finish_editing_button": 5.0,  # 完成编辑按钮需要等待UI更新
            "add_music_button": 3.0,       # 添加音乐按钮
            "privacy_button": 4.0,         # 隐私设置按钮
            "publish_button": 6.0,         # 发布按钮
            "video_file": 3.0,             # 视频文件选择
            "next_button": 3.0,            # 下一步按钮
        }

        # 获取配置的等待时间，如果没有则使用智能默认值
        config_wait_time = self.wait_times.get('after_click', 2.0)
        smart_wait_time = critical_elements.get(element_name, config_wait_time)

        # 对于关键元素，至少等待3秒
        if element_name in critical_elements:
            smart_wait_time = max(smart_wait_time, 3.0)

        return smart_wait_time

    async def _should_verify_click_effect(self, element_name: str) -> bool:
        """判断是否需要验证点击效果

        Args:
            element_name: 元素名称

        Returns:
            bool: 是否需要验证
        """
        # 这些元素点击后需要验证效果
        verify_elements = [
            "finish_editing_button",  # 完成按钮点击后应该进入下一个界面
            "next_button",            # 下一步按钮
            "publish_button",         # 发布按钮
        ]

        return element_name in verify_elements

    async def _verify_click_effect(self, driver, element_name: str) -> bool:
        """验证点击效果

        Args:
            driver: Appium驱动
            element_name: 元素名称

        Returns:
            bool: 点击效果是否符合预期
        """
        try:
            if element_name == "finish_editing_button":
                # 验证是否进入了下一个界面（完成按钮应该消失）
                logger.info("🔍 验证完成按钮点击效果...")
                await asyncio.sleep(2)  # 等待界面切换

                # 尝试再次查找完成按钮，如果找不到说明已经切换界面
                try:
                    element = await asyncio.wait_for(
                        asyncio.to_thread(
                            lambda: driver.find_element("xpath", "//android.widget.Button[@content-desc='将片段添加至项目']")
                        ),
                        timeout=3.0
                    )
                    if element:
                        logger.warning("⚠️ 完成按钮仍然存在，可能点击未生效")
                        return False
                except:
                    logger.info("✅ 完成按钮已消失，界面切换成功")
                    return True

            elif element_name in ["next_button", "publish_button"]:
                # 对于其他按钮，简单等待界面稳定
                logger.info(f"🔍 验证 {element_name} 点击效果...")
                await asyncio.sleep(3)  # 等待界面稳定
                return True

        except Exception as e:
            logger.warning(f"⚠️ 验证点击效果时出错: {str(e)}")

        return True  # 默认认为成功

    def _find_clickable_parent(self, element):
        """查找可点击的父元素

        Args:
            element: 当前元素

        Returns:
            WebElement: 可点击的父元素，如果找不到则返回None
        """
        try:
            current = element
            max_levels = 3  # 最多向上查找3级父元素

            for level in range(max_levels):
                if current.get_attribute("clickable") == "true":
                    logger.info(f"🎯 找到可点击的父元素（第{level}级）")
                    return current

                # 查找父元素
                try:
                    current = current.find_element(AppiumBy.XPATH, "./..")
                except:
                    logger.debug(f"无法找到第{level+1}级父元素")
                    break

            logger.debug("未找到可点击的父元素")
            return None

        except Exception as e:
            logger.debug(f"查找可点击父元素异常: {str(e)}")
            return None

    def _should_prefer_parent_strategy(self, element_name: str) -> bool:
        """判断是否应该优先使用父元素策略

        Args:
            element_name: 元素名称

        Returns:
            bool: 是否优先使用父元素策略
        """
        # 这些元素通常需要点击父元素
        parent_preferred_elements = [
            "finish_editing_button",  # 完成按钮
            "saved_music_tab",        # 已保存标签页
            "add_music_to_video_button",  # 添加音乐按钮
        ]

        return element_name in parent_preferred_elements
