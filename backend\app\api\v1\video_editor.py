"""
视频编辑器API路由

提供视频编辑器相关的API接口，包括项目管理、实时编辑、渲染导出等功能。
"""

import os
import json
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import logging

from app.api.auth import get_current_user
from app.core.grpc_client import get_core_client, get_core_client_by_service_id
# 数据库连接函数
def get_database(request: Request):
    """获取数据库连接"""
    return request.app.state.mongo_db

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/video-editor", tags=["视频编辑器"])

# 数据模型定义
class VideoResolution(BaseModel):
    width: int = Field(..., ge=320, le=7680, description="视频宽度")
    height: int = Field(..., ge=240, le=4320, description="视频高度")

class ProjectSettings(BaseModel):
    resolution: VideoResolution
    frame_rate: int = Field(30, ge=1, le=120, description="帧率")
    audio_sample_rate: int = Field(48000, description="音频采样率")
    audio_channels: int = Field(2, ge=1, le=8, description="音频声道数")
    color_space: str = Field("rec709", description="色彩空间")
    timecode_format: str = Field("non-drop", description="时间码格式")
    auto_save: bool = Field(True, description="自动保存")

class Track(BaseModel):
    id: str
    type: str = Field(..., regex="^(video|audio|subtitle|effect)$")
    name: str
    enabled: bool = True
    locked: bool = False
    clips: List[Dict[str, Any]] = []

class Timeline(BaseModel):
    tracks: List[Track]
    total_duration: float = 0.0
    markers: List[Dict[str, Any]] = []

class VideoProject(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    settings: ProjectSettings
    timeline: Timeline

class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    settings: Optional[ProjectSettings] = None
    timeline: Optional[Timeline] = None

class MediaAsset(BaseModel):
    name: str
    type: str = Field(..., regex="^(video|audio|image|subtitle)$")
    url: str
    thumbnail: Optional[str] = None
    size: int
    duration: Optional[float] = None
    resolution: Optional[VideoResolution] = None

class EditOperation(BaseModel):
    type: str = Field(..., regex="^(cut|split|merge|move|resize|delete)$")
    clip_id: str
    parameters: Dict[str, Any]

class ExportSettings(BaseModel):
    format: str = Field("mp4", description="导出格式")
    resolution: str = Field("original", description="导出分辨率")
    frame_rate: str = Field("original", description="导出帧率")
    quality: int = Field(80, ge=1, le=100, description="导出质量")
    audio_codec: str = Field("aac", description="音频编码")
    audio_bitrate: str = Field("192", description="音频比特率")
    export_range: str = Field("all", description="导出范围")
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    output_path: str
    file_name: str

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.project_connections: Dict[str, List[str]] = {}

    async def connect(self, websocket: WebSocket, client_id: str, project_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        
        if project_id not in self.project_connections:
            self.project_connections[project_id] = []
        self.project_connections[project_id].append(client_id)
        
        logger.info(f"Client {client_id} connected to project {project_id}")

    def disconnect(self, client_id: str, project_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        if project_id in self.project_connections:
            if client_id in self.project_connections[project_id]:
                self.project_connections[project_id].remove(client_id)
            
            if not self.project_connections[project_id]:
                del self.project_connections[project_id]
        
        logger.info(f"Client {client_id} disconnected from project {project_id}")

    async def send_personal_message(self, message: dict, client_id: str):
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_text(json.dumps(message))

    async def broadcast_to_project(self, message: dict, project_id: str, exclude_client: str = None):
        if project_id in self.project_connections:
            for client_id in self.project_connections[project_id]:
                if client_id != exclude_client:
                    await self.send_personal_message(message, client_id)

manager = ConnectionManager()

# 项目管理API
@router.post("/projects", response_model=Dict[str, Any])
async def create_project(
    project: VideoProject,
    current_user: dict = Depends(get_current_user),
    db = Depends(get_database)
):
    """创建新的视频编辑项目"""
    try:
        
        project_data = {
            "name": project.name,
            "description": project.description,
            "owner_id": current_user["user_id"],
            "settings": project.settings.dict(),
            "timeline": project.timeline.dict(),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "collaborators": []
        }
        
        result = await db.video_projects.insert_one(project_data)
        project_data["_id"] = str(result.inserted_id)
        
        logger.info(f"Created video project: {project.name} by user {current_user['user_id']}")
        
        return {
            "success": True,
            "project_id": str(result.inserted_id),
            "message": "项目创建成功"
        }
        
    except Exception as e:
        logger.error(f"Failed to create project: {str(e)}")
        raise HTTPException(status_code=500, detail="项目创建失败")

@router.get("/projects", response_model=Dict[str, Any])
async def get_projects(
    page: int = 1,
    limit: int = 20,
    current_user: dict = Depends(get_current_user),
    db = Depends(get_database)
):
    """获取用户的视频编辑项目列表"""
    try:
        
        skip = (page - 1) * limit
        
        # 查询用户拥有或参与的项目
        query = {
            "$or": [
                {"owner_id": current_user["user_id"]},
                {"collaborators": current_user["user_id"]}
            ]
        }
        
        projects = await db.video_projects.find(query).skip(skip).limit(limit).to_list(length=limit)
        total = await db.video_projects.count_documents(query)
        
        # 转换ObjectId为字符串
        for project in projects:
            project["_id"] = str(project["_id"])
            project["created_at"] = project["created_at"].isoformat()
            project["updated_at"] = project["updated_at"].isoformat()
        
        return {
            "success": True,
            "projects": projects,
            "total": total,
            "page": page,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Failed to get projects: {str(e)}")
        raise HTTPException(status_code=500, detail="获取项目列表失败")

@router.get("/projects/{project_id}", response_model=Dict[str, Any])
async def get_project(
    project_id: str,
    current_user: dict = Depends(get_current_user),
    db = Depends(get_database)
):
    """获取指定项目的详细信息"""
    try:
        
        project = await db.video_projects.find_one({"_id": project_id})
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 检查权限
        if (project["owner_id"] != current_user["user_id"] and 
            current_user["user_id"] not in project.get("collaborators", [])):
            raise HTTPException(status_code=403, detail="无权限访问此项目")
        
        project["_id"] = str(project["_id"])
        project["created_at"] = project["created_at"].isoformat()
        project["updated_at"] = project["updated_at"].isoformat()
        
        return {
            "success": True,
            "project": project
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="获取项目失败")

@router.put("/projects/{project_id}", response_model=Dict[str, Any])
async def update_project(
    project_id: str,
    project_update: ProjectUpdate,
    current_user: dict = Depends(get_current_user),
    db = Depends(get_database)
):
    """更新项目信息"""
    try:
        
        # 检查项目存在性和权限
        project = await db.video_projects.find_one({"_id": project_id})
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        if (project["owner_id"] != current_user["user_id"] and 
            current_user["user_id"] not in project.get("collaborators", [])):
            raise HTTPException(status_code=403, detail="无权限修改此项目")
        
        # 构建更新数据
        update_data = {"updated_at": datetime.utcnow()}
        
        if project_update.name is not None:
            update_data["name"] = project_update.name
        if project_update.description is not None:
            update_data["description"] = project_update.description
        if project_update.settings is not None:
            update_data["settings"] = project_update.settings.dict()
        if project_update.timeline is not None:
            update_data["timeline"] = project_update.timeline.dict()
        
        await db.video_projects.update_one(
            {"_id": project_id},
            {"$set": update_data}
        )
        
        # 广播更新给其他协作者
        await manager.broadcast_to_project(
            {
                "type": "project_updated",
                "project_id": project_id,
                "updated_by": current_user["user_id"],
                "timestamp": datetime.utcnow().isoformat()
            },
            project_id,
            exclude_client=current_user["user_id"]
        )
        
        logger.info(f"Updated project {project_id} by user {current_user['user_id']}")
        
        return {
            "success": True,
            "message": "项目更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="项目更新失败")

@router.delete("/projects/{project_id}", response_model=Dict[str, Any])
async def delete_project(
    project_id: str,
    current_user: dict = Depends(get_current_user)
):
    """删除项目"""
    try:
        db = get_database()
        
        # 检查项目存在性和权限
        project = await db.video_projects.find_one({"_id": project_id})
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        if project["owner_id"] != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="只有项目所有者可以删除项目")
        
        await db.video_projects.delete_one({"_id": project_id})
        
        logger.info(f"Deleted project {project_id} by user {current_user['user_id']}")
        
        return {
            "success": True,
            "message": "项目删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="项目删除失败")

# 素材管理API
@router.post("/media/upload", response_model=Dict[str, Any])
async def upload_media(
    # 这里应该使用文件上传，暂时简化
    current_user: dict = Depends(get_current_user)
):
    """上传媒体素材"""
    # TODO: 实现文件上传逻辑
    return {
        "success": True,
        "message": "文件上传成功",
        "media_id": "media_123"
    }

@router.get("/media", response_model=Dict[str, Any])
async def get_media_assets(
    type: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """获取用户的媒体素材列表"""
    try:
        db = get_database()
        
        query = {"owner_id": current_user["user_id"]}
        if type:
            query["type"] = type
        
        media_assets = await db.media_assets.find(query).to_list(length=None)
        
        for asset in media_assets:
            asset["_id"] = str(asset["_id"])
            asset["created_at"] = asset["created_at"].isoformat()
        
        return {
            "success": True,
            "media_assets": media_assets
        }
        
    except Exception as e:
        logger.error(f"Failed to get media assets: {str(e)}")
        raise HTTPException(status_code=500, detail="获取素材列表失败")

# 实时编辑API
@router.post("/edit/{project_id}", response_model=Dict[str, Any])
async def apply_edit_operation(
    project_id: str,
    operation: EditOperation,
    current_user: dict = Depends(get_current_user)
):
    """应用编辑操作"""
    try:
        # 检查项目权限
        db = get_database()
        project = await db.video_projects.find_one({"_id": project_id})
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        if (project["owner_id"] != current_user["user_id"] and 
            current_user["user_id"] not in project.get("collaborators", [])):
            raise HTTPException(status_code=403, detail="无权限编辑此项目")
        
        # 应用编辑操作
        result = await apply_operation(project_id, operation)
        
        # 广播编辑操作给其他协作者
        await manager.broadcast_to_project(
            {
                "type": "edit_operation",
                "project_id": project_id,
                "operation": operation.dict(),
                "applied_by": current_user["user_id"],
                "timestamp": datetime.utcnow().isoformat()
            },
            project_id,
            exclude_client=current_user["user_id"]
        )
        
        return {
            "success": True,
            "result": result,
            "message": "编辑操作应用成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to apply edit operation: {str(e)}")
        raise HTTPException(status_code=500, detail="编辑操作失败")

async def apply_operation(project_id: str, operation: EditOperation) -> Dict[str, Any]:
    """应用具体的编辑操作"""
    # 这里应该根据操作类型调用相应的Core服务
    # 暂时返回模拟结果
    return {
        "operation_id": f"op_{int(datetime.utcnow().timestamp())}",
        "status": "completed"
    }

# WebSocket连接
@router.websocket("/ws/preview/{project_id}")
async def websocket_preview(websocket: WebSocket, project_id: str):
    """WebSocket连接用于实时预览"""
    client_id = f"client_{int(datetime.utcnow().timestamp())}"
    
    await manager.connect(websocket, client_id, project_id)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理不同类型的消息
            if message["type"] == "seek":
                # 处理时间跳转
                await handle_seek(client_id, project_id, message["time"])
            elif message["type"] == "play":
                # 处理播放
                await handle_play(client_id, project_id, message.get("time", 0))
            elif message["type"] == "pause":
                # 处理暂停
                await handle_pause(client_id, project_id)
            
    except WebSocketDisconnect:
        manager.disconnect(client_id, project_id)

async def handle_seek(client_id: str, project_id: str, time: float):
    """处理时间跳转"""
    # 这里应该调用Core服务生成指定时间的预览帧
    await manager.send_personal_message({
        "type": "frame",
        "time": time,
        "frame_data": "base64_encoded_frame_data"
    }, client_id)

async def handle_play(client_id: str, project_id: str, start_time: float):
    """处理播放"""
    # 这里应该启动实时预览流
    await manager.send_personal_message({
        "type": "playback_started",
        "time": start_time
    }, client_id)

async def handle_pause(client_id: str, project_id: str):
    """处理暂停"""
    await manager.send_personal_message({
        "type": "playback_paused"
    }, client_id)

# 渲染和导出API
@router.post("/render/preview/{project_id}", response_model=Dict[str, Any])
async def generate_preview(
    project_id: str,
    time: float,
    quality: str = "medium",
    core_service_id: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """生成指定时间点的预览帧"""
    try:
        # 检查项目权限
        db = get_database()
        project = await db.video_projects.find_one({"_id": project_id})

        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        if (project["owner_id"] != current_user["user_id"] and
            current_user["user_id"] not in project.get("collaborators", [])):
            raise HTTPException(status_code=403, detail="无权限访问此项目")

        # 选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务生成预览: {core_service_id}")
        else:
            core_client = get_core_client()
            logger.info("使用默认Core服务生成预览")

        if not core_client:
            raise HTTPException(status_code=503, detail="Core服务不可用")

        # 调用Core服务生成预览
        preview_result = await core_client.generate_preview_frame(
            project_data=project,
            timestamp=time,
            quality=quality
        )

        return {
            "success": True,
            "frame_data": preview_result.get("frame_data"),
            "timestamp": time,
            "quality": quality
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to generate preview: {str(e)}")
        raise HTTPException(status_code=500, detail="生成预览失败")

@router.post("/export/{project_id}", response_model=Dict[str, Any])
async def export_video(
    project_id: str,
    export_settings: ExportSettings,
    background_tasks: BackgroundTasks,
    core_service_id: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """导出视频"""
    try:
        # 检查项目权限
        db = get_database()
        project = await db.video_projects.find_one({"_id": project_id})

        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        if (project["owner_id"] != current_user["user_id"] and
            current_user["user_id"] not in project.get("collaborators", [])):
            raise HTTPException(status_code=403, detail="无权限导出此项目")

        # 创建导出任务
        export_task = {
            "project_id": project_id,
            "user_id": current_user["user_id"],
            "settings": export_settings.dict(),
            "status": "pending",
            "progress": 0,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "core_service_id": core_service_id
        }

        result = await db.export_tasks.insert_one(export_task)
        task_id = str(result.inserted_id)

        # 添加后台任务
        background_tasks.add_task(
            process_export_task,
            task_id,
            project,
            export_settings,
            core_service_id
        )

        logger.info(f"Created export task {task_id} for project {project_id}")

        return {
            "success": True,
            "task_id": task_id,
            "message": "导出任务已创建"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create export task: {str(e)}")
        raise HTTPException(status_code=500, detail="创建导出任务失败")

@router.get("/export/status/{task_id}", response_model=Dict[str, Any])
async def get_export_status(
    task_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取导出任务状态"""
    try:
        db = get_database()
        task = await db.export_tasks.find_one({"_id": task_id})

        if not task:
            raise HTTPException(status_code=404, detail="导出任务不存在")

        if task["user_id"] != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="无权限查看此任务")

        task["_id"] = str(task["_id"])
        task["created_at"] = task["created_at"].isoformat()
        task["updated_at"] = task["updated_at"].isoformat()

        return {
            "success": True,
            "task": task
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get export status: {str(e)}")
        raise HTTPException(status_code=500, detail="获取导出状态失败")

async def process_export_task(
    task_id: str,
    project: dict,
    export_settings: ExportSettings,
    core_service_id: Optional[str] = None
):
    """处理导出任务"""
    db = get_database()

    try:
        # 更新任务状态为处理中
        await db.export_tasks.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "status": "processing",
                    "updated_at": datetime.utcnow()
                }
            }
        )

        # 选择Core服务
        if core_service_id:
            core_client = get_core_client_by_service_id(core_service_id)
            logger.info(f"使用指定的Core服务导出视频: {core_service_id}")
        else:
            core_client = get_core_client()
            logger.info("使用默认Core服务导出视频")

        if not core_client:
            raise Exception("Core服务不可用")

        # 调用Core服务导出视频
        export_result = await core_client.export_video(
            project_data=project,
            export_settings=export_settings.dict()
        )

        if export_result.get("success"):
            # 导出成功
            await db.export_tasks.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "status": "completed",
                        "progress": 100,
                        "output_file": export_result.get("output_file"),
                        "file_size": export_result.get("file_size"),
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            logger.info(f"Export task {task_id} completed successfully")
        else:
            # 导出失败
            await db.export_tasks.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "status": "failed",
                        "error_message": export_result.get("error", "未知错误"),
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            logger.error(f"Export task {task_id} failed: {export_result.get('error')}")

    except Exception as e:
        # 处理异常
        await db.export_tasks.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "status": "failed",
                    "error_message": str(e),
                    "updated_at": datetime.utcnow()
                }
            }
        )
        logger.error(f"Export task {task_id} failed with exception: {str(e)}")

# 协作功能API
@router.post("/projects/{project_id}/collaborators", response_model=Dict[str, Any])
async def add_collaborator(
    project_id: str,
    collaborator_email: str,
    current_user: dict = Depends(get_current_user)
):
    """添加项目协作者"""
    try:
        db = get_database()

        # 检查项目权限
        project = await db.video_projects.find_one({"_id": project_id})
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        if project["owner_id"] != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="只有项目所有者可以添加协作者")

        # 查找协作者用户
        collaborator = await db.users.find_one({"email": collaborator_email})
        if not collaborator:
            raise HTTPException(status_code=404, detail="用户不存在")

        collaborator_id = str(collaborator["_id"])

        # 检查是否已经是协作者
        if collaborator_id in project.get("collaborators", []):
            raise HTTPException(status_code=400, detail="用户已经是协作者")

        # 添加协作者
        await db.video_projects.update_one(
            {"_id": project_id},
            {
                "$push": {"collaborators": collaborator_id},
                "$set": {"updated_at": datetime.utcnow()}
            }
        )

        logger.info(f"Added collaborator {collaborator_id} to project {project_id}")

        return {
            "success": True,
            "message": "协作者添加成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add collaborator: {str(e)}")
        raise HTTPException(status_code=500, detail="添加协作者失败")

@router.delete("/projects/{project_id}/collaborators/{collaborator_id}", response_model=Dict[str, Any])
async def remove_collaborator(
    project_id: str,
    collaborator_id: str,
    current_user: dict = Depends(get_current_user)
):
    """移除项目协作者"""
    try:
        db = get_database()

        # 检查项目权限
        project = await db.video_projects.find_one({"_id": project_id})
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        if project["owner_id"] != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="只有项目所有者可以移除协作者")

        # 移除协作者
        await db.video_projects.update_one(
            {"_id": project_id},
            {
                "$pull": {"collaborators": collaborator_id},
                "$set": {"updated_at": datetime.utcnow()}
            }
        )

        logger.info(f"Removed collaborator {collaborator_id} from project {project_id}")

        return {
            "success": True,
            "message": "协作者移除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove collaborator: {str(e)}")
        raise HTTPException(status_code=500, detail="移除协作者失败")
