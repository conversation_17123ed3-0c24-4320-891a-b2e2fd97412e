/**
 * 视频详细信息API - 优化版本
 * 支持分页加载和缓存机制
 */

import request from '@/utils/request'

// 类型定义
export interface VideoInfo {
  _id: string
  title: string
  description?: string
  platform: string
  original_url?: string
  author: {
    name: string
    channel_id?: string
    avatar_url?: string
  }
  content_type: string
  created_at: string
  updated_at?: string
  file_info: {
    local_path: string
    file_size?: number
    file_format?: string
    duration?: number
    media_info?: any
    media_info_cached?: boolean
  }
  metadata?: {
    tags?: string[]
    category?: string
    language?: string
    publish_date?: string
    view_count?: number
    like_count?: number
    comment_count?: number
  }
  analysis?: any
}

export interface VideoListParams {
  page?: number
  limit?: number
  platform?: string
  content_type?: string
  search?: string
  include_media_info?: boolean
}

export interface VideoListResponse {
  success: boolean
  videos: VideoInfo[]
  total: number
  page: number
  limit: number
  has_next: boolean
}

export interface VideoDetailResponse {
  success: boolean
  video_info: VideoInfo
  media_info?: any
  analysis_info?: any
  from_cache: boolean
}

export interface VideoMediaInfoResponse {
  success: boolean
  media_info: any
  from_cache: boolean
  error?: string
}

export interface VideoAnalysisResponse {
  success: boolean
  analysis: any
  error?: string
}

/**
 * 获取视频列表（分页优化）
 */
export const getVideoList = (params: VideoListParams = {}) => {
  return request<VideoListResponse>({
    url: '/api/v1/video-details/list',
    method: 'get',
    params: {
      page: 1,
      limit: 20,
      content_type: 'video',
      include_media_info: false,
      ...params
    }
  })
}

/**
 * 获取视频详情（基础信息）
 */
export const getVideoDetail = (
  videoId: string, 
  options: {
    include_media_info?: boolean
    include_analysis?: boolean
  } = {}
) => {
  return request<VideoDetailResponse>({
    url: `/api/v1/video-details/${videoId}`,
    method: 'get',
    params: {
      include_media_info: false,
      include_analysis: false,
      ...options
    }
  })
}

/**
 * 获取视频媒体信息（支持缓存）
 */
export const getVideoMediaInfo = (
  videoId: string, 
  forceRefresh: boolean = false
) => {
  return request<VideoMediaInfoResponse>({
    url: `/api/v1/video-details/${videoId}/media-info`,
    method: 'get',
    params: {
      force_refresh: forceRefresh
    }
  })
}

/**
 * 获取视频分析信息
 */
export const getVideoAnalysis = (videoId: string) => {
  return request<VideoAnalysisResponse>({
    url: `/api/v1/video-details/${videoId}/analysis`,
    method: 'get'
  })
}

/**
 * 批量获取视频基础信息
 */
export const getBatchVideoInfo = (videoIds: string[]) => {
  return request({
    url: '/api/v1/video-details/batch',
    method: 'post',
    data: {
      video_ids: videoIds
    }
  })
}

/**
 * 搜索视频
 */
export const searchVideos = (
  query: string,
  params: {
    page?: number
    limit?: number
    platform?: string
  } = {}
) => {
  return request<VideoListResponse>({
    url: '/api/v1/video-details/search',
    method: 'get',
    params: {
      search: query,
      page: 1,
      limit: 20,
      ...params
    }
  })
}

/**
 * 获取视频统计信息
 */
export const getVideoStatistics = (params: {
  platform?: string
  date_range?: string
} = {}) => {
  return request({
    url: '/api/v1/video-details/statistics',
    method: 'get',
    params
  })
}

/**
 * 预加载视频媒体信息
 */
export const preloadVideoMediaInfo = (videoIds: string[]) => {
  return request({
    url: '/api/v1/video-details/preload-media-info',
    method: 'post',
    data: {
      video_ids: videoIds
    }
  })
}

/**
 * 获取视频缩略图URL
 */
export const getVideoThumbnailUrl = (videoPath: string) => {
  // 构建缩略图URL
  const encodedPath = encodeURIComponent(videoPath)
  return `/api/v1/filesystem/video/thumbnail?video_path=${encodedPath}`
}

/**
 * 获取视频预览URL
 */
export const getVideoPreviewUrl = (videoPath: string) => {
  // 构建预览URL
  const encodedPath = encodeURIComponent(videoPath)
  return `/api/v1/filesystem/video/preview?video_path=${encodedPath}`
}

/**
 * 格式化视频时长
 */
export const formatDuration = (seconds: number): string => {
  if (!seconds || seconds <= 0) return '00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
}

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes <= 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const k = 1024
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`
}

/**
 * 格式化比特率
 */
export const formatBitrate = (bitrate: number): string => {
  if (!bitrate || bitrate <= 0) return '0 bps'
  
  const units = ['bps', 'Kbps', 'Mbps', 'Gbps']
  const k = 1000
  const i = Math.floor(Math.log(bitrate) / Math.log(k))
  
  return `${(bitrate / Math.pow(k, i)).toFixed(1)} ${units[i]}`
}

/**
 * 格式化数字（观看数、点赞数等）
 */
export const formatNumber = (num: number): string => {
  if (!num || num <= 0) return '0'
  
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(1)}B`
  } else if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  } else {
    return num.toString()
  }
}

/**
 * 检查视频是否支持预览
 */
export const isVideoPreviewSupported = (fileFormat: string): boolean => {
  const supportedFormats = ['mp4', 'webm', 'ogg', 'mov', 'avi', 'mkv']
  return supportedFormats.includes(fileFormat?.toLowerCase() || '')
}

/**
 * 获取平台图标
 */
export const getPlatformIcon = (platform: string): string => {
  const icons: Record<string, string> = {
    youtube: '📺',
    tiktok: '🎵',
    instagram: '📷',
    twitter: '🐦',
    facebook: '👥',
    bilibili: '📺',
    douyin: '🎵'
  }
  
  return icons[platform?.toLowerCase()] || '🎬'
}

/**
 * 获取内容类型图标
 */
export const getContentTypeIcon = (contentType: string): string => {
  const icons: Record<string, string> = {
    video: '🎬',
    audio: '🎵',
    image: '🖼️',
    text: '📝'
  }
  
  return icons[contentType?.toLowerCase()] || '📄'
}
