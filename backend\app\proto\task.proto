syntax = "proto3";

package task;

// 任务服务
service TaskService {
  // 创建任务
  rpc CreateTask(TaskRequest) returns (TaskResponse) {}
  
  // 开始执行任务
  rpc StartTask(TaskIdRequest) returns (TaskResponse) {}
  
  // 暂停任务
  rpc PauseTask(TaskIdRequest) returns (TaskResponse) {}
  
  // 取消任务
  rpc CancelTask(TaskIdRequest) returns (TaskResponse) {}
  
  // 获取任务状态
  rpc GetTaskStatus(TaskIdRequest) returns (TaskStatusResponse) {}
  
  // 获取任务日志
  rpc GetTaskLogs(TaskIdRequest) returns (TaskLogsResponse) {}

  // 获取工作流配置
  rpc GetWorkflowConfig(WorkflowConfigRequest) returns (WorkflowConfigResponse) {}
}

// 任务请求
message TaskRequest {
  // 任务ID
  string task_id = 1;
  // 平台ID
  string platform_id = 2;
  // 账号ID
  string account_id = 3;
  // 设备ID
  string device_id = 4;
  // 内容路径
  string content_path = 5;
  // 工作流ID（可选）
  string workflow_id = 6;
  // 任务参数（可选）
  map<string, string> params = 7;
  // 🔧 重要修复：添加任务类型字段
  string task_type = 8;
}

// 任务ID请求
message TaskIdRequest {
  // 任务ID
  string task_id = 1;
}

// 任务响应
message TaskResponse {
  // 是否成功
  bool success = 1;
  // 任务ID
  string task_id = 2;
  // 错误信息
  string error = 3;
}

// 任务状态响应
message TaskStatusResponse {
  // 任务ID
  string task_id = 1;
  // 任务状态
  string status = 2;
  // 进度（0-100）
  int32 progress = 3;
  // 开始时间
  string start_time = 4;
  // 预计结束时间
  string estimated_end_time = 5;
  // 设备使用情况
  DeviceUsage device_usage = 6;
  // 最近日志
  repeated TaskLog logs = 7;
}

// 设备使用情况
message DeviceUsage {
  // CPU使用率
  int32 cpu = 1;
  // 内存使用率
  int32 memory = 2;
  // 网络状态
  string network = 3;
}

// 任务日志
message TaskLog {
  // 日志消息
  string message = 1;
  // 日志级别
  string level = 2;
  // 时间戳
  string timestamp = 3;
}

// 任务日志响应
message TaskLogsResponse {
  // 任务ID
  string task_id = 1;
  // 日志列表
  repeated TaskLog logs = 2;
}

// 工作流配置请求
message WorkflowConfigRequest {
  // 平台ID
  string platform_id = 1;
  // 内容类型（如 "shorts", "video"）
  string content_type = 2;
}

// 工作流步骤
message WorkflowStep {
  // 步骤ID
  string id = 1;
  // 步骤名称
  string name = 2;
  // 步骤描述
  string description = 3;
  // 动作类型
  string action = 4;
  // 是否必需
  bool required = 5;
  // 超时时间（秒）
  int32 timeout = 6;
  // 重试次数
  int32 retry_count = 7;
  // 等待时间（秒）
  int32 wait_after = 8;
  // 条件
  string condition = 9;
  // 元素定位
  string element = 10;
  // 参数
  string parameters = 11;
  // 备注
  string notes = 12;
}

// 工作流配置响应
message WorkflowConfigResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 工作流ID
  string workflow_id = 3;
  // 工作流名称
  string workflow_name = 4;
  // 工作流描述
  string workflow_description = 5;
  // 工作流版本
  string workflow_version = 6;
  // 工作流步骤
  repeated WorkflowStep steps = 7;
  // 配置信息
  string config = 8;
}
