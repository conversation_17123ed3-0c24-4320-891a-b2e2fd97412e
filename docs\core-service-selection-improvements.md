# 文件管理Core服务选择功能全面改进

## 问题描述

之前文件管理功能中，只有文件列表获取支持Core服务选择，但各种数据处理操作（删除、视频处理、音频处理等）都还在使用默认服务，没有全面支持Core服务选择。

## 改进内容

### 1. 文件删除操作

**后端改进：**
- 修改 `delete_files_only` API，添加 `core_service_id` 查询参数
- 支持通过指定的Core服务删除文件，如果Core服务不可用则降级到本地删除
- 添加详细的日志记录，显示使用的Core服务

**前端改进：**
- 修改 `deleteFile` API函数，支持传递 `coreServiceId` 参数
- 在文件管理页面删除操作中传递选择的Core服务ID

### 2. 视频旋转操作

**后端改进：**
- 修改 `rotate_videos` API，添加 `core_service_id` 查询参数
- 根据参数选择指定的Core服务或默认服务进行视频旋转

### 3. 视频裁剪操作

**后端改进：**
- 修改 `clip_videos` API，添加 `core_service_id` 查询参数
- 同步处理和后台处理都支持Core服务选择
- 修改后台任务数据结构，保存 `core_service_id` 参数
- 修改后台任务执行函数，从任务参数中获取并使用指定的Core服务

**前端改进：**
- 修改 `clipVideos` API函数，支持传递 `coreServiceId` 参数

### 4. 视频加速操作

**后端改进：**
- 修改 `accelerate_videos` API，添加 `core_service_id` 查询参数
- 同步处理和后台处理都支持Core服务选择
- 修改后台任务数据结构和执行函数，支持Core服务选择

**前端改进：**
- 修改 `accelerateVideos` API函数，支持传递 `coreServiceId` 参数

### 5. 三拼视频操作

**后端改进：**
- 修改 `create_triple_video_task` API，添加 `core_service_id` 查询参数
- 修改后台处理函数 `process_triple_video_task`，支持Core服务选择
- 使用 `get_core_client_by_service_id` 函数根据参数选择Core服务

**前端改进：**
- 修改 `createTripleVideoTask` API函数，支持传递 `coreServiceId` 参数

## 技术实现细节

### Core服务选择逻辑

所有支持Core服务选择的API都遵循以下逻辑：

```python
# 根据core_service_id选择Core服务
if core_service_id:
    core_client = get_core_client_by_service_id(core_service_id)
    logger.info(f"使用指定的Core服务: {core_service_id}")
else:
    from app.core.grpc_client import get_core_client
    core_client = get_core_client()
    logger.info("使用默认Core服务")

if not core_client:
    raise HTTPException(status_code=503, detail="Core服务不可用")
```

### 后台任务支持

对于支持后台处理的操作（如视频裁剪、加速），我们：

1. 在任务数据结构中添加 `core_service_id` 字段
2. 在后台任务执行函数中从任务参数获取 `core_service_id`
3. 使用相同的Core服务选择逻辑

### 前端API调用

所有前端API函数都添加了可选的 `coreServiceId` 参数：

```typescript
export const someAPI = (data: RequestType, coreServiceId?: string) => {
  return request({
    url: '/api/v1/some-endpoint',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

## 使用方式

### 前端使用

在文件管理页面中，用户选择的Core服务会自动传递给所有操作：

```javascript
// 删除文件时传递选择的Core服务ID
const response = await deleteFileAPI(file.path, selectedCoreService.value)

// 其他操作类似
const clipResult = await clipVideos(clipRequest, selectedCoreService.value)
```

### 降级处理

当指定的Core服务不可用时，系统会：

1. 对于删除操作：降级到本地文件系统删除
2. 对于其他操作：返回错误信息，提示Core服务不可用

## 日志记录

所有操作都添加了详细的日志记录：

- 记录使用的Core服务ID
- 记录Core服务选择结果
- 记录操作执行状态

## 兼容性

所有改进都保持向后兼容：

- `core_service_id` 参数都是可选的
- 如果不传递该参数，系统使用默认Core服务
- 现有的API调用不需要修改即可继续工作

## 测试建议

1. 测试选择不同Core服务时的操作执行
2. 测试Core服务不可用时的降级处理
3. 测试后台任务的Core服务选择功能
4. 验证日志记录的完整性

## 后续改进

可以考虑为以下功能添加Core服务选择支持：

1. 音频处理操作（分离音频、人声分离等）
2. 视频水印处理
3. 文件移动和复制操作
4. 其他视频处理功能

这些功能可以按照相同的模式进行改进。
