"""
内容生成gRPC服务实现
"""

import logging
import asyncio
from typing import Dict, Any
import grpc
from . import content_generation_pb2
from . import content_generation_pb2_grpc
from ..services.content_generation_service import get_content_generation_service
from ..services.comfyui_service import test_comfyui_connection, get_comfyui_service
from ..services.file_storage_service import get_file_storage_service

logger = logging.getLogger(__name__)

class ContentGenerationServiceImpl(content_generation_pb2_grpc.ContentGenerationServiceServicer):
    """内容生成gRPC服务实现"""
    
    def __init__(self):
        self.content_service = get_content_generation_service()
        
    async def CreateGenerationTask(self, request, context):
        """创建内容生成任务"""
        try:
            logger.info(f"创建内容生成任务: {request.title}, 账号: {request.account_id}")
            
            # 转换配置
            config = {}
            if request.config:
                config = {
                    "comfyui_workflow": request.config.comfyui_workflow,
                    "duration": request.config.duration,
                    "resolution": request.config.resolution,
                    "fps": request.config.fps,
                    "quality": request.config.quality,
                    "model_settings": dict(request.config.model_settings),
                    "output_settings": dict(request.config.output_settings)
                }
            
            # 创建任务
            result = await self.content_service.create_generation_task(
                account_id=request.account_id,
                platform_id=request.platform_id,
                title=request.title,
                task_type=request.type,
                description=request.description,
                style=request.style or "realistic",
                count=request.count or 1,
                config=config
            )
            
            if result["success"]:
                return content_generation_pb2.CreateGenerationTaskResponse(
                    success=True,
                    task_id=result["task_id"]
                )
            else:
                return content_generation_pb2.CreateGenerationTaskResponse(
                    success=False,
                    error=result["error"]
                )
                
        except Exception as e:
            logger.error(f"创建内容生成任务失败: {str(e)}", exc_info=True)
            return content_generation_pb2.CreateGenerationTaskResponse(
                success=False,
                error=str(e)
            )
            
    async def StartGenerationTask(self, request, context):
        """启动内容生成任务"""
        try:
            logger.info(f"启动内容生成任务: {request.task_id}, 账号: {request.account_id}")
            
            result = await self.content_service.start_generation_task(
                task_id=request.task_id,
                account_id=request.account_id
            )
            
            if result["success"]:
                return content_generation_pb2.StartGenerationTaskResponse(
                    success=True
                )
            else:
                return content_generation_pb2.StartGenerationTaskResponse(
                    success=False,
                    error=result["error"]
                )
                
        except Exception as e:
            logger.error(f"启动内容生成任务失败: {str(e)}", exc_info=True)
            return content_generation_pb2.StartGenerationTaskResponse(
                success=False,
                error=str(e)
            )
            
    async def GetGenerationTaskStatus(self, request, context):
        """获取任务状态"""
        try:
            result = await self.content_service.get_task_status(
                task_id=request.task_id,
                account_id=request.account_id
            )
            
            if result["success"]:
                status_data = result["status"]
                status = content_generation_pb2.GenerationTaskStatus(
                    task_id=status_data["task_id"],
                    status=status_data["status"],
                    progress=status_data["progress"],
                    current_step=status_data.get("current_step", ""),
                    error_message=status_data.get("error_message", ""),
                    created_at=status_data["created_at"],
                    updated_at=status_data["updated_at"],
                    completed_at=status_data.get("completed_at", "")
                )
                
                return content_generation_pb2.GetGenerationTaskStatusResponse(
                    success=True,
                    status=status
                )
            else:
                return content_generation_pb2.GetGenerationTaskStatusResponse(
                    success=False,
                    error=result["error"]
                )
                
        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}", exc_info=True)
            return content_generation_pb2.GetGenerationTaskStatusResponse(
                success=False,
                error=str(e)
            )
            
    async def CancelGenerationTask(self, request, context):
        """取消任务"""
        try:
            # TODO: 实现任务取消逻辑
            return content_generation_pb2.CancelGenerationTaskResponse(
                success=True
            )
            
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}", exc_info=True)
            return content_generation_pb2.CancelGenerationTaskResponse(
                success=False,
                error=str(e)
            )
            
    async def GetGenerationResults(self, request, context):
        """获取生成结果"""
        try:
            result = await self.content_service.get_generation_results(
                task_id=request.task_id,
                account_id=request.account_id
            )
            
            if result["success"]:
                results = []
                for result_data in result["results"]:
                    pb_result = content_generation_pb2.GenerationResult(
                        id=result_data["id"],
                        type=result_data["type"],
                        title=result_data["title"],
                        file_path=result_data["file_path"],
                        file_size=result_data["file_size"],
                        preview_url=result_data["preview_url"],
                        download_url=result_data["download_url"],
                        thumbnail_path=result_data.get("thumbnail_path", ""),
                        metadata=result_data.get("metadata", {})
                    )
                    results.append(pb_result)
                
                return content_generation_pb2.GetGenerationResultsResponse(
                    success=True,
                    results=results
                )
            else:
                return content_generation_pb2.GetGenerationResultsResponse(
                    success=False,
                    error=result["error"]
                )
                
        except Exception as e:
            logger.error(f"获取生成结果失败: {str(e)}", exc_info=True)
            return content_generation_pb2.GetGenerationResultsResponse(
                success=False,
                error=str(e)
            )
            
    async def DownloadGeneratedFile(self, request, context):
        """下载生成的文件"""
        try:
            # 解析result_id获取文件信息
            # result_id格式: {task_id}_result_{index}
            if "_result_" not in request.result_id:
                return content_generation_pb2.DownloadGeneratedFileResponse(
                    success=False,
                    error="无效的结果ID"
                )
            
            # 获取任务结果
            task_id = request.result_id.split("_result_")[0]
            result = await self.content_service.get_generation_results(
                task_id=task_id,
                account_id=request.account_id
            )
            
            if not result["success"]:
                return content_generation_pb2.DownloadGeneratedFileResponse(
                    success=False,
                    error=result["error"]
                )
            
            # 查找对应的结果
            target_result = None
            for res in result["results"]:
                if res["id"] == request.result_id:
                    target_result = res
                    break
            
            if not target_result:
                return content_generation_pb2.DownloadGeneratedFileResponse(
                    success=False,
                    error="结果不存在"
                )
            
            # 读取文件内容
            file_storage = get_file_storage_service()
            file_result = await file_storage.read_file(
                target_result["file_path"],
                request.account_id
            )
            
            if file_result["success"]:
                return content_generation_pb2.DownloadGeneratedFileResponse(
                    success=True,
                    file_data=file_result["content"],
                    filename=file_result["filename"],
                    content_type="application/octet-stream"
                )
            else:
                return content_generation_pb2.DownloadGeneratedFileResponse(
                    success=False,
                    error=file_result["error"]
                )
                
        except Exception as e:
            logger.error(f"下载文件失败: {str(e)}", exc_info=True)
            return content_generation_pb2.DownloadGeneratedFileResponse(
                success=False,
                error=str(e)
            )
            
    async def TestComfyUIConnection(self, request, context):
        """测试ComfyUI连接"""
        try:
            result = await test_comfyui_connection()
            
            return content_generation_pb2.TestComfyUIConnectionResponse(
                success=result["success"],
                status=result["status"],
                version=result.get("version", ""),
                queue_size=result.get("queue_size", 0),
                active_tasks=result.get("active_tasks", 0),
                error=result.get("error", "")
            )
            
        except Exception as e:
            logger.error(f"测试ComfyUI连接失败: {str(e)}", exc_info=True)
            return content_generation_pb2.TestComfyUIConnectionResponse(
                success=False,
                status="disconnected",
                error=str(e)
            )
            
    async def GetAvailableModels(self, request, context):
        """获取可用模型"""
        try:
            # 模拟模型数据
            text_models = [
                content_generation_pb2.ModelInfo(
                    id="gpt-4",
                    name="GPT-4",
                    description="OpenAI GPT-4 文本生成模型",
                    type="text"
                ),
                content_generation_pb2.ModelInfo(
                    id="claude-3",
                    name="Claude-3",
                    description="Anthropic Claude-3 文本生成模型",
                    type="text"
                )
            ]
            
            image_models = [
                content_generation_pb2.ModelInfo(
                    id="stable-diffusion",
                    name="Stable Diffusion",
                    description="开源图像生成模型",
                    type="image"
                ),
                content_generation_pb2.ModelInfo(
                    id="midjourney",
                    name="Midjourney",
                    description="高质量艺术图像生成",
                    type="image"
                )
            ]
            
            video_models = [
                content_generation_pb2.ModelInfo(
                    id="runway",
                    name="Runway ML",
                    description="专业视频生成平台",
                    type="video"
                ),
                content_generation_pb2.ModelInfo(
                    id="pika",
                    name="Pika Labs",
                    description="AI视频生成工具",
                    type="video"
                )
            ]
            
            return content_generation_pb2.GetAvailableModelsResponse(
                success=True,
                text_models=text_models,
                image_models=image_models,
                video_models=video_models
            )
            
        except Exception as e:
            logger.error(f"获取可用模型失败: {str(e)}", exc_info=True)
            return content_generation_pb2.GetAvailableModelsResponse(
                success=False,
                error=str(e)
            )
