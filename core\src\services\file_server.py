"""
Core服务的HTTP文件服务器
用于提供视频缩略图等静态文件的HTTP访问
"""

import os
import asyncio
import logging
import socket
from pathlib import Path
from typing import Optional
from aiohttp import web, web_request
from aiohttp.web_response import Response
import mimetypes

logger = logging.getLogger(__name__)


def get_local_ip() -> str:
    """获取本机IP地址"""
    try:
        # 创建一个UDP socket连接到外部地址来获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 连接到一个外部地址（不会实际发送数据）
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            return local_ip
    except Exception as e:
        logger.warning(f"获取本机IP失败，使用localhost: {str(e)}")
        return "localhost"


class CoreFileServer:
    """Core文件服务器，提供静态文件的HTTP访问"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8001):
        self.host = host
        self.port = port
        self.app = None
        self.runner = None
        self.site = None
        
        # 允许访问的文件类型
        self.allowed_extensions = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',  # 图片
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv'     # 视频
        }
        
        # 安全的根目录列表（只允许访问这些目录下的文件）
        # 使用Core项目根目录作为基础路径
        import os
        core_root = Path(__file__).parent.parent.parent  # 从 core/src/services/ 回到 core/
        self.safe_roots = [
            (core_root / ".video_cache").resolve(),  # 视频缓存目录
            (core_root / "temp").resolve(),          # 临时文件目录
            Path("H:/PublishSystem").resolve(),      # 发布系统根目录（包含人脸检测图片）
        ]

    async def start(self):
        """启动文件服务器"""
        try:
            self.app = web.Application()

            # 添加路由
            self.app.router.add_get('/files/{path:.*}', self.serve_file)
            self.app.router.add_get('/health', self.health_check)

            # 添加CORS支持
            self.app.middlewares.append(self.cors_middleware)

            self.runner = web.AppRunner(self.app)
            await self.runner.setup()

            self.site = web.TCPSite(self.runner, self.host, self.port)
            await self.site.start()

            logger.info(f"Core文件服务器启动成功: http://{self.host}:{self.port}")
            logger.info(f"安全根目录: {[str(root) for root in self.safe_roots]}")

        except Exception as e:
            logger.error(f"启动Core文件服务器失败: {str(e)}")
            raise

    async def stop(self):
        """停止文件服务器"""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.info("Core文件服务器已停止")
        except Exception as e:
            logger.error(f"停止Core文件服务器失败: {str(e)}")

    @web.middleware
    async def cors_middleware(self, request: web_request.Request, handler):
        """CORS中间件"""
        try:
            response = await handler(request)
        except Exception as e:
            # 处理异常并返回适当的响应
            logger.error(f"请求处理异常: {str(e)}")
            response = web.Response(status=500, text="Internal Server Error")
        
        # 添加CORS头
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        
        return response

    async def health_check(self, request: web_request.Request) -> Response:
        """健康检查端点"""
        return web.json_response({"status": "ok", "service": "core-file-server"})

    async def serve_file(self, request: web_request.Request) -> Response:
        """提供文件服务"""
        try:
            # 获取请求的文件路径并进行URL解码
            import urllib.parse
            file_path = request.match_info['path']
            # URL解码，处理中文字符
            file_path = urllib.parse.unquote(file_path)
            logger.info(f"收到文件请求: {file_path}")
            logger.info(f"URL解码后的路径: {file_path}")

            # 安全检查：防止路径遍历攻击
            if not self._is_safe_path(file_path):
                logger.warning(f"拒绝不安全的文件路径访问: {file_path}")
                return web.Response(status=403, text="Forbidden")

            # 解析实际文件路径
            actual_path = self._resolve_file_path(file_path)
            logger.info(f"解析后的文件路径: {actual_path}")

            if not actual_path or not actual_path.exists():
                logger.warning(f"文件不存在: {file_path} -> {actual_path}")
                logger.info(f"当前工作目录: {Path.cwd()}")
                logger.info(f"安全根目录: {[str(root) for root in self.safe_roots]}")
                return web.Response(status=404, text="File not found")
            
            # 检查文件扩展名
            if not self._is_allowed_file(actual_path):
                logger.warning(f"不允许访问的文件类型: {actual_path}")
                return web.Response(status=403, text="File type not allowed")
            
            # 读取并返回文件
            try:
                with open(actual_path, 'rb') as f:
                    content = f.read()
                
                # 获取MIME类型
                mime_type, _ = mimetypes.guess_type(str(actual_path))
                if not mime_type:
                    mime_type = 'application/octet-stream'
                
                logger.debug(f"提供文件: {actual_path} ({len(content)} bytes, {mime_type})")
                
                return web.Response(
                    body=content,
                    content_type=mime_type,
                    headers={
                        'Cache-Control': 'public, max-age=3600',  # 缓存1小时
                        'Content-Length': str(len(content))
                    }
                )
                
            except IOError as e:
                logger.error(f"读取文件失败: {actual_path}, {str(e)}")
                return web.Response(status=500, text="Failed to read file")
                
        except Exception as e:
            logger.error(f"文件服务异常: {str(e)}")
            return web.Response(status=500, text="Internal server error")

    def _is_safe_path(self, path: str) -> bool:
        """检查路径是否安全（防止路径遍历攻击）"""
        # 检查是否包含危险字符
        dangerous_patterns = ['..', '~', '\\', '//', '\\\\']
        for pattern in dangerous_patterns:
            if pattern in path:
                return False
        
        # 检查是否为绝对路径
        if os.path.isabs(path):
            return False
            
        return True

    def _resolve_file_path(self, path: str) -> Optional[Path]:
        """解析文件路径到实际的文件系统路径"""
        try:
            # 规范化路径，移除开头的 ./ 或 ./
            normalized_path = path.lstrip('./')

            # 尝试在各个安全根目录中查找文件
            for root in self.safe_roots:
                full_path = None

                # 如果请求路径以.video_cache/开头，移除这个前缀
                if normalized_path.startswith('.video_cache/'):
                    # 移除 .video_cache/ 前缀，因为root已经是.video_cache目录
                    relative_path = normalized_path[len('.video_cache/'):]
                    full_path = (root / relative_path).resolve()
                else:
                    # 直接在root目录下查找
                    full_path = (root / normalized_path).resolve()

                # 确保解析后的路径仍在安全根目录内
                try:
                    full_path.relative_to(root)
                except ValueError:
                    continue  # 路径不在安全根目录内，跳过

                logger.debug(f"尝试路径: {full_path}, 存在: {full_path.exists()}")

                if full_path.exists() and full_path.is_file():
                    return full_path

            return None

        except Exception as e:
            logger.error(f"解析文件路径失败: {path}, {str(e)}")
            return None

    def _is_allowed_file(self, file_path: Path) -> bool:
        """检查文件类型是否被允许"""
        return file_path.suffix.lower() in self.allowed_extensions

    def get_file_url(self, file_path: str) -> str:
        """获取文件的HTTP URL"""
        # 确定实际的主机地址
        actual_host = self.host
        if self.host == "0.0.0.0":
            # 如果监听地址是0.0.0.0，获取本机真实IP地址
            actual_host = get_local_ip()

        # 将本地文件路径转换为HTTP URL
        # 移除路径中的安全根目录前缀
        for root in self.safe_roots:
            try:
                file_path_obj = Path(file_path)
                rel_path = file_path_obj.relative_to(root)
                # 确保使用正斜杠作为URL路径分隔符
                url_path = rel_path.as_posix()
                return f"http://{actual_host}:{self.port}/files/{url_path}"
            except ValueError:
                continue

        # 如果文件不在安全根目录中，尝试智能处理
        # 检查是否是.video_cache路径
        file_path_str = str(file_path).replace('\\', '/')
        if '.video_cache/' in file_path_str:
            # 提取.video_cache/之后的部分
            cache_index = file_path_str.find('.video_cache/')
            if cache_index >= 0:
                relative_part = file_path_str[cache_index + len('.video_cache/'):]
                return f"http://{actual_host}:{self.port}/files/{relative_part}"

        # 默认情况：将Windows路径分隔符转换为URL路径分隔符
        url_path = file_path_str
        return f"http://{actual_host}:{self.port}/files/{url_path}"


# 全局文件服务器实例
_file_server: Optional[CoreFileServer] = None


async def start_file_server(host: str = "0.0.0.0", port: int = 8001) -> CoreFileServer:
    """启动Core文件服务器"""
    global _file_server
    
    if _file_server is None:
        _file_server = CoreFileServer(host, port)
        await _file_server.start()
    
    return _file_server


async def stop_file_server():
    """停止Core文件服务器"""
    global _file_server
    
    if _file_server:
        await _file_server.stop()
        _file_server = None


def get_file_server() -> Optional[CoreFileServer]:
    """获取文件服务器实例"""
    return _file_server


def get_file_url(file_path: str) -> Optional[str]:
    """获取文件的HTTP URL"""
    if _file_server:
        return _file_server.get_file_url(file_path)
    return None
