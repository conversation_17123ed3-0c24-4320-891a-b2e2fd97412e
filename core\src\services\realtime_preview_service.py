"""
实时预览服务

提供视频编辑器的实时预览功能，支持低延迟的视频帧生成和流式传输。
"""

import os
import asyncio
import logging
import time
import json
import base64
from typing import Dict, Any, Optional, List, Tuple
import cv2
import numpy as np
from PIL import Image
import io
import tempfile
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class RealtimePreviewService:
    """实时预览服务"""
    
    def __init__(self):
        self.logger = logger
        self.preview_cache = {}  # 预览帧缓存
        self.cache_size_limit = 100  # 最大缓存帧数
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 预览质量设置
        self.quality_settings = {
            'low': {'width': 480, 'height': 270, 'quality': 60},
            'medium': {'width': 720, 'height': 405, 'quality': 75},
            'high': {'width': 1280, 'height': 720, 'quality': 85},
            'full': {'width': 1920, 'height': 1080, 'quality': 95}
        }
    
    async def generate_preview_frame(self, project_data: Dict[str, Any], 
                                   timestamp: float, quality: str = "medium") -> Dict[str, Any]:
        """
        生成指定时间点的预览帧
        
        Args:
            project_data: 项目数据
            timestamp: 时间戳（秒）
            quality: 预览质量 (low/medium/high/full)
        
        Returns:
            包含预览帧数据的字典
        """
        try:
            start_time = time.time()
            
            # 检查缓存
            cache_key = f"{project_data.get('_id', 'unknown')}_{timestamp}_{quality}"
            if cache_key in self.preview_cache:
                self.logger.debug(f"使用缓存的预览帧: {cache_key}")
                return self.preview_cache[cache_key]
            
            # 获取质量设置
            quality_config = self.quality_settings.get(quality, self.quality_settings['medium'])
            
            # 生成预览帧
            frame_data = await self._render_frame_at_time(
                project_data, timestamp, quality_config
            )
            
            if frame_data:
                # 编码为base64
                frame_base64 = self._encode_frame_to_base64(frame_data, quality_config['quality'])
                
                result = {
                    'success': True,
                    'frame_data': frame_base64,
                    'timestamp': timestamp,
                    'quality': quality,
                    'resolution': {
                        'width': quality_config['width'],
                        'height': quality_config['height']
                    },
                    'processing_time_ms': int((time.time() - start_time) * 1000)
                }
                
                # 缓存结果
                self._cache_frame(cache_key, result)
                
                return result
            else:
                return {
                    'success': False,
                    'error': '无法生成预览帧',
                    'timestamp': timestamp
                }
                
        except Exception as e:
            self.logger.error(f"生成预览帧失败: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'timestamp': timestamp
            }
    
    async def _render_frame_at_time(self, project_data: Dict[str, Any], 
                                  timestamp: float, quality_config: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        在指定时间点渲染视频帧
        
        Args:
            project_data: 项目数据
            timestamp: 时间戳
            quality_config: 质量配置
        
        Returns:
            渲染的帧数据 (numpy数组)
        """
        try:
            # 获取项目设置
            settings = project_data.get('settings', {})
            resolution = settings.get('resolution', {'width': 1920, 'height': 1080})
            
            # 创建空白画布
            canvas_width = quality_config['width']
            canvas_height = quality_config['height']
            canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
            
            # 获取时间轴数据
            timeline = project_data.get('timeline', {})
            tracks = timeline.get('tracks', [])
            
            # 按轨道类型和层级排序
            video_tracks = [t for t in tracks if t.get('type') == 'video' and t.get('enabled', True)]
            
            # 渲染每个视频轨道
            for track in video_tracks:
                track_frame = await self._render_track_at_time(track, timestamp, quality_config)
                if track_frame is not None:
                    # 合成到画布上
                    canvas = self._composite_frames(canvas, track_frame)
            
            # 渲染文字和效果
            canvas = await self._render_overlays(canvas, tracks, timestamp, quality_config)
            
            return canvas
            
        except Exception as e:
            self.logger.error(f"渲染帧失败: {str(e)}")
            return None
    
    async def _render_track_at_time(self, track: Dict[str, Any], 
                                  timestamp: float, quality_config: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        渲染指定轨道在指定时间的内容
        
        Args:
            track: 轨道数据
            timestamp: 时间戳
            quality_config: 质量配置
        
        Returns:
            轨道渲染结果
        """
        try:
            clips = track.get('clips', [])
            
            # 找到当前时间点的活跃片段
            active_clips = []
            for clip in clips:
                start_time = clip.get('startTime', 0)
                duration = clip.get('duration', 0)
                end_time = start_time + duration
                
                if start_time <= timestamp <= end_time:
                    active_clips.append(clip)
            
            if not active_clips:
                return None
            
            # 渲染第一个活跃片段（简化处理）
            clip = active_clips[0]
            return await self._render_clip_at_time(clip, timestamp, quality_config)
            
        except Exception as e:
            self.logger.error(f"渲染轨道失败: {str(e)}")
            return None
    
    async def _render_clip_at_time(self, clip: Dict[str, Any], 
                                 timestamp: float, quality_config: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        渲染指定片段在指定时间的内容
        
        Args:
            clip: 片段数据
            timestamp: 时间戳
            quality_config: 质量配置
        
        Returns:
            片段渲染结果
        """
        try:
            # 计算片段内的相对时间
            start_time = clip.get('startTime', 0)
            relative_time = timestamp - start_time
            
            # 获取媒体资源信息
            media_asset_id = clip.get('mediaAssetId')
            if not media_asset_id:
                return None
            
            # 这里应该根据媒体资源类型进行不同的处理
            # 暂时生成一个彩色矩形作为占位符
            width = quality_config['width']
            height = quality_config['height']
            
            # 生成基于时间的颜色
            hue = int((relative_time * 30) % 360)  # 30度/秒的色相变化
            color = self._hsv_to_rgb(hue, 100, 80)
            
            frame = np.full((height, width, 3), color, dtype=np.uint8)
            
            # 添加时间信息文字
            cv2.putText(frame, f"Time: {timestamp:.2f}s", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Clip: {clip.get('id', 'unknown')}", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return frame
            
        except Exception as e:
            self.logger.error(f"渲染片段失败: {str(e)}")
            return None
    
    async def _render_overlays(self, canvas: np.ndarray, tracks: List[Dict[str, Any]], 
                             timestamp: float, quality_config: Dict[str, Any]) -> np.ndarray:
        """
        渲染覆盖层（文字、效果等）
        
        Args:
            canvas: 基础画布
            tracks: 所有轨道
            timestamp: 时间戳
            quality_config: 质量配置
        
        Returns:
            添加覆盖层后的画布
        """
        try:
            # 渲染字幕轨道
            subtitle_tracks = [t for t in tracks if t.get('type') == 'subtitle' and t.get('enabled', True)]
            
            for track in subtitle_tracks:
                clips = track.get('clips', [])
                for clip in clips:
                    start_time = clip.get('startTime', 0)
                    duration = clip.get('duration', 0)
                    end_time = start_time + duration
                    
                    if start_time <= timestamp <= end_time:
                        # 渲染字幕
                        text = clip.get('text', '')
                        if text:
                            canvas = self._render_text_on_canvas(canvas, text, clip)
            
            return canvas
            
        except Exception as e:
            self.logger.error(f"渲染覆盖层失败: {str(e)}")
            return canvas
    
    def _render_text_on_canvas(self, canvas: np.ndarray, text: str, clip: Dict[str, Any]) -> np.ndarray:
        """
        在画布上渲染文字
        
        Args:
            canvas: 画布
            text: 文字内容
            clip: 文字片段信息
        
        Returns:
            添加文字后的画布
        """
        try:
            # 获取文字样式
            style = clip.get('style', {})
            font_size = style.get('fontSize', 24)
            color = style.get('color', '#ffffff')
            
            # 转换颜色格式
            if color.startswith('#'):
                color_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
                color_bgr = (color_rgb[2], color_rgb[1], color_rgb[0])  # BGR格式
            else:
                color_bgr = (255, 255, 255)
            
            # 计算文字位置
            text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, font_size/30, 2)[0]
            x = (canvas.shape[1] - text_size[0]) // 2  # 居中
            y = canvas.shape[0] - 50  # 底部
            
            # 绘制文字
            cv2.putText(canvas, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX, 
                       font_size/30, color_bgr, 2, cv2.LINE_AA)
            
            return canvas
            
        except Exception as e:
            self.logger.error(f"渲染文字失败: {str(e)}")
            return canvas
    
    def _composite_frames(self, base: np.ndarray, overlay: np.ndarray) -> np.ndarray:
        """
        合成两个帧
        
        Args:
            base: 基础帧
            overlay: 覆盖帧
        
        Returns:
            合成后的帧
        """
        try:
            # 简单的alpha混合（这里假设overlay有透明度）
            # 实际实现中应该支持更复杂的混合模式
            alpha = 0.8
            result = cv2.addWeighted(base, 1-alpha, overlay, alpha, 0)
            return result
            
        except Exception as e:
            self.logger.error(f"帧合成失败: {str(e)}")
            return base
    
    def _encode_frame_to_base64(self, frame: np.ndarray, quality: int) -> str:
        """
        将帧编码为base64字符串
        
        Args:
            frame: 帧数据
            quality: JPEG质量
        
        Returns:
            base64编码的图像数据
        """
        try:
            # 转换为PIL图像
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # 编码为JPEG
            buffer = io.BytesIO()
            pil_image.save(buffer, format='JPEG', quality=quality, optimize=True)
            
            # 转换为base64
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return image_base64
            
        except Exception as e:
            self.logger.error(f"帧编码失败: {str(e)}")
            return ""
    
    def _cache_frame(self, cache_key: str, frame_data: Dict[str, Any]):
        """
        缓存预览帧
        
        Args:
            cache_key: 缓存键
            frame_data: 帧数据
        """
        try:
            # 检查缓存大小限制
            if len(self.preview_cache) >= self.cache_size_limit:
                # 移除最旧的缓存项
                oldest_key = next(iter(self.preview_cache))
                del self.preview_cache[oldest_key]
            
            self.preview_cache[cache_key] = frame_data
            
        except Exception as e:
            self.logger.error(f"缓存帧失败: {str(e)}")
    
    def _hsv_to_rgb(self, h: int, s: int, v: int) -> Tuple[int, int, int]:
        """
        HSV转RGB
        
        Args:
            h: 色相 (0-360)
            s: 饱和度 (0-100)
            v: 明度 (0-100)
        
        Returns:
            RGB颜色元组
        """
        import colorsys
        r, g, b = colorsys.hsv_to_rgb(h/360, s/100, v/100)
        return (int(r*255), int(g*255), int(b*255))
    
    def clear_cache(self):
        """清除预览缓存"""
        self.preview_cache.clear()
        self.logger.info("预览缓存已清除")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self.preview_cache),
            'cache_limit': self.cache_size_limit,
            'cache_keys': list(self.preview_cache.keys())
        }
