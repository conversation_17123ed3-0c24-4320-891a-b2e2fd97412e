"""
视频预览服务
负责生成视频缩略图、预览片段和提取视频元数据
"""

import os
import asyncio
import subprocess
import json
import hashlib
import time
import logging
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from src.services.file_server import get_file_url

logger = logging.getLogger(__name__)


class VideoPreviewService:
    """视频预览服务类"""

    def __init__(self, cache_dir: str = None):
        """初始化视频预览服务
        
        Args:
            cache_dir: 缓存目录路径，默认为当前目录下的.video_cache
        """
        self.cache_dir = Path(cache_dir or ".video_cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.thumbnail_cache_dir = self.cache_dir / "thumbnails"
        self.preview_cache_dir = self.cache_dir / "previews"
        self.thumbnail_cache_dir.mkdir(exist_ok=True)
        self.preview_cache_dir.mkdir(exist_ok=True)
        
        # 支持的视频格式
        self.supported_video_extensions = {
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', 
            '.webm', '.3gp', '.mpg', '.mpeg', '.ts', '.mts'
        }

    def _get_file_hash(self, file_path: str) -> str:
        """获取文件的MD5哈希值（用于缓存键）"""
        try:
            # 使用文件路径和修改时间生成哈希，避免读取大文件
            stat = os.stat(file_path)
            hash_input = f"{file_path}_{stat.st_size}_{stat.st_mtime}"
            return hashlib.md5(hash_input.encode()).hexdigest()
        except Exception as e:
            logger.error(f"获取文件哈希失败: {str(e)}")
            return hashlib.md5(file_path.encode()).hexdigest()

    def _is_video_file(self, file_path: str) -> bool:
        """检查文件是否为支持的视频格式"""
        return Path(file_path).suffix.lower() in self.supported_video_extensions

    def _calculate_thumbnail_size(self, original_width: int, original_height: int,
                                max_width: int, max_height: int) -> tuple[int, int]:
        """计算保持比例的缩略图尺寸

        Args:
            original_width: 原始视频宽度
            original_height: 原始视频高度
            max_width: 最大允许宽度
            max_height: 最大允许高度

        Returns:
            (width, height) 计算后的缩略图尺寸
        """
        if original_width <= 0 or original_height <= 0:
            return max_width, max_height

        # 计算原始比例
        aspect_ratio = original_width / original_height

        # 根据最大尺寸限制计算新尺寸
        if aspect_ratio > 1:  # 横版视频
            # 以宽度为准
            new_width = min(max_width, original_width)
            new_height = int(new_width / aspect_ratio)

            # 如果高度超出限制，以高度为准重新计算
            if new_height > max_height:
                new_height = max_height
                new_width = int(new_height * aspect_ratio)
        else:  # 竖版或正方形视频
            # 以高度为准
            new_height = min(max_height, original_height)
            new_width = int(new_height * aspect_ratio)

            # 如果宽度超出限制，以宽度为准重新计算
            if new_width > max_width:
                new_width = max_width
                new_height = int(new_width / aspect_ratio)

        # 确保尺寸至少为1
        new_width = max(1, new_width)
        new_height = max(1, new_height)

        return new_width, new_height

    async def _get_image_dimensions(self, image_path: str) -> tuple[int, int]:
        """获取图片的实际尺寸

        Args:
            image_path: 图片文件路径

        Returns:
            (width, height) 图片的实际尺寸
        """
        try:
            # 使用ffprobe获取图片尺寸
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', image_path
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                import json
                probe_data = json.loads(stdout.decode('utf-8'))

                for stream in probe_data.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        width = stream.get('width', 0)
                        height = stream.get('height', 0)
                        if width > 0 and height > 0:
                            return width, height

            # 如果ffprobe失败，返回默认值
            logger.warning(f"无法获取图片尺寸: {image_path}")
            return 0, 0

        except Exception as e:
            logger.error(f"获取图片尺寸异常: {str(e)}")
            return 0, 0

    async def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """获取视频基本信息"""
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        if not self._is_video_file(video_path):
            raise ValueError(f"不支持的视频格式: {video_path}")

        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', 
                '-show_format', '-show_streams', video_path
            ]
            
            logger.debug(f"执行ffprobe命令获取视频信息: {video_path}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"ffprobe命令执行失败: {cmd}")
                logger.error(f"返回码: {process.returncode}")
                logger.error(f"错误输出: {error_msg}")
                logger.error(f"标准输出: {stdout.decode('utf-8', errors='ignore')}")
                raise RuntimeError(f"ffprobe执行失败: {error_msg}")
            
            # 解析JSON输出
            info = json.loads(stdout.decode('utf-8'))
            
            # 提取基本信息
            format_info = info.get('format', {})
            streams = info.get('streams', [])
            
            # 查找视频流
            video_stream = None
            audio_stream = None
            
            for stream in streams:
                if stream.get('codec_type') == 'video' and not video_stream:
                    video_stream = stream
                elif stream.get('codec_type') == 'audio' and not audio_stream:
                    audio_stream = stream
            
            if not video_stream:
                raise ValueError("未找到视频流")
            
            # 构建返回信息
            result = {
                'duration': float(format_info.get('duration', 0)),
                'size': int(format_info.get('size', 0)),
                'format_name': format_info.get('format_name', ''),
                'video': {
                    'codec': video_stream.get('codec_name', ''),
                    'width': int(video_stream.get('width', 0)),
                    'height': int(video_stream.get('height', 0)),
                    'frame_rate': self._parse_frame_rate(video_stream.get('r_frame_rate', '0/1')),
                    'bitrate': int(video_stream.get('bit_rate', 0)),
                    'pixel_format': video_stream.get('pix_fmt', ''),
                },
                'audio': None
            }
            
            if audio_stream:
                result['audio'] = {
                    'codec': audio_stream.get('codec_name', ''),
                    'sample_rate': int(audio_stream.get('sample_rate', 0)),
                    'channels': int(audio_stream.get('channels', 0)),
                    'bitrate': int(audio_stream.get('bit_rate', 0)),
                }
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"解析ffprobe输出失败: {str(e)}")
            raise RuntimeError(f"解析视频信息失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取视频信息失败: {str(e)}")
            raise

    def _parse_frame_rate(self, frame_rate_str: str) -> float:
        """解析帧率字符串"""
        try:
            if '/' in frame_rate_str:
                num, den = frame_rate_str.split('/')
                return float(num) / float(den) if float(den) != 0 else 0.0
            return float(frame_rate_str)
        except:
            return 0.0

    async def generate_thumbnail(self, video_path: str, output_path: str = None,
                               timestamp: float = None, max_width: int = 320,
                               max_height: int = 180, quality: int = 85,
                               force_regenerate: bool = False) -> Dict[str, Any]:
        """生成视频缩略图

        Args:
            video_path: 视频文件路径
            output_path: 输出路径（可选）
            timestamp: 截取时间点（可选，默认为视频中间位置）
            max_width: 最大宽度（保持比例的情况下）
            max_height: 最大高度（保持比例的情况下）
            quality: 质量（1-100）
            force_regenerate: 是否强制重新生成

        Returns:
            包含缩略图信息的字典
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        if not self._is_video_file(video_path):
            raise ValueError(f"不支持的视频格式: {video_path}")

        start_time = time.time()

        # 获取视频信息以确定原始尺寸
        video_info = await self.get_video_info(video_path)
        original_width = video_info.get('width', max_width)
        original_height = video_info.get('height', max_height)

        # 计算保持比例的缩略图尺寸
        width, height = self._calculate_thumbnail_size(
            original_width, original_height, max_width, max_height
        )

        # 生成缓存键和输出路径
        file_hash = self._get_file_hash(video_path)
        cache_key = f"{file_hash}_{timestamp or 'mid'}_{width}x{height}_q{quality}"

        if not output_path:
            output_path = str(self.thumbnail_cache_dir / f"{cache_key}.jpg")

        logger.info(f"🔍 缩略图缓存检查: {video_path}")
        logger.info(f"📁 缓存路径: {output_path}")
        logger.info(f"🔑 缓存键: {cache_key}")
        logger.info(f"🔄 强制重新生成: {force_regenerate}")

        # 检查缓存
        if not force_regenerate and os.path.exists(output_path):
            logger.info(f"✅ 使用缓存的缩略图: {output_path}")

            # 获取实际生成的缩略图尺寸
            actual_width, actual_height = await self._get_image_dimensions(output_path)

            # 获取HTTP URL
            http_url = get_file_url(output_path)
            if not http_url:
                # 如果文件服务器未启动，生成一个默认URL
                # 使用相对于.video_cache目录的路径
                try:
                    cache_dir = Path(output_path).parent.parent  # 从thumbnails回到.video_cache
                    rel_path = Path(output_path).relative_to(cache_dir)
                    http_url = f"http://localhost:8001/files/{rel_path.as_posix()}"
                except ValueError:
                    # 如果无法计算相对路径，使用文件名
                    http_url = f"http://localhost:8001/files/thumbnails/{Path(output_path).name}"
            return {
                'success': True,
                'thumbnail_path': output_path,
                'thumbnail_url': http_url,  # 添加HTTP URL
                'thumbnail_size': os.path.getsize(output_path),
                'actual_width': actual_width,  # 实际生成的宽度
                'actual_height': actual_height,  # 实际生成的高度
                'generation_time_ms': int((time.time() - start_time) * 1000),
                'from_cache': True
            }
        
        # 缓存不存在，需要生成新缩略图
        logger.info(f"❌ 缓存不存在，开始生成新缩略图: {video_path}")

        try:
            # 获取视频信息以确定时间戳
            if timestamp is None:
                video_info = await self.get_video_info(video_path)
                timestamp = video_info['duration'] / 2  # 默认取中间位置
            
            # 构建ffmpeg命令 - 使用智能缩放保持比例
            # 使用scale滤镜的智能模式：scale=w:h:force_original_aspect_ratio=decrease
            # 这会保持原始比例，并确保输出尺寸不超过指定的最大值
            scale_filter = f'scale={width}:{height}:force_original_aspect_ratio=decrease'

            cmd = [
                'ffmpeg', '-y', '-i', video_path,
                '-ss', str(timestamp),
                '-vframes', '1',
                '-vf', scale_filter,
                '-q:v', str(100 - quality),  # ffmpeg的质量参数是反向的
                output_path
            ]
            
            logger.debug(f"生成缩略图: {video_path} -> {output_path}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore')
                raise RuntimeError(f"ffmpeg生成缩略图失败: {error_msg}")
            
            if not os.path.exists(output_path):
                raise RuntimeError("缩略图文件生成失败")

            # 获取实际生成的缩略图尺寸
            actual_width, actual_height = await self._get_image_dimensions(output_path)

            # 获取HTTP URL
            http_url = get_file_url(output_path)
            if not http_url:
                # 如果文件服务器未启动，生成一个默认URL
                # 使用相对于.video_cache目录的路径
                try:
                    cache_dir = Path(output_path).parent.parent  # 从thumbnails回到.video_cache
                    rel_path = Path(output_path).relative_to(cache_dir)
                    http_url = f"http://localhost:8001/files/{rel_path.as_posix()}"
                except ValueError:
                    # 如果无法计算相对路径，使用文件名
                    http_url = f"http://localhost:8001/files/thumbnails/{Path(output_path).name}"

            logger.info(f"✅ 缩略图生成成功: {output_path}")
            logger.info(f"🌐 HTTP URL: {http_url}")

            return {
                'success': True,
                'thumbnail_path': output_path,
                'thumbnail_url': http_url,  # 添加HTTP URL
                'thumbnail_size': os.path.getsize(output_path),
                'actual_width': actual_width,  # 实际生成的宽度
                'actual_height': actual_height,  # 实际生成的高度
                'generation_time_ms': int((time.time() - start_time) * 1000),
                'from_cache': False
            }
            
        except Exception as e:
            logger.error(f"生成缩略图失败: {str(e)}")
            raise

    async def generate_preview_clip(self, video_path: str, output_path: str = None,
                                  start_time: float = 0, duration: float = 30,
                                  output_quality: str = 'medium',
                                  force_regenerate: bool = False) -> Dict[str, Any]:
        """生成视频预览片段"""
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        if not self._is_video_file(video_path):
            raise ValueError(f"不支持的视频格式: {video_path}")

        start_time_gen = time.time()
        
        # 生成缓存键和输出路径
        file_hash = self._get_file_hash(video_path)
        cache_key = f"{file_hash}_{start_time}_{duration}_{output_quality}"
        
        if not output_path:
            video_name = Path(video_path).stem
            output_path = str(self.preview_cache_dir / f"{cache_key}_{video_name}_preview.mp4")
        
        # 检查缓存
        if not force_regenerate and os.path.exists(output_path):
            logger.debug(f"使用缓存的预览片段: {output_path}")
            return {
                'success': True,
                'preview_clip_path': output_path,
                'preview_clip_size': os.path.getsize(output_path),
                'generation_time_ms': int((time.time() - start_time_gen) * 1000),
                'from_cache': True
            }
        
        try:
            # 质量参数映射
            quality_params = {
                'high': ['-crf', '18'],
                'medium': ['-crf', '23'],
                'low': ['-crf', '28']
            }
            
            quality_args = quality_params.get(output_quality, quality_params['medium'])
            
            # 构建ffmpeg命令
            cmd = [
                'ffmpeg', '-y', '-i', video_path,
                '-ss', str(start_time),
                '-t', str(duration),
                '-c:v', 'libx264', '-preset', 'fast'
            ] + quality_args + [
                '-c:a', 'aac', '-b:a', '128k',
                output_path
            ]
            
            logger.debug(f"生成预览片段: {video_path} -> {output_path}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore')
                raise RuntimeError(f"ffmpeg生成预览片段失败: {error_msg}")
            
            if not os.path.exists(output_path):
                raise RuntimeError("预览片段文件生成失败")
            
            return {
                'success': True,
                'preview_clip_path': output_path,
                'preview_clip_size': os.path.getsize(output_path),
                'generation_time_ms': int((time.time() - start_time_gen) * 1000),
                'from_cache': False
            }
            
        except Exception as e:
            logger.error(f"生成预览片段失败: {str(e)}")
            raise

    def cleanup_cache(self, max_age_days: int = 7) -> Dict[str, int]:
        """清理过期的缓存文件"""
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600
        
        cleaned_thumbnails = 0
        cleaned_previews = 0
        
        # 清理缩略图缓存
        for file_path in self.thumbnail_cache_dir.glob("*"):
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    try:
                        file_path.unlink()
                        cleaned_thumbnails += 1
                    except Exception as e:
                        logger.warning(f"删除缓存文件失败: {file_path}, {str(e)}")
        
        # 清理预览片段缓存
        for file_path in self.preview_cache_dir.glob("*"):
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    try:
                        file_path.unlink()
                        cleaned_previews += 1
                    except Exception as e:
                        logger.warning(f"删除缓存文件失败: {file_path}, {str(e)}")
        
        logger.info(f"缓存清理完成: 删除 {cleaned_thumbnails} 个缩略图, {cleaned_previews} 个预览片段")
        
        return {
            'cleaned_thumbnails': cleaned_thumbnails,
            'cleaned_previews': cleaned_previews
        }
