"""
内容生成服务 - Core服务版本
负责管理内容生成任务的生命周期，包括任务调度、进度跟踪、结果管理等
支持按账号隔离的内容生成
"""

import logging
import asyncio
import os
import json
import uuid
import shutil
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
from .comfyui_service import get_comfyui_service
from .file_storage_service import get_file_storage_service

logger = logging.getLogger(__name__)

class ContentGenerationService:
    """内容生成服务"""
    
    def __init__(self):
        self.running_tasks = {}  # 正在运行的任务
        self.task_queue = asyncio.Queue()  # 任务队列
        self.max_concurrent_tasks = 3  # 最大并发任务数
        self.worker_tasks = []  # 工作线程
        self.is_running = False
        
    async def start(self):
        """启动服务"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 启动工作线程
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(worker)
            
        logger.info(f"内容生成服务已启动，工作线程数: {self.max_concurrent_tasks}")
        
    async def stop(self):
        """停止服务"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 取消所有工作线程
        for worker in self.worker_tasks:
            worker.cancel()
            
        # 等待工作线程结束
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()
        
        logger.info("内容生成服务已停止")
        
    async def create_generation_task(self, account_id: str, platform_id: str, title: str, 
                                   task_type: str, description: str, style: str = "realistic",
                                   count: int = 1, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """创建内容生成任务"""
        try:
            task_id = str(uuid.uuid4())
            
            # 创建任务数据
            task_data = {
                "task_id": task_id,
                "account_id": account_id,
                "platform_id": platform_id,
                "title": title,
                "type": task_type,
                "description": description,
                "style": style,
                "count": count,
                "config": config or {},
                "status": "pending",
                "progress": 0.0,
                "current_step": "任务已创建",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "results": []
            }
            
            # 保存任务数据到文件系统（按账号隔离）
            await self._save_task_data(task_data)
            
            logger.info(f"创建内容生成任务成功: {task_id}, 账号: {account_id}")
            
            return {
                "success": True,
                "task_id": task_id
            }
            
        except Exception as e:
            logger.error(f"创建内容生成任务失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def start_generation_task(self, task_id: str, account_id: str) -> Dict[str, Any]:
        """启动内容生成任务"""
        try:
            # 加载任务数据
            task_data = await self._load_task_data(task_id, account_id)
            if not task_data:
                return {
                    "success": False,
                    "error": "任务不存在"
                }
                
            # 检查任务状态
            if task_data["status"] not in ["pending", "paused", "failed"]:
                return {
                    "success": False,
                    "error": "任务状态不允许启动"
                }
                
            # 更新任务状态
            task_data["status"] = "processing"
            task_data["progress"] = 0.0
            task_data["current_step"] = "正在启动任务..."
            task_data["updated_at"] = datetime.now().isoformat()
            await self._save_task_data(task_data)
            
            # 提交到任务队列
            await self.task_queue.put(task_data)
            
            logger.info(f"启动内容生成任务: {task_id}")
            
            return {
                "success": True
            }
            
        except Exception as e:
            logger.error(f"启动内容生成任务失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def get_task_status(self, task_id: str, account_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            task_data = await self._load_task_data(task_id, account_id)
            if not task_data:
                return {
                    "success": False,
                    "error": "任务不存在"
                }
                
            return {
                "success": True,
                "status": {
                    "task_id": task_id,
                    "status": task_data["status"],
                    "progress": task_data["progress"],
                    "current_step": task_data.get("current_step"),
                    "error_message": task_data.get("error_message"),
                    "created_at": task_data["created_at"],
                    "updated_at": task_data["updated_at"],
                    "completed_at": task_data.get("completed_at")
                }
            }
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def get_generation_results(self, task_id: str, account_id: str) -> Dict[str, Any]:
        """获取生成结果"""
        try:
            task_data = await self._load_task_data(task_id, account_id)
            if not task_data:
                return {
                    "success": False,
                    "error": "任务不存在"
                }
                
            return {
                "success": True,
                "results": task_data.get("results", [])
            }
            
        except Exception as e:
            logger.error(f"获取生成结果失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"内容生成工作线程启动: {worker_name}")
        
        while self.is_running:
            try:
                # 从队列获取任务
                task_data = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                task_id = task_data["task_id"]
                account_id = task_data["account_id"]
                
                logger.info(f"工作线程 {worker_name} 开始处理任务: {task_id}")
                
                # 记录正在运行的任务
                self.running_tasks[task_id] = {
                    "worker": worker_name,
                    "started_at": datetime.now(),
                    "task_data": task_data
                }
                
                try:
                    # 处理任务
                    await self._process_task(task_data)
                    
                except Exception as e:
                    logger.error(f"处理任务失败: {task_id}, 错误: {str(e)}", exc_info=True)
                    
                    # 更新任务状态为失败
                    task_data["status"] = "failed"
                    task_data["error_message"] = str(e)
                    task_data["updated_at"] = datetime.now().isoformat()
                    await self._save_task_data(task_data)
                    
                finally:
                    # 移除正在运行的任务记录
                    if task_id in self.running_tasks:
                        del self.running_tasks[task_id]
                        
                    # 标记任务完成
                    self.task_queue.task_done()
                    
                logger.info(f"工作线程 {worker_name} 完成任务: {task_id}")
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
                
            except asyncio.CancelledError:
                logger.info(f"工作线程 {worker_name} 被取消")
                break
                
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 异常: {str(e)}", exc_info=True)
                await asyncio.sleep(1)  # 避免快速重试
                
        logger.info(f"内容生成工作线程结束: {worker_name}")
        
    async def _process_task(self, task_data: Dict[str, Any]):
        """处理单个任务"""
        task_id = task_data["task_id"]
        account_id = task_data["account_id"]
        
        try:
            # 更新任务状态
            await self._update_task_progress(task_data, 0.1, "正在初始化生成环境...")
            
            # 获取ComfyUI服务
            comfyui_service = get_comfyui_service()
            
            # 测试连接
            connection_test = await comfyui_service.test_connection()
            if not connection_test["success"]:
                raise Exception(f"ComfyUI服务不可用: {connection_test.get('error', '未知错误')}")
                
            await self._update_task_progress(task_data, 0.2, "已连接到ComfyUI服务")
            
            # 生成工作流
            workflow = await self._generate_workflow(task_data)
            await self._update_task_progress(task_data, 0.3, "正在生成工作流...")
            
            # 提交工作流到ComfyUI
            submit_result = await comfyui_service.submit_workflow(workflow)
            if not submit_result["success"]:
                raise Exception(f"提交工作流失败: {submit_result.get('error', '未知错误')}")
                
            prompt_id = submit_result["prompt_id"]
            await self._update_task_progress(task_data, 0.4, f"工作流已提交，ID: {prompt_id}")
            
            # 监控工作流执行
            await self._monitor_workflow(task_data, prompt_id, comfyui_service)
            
        except Exception as e:
            logger.error(f"处理任务异常: {task_id}, 错误: {str(e)}", exc_info=True)
            raise
            
    async def _generate_workflow(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成ComfyUI工作流"""
        task_type = task_data.get("type", "image")
        description = task_data.get("description", "")
        style = task_data.get("style", "realistic")
        config = task_data.get("config", {})

        # 根据任务类型生成不同的工作流
        if task_type == "image":
            return await self._generate_image_workflow(task_data, description, style, config)
        elif task_type == "video":
            return await self._generate_video_workflow(task_data, description, style, config)
        elif task_type == "text":
            return await self._generate_text_workflow(task_data, description, style, config)
        elif task_type == "storyboard":
            return await self._generate_storyboard_workflow(task_data, description, style, config)
        else:
            # 默认使用图片生成
            return await self._generate_image_workflow(task_data, description, style, config)

    async def _generate_image_workflow(self, task_data: Dict[str, Any], description: str, style: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """生成图片生成工作流"""

        # Stable Diffusion 图片生成工作流
        workflow = {
            "1": {
                "inputs": {
                    "text": f"{style} style: {description}",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "2": {
                "inputs": {
                    "text": "low quality, blurry, distorted",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "3": {
                "inputs": {
                    "seed": 42,
                    "steps": config.get("steps", 20),
                    "cfg": config.get("cfg", 7.0),
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "positive": ["1", 0],
                    "negative": ["2", 0],
                    "model": ["4", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "sd_xl_base_1.0.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "5": {
                "inputs": {
                    "width": config.get("width", 1024),
                    "height": config.get("height", 1024),
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage"
            },
            "6": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "7": {
                "inputs": {
                    "filename_prefix": f"generated_{task_data['task_id']}",
                    "images": ["6", 0]
                },
                "class_type": "SaveImage"
            }
        }

        return workflow

    async def _generate_video_workflow(self, task_data: Dict[str, Any], description: str, style: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """生成视频生成工作流"""
        # 基础的视频生成工作流（使用AnimateDiff或SVD）
        workflow = {
            "1": {
                "inputs": {
                    "text": f"{style} style: {description}",
                    "width": config.get("width", 512),
                    "height": config.get("height", 512),
                    "length": config.get("length", 16),
                    "context": config.get("context", 16),
                    "motion_bucket_id": config.get("motion_bucket_id", 127),
                    "fps": config.get("fps", 6),
                    "augmentation_level": config.get("augmentation_level", 0)
                },
                "class_type": "SVD_img2vid_Conditioning"
            },
            "2": {
                "inputs": {
                    "filename_prefix": f"video_{task_data['task_id']}",
                    "fps": config.get("output_fps", 8),
                    "images": ["1", 0]
                },
                "class_type": "VHS_VideoCombine"
            }
        }

        return workflow

    async def _generate_text_workflow(self, task_data: Dict[str, Any], description: str, style: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """生成文本生成工作流"""
        # 文本生成工作流（需要相应的ComfyUI文本生成节点）
        workflow = {
            "1": {
                "inputs": {
                    "prompt": f"Generate {style} style content about: {description}",
                    "max_tokens": config.get("max_tokens", 500),
                    "temperature": config.get("temperature", 0.7),
                    "top_p": config.get("top_p", 0.9)
                },
                "class_type": "LLMTextGeneration"
            },
            "2": {
                "inputs": {
                    "filename_prefix": f"text_{task_data['task_id']}",
                    "text": ["1", 0]
                },
                "class_type": "SaveText"
            }
        }

        return workflow

    async def _generate_storyboard_workflow(self, task_data: Dict[str, Any], description: str, style: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """生成分镜脚本工作流"""
        # 分镜脚本生成工作流
        scene_count = config.get("scene_count", 4)

        workflow = {
            "1": {
                "inputs": {
                    "story_prompt": description,
                    "style": style,
                    "scene_count": scene_count,
                    "duration_per_scene": config.get("duration_per_scene", 5)
                },
                "class_type": "StoryboardGenerator"
            }
        }

        # 为每个场景生成图片
        for i in range(scene_count):
            scene_node_id = str(10 + i)
            workflow[scene_node_id] = {
                "inputs": {
                    "scene_description": ["1", i],
                    "style": style,
                    "width": config.get("width", 1024),
                    "height": config.get("height", 576)
                },
                "class_type": "SceneImageGeneration"
            }

        # 保存分镜脚本
        workflow["99"] = {
            "inputs": {
                "filename_prefix": f"storyboard_{task_data['task_id']}",
                "storyboard_data": ["1", 0],
                "scene_images": [str(10 + i) for i in range(scene_count)]
            },
            "class_type": "SaveStoryboard"
        }

        return workflow

    async def _monitor_workflow(self, task_data: Dict[str, Any], prompt_id: str, comfyui_service):
        """监控工作流执行"""
        max_wait_time = 300  # 最大等待时间（秒）
        check_interval = 5   # 检查间隔（秒）
        elapsed_time = 0

        while elapsed_time < max_wait_time:
            try:
                # 获取工作流状态
                status_result = await comfyui_service.get_workflow_status(prompt_id)

                if not status_result["success"]:
                    raise Exception(f"获取工作流状态失败: {status_result.get('error', '未知错误')}")

                status = status_result["status"]

                if status == "completed":
                    # 工作流完成，处理结果
                    await self._handle_workflow_completion(task_data, prompt_id, comfyui_service)
                    return

                elif status == "running":
                    progress = 0.5 + (elapsed_time / max_wait_time) * 0.4  # 50%-90%
                    await self._update_task_progress(
                        task_data, progress, f"正在生成内容... (已等待 {elapsed_time}s)"
                    )

                elif status == "pending":
                    queue_position = status_result.get("queue_position", 0)
                    await self._update_task_progress(
                        task_data, 0.4, f"排队中，位置: {queue_position}"
                    )

                # 等待下次检查
                await asyncio.sleep(check_interval)
                elapsed_time += check_interval

            except Exception as e:
                logger.error(f"监控工作流异常: {str(e)}")
                raise

        # 超时
        raise Exception(f"工作流执行超时，超过 {max_wait_time} 秒")

    async def _handle_workflow_completion(self, task_data: Dict[str, Any], prompt_id: str, comfyui_service):
        """处理工作流完成"""
        try:
            await self._update_task_progress(task_data, 0.9, "正在处理生成结果...")

            # 获取输出文件
            output_files = await comfyui_service.get_output_files(prompt_id)

            # 获取文件存储服务
            file_storage = get_file_storage_service()

            # 处理生成的文件
            results = []
            for i, comfyui_file_path in enumerate(output_files):
                # 复制文件到账号专属目录
                copy_result = await file_storage.copy_file_for_account(
                    comfyui_file_path,
                    task_data["account_id"],
                    task_data["task_id"],
                    task_data.get("type", "video")
                )

                if copy_result["success"]:
                    result = {
                        "id": f"{task_data['task_id']}_result_{i+1}",
                        "type": task_data.get("type", "video"),
                        "title": f"{task_data.get('title', '生成内容')} - 结果 {i+1}",
                        "file_path": copy_result["file_path"],
                        "file_size": copy_result["file_size"],
                        "preview_url": f"/files/{task_data['account_id']}/content_generation/{copy_result['filename']}",
                        "download_url": f"/files/{task_data['account_id']}/content_generation/{copy_result['filename']}",
                        "metadata": {
                            "prompt_id": prompt_id,
                            "generated_at": datetime.now().isoformat(),
                            "style": task_data.get("style"),
                            "description": task_data.get("description"),
                            "original_comfyui_path": comfyui_file_path
                        }
                    }
                    results.append(result)
                else:
                    logger.warning(f"复制文件失败: {comfyui_file_path}, 错误: {copy_result.get('error')}")

            # 更新任务为完成状态
            task_data["status"] = "completed"
            task_data["progress"] = 1.0
            task_data["current_step"] = "生成完成"
            task_data["completed_at"] = datetime.now().isoformat()
            task_data["updated_at"] = datetime.now().isoformat()
            task_data["results"] = results

            await self._save_task_data(task_data)

            logger.info(f"任务完成: {task_data['task_id']}, 生成了 {len(results)} 个结果")

        except Exception as e:
            logger.error(f"处理工作流完成异常: {str(e)}", exc_info=True)
            raise

    async def _update_task_progress(self, task_data: Dict[str, Any], progress: float, current_step: str):
        """更新任务进度"""
        task_data["progress"] = progress
        task_data["current_step"] = current_step
        task_data["updated_at"] = datetime.now().isoformat()
        await self._save_task_data(task_data)

    async def _save_task_data(self, task_data: Dict[str, Any]):
        """保存任务数据到文件系统（按账号隔离）"""
        try:
            account_id = task_data["account_id"]
            task_id = task_data["task_id"]

            # 创建账号专属目录
            tasks_dir = Path("data/content_generation/tasks") / account_id
            tasks_dir.mkdir(parents=True, exist_ok=True)

            # 保存任务数据
            task_file = tasks_dir / f"{task_id}.json"
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(task_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"保存任务数据失败: {str(e)}", exc_info=True)
            raise

    async def _load_task_data(self, task_id: str, account_id: str) -> Optional[Dict[str, Any]]:
        """加载任务数据"""
        try:
            task_file = Path("data/content_generation/tasks") / account_id / f"{task_id}.json"

            if not task_file.exists():
                return None

            with open(task_file, 'r', encoding='utf-8') as f:
                return json.load(f)

        except Exception as e:
            logger.error(f"加载任务数据失败: {str(e)}", exc_info=True)
            return None

# 全局服务实例
_content_generation_service = None

def get_content_generation_service() -> ContentGenerationService:
    """获取内容生成服务实例"""
    global _content_generation_service

    if _content_generation_service is None:
        _content_generation_service = ContentGenerationService()

    return _content_generation_service
