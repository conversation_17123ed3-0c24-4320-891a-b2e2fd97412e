# 下载器配置文件

# 全局下载配置
global:
  # 并发下载限制
  max_concurrent_downloads: 3
  
  # 网络配置
  network:
    timeout: 120               # 网络超时从30秒增加到120秒
    retry_count: 5             # 重试次数从3次增加到5次
    rate_limit:
      enabled: true
      requests_per_second: 2
      burst_size: 5
  
  # 文件配置
  files:
    chunk_size: 8192
    verify_integrity: true
    resume_support: true
    
  # 错误处理
  error_handling:
    max_retries: 5             # 最大重试次数从3次增加到5次
    retry_delay: 10            # 重试延迟从5秒增加到10秒
    continue_on_error: true
    log_errors: true

# 平台特定配置
platforms:
  # 抖音配置
  douyin:
    name: "抖音"
    enabled: true
    
    # API配置
    api:
      base_url: "https://www.douyin.com"
      endpoints:
        profile: "/aweme/v1/web/aweme/profile/"
        post_list: "/aweme/v1/web/aweme/post/"
      
      headers:
        User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        Referer: "https://www.douyin.com/"
        Accept: "application/json, text/plain, */*"
        Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
    
    # 下载配置
    download:
      max_videos_per_request: 20
      max_pages: 10
      rate_limit:
        requests_per_second: 1
        delay_between_requests: 1
      
      quality_options:
        high: "1080p"
        medium: "720p"
        low: "480p"
      
      naming_rules:
        timestamp: "{timestamp}_{video_id}.mp4"
        title: "{title}_{video_id}.mp4"
        id: "{video_id}.mp4"
    
    # 过滤配置
    filters:
      default_min_views: 0
      default_min_likes: 0
      default_min_duration: 0
      default_max_duration: 0
      
    # 工作流配置
    workflow:
      config_path: "platforms/douyin/workflows/content_download.yaml"

  # TikTok配置（使用抖音下载器）
  tiktok:
    name: "TikTok"
    enabled: true
    inherit_from: "douyin"  # 继承抖音配置
    
    # 覆盖特定配置
    api:
      base_url: "https://www.tiktok.com"
      headers:
        User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        Referer: "https://www.tiktok.com/"

  # YouTube配置
  youtube:
    name: "YouTube"
    enabled: true
    
    # 依赖检查
    dependencies:
      - name: "yt-dlp"
        command: "yt-dlp --version"
        install_hint: "pip install yt-dlp"
    
    # 下载配置
    download:
      format: "best[ext=mp4]"
      quality_options:
        high: "best[height<=1080]"
        medium: "best[height<=720]"
        low: "best[height<=480]"
      
      naming_rules:
        timestamp: "{timestamp}_{id}.%(ext)s"
        title: "{title}_{id}.%(ext)s"
        id: "{id}.%(ext)s"
    
    # yt-dlp配置
    yt_dlp:
      options:
        - "--no-warnings"
        - "--extract-flat"
        - "--write-info-json"
        - "--write-thumbnail"
      
      format_selector: "best[ext=mp4]/best"
      
    # 工作流配置
    workflow:
      config_path: "platforms/youtube/workflows/content_download.yaml"

  # Instagram配置（预留）
  instagram:
    name: "Instagram"
    enabled: false
    
    # TODO: 实现Instagram下载器
    api:
      base_url: "https://www.instagram.com"
    
    download:
      quality_options:
        high: "original"
        medium: "medium"
        low: "low"

# 存储配置
storage:
  # 基础路径配置
  base_paths:
    root: "H:\\PublishSystem"
    downloads: "{root}\\Downloads"
    temp: "{root}\\Temp"
    logs: "{root}\\Logs"
  
  # 目录结构模板
  directory_structure:
    account: "{platform}\\{account_name}"
    content: "{account}\\{year}-{month}"
    videos: "{content}\\videos"
    metadata: "{content}\\metadata"
    covers: "{content}\\covers"
    reports: "{content}\\reports"
  
  # 文件命名模板
  file_naming:
    video: "{timestamp}_{video_id}.{ext}"
    metadata: "{timestamp}_{video_id}.json"
    cover: "{timestamp}_{video_id}_cover.{ext}"
    report: "download_report_{timestamp}.json"

# 日志配置
logging:
  level: "INFO"
  max_entries: 1000
  include_timestamps: true
  include_progress: true
  
  # 日志文件配置
  file_logging:
    enabled: true
    max_size: "10MB"
    backup_count: 5
    format: "[{timestamp}] [{level}] [{platform}] {message}"

# 监控配置
monitoring:
  # 性能监控
  performance:
    track_download_speed: true
    track_memory_usage: true
    track_disk_usage: true
  
  # 统计信息
  statistics:
    track_success_rate: true
    track_error_types: true
    track_platform_usage: true
  
  # 报告配置
  reports:
    generate_daily_report: true
    generate_weekly_report: true
    include_performance_metrics: true
    include_error_analysis: true

# 安全配置
security:
  # 请求限制
  rate_limiting:
    enabled: true
    global_limit: 100  # 每分钟最大请求数
    per_platform_limit: 50
  
  # 用户代理轮换
  user_agent_rotation:
    enabled: false
    agents: []
  
  # 代理配置
  proxy:
    enabled: false
    http_proxy: ""
    https_proxy: ""
    
# 清理配置
cleanup:
  # 临时文件清理
  temp_files:
    auto_cleanup: true
    max_age_hours: 24
  
  # 日志清理
  logs:
    auto_cleanup: true
    max_age_days: 30
  
  # 缓存清理
  cache:
    auto_cleanup: true
    max_size_mb: 1024
