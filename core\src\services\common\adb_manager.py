"""
ADB 连接管理器
监控和恢复 ADB 连接，防止设备连接丢失导致的崩溃
"""

import asyncio
import logging
import subprocess
import time
from typing import Optional, List

logger = logging.getLogger(__name__)

class ADBManager:
    """ADB 连接管理器"""
    
    def __init__(self):
        self.last_check_time = 0
        self.check_interval = 30  # 30秒检查一次
        self.adb_path = "adb"  # 默认使用系统PATH中的adb

    def _safe_decode(self, data: bytes) -> str:
        """安全解码字节数据，处理编码问题"""
        if isinstance(data, str):
            return data

        # 尝试多种编码方式
        encodings = ['utf-8', 'gbk', 'cp936', 'latin1']
        for encoding in encodings:
            try:
                return data.decode(encoding)
            except (UnicodeDecodeError, LookupError):
                continue

        # 如果所有编码都失败，使用错误忽略模式
        return data.decode('utf-8', errors='ignore')
        
    async def check_device_connection(self, device_id: str) -> bool:
        """检查设备连接状态"""
        try:
            # 执行 adb devices 命令
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "devices",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"ADB命令执行失败: {self._safe_decode(stderr)}")
                return False

            # 解析输出，查找设备
            output = self._safe_decode(stdout)
            lines = output.strip().split('\n')[1:]  # 跳过标题行
            
            for line in lines:
                if device_id in line and 'device' in line:
                    logger.debug(f"设备 {device_id} 连接正常")
                    return True
            
            logger.warning(f"设备 {device_id} 未找到或状态异常")
            return False
            
        except Exception as e:
            logger.error(f"检查设备连接失败: {str(e)}")
            return False
    
    async def get_connected_devices(self) -> List[str]:
        """获取所有连接的设备列表"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "devices",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                return []
            
            output = self._safe_decode(stdout)
            lines = output.strip().split('\n')[1:]  # 跳过标题行
            
            devices = []
            for line in lines:
                if '\tdevice' in line:
                    device_id = line.split('\t')[0]
                    devices.append(device_id)
            
            return devices
            
        except Exception as e:
            logger.error(f"获取设备列表失败: {str(e)}")
            return []
    
    async def restart_adb_server(self) -> bool:
        """重启 ADB 服务器"""
        try:
            logger.info("🔧 重启 ADB 服务器...")
            
            # 停止 ADB 服务器
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "kill-server",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # 等待一下
            await asyncio.sleep(2)
            
            # 启动 ADB 服务器
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "start-server",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✅ ADB 服务器重启成功")
                return True
            else:
                logger.error(f"❌ ADB 服务器重启失败: {self._safe_decode(stderr)}")
                return False
                
        except Exception as e:
            logger.error(f"重启 ADB 服务器异常: {str(e)}")
            return False

    async def restart_adb_server_if_necessary(self, device_id: str, force: bool = False) -> bool:
        """仅在必要时重启ADB服务器

        Args:
            device_id: 设备ID
            force: 是否强制重启（默认False）

        Returns:
            bool: 是否重启成功
        """
        if not force:
            logger.warning(f"⚠️ 设备 {device_id} 请求重启ADB服务器")
            logger.warning("⚠️ 重启ADB服务器会影响所有设备连接")
            logger.warning("⚠️ 建议先检查设备状态，确认必要性后使用force=True参数")
            return False

        logger.warning(f"🔧 强制重启ADB服务器（由设备 {device_id} 触发）")
        logger.warning("⚠️ 这将影响所有设备连接")

        return await self.restart_adb_server()
    
    async def reconnect_device(self, device_id: str) -> bool:
        """重新连接设备"""
        try:
            logger.info(f"🔧 尝试重新连接设备: {device_id}")
            
            # 如果是网络设备，尝试重新连接
            if ':' in device_id:  # 网络ADB (IP:PORT)
                # 先断开
                process = await asyncio.create_subprocess_exec(
                    self.adb_path, "disconnect", device_id,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()

                await asyncio.sleep(1)

                # 重新连接
                process = await asyncio.create_subprocess_exec(
                    self.adb_path, "connect", device_id,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()

                if process.returncode == 0:
                    logger.info(f"✅ 设备 {device_id} 重新连接成功")
                    return True
                else:
                    logger.error(f"❌ 设备 {device_id} 重新连接失败: {self._safe_decode(stderr)}")
                    return False
            elif device_id.startswith('emulator-'):
                # 🔧 重要修复：模拟器设备崩溃时不重启ADB服务，避免影响其他设备
                logger.info(f"检测到模拟器设备: {device_id}")

                # 🔧 优化：先尝试简单的设备重连，不重启ADB服务器
                logger.info(f"尝试简单重连模拟器设备: {device_id}")

                # 等待一下，让设备有时间自动恢复
                await asyncio.sleep(3)

                # 检查设备是否自动恢复
                if await self.check_device_connection(device_id):
                    logger.info(f"✅ 模拟器 {device_id} 自动恢复连接成功")
                    return True

                # 如果简单重连失败，直接尝试重启模拟器（不重启ADB）
                logger.warning(f"⚠️ 模拟器 {device_id} 简单重连失败，尝试重启模拟器")
                restart_success = await self._restart_emulator(device_id)
                if restart_success:
                    # 等待模拟器启动完成（模拟器启动需要较长时间）
                    logger.info(f"等待模拟器 {device_id} 启动完成...")
                    await asyncio.sleep(20)  # 增加等待时间到20秒

                    # 多次检查连接，因为模拟器启动可能需要更长时间
                    for attempt in range(6):  # 最多尝试6次，每次间隔5秒
                        if await self.check_device_connection(device_id):
                            logger.info(f"✅ 模拟器 {device_id} 重启后重新连接成功 (尝试 {attempt + 1})")
                            return True
                        else:
                            logger.info(f"模拟器 {device_id} 连接检查失败，等待5秒后重试... (尝试 {attempt + 1}/6)")
                            await asyncio.sleep(5)

                    # 🔧 最后手段：如果模拟器重启后仍无法连接，才考虑重启ADB（但要谨慎）
                    logger.warning(f"⚠️ 模拟器 {device_id} 重启后仍无法连接，考虑是否需要重启ADB服务")
                    logger.warning("⚠️ 注意：重启ADB服务会影响其他设备，建议手动检查")

                    # 记录详细信息供调试
                    logger.error(f"❌ 模拟器 {device_id} 恢复失败，请检查模拟器状态")
                    return False
                else:
                    logger.error(f"❌ 模拟器 {device_id} 重启失败")
                    return False
            else:
                # 🔧 重要修复：USB设备也避免直接重启ADB服务器
                logger.info(f"检测到USB设备: {device_id}")

                # 先尝试简单的等待和重连
                logger.info(f"等待USB设备 {device_id} 自动恢复...")
                await asyncio.sleep(5)

                # 检查设备是否自动恢复
                if await self.check_device_connection(device_id):
                    logger.info(f"✅ USB设备 {device_id} 自动恢复连接成功")
                    return True

                # 如果自动恢复失败，记录警告但不重启ADB
                logger.warning(f"⚠️ USB设备 {device_id} 连接恢复失败")
                logger.warning("⚠️ 建议检查USB连接或手动重启ADB服务（会影响所有设备）")
                return False
                
        except Exception as e:
            logger.error(f"重新连接设备异常: {str(e)}")
            return False
    
    async def ensure_device_connected(self, device_id: str) -> bool:
        """确保设备连接（检查并恢复）"""
        # 检查是否需要检查（避免过于频繁）
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return True  # 假设连接正常
        
        self.last_check_time = current_time
        
        # 检查连接状态
        if await self.check_device_connection(device_id):
            return True
        
        logger.warning(f"⚠️ 设备 {device_id} 连接丢失，尝试恢复...")
        
        # 尝试恢复连接
        recovery_success = await self.reconnect_device(device_id)
        
        if recovery_success:
            # 再次检查
            await asyncio.sleep(3)
            return await self.check_device_connection(device_id)
        
        return False

    async def _restart_emulator(self, device_id: str) -> bool:
        """重启模拟器

        Args:
            device_id: 模拟器设备ID (如 emulator-5620)

        Returns:
            bool: 是否重启成功
        """
        try:
            logger.info(f"🔄 开始重启模拟器: {device_id}")

            # 从设备ID提取端口号
            if not device_id.startswith('emulator-'):
                logger.error(f"设备ID格式错误，不是模拟器: {device_id}")
                return False

            try:
                port = int(device_id.split('-')[1])
                # 雷电模拟器的索引通常是 (port - 5554) / 2
                emulator_index = (port - 5554) // 2
                logger.info(f"模拟器端口: {port}, 推测索引: {emulator_index}")
            except (ValueError, IndexError):
                logger.error(f"无法从设备ID {device_id} 解析端口号")
                return False

            # 🔧 重要：调用Core主服务的重启设备方法
            try:
                # 导入Core主服务
                from ...main_service import get_main_service
                main_service = get_main_service()

                if main_service:
                    logger.info(f"通过Core主服务重启设备索引: {emulator_index}")
                    restart_success = await main_service.restart_device(str(emulator_index))

                    if restart_success:
                        logger.info(f"✅ 模拟器 {device_id} (索引 {emulator_index}) 重启成功")
                        return True
                    else:
                        logger.error(f"❌ 模拟器 {device_id} (索引 {emulator_index}) 重启失败")
                        return False
                else:
                    logger.error("Core主服务不可用，无法重启模拟器")
                    return False

            except Exception as service_error:
                logger.error(f"调用Core主服务重启模拟器失败: {str(service_error)}")

                # 🔧 备选方案：直接调用雷电控制台命令
                logger.info("尝试直接调用雷电控制台重启模拟器...")
                return await self._restart_emulator_direct(emulator_index)

        except Exception as e:
            logger.error(f"重启模拟器异常: {str(e)}", exc_info=True)
            return False

    async def _restart_emulator_direct(self, emulator_index: int) -> bool:
        """直接调用雷电控制台重启模拟器

        Args:
            emulator_index: 模拟器索引

        Returns:
            bool: 是否重启成功
        """
        try:
            import os

            # 雷电控制台路径
            ldconsole_paths = [
                r"D:\LDPlayer\LDPlayer9\ldconsole.exe",
                r"C:\LDPlayer\LDPlayer9\ldconsole.exe",
                r"D:\LDPlayer\ldconsole.exe",
                r"C:\LDPlayer\ldconsole.exe"
            ]

            ldconsole_path = None
            for path in ldconsole_paths:
                if os.path.exists(path):
                    ldconsole_path = path
                    break

            if not ldconsole_path:
                logger.error("找不到雷电控制台程序")
                return False

            logger.info(f"使用雷电控制台: {ldconsole_path}")

            # 先停止模拟器
            stop_cmd = f'"{ldconsole_path}" quit --index {emulator_index}'
            logger.info(f"停止模拟器命令: {stop_cmd}")

            process = await asyncio.create_subprocess_shell(
                stop_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.warning(f"停止模拟器可能失败: {self._safe_decode(stderr)}")

            # 等待停止完成
            await asyncio.sleep(3)

            # 启动模拟器
            start_cmd = f'"{ldconsole_path}" launch --index {emulator_index}'
            logger.info(f"启动模拟器命令: {start_cmd}")

            process = await asyncio.create_subprocess_shell(
                start_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"✅ 模拟器索引 {emulator_index} 启动命令执行成功")
                return True
            else:
                logger.error(f"❌ 模拟器索引 {emulator_index} 启动失败: {self._safe_decode(stderr)}")
                return False

        except Exception as e:
            logger.error(f"直接重启模拟器异常: {str(e)}", exc_info=True)
            return False

# 全局 ADB 管理器
adb_manager = ADBManager()

def get_adb_manager() -> ADBManager:
    """获取全局 ADB 管理器"""
    return adb_manager
