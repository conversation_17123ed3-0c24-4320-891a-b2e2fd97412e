#!/usr/bin/env python3
"""
测试工作流API修复效果
"""

import requests
import json
import sys

def test_workflow_api(backend_url="http://localhost:8000", task_id="50a97d1a-a58e-4166-8dce-6c38146afa42"):
    """
    测试工作流API
    
    Args:
        backend_url: Backend服务地址
        task_id: 要测试的任务ID
    """
    try:
        print(f"🔧 测试工作流API...")
        print(f"Backend地址: {backend_url}")
        print(f"任务ID: {task_id}")
        
        # 调用工作流API
        response = requests.get(
            f"{backend_url}/api/tasks/{task_id}/workflow",
            timeout=30
        )
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功!")
            print(f"   成功: {result.get('success')}")
            print(f"   数据源: {result.get('source')}")
            print(f"   消息: {result.get('message')}")
            
            if result.get("data"):
                data = result["data"]
                print(f"   工作流名称: {data.get('workflow_name')}")
                print(f"   当前步骤索引: {data.get('current_step_index')}")
                print(f"   总步骤数: {data.get('total_steps')}")
                print(f"   可恢复: {data.get('can_resume')}")
                
                steps = data.get("steps", [])
                print(f"   步骤详情:")
                for i, step in enumerate(steps):
                    status_icon = {
                        'pending': '⏳',
                        'running': '🔄',
                        'completed': '✅',
                        'failed': '❌',
                        'paused': '⏸️',
                        'skipped': '⏭️'
                    }.get(step.get('status'), '❓')
                    
                    print(f"     {i+1}. {status_icon} {step.get('name')} - {step.get('status')} ({step.get('progress', 0)}%)")
                    if step.get('error_message'):
                        print(f"        错误: {step.get('error_message')}")
            
            return True
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("工作流API测试工具")
    print("=" * 60)
    
    # 解析命令行参数
    backend_url = "http://localhost:8000"
    task_id = "50a97d1a-a58e-4166-8dce-6c38146afa42"
    
    if len(sys.argv) > 1:
        backend_url = sys.argv[1]
    if len(sys.argv) > 2:
        task_id = sys.argv[2]
    
    print(f"使用参数:")
    print(f"  Backend地址: {backend_url}")
    print(f"  任务ID: {task_id}")
    print()
    
    # 执行测试
    success = test_workflow_api(backend_url, task_id)
    
    if success:
        print("\n🎉 工作流API测试成功!")
        print("现在应该能看到真实的任务执行状态了。")
    else:
        print("\n💥 工作流API测试失败，请检查错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
