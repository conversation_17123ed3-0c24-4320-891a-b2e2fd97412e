# 视频处理功能Core服务选择修复

## 问题描述

用户反馈在文件管理功能中选择Core服务后，只有列表功能对应正确的服务，但各种数据处理功能仍调用默认服务，需要全面改进服务调用逻辑。

## 修复的API

### 1. 批量音频处理API

#### 1.1 批量生成字幕API
**后端修改**：`backend/app/api/v1/filesystem.py`
- 添加 `core_service_id` 参数到函数签名
- 实现Core服务选择逻辑
- 更新API文档说明

**前端修改**：`frontend/src/api/social.ts`
- 为 `generateSubtitlesBatch` 函数添加可选的 `coreServiceId` 参数
- 通过查询参数传递给后端

**文件管理页面修改**：`frontend/src/views/doc/Manager.vue`
- 更新API调用，传递选择的Core服务ID

#### 1.2 批量分离音频API
**后端修改**：
- 添加 `core_service_id` 参数支持
- 实现Core服务选择逻辑

**前端修改**：
- 为 `extractAudioBatch` 函数添加 `coreServiceId` 参数
- 更新文件管理页面中的调用

#### 1.3 批量人声分离API
**后端修改**：
- 添加 `core_service_id` 参数支持
- 实现Core服务选择逻辑

**前端修改**：
- 为 `separateVocalsBatch` 函数添加 `coreServiceId` 参数
- 更新文件管理页面中的调用

#### 1.4 批量替换音频API
**后端修改**：
- 添加 `core_service_id` 参数支持
- 实现Core服务选择逻辑

**前端修改**：
- 为 `replaceAudioBatch` 函数添加 `coreServiceId` 参数
- 更新文件管理页面中的调用

### 2. 批量视频合并API

**后端修改**：`backend/app/api/v1/filesystem.py`
- 为 `create_video_merge_task` 函数添加 `core_service_id` 参数
- 更新后台任务函数 `process_video_merge_task`，接收和使用 `core_service_id`
- 更新Core服务调用逻辑，使用 `get_core_client_by_service_id`

**前端修改**：`frontend/src/api/social.ts`
- 为 `createVideoMergeTask` 函数添加可选的 `coreServiceId` 参数
- 通过查询参数传递给后端

**文件管理页面修改**：`frontend/src/views/doc/Manager.vue`
- 更新API调用，传递选择的Core服务ID

### 3. 水印处理API

#### 3.1 水印检测API
**后端修改**：`backend/app/api/v1/filesystem.py`
- 为 `detect_video_watermark` 函数添加 `core_service_id` 参数
- 更新Core服务调用逻辑，使用 `get_core_client_by_service_id`

**前端修改**：`frontend/src/api/social.ts`
- 添加水印检测相关的接口定义
- 创建 `detectVideoWatermark` API函数，支持 `coreServiceId` 参数

#### 3.2 水印清除API
**后端修改**：
- 为 `remove_video_watermark` 函数添加 `core_service_id` 参数
- 更新Core服务调用逻辑

**前端修改**：
- 添加水印清除相关的接口定义
- 创建 `removeVideoWatermark` API函数
- 更新文件管理页面中的水印处理功能，使用封装的API函数而不是直接的fetch调用

#### 3.3 批量水印处理API
**后端修改**：
- 为 `batch_process_watermark` 函数添加 `core_service_id` 参数
- 更新后台任务函数 `execute_batch_watermark_processing`，接收和使用 `core_service_id`
- 更新Core服务调用逻辑

**前端修改**：
- 创建 `batchWatermarkProcess` API函数
- 更新文件管理页面中的批量水印处理功能

## 技术实现细节

### Core服务选择逻辑

所有修复的API都遵循相同的Core服务选择模式：

```python
# 根据core_service_id选择Core服务
if core_service_id:
    core_client = get_core_client_by_service_id(core_service_id)
    logger.info(f"使用指定的Core服务: {core_service_id}")
else:
    from app.core.grpc_client import get_core_client
    core_client = get_core_client()
    logger.info("使用默认Core服务")

if not core_client:
    raise HTTPException(status_code=503, detail="Core服务不可用")
```

### 前端API函数模式

所有前端API函数都添加了可选的 `coreServiceId` 参数：

```typescript
export const someAPI = (data: RequestType, coreServiceId?: string) => {
  return request<ResponseType>({
    url: '/api/v1/some-endpoint',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

### 文件管理页面调用更新

所有API调用都更新为传递选择的Core服务ID：

```typescript
const response = await someAPI(requestData, selectedCoreService.value || undefined)
```

## 修复效果

1. **统一性**：所有视频处理功能现在都支持Core服务选择
2. **一致性**：用户在文件管理页面选择的Core服务会被所有功能使用
3. **可追踪性**：添加了详细的日志记录，显示使用的Core服务信息
4. **向后兼容**：如果没有指定Core服务，会自动使用默认服务
5. **后台任务支持**：所有后台任务（包括视频合并）都能正确使用指定的Core服务

## 测试建议

1. 在文件管理页面选择不同的Core服务
2. 测试各种音频处理功能（生成字幕、分离音频、人声分离、替换音频）
3. 测试批量视频合并功能
4. 测试水印检测和清除功能
5. 检查后端日志，确认使用了正确的Core服务
6. 验证功能正常工作且结果正确
