"""
gRPC客户端管理模块
"""

import logging
from typing import Optional, Dict
from app.core.client import FileServiceClient, TaskServiceClient
from app.services.consul_discovery import ConsulDiscovery
from app.config.database import DatabaseConfig
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

# 全局客户端实例缓存
_file_clients: Dict[str, FileServiceClient] = {}
_task_clients: Dict[str, TaskServiceClient] = {}

def get_core_client() -> Optional[FileServiceClient]:
    """获取默认Core服务的文件客户端

    Returns:
        FileServiceClient实例，如果连接失败则返回None
    """
    return get_core_client_by_service_id(None)

def get_core_client_by_service_id(service_id: Optional[str]) -> Optional[FileServiceClient]:
    """根据服务ID获取Core服务的文件客户端

    Args:
        service_id: Core服务ID，如果为None或空字符串则使用第一个可用的Core服务

    Returns:
        FileServiceClient实例，如果连接失败则返回None
    """
    try:
        logger.info(f"获取Core服务客户端，service_id: '{service_id}'")
        # 从Consul获取服务信息
        db_config = DatabaseConfig()
        logger.info(f"数据库配置consul_url: {db_config.consul_url}")
        consul_url = urlparse(db_config.consul_url)
        consul_host = consul_url.hostname or "localhost"
        consul_port = consul_url.port or 8500
        logger.info(f"解析后的Consul地址: {consul_host}:{consul_port}")

        consul_discovery = ConsulDiscovery(
            consul_host=consul_host,
            consul_port=consul_port
        )
        logger.info("已创建ConsulDiscovery实例")

        # 获取Core服务实例
        services = consul_discovery.get_all_services("thunderhub-core")
        logger.info(f"从Consul获取到的Core服务列表: {list(services.keys()) if services else '无'}")

        if not services:
            logger.warning("未找到任何Core服务，使用默认连接")
            # 如果Consul中没有服务，使用默认连接作为降级方案
            client_key = "default"
            if client_key not in _file_clients:
                _file_clients[client_key] = FileServiceClient(host='localhost', port=50051)
                logger.info("已创建默认Core服务文件客户端")
            return _file_clients[client_key]

        # 如果没有指定服务ID，使用第一个可用的Core服务
        if not service_id:
            service_id = next(iter(services.keys()))
            logger.info(f"未指定Core服务ID，使用第一个可用服务: {service_id}")

        # 检查缓存中是否已有该服务的客户端
        if service_id in _file_clients:
            logger.info(f"使用缓存的Core服务客户端: {service_id}")
            return _file_clients[service_id]

        # 检查指定的服务是否存在
        if service_id not in services:
            logger.warning(f"未找到指定的Core服务: {service_id}")
            return None

        # 获取服务配置
        service_info = services[service_id]
        host = service_info.get("host", "localhost")
        grpc_port = service_info.get("port", 50051)  # 修复：使用正确的字段名
        logger.info(f"从Consul获取服务信息: {service_info}")

        # 创建新的客户端实例
        _file_clients[service_id] = FileServiceClient(host=host, port=grpc_port)
        logger.info(f"已创建Core服务文件客户端: {service_id} ({host}:{grpc_port})")
        logger.error(f"!!! 重要：实际连接到Core服务 {service_id} 地址: {host}:{grpc_port} !!!")

        return _file_clients[service_id]

    except Exception as e:
        logger.error(f"获取Core服务客户端失败 (service_id: {service_id}): {str(e)}", exc_info=True)
        # 作为最后的降级方案，返回默认连接
        logger.warning("使用最后的降级方案：默认localhost连接")
        try:
            client_key = "fallback"
            if client_key not in _file_clients:
                _file_clients[client_key] = FileServiceClient(host='localhost', port=50051)
                logger.info("已创建降级Core服务文件客户端")
            return _file_clients[client_key]
        except Exception as fallback_error:
            logger.error(f"创建降级客户端也失败: {str(fallback_error)}")
            return None

async def get_task_client(service_id: Optional[str] = None) -> Optional[TaskServiceClient]:
    """获取Core服务的任务客户端

    Args:
        service_id: Core服务ID，如果为None则使用第一个可用的Core服务

    Returns:
        TaskServiceClient实例，如果连接失败则返回None
    """
    try:
        logger.info(f"获取Core服务任务客户端，service_id: '{service_id}'")

        # 从Consul获取服务信息
        db_config = DatabaseConfig()
        consul_url = urlparse(db_config.consul_url)
        consul_host = consul_url.hostname or "localhost"
        consul_port = consul_url.port or 8500

        consul_discovery = ConsulDiscovery(
            consul_host=consul_host,
            consul_port=consul_port
        )

        # 获取Core服务实例
        services = consul_discovery.get_all_services("thunderhub-core")
        logger.info(f"从Consul获取到的Core服务列表: {list(services.keys()) if services else '无'}")

        if not services:
            logger.warning("未找到任何Core服务，使用默认连接")
            # 如果Consul中没有服务，使用默认连接作为降级方案
            client_key = "default"
            if client_key not in _task_clients:
                _task_clients[client_key] = TaskServiceClient(host='localhost', port=50051)
                logger.info("已创建默认Core服务任务客户端")
            return _task_clients[client_key]

        # 如果没有指定服务ID，使用第一个可用的Core服务
        if not service_id:
            service_id = next(iter(services.keys()))
            logger.info(f"未指定Core服务ID，使用第一个可用服务: {service_id}")

        # 检查缓存中是否已有该服务的客户端
        if service_id in _task_clients:
            logger.info(f"使用缓存的Core服务任务客户端: {service_id}")
            return _task_clients[service_id]

        # 检查指定的服务是否存在
        if service_id not in services:
            logger.warning(f"未找到指定的Core服务: {service_id}")
            return None

        # 获取服务配置
        service_info = services[service_id]
        host = service_info.get("host", "localhost")
        grpc_port = service_info.get("port", 50051)
        logger.info(f"从Consul获取任务服务信息: {service_info}")

        # 创建新的客户端实例
        _task_clients[service_id] = TaskServiceClient(host=host, port=grpc_port)
        logger.info(f"已创建Core服务任务客户端: {service_id} ({host}:{grpc_port})")

        return _task_clients[service_id]

    except Exception as e:
        logger.error(f"获取Core服务任务客户端失败 (service_id: {service_id}): {str(e)}", exc_info=True)
        # 作为最后的降级方案，返回默认连接
        logger.warning("使用最后的降级方案：默认localhost任务客户端连接")
        try:
            client_key = "fallback"
            if client_key not in _task_clients:
                _task_clients[client_key] = TaskServiceClient(host='localhost', port=50051)
                logger.info("已创建降级Core服务任务客户端")
            return _task_clients[client_key]
        except Exception as fallback_error:
            logger.error(f"创建降级任务客户端也失败: {str(fallback_error)}")
            return None

def close_core_client():
    """关闭Core服务客户端连接"""
    global _file_client
    
    if _file_client:
        try:
            # 注意：这里是同步关闭，如果需要异步关闭需要在调用处使用await
            # await _file_client.close()
            _file_client = None
            logger.info("已关闭Core服务客户端连接")
        except Exception as e:
            logger.error(f"关闭Core服务客户端失败: {str(e)}")
