"""
高性能任务队列管理器
优化多任务处理，减少阻塞
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class QueuedTask:
    """队列任务"""
    task_id: str
    task_func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    created_at: float = field(default_factory=time.time)
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3


class TaskQueueManager:
    """高性能任务队列管理器"""
    
    def __init__(self, 
                 max_concurrent_tasks: int = 20,
                 worker_count: int = 5,
                 queue_size: int = 1000,
                 thread_pool_size: int = 10):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.worker_count = worker_count
        self.queue_size = queue_size
        
        # 任务队列 - 按优先级分组
        self.task_queues: Dict[TaskPriority, asyncio.Queue] = {
            priority: asyncio.Queue(maxsize=queue_size) 
            for priority in TaskPriority
        }
        
        # 运行中的任务
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
        # 信号量控制并发
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        
        # 线程池用于CPU密集型任务
        self.thread_pool = ThreadPoolExecutor(max_workers=thread_pool_size)
        
        # 工作者任务
        self.workers: List[asyncio.Task] = []
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'queue_sizes': {priority: 0 for priority in TaskPriority}
        }
        
        # 运行状态
        self.is_running = False
        self._shutdown_event = asyncio.Event()
        
        logger.info(f"任务队列管理器初始化: 最大并发={max_concurrent_tasks}, 工作者={worker_count}")
    
    async def start(self):
        """启动任务队列管理器"""
        if self.is_running:
            logger.warning("任务队列管理器已在运行")
            return
        
        self.is_running = True
        self._shutdown_event.clear()
        
        # 启动工作者
        for i in range(self.worker_count):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"任务队列管理器已启动，{len(self.workers)} 个工作者")
    
    async def stop(self):
        """停止任务队列管理器"""
        if not self.is_running:
            return
        
        logger.info("正在停止任务队列管理器...")
        self.is_running = False
        self._shutdown_event.set()
        
        # 等待所有工作者完成
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)
        
        # 取消所有运行中的任务
        for task_id, task in list(self.running_tasks.items()):
            if not task.done():
                task.cancel()
                logger.info(f"取消任务: {task_id}")
        
        # 关闭线程池
        self.thread_pool.shutdown(wait=True)
        
        logger.info("任务队列管理器已停止")
    
    async def submit_task(self, 
                         task_id: str,
                         task_func: Callable,
                         *args,
                         priority: TaskPriority = TaskPriority.NORMAL,
                         timeout: Optional[float] = None,
                         max_retries: int = 3,
                         **kwargs) -> bool:
        """提交任务到队列
        
        Args:
            task_id: 任务ID
            task_func: 任务函数
            *args: 位置参数
            priority: 任务优先级
            timeout: 超时时间
            max_retries: 最大重试次数
            **kwargs: 关键字参数
            
        Returns:
            bool: 是否成功提交
        """
        if not self.is_running:
            logger.error("任务队列管理器未运行")
            return False
        
        # 检查任务是否已存在
        if task_id in self.running_tasks:
            logger.warning(f"任务已存在: {task_id}")
            return False
        
        # 创建队列任务
        queued_task = QueuedTask(
            task_id=task_id,
            task_func=task_func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            max_retries=max_retries
        )
        
        try:
            # 添加到对应优先级队列
            queue = self.task_queues[priority]
            await queue.put(queued_task)
            
            self.stats['total_tasks'] += 1
            self.stats['queue_sizes'][priority] += 1
            
            logger.info(f"任务已提交到队列: {task_id} (优先级: {priority.name})")
            return True
            
        except asyncio.QueueFull:
            logger.error(f"队列已满，无法提交任务: {task_id}")
            return False
    
    async def _worker(self, worker_name: str):
        """工作者协程"""
        logger.info(f"工作者 {worker_name} 启动")
        
        while self.is_running:
            try:
                # 按优先级获取任务
                queued_task = await self._get_next_task()
                
                if queued_task is None:
                    # 没有任务，短暂等待
                    await asyncio.sleep(0.1)
                    continue
                
                # 使用信号量控制并发
                async with self.semaphore:
                    await self._execute_task(queued_task, worker_name)
                    
            except asyncio.CancelledError:
                logger.info(f"工作者 {worker_name} 被取消")
                break
            except Exception as e:
                logger.error(f"工作者 {worker_name} 异常: {str(e)}", exc_info=True)
                await asyncio.sleep(1)  # 出错后稍等再继续
        
        logger.info(f"工作者 {worker_name} 停止")
    
    async def _get_next_task(self) -> Optional[QueuedTask]:
        """按优先级获取下一个任务"""
        # 按优先级从高到低检查队列
        for priority in sorted(TaskPriority, key=lambda x: x.value, reverse=True):
            queue = self.task_queues[priority]
            try:
                # 非阻塞获取任务
                task = queue.get_nowait()
                self.stats['queue_sizes'][priority] -= 1
                return task
            except asyncio.QueueEmpty:
                continue
        
        return None
    
    async def _execute_task(self, queued_task: QueuedTask, worker_name: str):
        """执行任务"""
        task_id = queued_task.task_id
        
        try:
            logger.info(f"工作者 {worker_name} 开始执行任务: {task_id}")
            
            # 创建任务协程
            if asyncio.iscoroutinefunction(queued_task.task_func):
                # 异步函数
                task_coro = queued_task.task_func(*queued_task.args, **queued_task.kwargs)
            else:
                # 同步函数，在线程池中执行
                loop = asyncio.get_event_loop()
                task_coro = loop.run_in_executor(
                    self.thread_pool,
                    lambda: queued_task.task_func(*queued_task.args, **queued_task.kwargs)
                )
            
            # 执行任务（带超时）
            if queued_task.timeout:
                task = asyncio.create_task(task_coro)
                self.running_tasks[task_id] = task
                
                try:
                    await asyncio.wait_for(task, timeout=queued_task.timeout)
                    logger.info(f"任务执行成功: {task_id}")
                    self.stats['completed_tasks'] += 1
                    
                except asyncio.TimeoutError:
                    logger.error(f"任务执行超时: {task_id}")
                    task.cancel()
                    await self._handle_task_failure(queued_task)
                    
            else:
                # 无超时限制
                task = asyncio.create_task(task_coro)
                self.running_tasks[task_id] = task
                await task
                logger.info(f"任务执行成功: {task_id}")
                self.stats['completed_tasks'] += 1
                
        except Exception as e:
            logger.error(f"任务执行失败: {task_id}, 错误: {str(e)}", exc_info=True)
            await self._handle_task_failure(queued_task)
            
        finally:
            # 清理运行中的任务记录
            self.running_tasks.pop(task_id, None)
    
    async def _handle_task_failure(self, queued_task: QueuedTask):
        """处理任务失败"""
        queued_task.retry_count += 1
        
        if queued_task.retry_count <= queued_task.max_retries:
            logger.info(f"任务重试: {queued_task.task_id} ({queued_task.retry_count}/{queued_task.max_retries})")
            # 重新提交到队列
            queue = self.task_queues[queued_task.priority]
            try:
                await queue.put(queued_task)
                self.stats['queue_sizes'][queued_task.priority] += 1
            except asyncio.QueueFull:
                logger.error(f"重试队列已满: {queued_task.task_id}")
                self.stats['failed_tasks'] += 1
        else:
            logger.error(f"任务重试次数已达上限: {queued_task.task_id}")
            self.stats['failed_tasks'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'running_tasks_count': len(self.running_tasks),
            'is_running': self.is_running,
            'worker_count': len(self.workers)
        }
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            if not task.done():
                task.cancel()
                logger.info(f"任务已取消: {task_id}")
                return True
        
        logger.warning(f"任务不存在或已完成: {task_id}")
        return False


# 全局任务队列管理器实例
task_queue_manager = TaskQueueManager()
