"""
结果存储服务
实现生成结果的分类存储、版本管理和元数据管理
"""

import logging
import os
import json
import uuid
import hashlib
import shutil
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import mimetypes

logger = logging.getLogger(__name__)

class ResultType(Enum):
    """结果类型"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    TEXT = "text"
    DOCUMENT = "document"
    OTHER = "other"

class ResultStatus(Enum):
    """结果状态"""
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    ARCHIVED = "archived"
    DELETED = "deleted"

@dataclass
class ResultMetadata:
    """结果元数据"""
    id: str
    task_id: str
    batch_id: Optional[str]
    type: ResultType
    status: ResultStatus
    title: str
    description: str = ""
    file_path: str = ""
    file_name: str = ""
    file_size: int = 0
    file_hash: str = ""
    mime_type: str = ""
    dimensions: Optional[Dict[str, int]] = None  # width, height for images/videos
    duration: Optional[float] = None  # duration for videos/audio
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    version: int = 1
    parent_id: Optional[str] = None  # for versioning
    created_at: str = ""
    updated_at: str = ""
    created_by: str = "system"
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = self.created_at

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['type'] = self.type.value
        result['status'] = self.status.value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResultMetadata':
        """从字典创建实例"""
        data = data.copy()
        data['type'] = ResultType(data['type'])
        data['status'] = ResultStatus(data['status'])
        return cls(**data)

class ResultStorageService:
    """结果存储服务"""
    
    def __init__(self, storage_root: str = None):
        self.storage_root = storage_root or os.getenv("RESULT_STORAGE_ROOT", "data/results")
        self.metadata_file = os.path.join(self.storage_root, "metadata.json")
        self.results: Dict[str, ResultMetadata] = {}
        self._ensure_storage_structure()
        self._load_metadata()
    
    def _ensure_storage_structure(self):
        """确保存储目录结构"""
        # 创建主目录
        os.makedirs(self.storage_root, exist_ok=True)
        
        # 创建分类目录
        for result_type in ResultType:
            type_dir = os.path.join(self.storage_root, result_type.value)
            os.makedirs(type_dir, exist_ok=True)
            
            # 创建子目录
            for subdir in ["originals", "thumbnails", "processed"]:
                os.makedirs(os.path.join(type_dir, subdir), exist_ok=True)
    
    def _load_metadata(self):
        """加载元数据"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.results = {
                        result_id: ResultMetadata.from_dict(result_data)
                        for result_id, result_data in data.items()
                    }
            else:
                self.results = {}
        except Exception as e:
            logger.error(f"加载结果元数据失败: {str(e)}")
            self.results = {}
    
    def _save_metadata(self):
        """保存元数据"""
        try:
            data = {
                result_id: result.to_dict()
                for result_id, result in self.results.items()
            }
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存结果元数据失败: {str(e)}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {str(e)}")
            return ""
    
    def _detect_result_type(self, file_path: str) -> ResultType:
        """检测结果类型"""
        mime_type, _ = mimetypes.guess_type(file_path)
        
        if mime_type:
            if mime_type.startswith('image/'):
                return ResultType.IMAGE
            elif mime_type.startswith('video/'):
                return ResultType.VIDEO
            elif mime_type.startswith('audio/'):
                return ResultType.AUDIO
            elif mime_type.startswith('text/'):
                return ResultType.TEXT
            elif mime_type in ['application/pdf', 'application/msword']:
                return ResultType.DOCUMENT
        
        return ResultType.OTHER
    
    def _get_file_dimensions(self, file_path: str, result_type: ResultType) -> Optional[Dict[str, int]]:
        """获取文件尺寸信息"""
        try:
            if result_type == ResultType.IMAGE:
                # 这里可以使用PIL或其他库获取图片尺寸
                # 暂时返回None，实际实现需要安装相应库
                return None
            elif result_type == ResultType.VIDEO:
                # 这里可以使用ffmpeg-python或其他库获取视频尺寸
                return None
        except Exception as e:
            logger.error(f"获取文件尺寸失败: {str(e)}")
        
        return None
    
    def store_result(self, task_id: str, file_path: str, title: str,
                    description: str = "", batch_id: Optional[str] = None,
                    tags: List[str] = None, metadata: Dict[str, Any] = None,
                    created_by: str = "system") -> Optional[ResultMetadata]:
        """存储结果文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 生成结果ID
            result_id = str(uuid.uuid4())
            
            # 检测文件类型
            result_type = self._detect_result_type(file_path)
            
            # 计算文件信息
            file_size = os.path.getsize(file_path)
            file_hash = self._calculate_file_hash(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            dimensions = self._get_file_dimensions(file_path, result_type)
            
            # 生成存储路径
            file_extension = Path(file_path).suffix
            stored_filename = f"{result_id}{file_extension}"
            storage_dir = os.path.join(self.storage_root, result_type.value, "originals")
            stored_path = os.path.join(storage_dir, stored_filename)
            
            # 复制文件到存储位置
            shutil.copy2(file_path, stored_path)
            
            # 创建结果元数据
            result_metadata = ResultMetadata(
                id=result_id,
                task_id=task_id,
                batch_id=batch_id,
                type=result_type,
                status=ResultStatus.COMPLETED,
                title=title,
                description=description,
                file_path=stored_path,
                file_name=stored_filename,
                file_size=file_size,
                file_hash=file_hash,
                mime_type=mime_type or "",
                dimensions=dimensions,
                tags=tags or [],
                metadata=metadata or {},
                created_by=created_by
            )
            
            # 保存元数据
            self.results[result_id] = result_metadata
            self._save_metadata()
            
            logger.info(f"存储结果成功: {result_id} ({title})")
            return result_metadata
            
        except Exception as e:
            logger.error(f"存储结果失败: {str(e)}")
            return None
    
    def get_result(self, result_id: str) -> Optional[ResultMetadata]:
        """获取结果元数据"""
        return self.results.get(result_id)
    
    def get_results(self, task_id: str = None, batch_id: str = None,
                   result_type: ResultType = None, status: ResultStatus = None,
                   tags: List[str] = None, limit: int = None) -> List[ResultMetadata]:
        """获取结果列表"""
        results = list(self.results.values())
        
        # 应用筛选条件
        if task_id:
            results = [r for r in results if r.task_id == task_id]
        
        if batch_id:
            results = [r for r in results if r.batch_id == batch_id]
        
        if result_type:
            results = [r for r in results if r.type == result_type]
        
        if status:
            results = [r for r in results if r.status == status]
        
        if tags:
            results = [r for r in results if any(tag in r.tags for tag in tags)]
        
        # 按创建时间倒序排序
        results.sort(key=lambda r: r.created_at, reverse=True)
        
        # 应用限制
        if limit:
            results = results[:limit]
        
        return results
    
    def create_version(self, parent_id: str, file_path: str, title: str,
                      description: str = "", metadata: Dict[str, Any] = None,
                      created_by: str = "system") -> Optional[ResultMetadata]:
        """创建结果版本"""
        parent = self.get_result(parent_id)
        if not parent:
            logger.error(f"父结果不存在: {parent_id}")
            return None
        
        # 创建新版本
        new_result = self.store_result(
            task_id=parent.task_id,
            file_path=file_path,
            title=title,
            description=description,
            batch_id=parent.batch_id,
            tags=parent.tags.copy(),
            metadata=metadata,
            created_by=created_by
        )
        
        if new_result:
            # 设置版本信息
            new_result.version = parent.version + 1
            new_result.parent_id = parent_id
            self._save_metadata()
            
            logger.info(f"创建结果版本: {new_result.id} (v{new_result.version})")
        
        return new_result
    
    def get_versions(self, result_id: str) -> List[ResultMetadata]:
        """获取结果的所有版本"""
        versions = []
        
        # 找到根版本
        root_result = self.get_result(result_id)
        if not root_result:
            return versions
        
        # 如果当前结果有父结果，找到根结果
        while root_result.parent_id:
            root_result = self.get_result(root_result.parent_id)
            if not root_result:
                break
        
        if root_result:
            versions.append(root_result)
            
            # 递归查找所有子版本
            self._collect_child_versions(root_result.id, versions)
        
        # 按版本号排序
        versions.sort(key=lambda r: r.version)
        return versions
    
    def _collect_child_versions(self, parent_id: str, versions: List[ResultMetadata]):
        """递归收集子版本"""
        for result in self.results.values():
            if result.parent_id == parent_id:
                versions.append(result)
                self._collect_child_versions(result.id, versions)
    
    def update_result(self, result_id: str, **kwargs) -> Optional[ResultMetadata]:
        """更新结果元数据"""
        result = self.results.get(result_id)
        if not result:
            return None
        
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(result, key) and key not in ['id', 'file_hash', 'created_at']:
                setattr(result, key, value)
        
        result.updated_at = datetime.now().isoformat()
        self._save_metadata()
        
        logger.info(f"更新结果元数据: {result_id}")
        return result
    
    def delete_result(self, result_id: str, remove_file: bool = False) -> bool:
        """删除结果"""
        result = self.results.get(result_id)
        if not result:
            return False
        
        try:
            # 删除文件（如果需要）
            if remove_file and os.path.exists(result.file_path):
                os.remove(result.file_path)
                
                # 删除相关的缩略图和处理文件
                self._cleanup_related_files(result)
            
            # 标记为已删除或直接删除
            if remove_file:
                del self.results[result_id]
            else:
                result.status = ResultStatus.DELETED
                result.updated_at = datetime.now().isoformat()
            
            self._save_metadata()
            
            logger.info(f"删除结果: {result_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除结果失败: {str(e)}")
            return False
    
    def _cleanup_related_files(self, result: ResultMetadata):
        """清理相关文件"""
        try:
            # 删除缩略图
            thumbnail_dir = os.path.join(self.storage_root, result.type.value, "thumbnails")
            thumbnail_path = os.path.join(thumbnail_dir, f"{result.id}_thumb.jpg")
            if os.path.exists(thumbnail_path):
                os.remove(thumbnail_path)
            
            # 删除处理文件
            processed_dir = os.path.join(self.storage_root, result.type.value, "processed")
            for file in os.listdir(processed_dir):
                if file.startswith(result.id):
                    os.remove(os.path.join(processed_dir, file))
                    
        except Exception as e:
            logger.error(f"清理相关文件失败: {str(e)}")
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = {
            "total_results": len(self.results),
            "by_type": {},
            "by_status": {},
            "total_size": 0,
            "storage_usage": {}
        }
        
        for result in self.results.values():
            # 按类型统计
            type_name = result.type.value
            stats["by_type"][type_name] = stats["by_type"].get(type_name, 0) + 1
            
            # 按状态统计
            status_name = result.status.value
            stats["by_status"][status_name] = stats["by_status"].get(status_name, 0) + 1
            
            # 总大小
            stats["total_size"] += result.file_size
        
        # 计算存储使用情况
        for result_type in ResultType:
            type_dir = os.path.join(self.storage_root, result_type.value)
            if os.path.exists(type_dir):
                size = self._get_directory_size(type_dir)
                stats["storage_usage"][result_type.value] = size
        
        return stats
    
    def _get_directory_size(self, directory: str) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            logger.error(f"计算目录大小失败: {str(e)}")
        
        return total_size

# 全局结果存储服务实例
_result_storage_service = None

def get_result_storage_service() -> ResultStorageService:
    """获取结果存储服务实例"""
    global _result_storage_service
    
    if _result_storage_service is None:
        _result_storage_service = ResultStorageService()
    
    return _result_storage_service
