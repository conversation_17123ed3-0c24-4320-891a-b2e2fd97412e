#!/usr/bin/env python3
"""
视频数据库索引优化脚本
用于创建必要的索引以提高视频详细信息查询性能
"""

import asyncio
import logging
from pymongo import ASCENDING, DESCENDING, TEXT
from pymongo.database import Database
from pymongo.errors import OperationFailure

from app.core.database import get_database

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_video_indexes(db: Database):
    """创建视频相关的索引"""
    
    collection = db.competitor_content
    
    # 索引配置
    indexes_to_create = [
        # 1. 基础查询索引
        {
            "keys": [("content_type", ASCENDING), ("created_at", DESCENDING)],
            "name": "content_type_created_at_idx",
            "background": True
        },
        
        # 2. 平台筛选索引
        {
            "keys": [("platform", ASCENDING), ("created_at", DESCENDING)],
            "name": "platform_created_at_idx", 
            "background": True
        },
        
        # 3. 复合查询索引
        {
            "keys": [
                ("content_type", ASCENDING),
                ("platform", ASCENDING), 
                ("created_at", DESCENDING)
            ],
            "name": "content_platform_created_idx",
            "background": True
        },
        
        # 4. 文本搜索索引
        {
            "keys": [
                ("title", TEXT),
                ("description", TEXT),
                ("author.name", TEXT)
            ],
            "name": "text_search_idx",
            "background": True,
            "default_language": "none"  # 支持中文搜索
        },
        
        # 5. 文件信息查询索引
        {
            "keys": [("file_info.local_path", ASCENDING)],
            "name": "file_path_idx",
            "background": True,
            "unique": True,
            "sparse": True
        },
        
        # 6. 媒体信息缓存索引
        {
            "keys": [
                ("file_info.media_info_cached", ASCENDING),
                ("file_info.media_info_updated_at", DESCENDING)
            ],
            "name": "media_cache_idx",
            "background": True,
            "sparse": True
        },
        
        # 7. 作者查询索引
        {
            "keys": [("author.channel_id", ASCENDING)],
            "name": "author_channel_idx",
            "background": True,
            "sparse": True
        },
        
        # 8. 元数据统计索引
        {
            "keys": [
                ("metadata.view_count", DESCENDING),
                ("metadata.like_count", DESCENDING)
            ],
            "name": "metadata_stats_idx",
            "background": True,
            "sparse": True
        },
        
        # 9. 发布时间索引
        {
            "keys": [("metadata.publish_date", DESCENDING)],
            "name": "publish_date_idx",
            "background": True,
            "sparse": True
        },
        
        # 10. 分析状态索引
        {
            "keys": [("analysis.sentiment", ASCENDING)],
            "name": "analysis_sentiment_idx",
            "background": True,
            "sparse": True
        }
    ]
    
    logger.info(f"开始创建 {len(indexes_to_create)} 个索引...")
    
    created_count = 0
    skipped_count = 0
    
    for index_config in indexes_to_create:
        try:
            index_name = index_config.pop("name")
            keys = index_config.pop("keys")
            
            # 检查索引是否已存在
            existing_indexes = await collection.list_indexes().to_list(length=None)
            index_exists = any(idx.get("name") == index_name for idx in existing_indexes)
            
            if index_exists:
                logger.info(f"索引 {index_name} 已存在，跳过创建")
                skipped_count += 1
                continue
            
            # 创建索引
            logger.info(f"创建索引: {index_name}")
            await collection.create_index(keys, name=index_name, **index_config)
            created_count += 1
            logger.info(f"索引 {index_name} 创建成功")
            
        except OperationFailure as e:
            logger.error(f"创建索引失败: {e}")
        except Exception as e:
            logger.error(f"创建索引时发生错误: {e}")
    
    logger.info(f"索引创建完成: 新建 {created_count} 个，跳过 {skipped_count} 个")


async def analyze_collection_performance(db: Database):
    """分析集合性能"""
    
    collection = db.competitor_content
    
    logger.info("开始分析集合性能...")
    
    # 1. 集合统计信息
    stats = await db.command("collStats", "competitor_content")
    logger.info(f"文档总数: {stats.get('count', 0):,}")
    logger.info(f"数据大小: {stats.get('size', 0) / 1024 / 1024:.2f} MB")
    logger.info(f"索引大小: {stats.get('totalIndexSize', 0) / 1024 / 1024:.2f} MB")
    
    # 2. 索引使用情况
    indexes = await collection.list_indexes().to_list(length=None)
    logger.info(f"当前索引数量: {len(indexes)}")
    
    for idx in indexes:
        logger.info(f"  - {idx.get('name', 'unnamed')}: {idx.get('key', {})}")
    
    # 3. 常见查询性能测试
    test_queries = [
        {"content_type": "video"},
        {"platform": "youtube"},
        {"content_type": "video", "platform": "youtube"},
        {"$text": {"$search": "测试"}},
        {"file_info.media_info_cached": True}
    ]
    
    logger.info("测试常见查询性能...")
    for i, query in enumerate(test_queries, 1):
        try:
            # 使用 explain 分析查询计划
            explain_result = await collection.find(query).explain()
            execution_stats = explain_result.get("executionStats", {})
            
            total_docs = execution_stats.get("totalDocsExamined", 0)
            returned_docs = execution_stats.get("totalDocsReturned", 0)
            execution_time = execution_stats.get("executionTimeMillis", 0)
            
            logger.info(f"查询 {i}: {query}")
            logger.info(f"  执行时间: {execution_time} ms")
            logger.info(f"  检查文档: {total_docs:,}")
            logger.info(f"  返回文档: {returned_docs:,}")
            
            if total_docs > 0:
                efficiency = returned_docs / total_docs * 100
                logger.info(f"  查询效率: {efficiency:.2f}%")
            
        except Exception as e:
            logger.error(f"查询 {i} 性能测试失败: {e}")


async def optimize_existing_data(db: Database):
    """优化现有数据"""
    
    collection = db.competitor_content
    
    logger.info("开始优化现有数据...")
    
    # 1. 统计需要优化的文档
    total_docs = await collection.count_documents({})
    logger.info(f"总文档数: {total_docs:,}")
    
    # 2. 统计缺少媒体信息缓存标记的文档
    missing_cache_flag = await collection.count_documents({
        "content_type": "video",
        "file_info.media_info_cached": {"$exists": False}
    })
    logger.info(f"缺少媒体信息缓存标记的文档: {missing_cache_flag:,}")
    
    # 3. 批量更新缺少缓存标记的文档
    if missing_cache_flag > 0:
        logger.info("批量添加媒体信息缓存标记...")
        
        update_result = await collection.update_many(
            {
                "content_type": "video",
                "file_info.media_info_cached": {"$exists": False}
            },
            {
                "$set": {
                    "file_info.media_info_cached": False
                }
            }
        )
        
        logger.info(f"更新了 {update_result.modified_count:,} 个文档")
    
    # 4. 统计文件路径重复的文档
    pipeline = [
        {"$group": {
            "_id": "$file_info.local_path",
            "count": {"$sum": 1},
            "docs": {"$push": "$_id"}
        }},
        {"$match": {"count": {"$gt": 1}}}
    ]
    
    duplicates = await collection.aggregate(pipeline).to_list(length=None)
    if duplicates:
        logger.warning(f"发现 {len(duplicates)} 个重复的文件路径")
        for dup in duplicates[:5]:  # 只显示前5个
            logger.warning(f"  路径: {dup['_id']}, 重复数: {dup['count']}")


async def create_compound_indexes_for_pagination(db: Database):
    """为分页查询创建复合索引"""
    
    collection = db.competitor_content
    
    logger.info("创建分页优化索引...")
    
    # 分页查询常用的复合索引
    pagination_indexes = [
        # 按创建时间分页
        {
            "keys": [("content_type", 1), ("created_at", -1), ("_id", 1)],
            "name": "pagination_created_at_idx"
        },
        
        # 按平台和创建时间分页
        {
            "keys": [("platform", 1), ("created_at", -1), ("_id", 1)],
            "name": "pagination_platform_created_idx"
        },
        
        # 按观看数分页
        {
            "keys": [("content_type", 1), ("metadata.view_count", -1), ("_id", 1)],
            "name": "pagination_view_count_idx"
        },
        
        # 按点赞数分页
        {
            "keys": [("content_type", 1), ("metadata.like_count", -1), ("_id", 1)],
            "name": "pagination_like_count_idx"
        }
    ]
    
    for index_config in pagination_indexes:
        try:
            index_name = index_config["name"]
            keys = index_config["keys"]
            
            # 检查索引是否已存在
            existing_indexes = await collection.list_indexes().to_list(length=None)
            index_exists = any(idx.get("name") == index_name for idx in existing_indexes)
            
            if not index_exists:
                logger.info(f"创建分页索引: {index_name}")
                await collection.create_index(keys, name=index_name, background=True)
                logger.info(f"分页索引 {index_name} 创建成功")
            else:
                logger.info(f"分页索引 {index_name} 已存在")
                
        except Exception as e:
            logger.error(f"创建分页索引失败: {e}")


async def main():
    """主函数"""
    try:
        # 获取数据库连接
        db = get_database()
        
        logger.info("开始视频数据库索引优化...")
        
        # 1. 分析当前性能
        await analyze_collection_performance(db)
        
        # 2. 创建基础索引
        await create_video_indexes(db)
        
        # 3. 创建分页优化索引
        await create_compound_indexes_for_pagination(db)
        
        # 4. 优化现有数据
        await optimize_existing_data(db)
        
        # 5. 再次分析性能
        logger.info("优化完成，重新分析性能...")
        await analyze_collection_performance(db)
        
        logger.info("视频数据库索引优化完成！")
        
    except Exception as e:
        logger.error(f"优化过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
