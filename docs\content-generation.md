# 内容生成功能文档

## 概述

ThunderHub的内容生成功能提供了完整的AI内容生成解决方案，支持文本、图片、视频等多种内容类型的生成。该功能集成了ComfyUI等AI工具，提供了从任务创建到结果管理的完整工作流。

## 功能特性

### 🎯 核心功能
- **多类型内容生成**: 支持文本、图片、视频等多种内容类型
- **ComfyUI集成**: 深度集成ComfyUI，支持自定义工作流
- **任务队列管理**: 支持并发任务处理和队列管理
- **进度监控**: 实时任务进度跟踪和状态更新
- **文件管理**: 完整的文件存储、预览、下载功能

### 🔧 技术特性
- **异步处理**: 基于asyncio的高性能异步任务处理
- **微服务架构**: 模块化设计，易于扩展和维护
- **数据库持久化**: MongoDB存储任务和结果数据
- **RESTful API**: 标准的REST API接口
- **WebSocket支持**: 实时进度推送（规划中）

## 系统架构

```
Frontend (Vue3)
    ↓ HTTP API
Backend (FastAPI)
    ↓ Task Queue
Content Generation Service
    ↓ gRPC/HTTP
ComfyUI Service
    ↓ File System
File Storage Service
```

### 组件说明

1. **Frontend**: Vue3前端界面，提供任务创建和管理界面
2. **Backend API**: FastAPI后端，提供RESTful API接口
3. **Content Generation Service**: 内容生成任务管理服务
4. **ComfyUI Service**: ComfyUI集成服务
5. **File Storage Service**: 文件存储管理服务

## API接口

### 任务管理

#### 创建任务
```http
POST /api/v1/content-generate/tasks
Content-Type: application/json

{
  "title": "测试视频生成",
  "type": "video",
  "description": "生成一个关于美丽风景的短视频",
  "style": "realistic",
  "count": 1,
  "config": {
    "duration": 30,
    "resolution": "1920x1080",
    "comfyui_workflow": "default_video"
  }
}
```

#### 获取任务列表
```http
GET /api/v1/content-generate/tasks?page=1&page_size=20&status=completed
```

#### 获取任务详情
```http
GET /api/v1/content-generate/tasks/{task_id}
```

#### 启动任务
```http
POST /api/v1/content-generate/tasks/{task_id}/start
```

#### 获取任务进度
```http
GET /api/v1/content-generate/tasks/{task_id}/progress
```

### 文件管理

#### 预览结果
```http
GET /api/v1/content-generate/results/{result_id}/preview
```

#### 下载结果
```http
GET /api/v1/content-generate/results/{result_id}/download
```

### 系统信息

#### 测试ComfyUI连接
```http
GET /api/v1/content-generate/comfyui/test
```

#### 获取可用模型
```http
GET /api/v1/content-generate/models
```

#### 获取统计信息
```http
GET /api/v1/content-generate/stats
```

## 配置说明

### 环境变量

```bash
# ComfyUI配置
COMFYUI_HOST=localhost
COMFYUI_PORT=8188

# 文件存储配置
CONTENT_GENERATION_STORAGE_PATH=data/content_generation

# 任务队列配置
MAX_CONCURRENT_GENERATION_TASKS=3
```

### ComfyUI工作流配置

系统支持多种预定义的ComfyUI工作流：

- `default_video`: 默认视频生成工作流
- `high_quality`: 高质量渲染工作流
- `fast_generation`: 快速生成工作流

## 使用指南

### 1. 环境准备

确保以下服务正常运行：
- MongoDB数据库
- Redis缓存
- ComfyUI服务（可选）

### 2. 启动服务

```bash
# 启动后端服务
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 启动前端服务
cd frontend
npm run dev
```

### 3. 创建生成任务

1. 打开前端界面：http://localhost:5173
2. 导航到"内容生成"页面
3. 点击"新建任务"按钮
4. 填写任务信息：
   - 任务名称
   - 生成类型（文本/图片/视频）
   - 内容描述
   - 生成风格
   - 生成数量
5. 配置高级选项（可选）
6. 点击"开始生成"

### 4. 监控任务进度

- 任务列表会实时显示任务状态
- 点击任务可查看详细进度
- 支持暂停、恢复、取消操作

### 5. 管理生成结果

- 任务完成后可预览生成结果
- 支持下载原始文件
- 支持批量操作

## 故障排除

### 常见问题

#### 1. ComfyUI连接失败
- 检查ComfyUI服务是否启动
- 确认端口配置正确
- 检查防火墙设置

#### 2. 任务创建失败
- 检查数据库连接
- 确认请求参数格式正确
- 查看后端日志

#### 3. 文件下载失败
- 检查文件存储路径
- 确认文件权限设置
- 检查磁盘空间

### 日志查看

```bash
# 查看后端日志
tail -f logs/backend.log

# 查看任务处理日志
tail -f logs/content_generation.log
```

## 开发指南

### 添加新的内容类型

1. 在`CreateTaskRequest`模型中添加新类型
2. 在`ContentGenerationTaskManager`中实现对应的工作流生成方法
3. 更新前端界面支持新类型

### 自定义ComfyUI工作流

1. 在ComfyUI中设计工作流
2. 导出工作流JSON
3. 在`_generate_workflow`方法中添加新工作流
4. 更新前端工作流选择器

### 扩展文件存储

1. 继承`FileStorageService`类
2. 实现自定义存储逻辑
3. 更新服务注册

## 性能优化

### 任务队列优化
- 调整并发任务数量
- 实现任务优先级
- 添加任务重试机制

### 文件存储优化
- 实现文件压缩
- 添加CDN支持
- 定期清理临时文件

### 数据库优化
- 添加索引
- 实现数据分片
- 定期归档历史数据

## 安全考虑

- API访问控制
- 文件上传限制
- 内容审核机制
- 用户权限管理

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 基础内容生成功能
- ComfyUI集成
- 文件管理功能

## 贡献指南

欢迎提交Issue和Pull Request来改进内容生成功能。

## 许可证

本项目采用MIT许可证。
