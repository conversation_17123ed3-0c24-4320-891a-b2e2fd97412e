"""
视频高清处理服务

提供视频和图片的高清处理功能，支持AI超分辨率和传统算法。
"""

import os
import asyncio
import subprocess
import logging
import time
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class VideoEnhanceService:
    """视频高清处理服务"""
    
    def __init__(self):
        self.logger = logger
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v', 'webm', '3gp', 'ts']
        self.supported_image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff']
        
    async def enhance_files(self, file_paths: List[str], enhance_mode: str = 'ai',
                           scale_factor: int = 2, output_quality: str = 'high',
                           denoise_level: str = 'medium', sharpen_level: str = 'medium',
                           overwrite_original: bool = False, output_suffix: str = '_enhanced',
                           max_concurrent: int = 2) -> Dict[str, Any]:
        """
        批量高清处理文件
        
        Args:
            file_paths: 文件路径列表
            enhance_mode: 处理模式 (ai, traditional)
            scale_factor: 放大倍数 (2, 3, 4)
            output_quality: 输出质量 (high, medium, low)
            denoise_level: 降噪级别 (none, low, medium, high)
            sharpen_level: 锐化级别 (none, low, medium, high)
            overwrite_original: 是否覆盖原文件
            output_suffix: 输出文件名后缀
            max_concurrent: 最大并发处理数
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始批量高清处理: {len(file_paths)} 个文件")
            logger.info(f"处理模式: {enhance_mode}, 放大倍数: {scale_factor}x")
            logger.info(f"输出质量: {output_quality}, 降噪: {denoise_level}, 锐化: {sharpen_level}")
            
            # 验证文件
            valid_files = []
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    logger.warning(f"文件不存在: {file_path}")
                    continue
                    
                if not self._is_supported_file(file_path):
                    logger.warning(f"不支持的文件类型: {file_path}")
                    continue
                    
                valid_files.append(file_path)
            
            if not valid_files:
                return {
                    "success": False,
                    "error": "没有找到有效的文件",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0,
                    "total_count": 0
                }
            
            logger.info(f"有效文件数量: {len(valid_files)}")
            
            # 创建输出目录
            output_folder = self._create_output_folder(valid_files[0], overwrite_original)
            
            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            # 创建处理任务
            tasks = []
            for file_path in valid_files:
                task = self._enhance_single_file_with_semaphore(
                    semaphore, file_path, enhance_mode, scale_factor,
                    output_quality, denoise_level, sharpen_level,
                    overwrite_original, output_suffix, output_folder
                )
                tasks.append(task)
            
            # 并发执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            successful_results = []
            failed_results = []
            total_output_size = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"处理文件异常: {valid_files[i]}, {str(result)}")
                    failed_results.append({
                        "original_path": valid_files[i],
                        "output_path": "",
                        "success": False,
                        "error_message": str(result),
                        "processing_time_ms": 0,
                        "original_file_size": 0,
                        "output_file_size": 0,
                        "original_resolution": "",
                        "output_resolution": "",
                        "scale_factor": scale_factor
                    })
                elif result["success"]:
                    successful_results.append(result)
                    total_output_size += result.get("output_file_size", 0)
                else:
                    failed_results.append(result)
            
            total_processing_time = int((time.time() - start_time) * 1000)
            
            logger.info(f"高清处理完成: 成功 {len(successful_results)} 个，失败 {len(failed_results)} 个")
            logger.info(f"总处理时间: {total_processing_time}ms")
            
            return {
                "success": True,
                "error": "",
                "results": successful_results + failed_results,
                "total_processing_time_ms": total_processing_time,
                "successful_count": len(successful_results),
                "failed_count": len(failed_results),
                "total_count": len(valid_files),
                "output_folder": output_folder,
                "total_output_size": total_output_size
            }
            
        except Exception as e:
            logger.error(f"批量高清处理异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": [],
                "total_processing_time_ms": int((time.time() - start_time) * 1000),
                "successful_count": 0,
                "failed_count": len(file_paths),
                "total_count": len(file_paths)
            }
    
    async def _enhance_single_file_with_semaphore(self, semaphore: asyncio.Semaphore,
                                                 file_path: str, enhance_mode: str,
                                                 scale_factor: int, output_quality: str,
                                                 denoise_level: str, sharpen_level: str,
                                                 overwrite_original: bool, output_suffix: str,
                                                 output_folder: str) -> Dict[str, Any]:
        """使用信号量控制的单文件处理"""
        async with semaphore:
            return await self._enhance_single_file(
                file_path, enhance_mode, scale_factor, output_quality,
                denoise_level, sharpen_level, overwrite_original,
                output_suffix, output_folder
            )
    
    async def _enhance_single_file(self, file_path: str, enhance_mode: str,
                                  scale_factor: int, output_quality: str,
                                  denoise_level: str, sharpen_level: str,
                                  overwrite_original: bool, output_suffix: str,
                                  output_folder: str) -> Dict[str, Any]:
        """
        处理单个文件
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始处理文件: {file_path}")
            
            # 获取文件信息
            original_file_size = os.path.getsize(file_path)
            original_resolution = await self._get_file_resolution(file_path)
            
            # 确定输出路径
            if overwrite_original:
                output_path = file_path
                # 创建临时文件用于处理
                temp_path = file_path + ".tmp_enhanced"
            else:
                filename = Path(file_path).stem
                extension = Path(file_path).suffix
                output_filename = f"{filename}{output_suffix}{extension}"
                output_path = os.path.join(output_folder, output_filename)
                temp_path = output_path
            
            # 检查输出文件是否已存在
            if not overwrite_original and os.path.exists(output_path):
                logger.info(f"输出文件已存在，跳过处理: {output_path}")
                return {
                    "original_path": file_path,
                    "output_path": output_path,
                    "success": True,
                    "error_message": "",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "original_file_size": original_file_size,
                    "output_file_size": os.path.getsize(output_path),
                    "original_resolution": original_resolution,
                    "output_resolution": await self._get_file_resolution(output_path),
                    "scale_factor": scale_factor
                }
            
            # 根据文件类型选择处理方法
            if self._is_video_file(file_path):
                success = await self._enhance_video_file(
                    file_path, temp_path, enhance_mode, scale_factor,
                    output_quality, denoise_level, sharpen_level
                )
            else:
                success = await self._enhance_image_file(
                    file_path, temp_path, enhance_mode, scale_factor,
                    output_quality, denoise_level, sharpen_level
                )
            
            if not success:
                return {
                    "original_path": file_path,
                    "output_path": "",
                    "success": False,
                    "error_message": "处理失败",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "original_file_size": original_file_size,
                    "output_file_size": 0,
                    "original_resolution": original_resolution,
                    "output_resolution": "",
                    "scale_factor": scale_factor
                }
            
            # 如果是覆盖模式，替换原文件
            if overwrite_original:
                if os.path.exists(temp_path):
                    os.replace(temp_path, file_path)
                    output_path = file_path
            
            # 获取输出文件信息
            output_file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
            output_resolution = await self._get_file_resolution(output_path) if os.path.exists(output_path) else ""
            
            processing_time = int((time.time() - start_time) * 1000)
            
            logger.info(f"文件处理完成: {file_path} -> {output_path} ({processing_time}ms)")
            
            return {
                "original_path": file_path,
                "output_path": output_path,
                "success": True,
                "error_message": "",
                "processing_time_ms": processing_time,
                "original_file_size": original_file_size,
                "output_file_size": output_file_size,
                "original_resolution": original_resolution,
                "output_resolution": output_resolution,
                "scale_factor": scale_factor
            }
            
        except Exception as e:
            logger.error(f"处理文件异常: {file_path}, {str(e)}")
            return {
                "original_path": file_path,
                "output_path": "",
                "success": False,
                "error_message": str(e),
                "processing_time_ms": int((time.time() - start_time) * 1000),
                "original_file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                "output_file_size": 0,
                "original_resolution": "",
                "output_resolution": "",
                "scale_factor": scale_factor
            }

    def _is_supported_file(self, file_path: str) -> bool:
        """检查是否为支持的文件类型"""
        extension = Path(file_path).suffix.lower().lstrip('.')
        return extension in self.supported_video_extensions or extension in self.supported_image_extensions

    def _is_video_file(self, file_path: str) -> bool:
        """检查是否为视频文件"""
        extension = Path(file_path).suffix.lower().lstrip('.')
        return extension in self.supported_video_extensions

    def _create_output_folder(self, sample_file_path: str, overwrite_original: bool) -> str:
        """创建输出文件夹"""
        if overwrite_original:
            return os.path.dirname(sample_file_path)

        # 在第一个文件的目录下创建enhanced文件夹
        base_dir = os.path.dirname(sample_file_path)
        output_folder = os.path.join(base_dir, "enhanced")

        if not os.path.exists(output_folder):
            os.makedirs(output_folder, exist_ok=True)
            logger.info(f"创建输出文件夹: {output_folder}")

        return output_folder

    async def _get_file_resolution(self, file_path: str) -> str:
        """获取文件分辨率"""
        try:
            if self._is_video_file(file_path):
                # 使用ffprobe获取视频分辨率
                cmd = [
                    'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams',
                    file_path
                ]

                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await process.communicate()

                if process.returncode == 0:
                    import json
                    data = json.loads(stdout.decode('utf-8'))
                    for stream in data.get('streams', []):
                        if stream.get('codec_type') == 'video':
                            width = stream.get('width', 0)
                            height = stream.get('height', 0)
                            if width and height:
                                return f"{width}x{height}"
            else:
                # 使用ffprobe获取图片分辨率
                cmd = [
                    'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams',
                    file_path
                ]

                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await process.communicate()

                if process.returncode == 0:
                    import json
                    data = json.loads(stdout.decode('utf-8'))
                    for stream in data.get('streams', []):
                        width = stream.get('width', 0)
                        height = stream.get('height', 0)
                        if width and height:
                            return f"{width}x{height}"

            return "unknown"

        except Exception as e:
            logger.warning(f"获取文件分辨率失败: {file_path}, {str(e)}")
            return "unknown"

    async def _enhance_video_file(self, input_path: str, output_path: str,
                                 enhance_mode: str, scale_factor: int,
                                 output_quality: str, denoise_level: str,
                                 sharpen_level: str) -> bool:
        """处理视频文件"""
        try:
            logger.debug(f"开始处理视频: {input_path} -> {output_path}")

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y', '-i', input_path]

            # 构建视频滤镜
            filters = []

            # 添加高级缩放和增强滤镜
            if enhance_mode == 'ai':
                # AI模式：使用多步骤增强流程
                logger.info("使用AI增强模式")

                # 1. 先进行预处理降噪（轻度）
                filters.append('hqdn3d=1:1:3:3')

                # 2. 使用高质量缩放算法
                filters.append(f'scale=iw*{scale_factor}:ih*{scale_factor}:flags=lanczos:param0=3')

                # 3. 添加细节增强
                filters.append('unsharp=7:7:1.5:7:7:0.0')  # 锐化细节

                # 4. 对比度和饱和度增强
                filters.append('eq=contrast=1.1:brightness=0.02:saturation=1.05')

                # 5. 边缘增强
                filters.append('convolution=0 -1 0:-1 5 -1:0 -1 0:0:1:0:128:128:128')

                # 6. 最终降噪处理
                if denoise_level != 'none':
                    denoise_strength = {'low': 1, 'medium': 2, 'high': 3}.get(denoise_level, 2)
                    filters.append(f'hqdn3d={denoise_strength}')

            else:
                # 传统模式：使用经典算法但优化参数
                logger.info("使用传统增强模式")

                # 1. 预处理
                if denoise_level != 'none':
                    denoise_strength = {'low': 1, 'medium': 2, 'high': 4}.get(denoise_level, 2)
                    filters.append(f'hqdn3d={denoise_strength}')

                # 2. 高质量缩放
                filters.append(f'scale=iw*{scale_factor}:ih*{scale_factor}:flags=bicubic')

                # 3. 锐化处理
                if sharpen_level != 'none':
                    sharpen_strength = {'low': 0.8, 'medium': 1.2, 'high': 1.8}.get(sharpen_level, 1.2)
                    filters.append(f'unsharp=5:5:{sharpen_strength}:5:5:0.0')

                # 4. 轻微对比度增强
                filters.append('eq=contrast=1.05:brightness=0.01')

            # 添加自定义锐化（如果用户指定了）
            if sharpen_level != 'none' and enhance_mode == 'ai':
                # AI模式下的额外锐化
                extra_sharpen = {'low': 0.3, 'medium': 0.5, 'high': 0.8}.get(sharpen_level, 0.5)
                filters.append(f'unsharp=3:3:{extra_sharpen}:3:3:0.0')

            # 组合滤镜
            if filters:
                filter_complex = ','.join(filters)
                cmd.extend(['-vf', filter_complex])
                logger.debug(f"使用滤镜链: {filter_complex}")

            # 设置高质量编码参数
            cmd.extend(['-c:v', 'libx264'])

            # 使用更慢但质量更好的预设
            if enhance_mode == 'ai':
                cmd.extend(['-preset', 'veryslow'])  # AI模式使用最慢预设
            else:
                cmd.extend(['-preset', 'slow'])     # 传统模式使用慢预设

            # 根据质量设置CRF（更低的值 = 更高质量）
            if output_quality == 'high':
                cmd.extend(['-crf', '16'])  # 从18降到16，更高质量
            elif output_quality == 'medium':
                cmd.extend(['-crf', '20'])  # 从23降到20
            else:  # low
                cmd.extend(['-crf', '24'])  # 从28降到24

            # 设置像素格式和色彩空间
            cmd.extend(['-pix_fmt', 'yuv420p'])
            cmd.extend(['-colorspace', 'bt709'])
            cmd.extend(['-color_primaries', 'bt709'])
            cmd.extend(['-color_trc', 'bt709'])

            # 音频处理
            cmd.extend(['-c:a', 'copy'])

            # 输出文件
            cmd.append(output_path)

            logger.info(f"执行高清处理命令，预计处理时间较长...")
            logger.debug(f"完整命令: {' '.join(cmd)}")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"视频高清处理成功: {output_path}")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"视频高清处理失败: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"视频处理异常: {str(e)}")
            return False

    async def _enhance_image_file(self, input_path: str, output_path: str,
                                 enhance_mode: str, scale_factor: int,
                                 output_quality: str, denoise_level: str,
                                 sharpen_level: str) -> bool:
        """处理图片文件"""
        try:
            logger.debug(f"开始处理图片: {input_path} -> {output_path}")

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y', '-i', input_path]

            # 构建滤镜
            filters = []

            # 添加高级图片增强滤镜
            if enhance_mode == 'ai':
                # AI模式：优先尝试真正的AI超分辨率工具
                logger.info("使用AI图片增强模式")

                # 首先尝试使用Real-ESRGAN
                if await self._try_real_esrgan_enhance(input_path, output_path, scale_factor):
                    return True

                # 如果Real-ESRGAN失败，尝试waifu2x
                if await self._try_waifu2x_enhance(input_path, output_path, scale_factor):
                    return True

                # 如果AI工具都失败，使用高级FFmpeg算法
                logger.info("AI工具不可用，使用高级FFmpeg算法")

                # 1. 预处理降噪
                if denoise_level != 'none':
                    denoise_strength = {'low': 1, 'medium': 2, 'high': 3}.get(denoise_level, 2)
                    filters.append(f'hqdn3d={denoise_strength}:1:3:3')

                # 2. 高质量超分辨率缩放
                filters.append(f'scale=iw*{scale_factor}:ih*{scale_factor}:flags=lanczos:param0=3')

                # 3. 细节增强和锐化
                filters.append('unsharp=7:7:2.0:7:7:0.0')

                # 4. 对比度和色彩增强
                filters.append('eq=contrast=1.15:brightness=0.03:saturation=1.08:gamma=0.95')

                # 5. 边缘增强（适用于图片）
                filters.append('convolution=0 -1 0:-1 5 -1:0 -1 0:0:1:0:128:128:128')

                # 6. 高频细节增强
                filters.append('unsharp=3:3:1.0:3:3:0.0')

                # 7. 最终轻微降噪
                filters.append('hqdn3d=0.5:0.5:1:1')

            else:
                # 传统模式：优化的经典算法
                logger.info("使用传统图片增强模式")

                # 1. 预处理降噪
                if denoise_level != 'none':
                    denoise_strength = {'low': 1, 'medium': 2, 'high': 4}.get(denoise_level, 2)
                    filters.append(f'hqdn3d={denoise_strength}')

                # 2. 高质量缩放
                filters.append(f'scale=iw*{scale_factor}:ih*{scale_factor}:flags=bicubic')

                # 3. 锐化处理
                if sharpen_level != 'none':
                    sharpen_strength = {'low': 1.0, 'medium': 1.5, 'high': 2.0}.get(sharpen_level, 1.5)
                    filters.append(f'unsharp=5:5:{sharpen_strength}:5:5:0.0')

                # 4. 对比度增强
                filters.append('eq=contrast=1.08:brightness=0.02:saturation=1.03')

            # 添加自定义锐化（如果用户指定了）
            if sharpen_level != 'none' and enhance_mode == 'ai':
                # AI模式下的额外锐化
                extra_sharpen = {'low': 0.5, 'medium': 0.8, 'high': 1.2}.get(sharpen_level, 0.8)
                filters.append(f'unsharp=3:3:{extra_sharpen}:3:3:0.0')

            # 组合滤镜
            if filters:
                filter_complex = ','.join(filters)
                cmd.extend(['-vf', filter_complex])
                logger.debug(f"使用图片滤镜链: {filter_complex}")

            # 设置高质量图片编码参数
            if output_quality == 'high':
                cmd.extend(['-q:v', '1'])    # 最高质量
            elif output_quality == 'medium':
                cmd.extend(['-q:v', '3'])    # 高质量
            else:  # low
                cmd.extend(['-q:v', '6'])    # 中等质量

            # 设置像素格式
            cmd.extend(['-pix_fmt', 'yuvj420p'])

            # 输出文件
            cmd.append(output_path)

            logger.info(f"执行图片高清处理...")
            logger.debug(f"完整命令: {' '.join(cmd)}")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"图片高清处理成功: {output_path}")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"图片高清处理失败: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"图片处理异常: {str(e)}")
            return False

    async def _try_real_esrgan_enhance(self, input_path: str, output_path: str, scale_factor: int) -> bool:
        """尝试使用Real-ESRGAN进行AI超分辨率处理"""
        try:
            # 检查是否安装了Real-ESRGAN
            realesrgan_paths = [
                'realesrgan-ncnn-vulkan',  # Linux/Mac
                'realesrgan-ncnn-vulkan.exe',  # Windows
                './realesrgan-ncnn-vulkan',  # 当前目录
                './realesrgan-ncnn-vulkan.exe',  # 当前目录Windows
                '/usr/local/bin/realesrgan-ncnn-vulkan',  # 系统安装
                'C:\\Program Files\\Real-ESRGAN\\realesrgan-ncnn-vulkan.exe'  # Windows安装
            ]

            realesrgan_cmd = None
            for path in realesrgan_paths:
                try:
                    # 检查命令是否存在
                    process = await asyncio.create_subprocess_exec(
                        path, '--help',
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    await process.communicate()
                    if process.returncode == 0:
                        realesrgan_cmd = path
                        break
                except:
                    continue

            if not realesrgan_cmd:
                logger.info("Real-ESRGAN未安装，使用FFmpeg增强算法")
                return False

            logger.info(f"使用Real-ESRGAN进行AI超分辨率处理: {realesrgan_cmd}")

            # 构建Real-ESRGAN命令
            cmd = [
                realesrgan_cmd,
                '-i', input_path,
                '-o', output_path,
                '-s', str(scale_factor),  # 缩放倍数
                '-f', 'jpg',  # 输出格式
                '-j', '1:1:1'  # 使用GPU加速
            ]

            # 根据缩放倍数选择模型
            if scale_factor == 2:
                cmd.extend(['-n', 'RealESRGAN_x2plus'])
            elif scale_factor == 4:
                cmd.extend(['-n', 'RealESRGAN_x4plus'])
            else:
                cmd.extend(['-n', 'RealESRGAN_x4plus'])  # 默认使用x4模型

            logger.debug(f"执行Real-ESRGAN命令: {' '.join(cmd)}")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"Real-ESRGAN处理成功: {output_path}")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.warning(f"Real-ESRGAN处理失败，降级到FFmpeg: {error_msg}")
                return False

        except Exception as e:
            logger.warning(f"Real-ESRGAN处理异常，降级到FFmpeg: {str(e)}")
            return False

    async def _try_waifu2x_enhance(self, input_path: str, output_path: str, scale_factor: int) -> bool:
        """尝试使用waifu2x进行AI超分辨率处理"""
        try:
            # 检查是否安装了waifu2x
            waifu2x_paths = [
                'waifu2x-ncnn-vulkan',
                'waifu2x-ncnn-vulkan.exe',
                './waifu2x-ncnn-vulkan',
                './waifu2x-ncnn-vulkan.exe'
            ]

            waifu2x_cmd = None
            for path in waifu2x_paths:
                try:
                    process = await asyncio.create_subprocess_exec(
                        path, '--help',
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    await process.communicate()
                    if process.returncode == 0:
                        waifu2x_cmd = path
                        break
                except:
                    continue

            if not waifu2x_cmd:
                logger.debug("waifu2x未安装")
                return False

            logger.info(f"使用waifu2x进行AI超分辨率处理: {waifu2x_cmd}")

            # 构建waifu2x命令
            cmd = [
                waifu2x_cmd,
                '-i', input_path,
                '-o', output_path,
                '-s', str(scale_factor),
                '-n', '2',  # 降噪级别
                '-m', 'models-cunet',  # 使用cunet模型
                '-g', '0'  # GPU ID
            ]

            logger.debug(f"执行waifu2x命令: {' '.join(cmd)}")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"waifu2x处理成功: {output_path}")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.warning(f"waifu2x处理失败: {error_msg}")
                return False

        except Exception as e:
            logger.warning(f"waifu2x处理异常: {str(e)}")
            return False
