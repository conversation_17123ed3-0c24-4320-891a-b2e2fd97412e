#!/usr/bin/env python3
"""
测试 Redis 任务状态处理
验证 Backend 是否正确接收和处理 Core 发布的任务状态
"""

import asyncio
import json
import redis.asyncio as redis
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_redis_task_status():
    """测试 Redis 任务状态发布和接收"""
    
    # Redis 连接配置
    redis_url = "redis://192.168.123.137:6379/1"  # 根据你的配置调整
    
    try:
        # 创建 Redis 客户端
        redis_client = redis.Redis.from_url(redis_url)
        
        # 测试连接
        await redis_client.ping()
        print("✅ Redis 连接成功")
        
        # 模拟 Core 发布任务状态
        test_task_id = f"test_task_{int(time.time())}"
        
        # 发布任务开始状态
        task_data = {
            "task_id": test_task_id,
            "status": "running",
            "progress": 0,
            "updated_at": time.time(),
            "message": "任务开始执行"
        }
        
        channel = f"task:{test_task_id}:status"
        message = json.dumps(task_data)
        
        print(f"📡 发布任务状态到 Redis: {channel}")
        print(f"📋 任务数据: {message}")
        
        # 发布消息
        result = await redis_client.publish(channel, message)
        print(f"✅ 消息发布成功，订阅者数量: {result}")
        
        # 等待一段时间
        await asyncio.sleep(2)
        
        # 发布任务进度更新
        task_data.update({
            "status": "running",
            "progress": 50,
            "message": "任务进行中"
        })
        
        message = json.dumps(task_data)
        result = await redis_client.publish(channel, message)
        print(f"📈 发布进度更新，订阅者数量: {result}")
        
        # 等待一段时间
        await asyncio.sleep(2)
        
        # 发布任务完成状态
        task_data.update({
            "status": "completed",
            "progress": 100,
            "message": "任务执行完成"
        })
        
        message = json.dumps(task_data)
        result = await redis_client.publish(channel, message)
        print(f"🎉 发布完成状态，订阅者数量: {result}")
        
        print(f"\n📊 测试总结:")
        print(f"  - 测试任务ID: {test_task_id}")
        print(f"  - 发布频道: {channel}")
        print(f"  - 发布了 3 条状态更新消息")
        
        if result > 0:
            print(f"✅ 有 {result} 个订阅者接收到消息")
            print("💡 请检查 Backend 日志确认任务状态是否被正确处理")
        else:
            print("⚠️  没有订阅者接收到消息")
            print("💡 请确认 Backend 服务是否正在运行并订阅了任务状态频道")
        
        await redis_client.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def monitor_redis_channels():
    """监控 Redis 频道订阅情况"""
    
    redis_url = "redis://192.168.123.137:6379/1"
    
    try:
        redis_client = redis.Redis.from_url(redis_url)
        await redis_client.ping()
        print("✅ Redis 连接成功")
        
        # 检查活跃的频道
        channels = await redis_client.pubsub_channels()
        print(f"📡 当前活跃的发布/订阅频道: {len(channels)}")
        for channel in channels:
            if isinstance(channel, bytes):
                channel = channel.decode('utf-8')
            print(f"  - {channel}")
        
        # 检查订阅者数量
        print(f"\n📊 频道订阅者统计:")
        for channel in channels:
            if isinstance(channel, bytes):
                channel = channel.decode('utf-8')
            
            subscribers = await redis_client.pubsub_numsub(channel)
            print(f"  - {channel}: {subscribers[0][1]} 个订阅者")
        
        await redis_client.close()
        
    except Exception as e:
        print(f"❌ 监控失败: {str(e)}")


async def main():
    """主函数"""
    print("🔍 Redis 任务状态处理测试工具")
    print("=" * 50)
    
    print("\n1. 监控 Redis 频道订阅情况...")
    await monitor_redis_channels()
    
    print("\n2. 测试任务状态发布...")
    await test_redis_task_status()
    
    print("\n✅ 测试完成")
    print("\n💡 使用说明:")
    print("  1. 确保 Backend 服务正在运行")
    print("  2. 检查 Backend 日志中是否有任务状态处理的日志")
    print("  3. 如果没有订阅者，检查 Redis 连接配置是否正确")


if __name__ == "__main__":
    asyncio.run(main())
