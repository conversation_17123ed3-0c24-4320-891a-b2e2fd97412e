"""
视频片头片尾处理服务
支持批量为视频添加指定的片头和片尾，包含特效过渡
"""

import os
import asyncio
import subprocess
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class VideoIntroOutroService:
    """视频片头片尾处理服务类"""

    def __init__(self):
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        self.supported_transition_effects = [
            'fade',      # 淡入淡出
            'dissolve',  # 溶解
            'wipe',      # 擦除
            'slide',     # 滑动
            'zoom',      # 缩放
            'none'       # 无特效
        ]

    def _is_video_file(self, file_path: str) -> bool:
        """检查是否为支持的视频文件"""
        if not os.path.isfile(file_path):
            return False
        
        extension = Path(file_path).suffix.lower().lstrip('.')
        return extension in self.supported_video_extensions

    async def _get_video_duration(self, video_path: str) -> float:
        """获取视频时长（秒）"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_entries',
                'format=duration', video_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                duration = float(data['format']['duration'])
                return duration
            else:
                logger.error(f"获取视频时长失败: {result.stderr}")
                return 0.0
                
        except Exception as e:
            logger.error(f"获取视频时长异常: {str(e)}")
            return 0.0

    def _build_transition_filter(self, effect: str, duration: float = 1.0) -> str:
        """构建转场特效滤镜"""
        if effect == 'fade':
            return f"fade=t=in:st=0:d={duration},fade=t=out:st={duration}:d={duration}"
        elif effect == 'dissolve':
            return f"fade=t=in:st=0:d={duration}:alpha=1,fade=t=out:st={duration}:d={duration}:alpha=1"
        elif effect == 'wipe':
            # 使用wipe效果（从左到右）
            return f"fade=t=in:st=0:d={duration}:color=black,fade=t=out:st={duration}:d={duration}:color=black"
        elif effect == 'slide':
            # 滑动效果（使用offset）
            return f"fade=t=in:st=0:d={duration},fade=t=out:st={duration}:d={duration}"
        elif effect == 'zoom':
            # 缩放效果
            return f"scale=iw*min(1\\,min(1920/iw\\,1080/ih)):ih*min(1\\,min(1920/iw\\,1080/ih)),pad=1920:1080:(1920-iw)/2:(1080-ih)/2"
        else:
            return ""  # 无特效

    async def scan_video_files(self, folder_path: str) -> List[Dict[str, Any]]:
        """扫描文件夹中的视频文件"""
        try:
            if not os.path.exists(folder_path):
                logger.error(f"文件夹不存在: {folder_path}")
                return []

            if not os.path.isdir(folder_path):
                logger.error(f"路径不是文件夹: {folder_path}")
                return []

            video_files = []
            
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    if self._is_video_file(file_path):
                        try:
                            # 获取文件信息
                            file_stat = os.stat(file_path)
                            duration = await self._get_video_duration(file_path)
                            
                            video_info = {
                                'path': file_path,
                                'name': file,
                                'size': file_stat.st_size,
                                'duration': duration,
                                'modified_time': file_stat.st_mtime
                            }
                            
                            video_files.append(video_info)
                            logger.debug(f"找到视频文件: {file_path} (时长: {duration:.1f}秒)")
                            
                        except Exception as e:
                            logger.warning(f"获取视频文件信息失败 {file_path}: {str(e)}")
                            continue

            logger.info(f"扫描完成，找到 {len(video_files)} 个视频文件")
            return video_files

        except Exception as e:
            logger.error(f"扫描视频文件失败: {str(e)}")
            return []

    async def _get_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """获取视频信息（分辨率、帧率等）"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams',
                '-select_streams', 'v:0', video_path
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"ffprobe 执行失败: {stderr.decode()}")
                return None

            import json
            probe_data = json.loads(stdout.decode())

            if not probe_data.get('streams'):
                logger.error(f"未找到视频流: {video_path}")
                return None

            stream = probe_data['streams'][0]

            # 提取视频信息
            width = stream.get('width', 1920)
            height = stream.get('height', 1080)

            # 解析帧率
            fps_str = stream.get('r_frame_rate', '30/1')
            if '/' in fps_str:
                num, den = fps_str.split('/')
                fps = float(num) / float(den) if float(den) != 0 else 30.0
            else:
                fps = float(fps_str) if fps_str else 30.0

            return {
                'width': width,
                'height': height,
                'fps': fps,
                'duration': float(stream.get('duration', 0)),
                'codec': stream.get('codec_name', 'unknown')
            }

        except Exception as e:
            logger.error(f"获取视频信息失败: {str(e)}")
            return None

    async def _process_single_video(self, video_path: str, intro_path: Optional[str],
                                  outro_path: Optional[str], output_path: str,
                                  transition_effect: str, transition_duration: float,
                                  output_quality: str) -> Dict[str, Any]:
        """处理单个视频文件"""
        start_time = time.time()
        
        try:
            logger.info(f"开始处理视频: {video_path}")
            
            # 验证输入文件
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"主视频文件不存在: {video_path}")
            
            if intro_path and not os.path.exists(intro_path):
                raise FileNotFoundError(f"片头文件不存在: {intro_path}")
                
            if outro_path and not os.path.exists(outro_path):
                raise FileNotFoundError(f"片尾文件不存在: {outro_path}")

            # 获取主视频的分辨率信息
            main_video_info = await self._get_video_info(video_path)
            if not main_video_info:
                raise Exception(f"无法获取主视频信息: {video_path}")

            main_width = main_video_info.get('width', 1920)
            main_height = main_video_info.get('height', 1080)
            main_fps = main_video_info.get('fps', 30)

            logger.info(f"主视频分辨率: {main_width}x{main_height}@{main_fps}fps")

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y']

            # 添加输入文件
            inputs = []
            if intro_path:
                cmd.extend(['-i', intro_path])
                inputs.append('intro')

            cmd.extend(['-i', video_path])
            inputs.append('main')

            if outro_path:
                cmd.extend(['-i', outro_path])
                inputs.append('outro')

            # 构建复杂滤镜 - 包含分辨率调整
            filter_complex = []

            # 为片头和片尾调整分辨率以匹配主视频
            if intro_path and outro_path:
                # 片头 + 主视频 + 片尾
                filter_complex.append(
                    f"[0:v]scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,"
                    f"pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,fps={main_fps},setsar=1[intro_scaled];"
                    f"[1:v]scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,"
                    f"pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,fps={main_fps},setsar=1[main_scaled];"
                    f"[2:v]scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,"
                    f"pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,fps={main_fps},setsar=1[outro_scaled];"
                    f"[intro_scaled][0:a][main_scaled][1:a][outro_scaled][2:a]concat=n=3:v=1:a=1[outv][outa]"
                )
            elif intro_path:
                # 仅片头 + 主视频
                filter_complex.append(
                    f"[0:v]scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,"
                    f"pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,fps={main_fps},setsar=1[intro_scaled];"
                    f"[1:v]scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,"
                    f"pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,fps={main_fps},setsar=1[main_scaled];"
                    f"[intro_scaled][0:a][main_scaled][1:a]concat=n=2:v=1:a=1[outv][outa]"
                )
            elif outro_path:
                # 仅主视频 + 片尾
                filter_complex.append(
                    f"[0:v]scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,"
                    f"pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,fps={main_fps},setsar=1[main_scaled];"
                    f"[1:v]scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,"
                    f"pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,fps={main_fps},setsar=1[outro_scaled];"
                    f"[main_scaled][0:a][outro_scaled][1:a]concat=n=2:v=1:a=1[outv][outa]"
                )
            else:
                # 仅主视频（标准化 SAR）
                filter_complex.append("[0:v]setsar=1[outv]; [0:a]copy[outa]")

            # 添加滤镜参数
            if filter_complex:
                cmd.extend(['-filter_complex', '; '.join(filter_complex)])
                cmd.extend(['-map', '[outv]', '-map', '[outa]'])

            # 设置编码参数
            cmd.extend(['-c:v', 'libx264', '-preset', 'fast'])
            
            # 根据质量设置CRF
            if output_quality == 'high':
                cmd.extend(['-crf', '18'])
            elif output_quality == 'medium':
                cmd.extend(['-crf', '23'])
            else:  # low
                cmd.extend(['-crf', '28'])
            
            # 音频编码
            cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
            
            # 输出文件
            cmd.append(output_path)

            logger.info(f"输入文件: {video_path}")
            logger.info(f"输出文件: {output_path}")
            logger.info(f"执行ffmpeg命令: {' '.join(cmd)}")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                processing_time = time.time() - start_time
                output_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
                
                logger.info(f"视频处理成功: {video_path} -> {output_path} (耗时: {processing_time:.1f}秒)")
                
                return {
                    'success': True,
                    'input_path': video_path,
                    'output_path': output_path,
                    'processing_time': processing_time,
                    'output_size': output_size,
                    'error_message': None
                }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else '未知错误'
                logger.error(f"视频处理失败: {video_path}, 错误: {error_msg}")
                
                return {
                    'success': False,
                    'input_path': video_path,
                    'output_path': output_path,
                    'processing_time': time.time() - start_time,
                    'output_size': 0,
                    'error_message': error_msg
                }
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"处理视频异常: {video_path}, 错误: {error_msg}")
            
            return {
                'success': False,
                'input_path': video_path,
                'output_path': output_path,
                'processing_time': time.time() - start_time,
                'output_size': 0,
                'error_message': error_msg
            }

    async def batch_add_intro_outro(self, folder_path: str, selected_files: Optional[List[str]] = None,
                                   intro_path: Optional[str] = None, outro_path: Optional[str] = None,
                                   output_folder: Optional[str] = None, transition_effect: str = 'fade',
                                   transition_duration: float = 1.0, output_quality: str = 'medium',
                                   max_concurrent: int = 3, overwrite_original: bool = False,
                                   output_suffix: str = '_processed') -> Dict[str, Any]:
        """批量为视频添加片头片尾"""
        try:
            logger.info(f"开始批量添加片头片尾: {folder_path}")

            # 验证输入参数
            if not intro_path and not outro_path:
                return {"success": False, "error": "必须指定片头或片尾文件", "processed_videos": 0}

            if not os.path.exists(folder_path):
                return {"success": False, "error": "输入文件夹不存在", "processed_videos": 0}

            if intro_path and not os.path.exists(intro_path):
                return {"success": False, "error": f"片头文件不存在: {intro_path}", "processed_videos": 0}

            if outro_path and not os.path.exists(outro_path):
                return {"success": False, "error": f"片尾文件不存在: {outro_path}", "processed_videos": 0}

            # 验证转场效果
            if transition_effect not in self.supported_transition_effects:
                logger.warning(f"不支持的转场效果: {transition_effect}，使用默认效果 'fade'")
                transition_effect = 'fade'

            # 获取要处理的视频文件
            if selected_files:
                # 使用选中的文件列表
                video_files = []
                for file_name in selected_files:
                    file_path = os.path.join(folder_path, file_name)
                    if os.path.exists(file_path) and os.path.isfile(file_path):
                        # 检查是否为视频文件
                        if any(file_name.lower().endswith(ext) for ext in self.supported_video_extensions):
                            # 构建视频文件信息字典，保持与 scan_video_files 一致的格式
                            video_info = {
                                'path': file_path,
                                'name': file_name,
                                'size': os.path.getsize(file_path)
                            }
                            video_files.append(video_info)
                        else:
                            logger.warning(f"跳过非视频文件: {file_name}")
                    else:
                        logger.warning(f"选中的文件不存在或不是文件: {file_name}")

                if not video_files:
                    return {"success": False, "error": "选中的文件中没有有效的视频文件", "processed_videos": 0}

                logger.info(f"处理选中的 {len(video_files)} 个视频文件")
            else:
                # 扫描整个文件夹
                video_files = await self.scan_video_files(folder_path)
                if not video_files:
                    return {"success": False, "error": "未找到有效的视频文件", "processed_videos": 0}

                logger.info(f"找到 {len(video_files)} 个视频文件，开始批量处理")

            # 确定输出文件夹
            if not output_folder:
                output_folder = os.path.join(folder_path, "processed")

            if not os.path.exists(output_folder):
                os.makedirs(output_folder, exist_ok=True)
                logger.info(f"创建输出文件夹: {output_folder}")

            # 创建处理任务
            tasks = []
            results = []

            # 使用信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_with_semaphore(video_info):
                async with semaphore:
                    video_path = video_info['path']
                    video_name = Path(video_path).stem
                    video_ext = Path(video_path).suffix

                    if overwrite_original:
                        output_path = video_path
                        # 创建备份
                        backup_path = video_path + '.backup'
                        if not os.path.exists(backup_path):
                            import shutil
                            shutil.copy2(video_path, backup_path)
                            logger.info(f"创建备份文件: {backup_path}")
                    else:
                        output_path = os.path.join(output_folder, f"{video_name}{output_suffix}{video_ext}")

                    logger.info(f"视频处理路径: {video_path} -> {output_path}")

                    return await self._process_single_video(
                        video_path, intro_path, outro_path, output_path,
                        transition_effect, transition_duration, output_quality
                    )

            # 创建所有任务
            for video_info in video_files:
                task = process_with_semaphore(video_info)
                tasks.append(task)

            # 执行所有任务
            logger.info(f"开始并发处理 {len(tasks)} 个视频，最大并发数: {max_concurrent}")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            successful_count = 0
            failed_count = 0
            total_processing_time = 0
            total_output_size = 0
            errors = []

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_count += 1
                    error_msg = f"处理视频异常: {video_files[i]['path']}, 错误: {str(result)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                elif result['success']:
                    successful_count += 1
                    total_processing_time += result['processing_time']
                    total_output_size += result['output_size']
                else:
                    failed_count += 1
                    errors.append(f"处理失败: {result['input_path']}, 错误: {result['error_message']}")

            logger.info(f"批量处理完成: 成功 {successful_count} 个，失败 {failed_count} 个")

            return {
                "success": successful_count > 0,
                "processed_videos": successful_count,
                "failed_videos": failed_count,
                "total_videos": len(video_files),
                "total_processing_time": total_processing_time,
                "total_output_size": total_output_size,
                "output_folder": output_folder,
                "errors": errors,
                "results": [r for r in results if not isinstance(r, Exception)]
            }

        except Exception as e:
            error_msg = f"批量处理异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg, "processed_videos": 0}

    def get_supported_effects(self) -> List[str]:
        """获取支持的转场效果列表"""
        return self.supported_transition_effects.copy()

    def get_supported_extensions(self) -> List[str]:
        """获取支持的视频格式列表"""
        return self.supported_video_extensions.copy()
