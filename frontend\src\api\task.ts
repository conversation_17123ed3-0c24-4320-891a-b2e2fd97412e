import request from '@/utils/request'

// 工作流步骤状态
export interface WorkflowStep {
  id: string
  name: string
  description?: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped' | 'paused'
  progress: number
  start_time?: string
  end_time?: string
  duration?: number
  error_message?: string
  retry_count?: number
  max_retries?: number
  required: boolean
  condition?: string
  action?: string
  element?: string
  params?: Record<string, any>
  logs?: string[]
}

// 工作流执行上下文
export interface WorkflowContext {
  workflow_id: string
  workflow_name: string
  workflow_version?: string
  current_step_index: number
  total_steps: number
  steps: WorkflowStep[]
  context_data?: Record<string, any>
  checkpoint_data?: Record<string, any>
  can_resume: boolean
  resume_from_step?: number
}

// 任务相关类型定义
export interface Task {
  id: string
  platform_id: string
  platform_name: string
  account_id: string
  account_name: string
  device_id: string
  content_path: string
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'canceled'
  progress: number
  task_type: 'main' | 'subtask' | 'single'
  workflow_name?: string
  content_type?: string
  video_file?: string
  created_at: string
  updated_at: string
  start_time?: string
  end_time?: string
  estimated_end_time?: string
  workflow_id?: string
  params?: Record<string, any>
  // 主任务特有字段
  total_subtasks?: number
  completed_subtasks?: number
  // 子任务特有字段
  parent_task_id?: string
  subtask_index?: number
  // 树形结构
  children?: Task[]
  // 工作流相关字段
  workflow_context?: WorkflowContext
}

export interface TaskListResponse {
  tasks: Task[]
  total: number
  pagination?: {
    limit: number
    offset: number
    has_more: boolean
  }
}

export interface TaskDetailResponse {
  task: Task
}

export interface TaskStatusResponse {
  status: string
  progress: number
  message?: string
}

export interface TaskLogEntry {
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'success'
  message: string
}

export interface TaskLogsResponse {
  logs: TaskLogEntry[]
}

export interface TaskStatsResponse {
  running: number
  pending: number
  paused: number
  completed: number
  failed: number
  canceled: number
  total: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 任务相关接口

// 获取任务列表
export const getTaskList = (params?: {
  status?: string
  platform_id?: string
  limit?: number
  offset?: number
}): Promise<ApiResponse<TaskListResponse>> => {
  return request({
    url: '/api/tasks/',
    method: 'get',
    params
  })
}

// 获取任务详情
export const getTaskDetail = (taskId: string): Promise<ApiResponse<TaskDetailResponse>> => {
  return request({
    url: `/api/task/detail/${taskId}`,
    method: 'get'
  })
}

// 获取任务状态
export const getTaskStatus = (taskId: string): Promise<ApiResponse<TaskStatusResponse>> => {
  return request({
    url: `/api/tasks/${taskId}/status`,
    method: 'get'
  })
}

// 获取任务日志
export const getTaskLogs = (taskId: string): Promise<ApiResponse<TaskLogsResponse>> => {
  return request({
    url: `/api/tasks/detail/${taskId}/logs`,
    method: 'get'
  })
}

// 获取运行中的任务
export const getRunningTasks = (): Promise<ApiResponse<TaskListResponse>> => {
  return request({
    url: '/api/tasks/running',
    method: 'get'
  })
}

// 启动任务
export const startTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/start`,
    method: 'post'
  })
}

// 暂停任务
export const pauseTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/pause`,
    method: 'post'
  })
}

// 取消任务
export const cancelTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/cancel`,
    method: 'post'
  })
}

// 获取任务结果
export const getTaskResult = (taskId: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/api/tasks/${taskId}/result`,
    method: 'get'
  })
}

// 删除任务
export const deleteTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}`,
    method: 'delete'
  })
}

// 批量操作任务
export const batchOperateTasks = (taskIds: string[], operation: string): Promise<ApiResponse<{ message: string; affected_count: number }>> => {
  return request({
    url: '/api/tasks/batch',
    method: 'post',
    data: {
      task_ids: taskIds,
      operation
    }
  })
}

// 获取任务统计
export const getTaskStats = (): Promise<ApiResponse<TaskStatsResponse>> => {
  return request({
    url: '/api/tasks/stats',
    method: 'get'
  })
}

// 获取任务历史
export const getTaskHistory = (params?: {
  start_date?: string
  end_date?: string
  status?: string
  platform_id?: string
  limit?: number
  offset?: number
}): Promise<ApiResponse<TaskListResponse>> => {
  return request({
    url: '/api/tasks/history',
    method: 'get',
    params
  })
}

// 清理任务历史
export const cleanTaskHistory = (params: {
  before_date?: string
  status?: string
  keep_count?: number
  clean_invalid?: boolean  // 新增：清理无效任务
}): Promise<ApiResponse<{ deleted_count: number; message: string }>> => {
  return request({
    url: '/api/tasks/history/clean',
    method: 'post',
    data: params
  })
}

// 导出任务数据
export const exportTasks = (params?: {
  start_date?: string
  end_date?: string
  status?: string
  format?: 'csv' | 'excel'
}): Promise<Blob> => {
  return request({
    url: '/api/tasks/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 工作流相关接口 ====================

// 获取任务工作流详情
export const getTaskWorkflow = (taskId: string): Promise<ApiResponse<WorkflowContext>> => {
  return request({
    url: `/api/tasks/${taskId}/workflow`,
    method: 'get'
  })
}

// 获取任务工作流日志
export const getTaskWorkflowLogs = (taskId: string, stepId?: string): Promise<ApiResponse<{ logs: any[], total: number }>> => {
  return request({
    url: `/api/tasks/${taskId}/workflow/logs`,
    method: 'get',
    params: stepId ? { step_id: stepId } : {}
  })
}

// 控制工作流执行
export const controlWorkflow = (taskId: string, action: 'pause' | 'resume' | 'retry' | 'skip', stepId?: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/workflow/control`,
    method: 'post',
    data: {
      action,
      step_id: stepId
    }
  })
}

// 从指定步骤恢复工作流
export const resumeWorkflowFromStep = (taskId: string, stepIndex: number): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/workflow/resume`,
    method: 'post',
    data: {
      step_index: stepIndex
    }
  })
}

// 获取工作流步骤日志
export const getWorkflowStepLogs = (taskId: string, stepId: string): Promise<ApiResponse<TaskLogsResponse>> => {
  return request({
    url: `/api/tasks/${taskId}/workflow/steps/${stepId}/logs`,
    method: 'get'
  })
}

// 重试工作流步骤
export const retryWorkflowStep = (taskId: string, stepId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/workflow/steps/${stepId}/retry`,
    method: 'post'
  })
}

// 跳过工作流步骤
export const skipWorkflowStep = (taskId: string, stepId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/workflow/steps/${stepId}/skip`,
    method: 'post'
  })
}
