"""
并发调试监控器
用于监控和调试任务并发执行问题
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import threading
import psutil
import os

logger = logging.getLogger(__name__)

@dataclass
class TaskExecutionInfo:
    """任务执行信息"""
    task_id: str
    device_id: str
    start_time: float
    end_time: Optional[float] = None
    status: str = "running"  # running, completed, failed, timeout
    error_message: Optional[str] = None
    appium_session_id: Optional[str] = None
    system_port: Optional[int] = None
    mjpeg_port: Optional[int] = None
    parent_task_id: Optional[str] = None
    
    def to_dict(self):
        return asdict(self)

@dataclass
class DeviceResourceInfo:
    """设备资源信息"""
    device_id: str
    appium_sessions: List[str]
    system_ports: List[int]
    mjpeg_ports: List[int]
    current_tasks: List[str]
    last_activity_time: float
    is_healthy: bool = True
    error_count: int = 0

@dataclass
class ConcurrencyStats:
    """并发统计信息"""
    total_tasks: int = 0
    running_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    timeout_tasks: int = 0
    max_concurrent_tasks: int = 0
    avg_task_duration: float = 0.0
    success_rate: float = 0.0

class ConcurrencyMonitor:
    """并发监控器"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskExecutionInfo] = {}
        self.devices: Dict[str, DeviceResourceInfo] = {}
        self.stats = ConcurrencyStats()
        self.monitoring = False
        self.monitor_task = None
        self.lock = threading.Lock()
        
        # 监控配置
        self.monitor_interval = 5  # 5秒监控间隔
        self.task_timeout = 1800   # 30分钟任务超时
        self.max_error_count = 5   # 最大错误次数
        
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_task = asyncio.create_task(self._monitor_loop())
            logger.info("🔍 并发监控器已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
        logger.info("⏹️ 并发监控器已停止")
    
    def register_task_start(self, task_id: str, device_id: str, parent_task_id: str = None, 
                           appium_session_id: str = None, system_port: int = None, mjpeg_port: int = None):
        """注册任务开始"""
        with self.lock:
            task_info = TaskExecutionInfo(
                task_id=task_id,
                device_id=device_id,
                start_time=time.time(),
                parent_task_id=parent_task_id,
                appium_session_id=appium_session_id,
                system_port=system_port,
                mjpeg_port=mjpeg_port
            )
            self.tasks[task_id] = task_info
            
            # 更新设备信息
            if device_id not in self.devices:
                self.devices[device_id] = DeviceResourceInfo(
                    device_id=device_id,
                    appium_sessions=[],
                    system_ports=[],
                    mjpeg_ports=[],
                    current_tasks=[],
                    last_activity_time=time.time()
                )
            
            device_info = self.devices[device_id]
            device_info.current_tasks.append(task_id)
            device_info.last_activity_time = time.time()
            
            if appium_session_id:
                device_info.appium_sessions.append(appium_session_id)
            if system_port:
                device_info.system_ports.append(system_port)
            if mjpeg_port:
                device_info.mjpeg_ports.append(mjpeg_port)
            
            # 更新统计
            self.stats.total_tasks += 1
            self.stats.running_tasks += 1
            self.stats.max_concurrent_tasks = max(self.stats.max_concurrent_tasks, self.stats.running_tasks)
            
            logger.info(f"📝 任务开始监控: {task_id} (设备: {device_id}, 并发数: {self.stats.running_tasks})")
    
    def register_task_end(self, task_id: str, status: str = "completed", error_message: str = None):
        """注册任务结束"""
        with self.lock:
            if task_id not in self.tasks:
                logger.warning(f"任务{task_id}未在监控中")
                return
            
            task_info = self.tasks[task_id]
            task_info.end_time = time.time()
            task_info.status = status
            task_info.error_message = error_message
            
            # 更新设备信息
            device_info = self.devices.get(task_info.device_id)
            if device_info:
                if task_id in device_info.current_tasks:
                    device_info.current_tasks.remove(task_id)
                device_info.last_activity_time = time.time()
                
                if status == "failed":
                    device_info.error_count += 1
                    if device_info.error_count >= self.max_error_count:
                        device_info.is_healthy = False
                        logger.warning(f"⚠️ 设备{task_info.device_id}错误次数过多，标记为不健康")
            
            # 更新统计
            self.stats.running_tasks -= 1
            if status == "completed":
                self.stats.completed_tasks += 1
            elif status == "failed":
                self.stats.failed_tasks += 1
            elif status == "timeout":
                self.stats.timeout_tasks += 1
            
            # 计算成功率
            total_finished = self.stats.completed_tasks + self.stats.failed_tasks + self.stats.timeout_tasks
            if total_finished > 0:
                self.stats.success_rate = self.stats.completed_tasks / total_finished * 100
            
            duration = task_info.end_time - task_info.start_time
            logger.info(f"✅ 任务结束监控: {task_id} (状态: {status}, 耗时: {duration:.2f}s, 成功率: {self.stats.success_rate:.1f}%)")
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                await self._check_task_timeouts()
                await self._check_device_health()
                await self._log_system_resources()
                await self._detect_concurrency_issues()
                
                await asyncio.sleep(self.monitor_interval)
            except Exception as e:
                logger.error(f"监控循环异常: {str(e)}", exc_info=True)
                await asyncio.sleep(self.monitor_interval)
    
    async def _check_task_timeouts(self):
        """检查任务超时"""
        current_time = time.time()
        timeout_tasks = []
        
        with self.lock:
            for task_id, task_info in self.tasks.items():
                if (task_info.status == "running" and 
                    current_time - task_info.start_time > self.task_timeout):
                    timeout_tasks.append(task_id)
        
        for task_id in timeout_tasks:
            logger.warning(f"⏰ 任务超时: {task_id}")
            self.register_task_end(task_id, "timeout", "任务执行超时")
    
    async def _check_device_health(self):
        """检查设备健康状态"""
        current_time = time.time()
        
        with self.lock:
            for device_id, device_info in self.devices.items():
                # 检查设备是否长时间无活动
                if current_time - device_info.last_activity_time > 3600:  # 1小时
                    logger.warning(f"🔋 设备{device_id}长时间无活动")
                
                # 检查端口冲突
                if len(device_info.system_ports) != len(set(device_info.system_ports)):
                    logger.error(f"🚨 设备{device_id}存在systemPort冲突: {device_info.system_ports}")
                
                if len(device_info.mjpeg_ports) != len(set(device_info.mjpeg_ports)):
                    logger.error(f"🚨 设备{device_id}存在mjpegPort冲突: {device_info.mjpeg_ports}")
    
    async def _log_system_resources(self):
        """记录系统资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 进程信息
            appium_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                try:
                    if 'node' in proc.info['name'].lower():
                        cmdline = proc.cmdline()
                        if any('appium' in arg.lower() for arg in cmdline):
                            memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                            appium_processes.append({
                                'pid': proc.info['pid'],
                                'memory_mb': memory_mb,
                                'cpu_percent': proc.info['cpu_percent']
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            logger.info(f"📊 系统资源 - CPU: {cpu_percent}%, 内存: {memory_percent}%, "
                       f"并发任务: {self.stats.running_tasks}, Appium进程: {len(appium_processes)}")
            
            # 如果资源使用过高，发出警告
            if cpu_percent > 80:
                logger.warning(f"⚠️ CPU使用率过高: {cpu_percent}%")
            if memory_percent > 80:
                logger.warning(f"⚠️ 内存使用率过高: {memory_percent}%")
            
            for proc in appium_processes:
                if proc['memory_mb'] > 500:  # 500MB
                    logger.warning(f"⚠️ Appium进程内存使用过高: PID {proc['pid']}, {proc['memory_mb']:.1f}MB")
                    
        except Exception as e:
            logger.error(f"获取系统资源信息失败: {str(e)}")
    
    async def _detect_concurrency_issues(self):
        """检测并发问题"""
        with self.lock:
            # 检查是否有设备被多个任务同时使用
            device_task_count = {}
            for task_id, task_info in self.tasks.items():
                if task_info.status == "running":
                    device_id = task_info.device_id
                    if device_id not in device_task_count:
                        device_task_count[device_id] = []
                    device_task_count[device_id].append(task_id)
            
            for device_id, task_list in device_task_count.items():
                if len(task_list) > 1:
                    # 检查是否是同一主任务的子任务（这是允许的串行执行）
                    parent_tasks = set()
                    for task_id in task_list:
                        parent_id = self.tasks[task_id].parent_task_id
                        if parent_id:
                            parent_tasks.add(parent_id)
                    
                    if len(parent_tasks) > 1:
                        logger.error(f"🚨 检测到并发冲突: 设备{device_id}被多个主任务同时使用: {task_list}")
                        logger.error(f"   涉及主任务: {list(parent_tasks)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            return {
                "stats": asdict(self.stats),
                "devices": {k: asdict(v) for k, v in self.devices.items()},
                "running_tasks": [asdict(task) for task in self.tasks.values() if task.status == "running"]
            }
    
    def get_detailed_report(self) -> str:
        """获取详细报告"""
        with self.lock:
            report = []
            report.append("=" * 60)
            report.append("📊 并发执行详细报告")
            report.append("=" * 60)
            
            # 统计信息
            report.append(f"总任务数: {self.stats.total_tasks}")
            report.append(f"运行中: {self.stats.running_tasks}")
            report.append(f"已完成: {self.stats.completed_tasks}")
            report.append(f"失败: {self.stats.failed_tasks}")
            report.append(f"超时: {self.stats.timeout_tasks}")
            report.append(f"最大并发数: {self.stats.max_concurrent_tasks}")
            report.append(f"成功率: {self.stats.success_rate:.1f}%")
            report.append("")
            
            # 设备信息
            report.append("🔧 设备状态:")
            for device_id, device_info in self.devices.items():
                status = "健康" if device_info.is_healthy else "异常"
                report.append(f"  设备 {device_id}: {status} (错误次数: {device_info.error_count})")
                report.append(f"    当前任务: {len(device_info.current_tasks)}")
                report.append(f"    使用端口: system={device_info.system_ports}, mjpeg={device_info.mjpeg_ports}")
                report.append("")
            
            # 运行中的任务
            running_tasks = [task for task in self.tasks.values() if task.status == "running"]
            if running_tasks:
                report.append("🏃 运行中的任务:")
                for task in running_tasks:
                    duration = time.time() - task.start_time
                    report.append(f"  {task.task_id}: 设备{task.device_id}, 运行{duration:.1f}s")
                    if task.system_port:
                        report.append(f"    端口: {task.system_port}/{task.mjpeg_port}")
            
            return "\n".join(report)

# 全局监控器实例
_monitor = None

def get_concurrency_monitor() -> ConcurrencyMonitor:
    """获取全局并发监控器实例"""
    global _monitor
    if _monitor is None:
        _monitor = ConcurrencyMonitor()
        _monitor.start_monitoring()
    return _monitor
