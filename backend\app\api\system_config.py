"""
系统配置相关API
"""
from fastapi import APIRouter, HTTPException, Depends, Query, Request
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import uuid

from app.core.security import get_current_user
from app.models.user import User

router = APIRouter(prefix="/api/v1/system-config", tags=["system-config"])

# 数据库服务获取函数
def get_db(request: Request):
    """获取数据库服务实例"""
    return request.app.state.mongo_db

# AI提供商相关模型
class AIProviderBase(BaseModel):
    name: str = Field(..., description="提供商名称")
    type: str = Field(..., description="提供商类型", pattern="^(openai|azure|claude|custom)$")
    endpoint: str = Field(..., description="API端点")
    api_key: str = Field(..., description="API密钥")
    model: str = Field(..., description="模型名称")
    max_tokens: Optional[int] = Field(4096, description="最大令牌数")
    temperature: Optional[float] = Field(0.7, description="温度参数")
    enabled: bool = Field(True, description="是否启用")

class AIProviderCreate(AIProviderBase):
    pass

class AIProviderUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    endpoint: Optional[str] = None
    api_key: Optional[str] = None
    model: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    enabled: Optional[bool] = None

class AIProvider(AIProviderBase):
    id: str
    created_at: datetime
    updated_at: datetime

# AI角色模板相关模型
class AIRoleTemplateBase(BaseModel):
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    system_prompt: str = Field(..., description="系统提示词")
    user_prompt_template: str = Field(..., description="用户提示词模板")
    category: str = Field(..., description="分类")
    tags: List[str] = Field(default_factory=list, description="标签")
    enabled: bool = Field(True, description="是否启用")

class AIRoleTemplateCreate(AIRoleTemplateBase):
    pass

class AIRoleTemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    system_prompt: Optional[str] = None
    user_prompt_template: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    enabled: Optional[bool] = None

class AIRoleTemplate(AIRoleTemplateBase):
    id: str
    created_at: datetime
    updated_at: datetime

# 通用响应模型
class ListResponse(BaseModel):
    items: List[dict]
    total: int
    page: int
    page_size: int

class TestResponse(BaseModel):
    success: bool
    message: str

class TestTemplateResponse(BaseModel):
    response: str

# AI提供商相关接口

@router.get("/ai-providers", response_model=ListResponse)
async def get_ai_providers(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    enabled: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """获取AI提供商列表"""
    try:
        # 构建查询条件
        query = {}
        if enabled is not None:
            query["enabled"] = enabled

        # 分页查询
        skip = (page - 1) * page_size
        cursor = db.ai_providers.find(query).skip(skip).limit(page_size)
        providers = await cursor.to_list(length=page_size)

        # 获取总数
        total = await db.ai_providers.count_documents(query)

        # 转换数据格式
        items = []
        for provider in providers:
            provider["id"] = str(provider["_id"])
            del provider["_id"]
            items.append(provider)

        return ListResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI提供商列表失败: {str(e)}")

@router.get("/ai-providers/{provider_id}", response_model=AIProvider)
async def get_ai_provider(
    provider_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """获取AI提供商详情"""
    try:
        from bson import ObjectId
        provider = await db.ai_providers.find_one({"_id": ObjectId(provider_id)})
        if not provider:
            raise HTTPException(status_code=404, detail="AI提供商不存在")

        provider["id"] = str(provider["_id"])
        del provider["_id"]
        return AIProvider(**provider)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI提供商详情失败: {str(e)}")

@router.post("/ai-providers", response_model=AIProvider)
async def create_ai_provider(
    provider_data: AIProviderCreate,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """创建AI提供商"""
    try:
        now = datetime.utcnow()
        provider_dict = provider_data.dict()
        provider_dict.update({
            "created_at": now,
            "updated_at": now
        })

        result = await db.ai_providers.insert_one(provider_dict)
        provider_dict["id"] = str(result.inserted_id)

        return AIProvider(**provider_dict)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建AI提供商失败: {str(e)}")

@router.put("/ai-providers/{provider_id}", response_model=AIProvider)
async def update_ai_provider(
    provider_id: str,
    provider_data: AIProviderUpdate,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """更新AI提供商"""
    try:
        from bson import ObjectId
        
        # 检查提供商是否存在
        existing = await db.ai_providers.find_one({"_id": ObjectId(provider_id)})
        if not existing:
            raise HTTPException(status_code=404, detail="AI提供商不存在")
        
        # 更新数据
        update_data = {k: v for k, v in provider_data.dict().items() if v is not None}
        update_data["updated_at"] = datetime.utcnow()
        
        await db.ai_providers.update_one(
            {"_id": ObjectId(provider_id)},
            {"$set": update_data}
        )

        # 返回更新后的数据
        updated = await db.ai_providers.find_one({"_id": ObjectId(provider_id)})
        updated["id"] = str(updated["_id"])
        del updated["_id"]
        
        return AIProvider(**updated)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新AI提供商失败: {str(e)}")

@router.delete("/ai-providers/{provider_id}")
async def delete_ai_provider(
    provider_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """删除AI提供商"""
    try:
        from bson import ObjectId

        result = await db.ai_providers.delete_one({"_id": ObjectId(provider_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="AI提供商不存在")

        return {"message": "删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除AI提供商失败: {str(e)}")

@router.post("/ai-providers/{provider_id}/test", response_model=TestResponse)
async def test_ai_provider(
    provider_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """测试AI提供商连接"""
    try:
        from bson import ObjectId

        # 获取提供商信息
        provider = await db.ai_providers.find_one({"_id": ObjectId(provider_id)})
        if not provider:
            raise HTTPException(status_code=404, detail="AI提供商不存在")

        # TODO: 实现实际的连接测试逻辑
        # 这里可以根据不同的提供商类型实现不同的测试逻辑

        return TestResponse(success=True, message="连接测试成功")
    except Exception as e:
        return TestResponse(success=False, message=f"连接测试失败: {str(e)}")

# AI角色模板相关接口

@router.get("/ai-role-templates", response_model=ListResponse)
async def get_ai_role_templates(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    enabled: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """获取AI角色模板列表"""
    try:
        # 构建查询条件
        query = {}
        if category:
            query["category"] = category
        if enabled is not None:
            query["enabled"] = enabled

        # 分页查询
        skip = (page - 1) * page_size
        cursor = db.ai_role_templates.find(query).skip(skip).limit(page_size)
        templates = await cursor.to_list(length=page_size)

        # 获取总数
        total = await db.ai_role_templates.count_documents(query)
        
        # 转换数据格式
        items = []
        for template in templates:
            template["id"] = str(template["_id"])
            del template["_id"]
            items.append(template)
        
        return ListResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI角色模板列表失败: {str(e)}")

@router.get("/ai-role-templates/categories", response_model=List[str])
async def get_template_categories(
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """获取模板分类列表"""
    try:
        categories = await db.ai_role_templates.distinct("category")
        return categories
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")

@router.get("/ai-role-templates/{template_id}", response_model=AIRoleTemplate)
async def get_ai_role_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """获取AI角色模板详情"""
    try:
        from bson import ObjectId
        template = await db.ai_role_templates.find_one({"_id": ObjectId(template_id)})
        if not template:
            raise HTTPException(status_code=404, detail="AI角色模板不存在")

        template["id"] = str(template["_id"])
        del template["_id"]
        return AIRoleTemplate(**template)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI角色模板详情失败: {str(e)}")

@router.post("/ai-role-templates", response_model=AIRoleTemplate)
async def create_ai_role_template(
    template_data: AIRoleTemplateCreate,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db_service)
):
    """创建AI角色模板"""
    try:
        now = datetime.utcnow()
        template_dict = template_data.dict()
        template_dict.update({
            "created_at": now,
            "updated_at": now
        })

        result = await db.ai_role_templates.insert_one(template_dict)
        template_dict["id"] = str(result.inserted_id)

        return AIRoleTemplate(**template_dict)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建AI角色模板失败: {str(e)}")

@router.put("/ai-role-templates/{template_id}", response_model=AIRoleTemplate)
async def update_ai_role_template(
    template_id: str,
    template_data: AIRoleTemplateUpdate,
    current_user: User = Depends(get_current_user),
    db_service = Depends(get_db_service)
):
    """更新AI角色模板"""
    try:
        from bson import ObjectId
        
        # 检查模板是否存在
        existing = await db_service.db.ai_role_templates.find_one({"_id": ObjectId(template_id)})
        if not existing:
            raise HTTPException(status_code=404, detail="AI角色模板不存在")
        
        # 更新数据
        update_data = {k: v for k, v in template_data.dict().items() if v is not None}
        update_data["updated_at"] = datetime.utcnow()
        
        await db_service.db.ai_role_templates.update_one(
            {"_id": ObjectId(template_id)},
            {"$set": update_data}
        )
        
        # 返回更新后的数据
        updated = await db_service.db.ai_role_templates.find_one({"_id": ObjectId(template_id)})
        updated["id"] = str(updated["_id"])
        del updated["_id"]
        
        return AIRoleTemplate(**updated)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新AI角色模板失败: {str(e)}")

@router.delete("/ai-role-templates/{template_id}")
async def delete_ai_role_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db_service = Depends(get_db_service)
):
    """删除AI角色模板"""
    try:
        from bson import ObjectId
        
        result = await db_service.db.ai_role_templates.delete_one({"_id": ObjectId(template_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="AI角色模板不存在")
        
        return {"message": "删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除AI角色模板失败: {str(e)}")

@router.post("/ai-role-templates/{template_id}/test", response_model=TestTemplateResponse)
async def test_ai_role_template(
    template_id: str,
    test_input: str,
    current_user: User = Depends(get_current_user),
    db_service = Depends(get_db_service)
):
    """测试AI角色模板"""
    try:
        from bson import ObjectId
        
        # 获取模板信息
        template = await db_service.db.ai_role_templates.find_one({"_id": ObjectId(template_id)})
        if not template:
            raise HTTPException(status_code=404, detail="AI角色模板不存在")
        
        # TODO: 实现实际的AI调用逻辑
        # 这里可以调用配置的AI提供商来测试模板
        
        response = f"这是使用模板 '{template['name']}' 生成的测试响应。输入: {test_input}"
        
        return TestTemplateResponse(response=response)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试AI角色模板失败: {str(e)}")
