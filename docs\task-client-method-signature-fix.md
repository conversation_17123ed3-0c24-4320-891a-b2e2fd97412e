# 任务客户端方法签名修复

## 问题描述

在任务执行过程中出现了以下错误：
```
TypeError: TaskServiceClient.create_task() takes 2 positional arguments but 3 were given
```

这个错误表明在调用`TaskServiceClient.create_task()`方法时传递了错误数量的参数。

## 根本原因分析

经过代码分析，发现问题出现在两个不同的客户端类有不同的方法签名：

### 1. TaskServiceClient
- `create_task(task_data)` - 只接受1个参数（任务数据）
- `start_task(task_id)` - 接受1个参数（任务ID）

### 2. CoreClient  
- `create_task(task_id, task_data)` - 接受2个参数（任务ID和任务数据）
- `start_task(task_id)` - 接受1个参数（任务ID）

### 3. 问题所在
在`start_core_task_async`函数中：
- 当使用指定的Core服务时，通过`get_task_client()`获取的是`TaskServiceClient`实例
- 但代码中调用的是`core_client.create_task(task_id, core_task_data)`（两个参数）
- 这导致了参数数量不匹配的错误

## 修复方案

### 1. 客户端类型检测
通过检查客户端是否有`task_client`属性来区分客户端类型：

```python
# 🔧 重要修复：根据客户端类型调用不同的方法
if hasattr(core_client, 'task_client'):
    # 这是CoreClient实例，使用两个参数的方法
    create_result = await asyncio.wait_for(
        core_client.create_task(task_id, core_task_data),
        timeout=120.0
    )
else:
    # 这是TaskServiceClient实例，使用一个参数的方法
    # 确保task_data包含task_id
    core_task_data["task_id"] = task_id
    create_result = await asyncio.wait_for(
        core_client.create_task(core_task_data),
        timeout=120.0
    )
```

### 2. 修复的文件

**文件**: `backend/app/api/task.py`

#### 修复1：客户端获取逻辑
```python
# 🔧 重要修复：从任务数据中获取core_service_id
core_service_id = task_data.get("core_service_id")
if core_service_id:
    logger.info(f"✅ 任务指定使用Core服务: {core_service_id}")
    # 使用指定的Core服务 - 注意：get_task_client返回TaskServiceClient
    task_client = await get_task_client(core_service_id)
    if not task_client:
        logger.error(f"❌ 无法连接到指定的Core服务: {core_service_id}")
        raise Exception(f"无法连接到指定的Core服务: {core_service_id}")
    # TaskServiceClient的create_task方法只接受一个参数
    core_client = task_client
else:
    logger.warning(f"⚠️ 任务 {task_id} 缺少core_service_id，使用默认Core服务")
    logger.warning("   这可能导致任务在错误的Core服务上执行")
    logger.warning("   建议为任务补充core_service_id信息")
    # 获取Core客户端 - 自动从 Consul 发现服务
    core_client = get_core_client()
```

#### 修复2：create_task调用逻辑
```python
# 创建任务
try:
    logger.info(f"🔄 开始创建Core服务任务: {task_id}")
    
    # 🔧 重要修复：根据客户端类型调用不同的方法
    if hasattr(core_client, 'task_client'):
        # 这是CoreClient实例，使用两个参数的方法
        create_result = await asyncio.wait_for(
            core_client.create_task(task_id, core_task_data),
            timeout=120.0  # 2分钟超时
        )
    else:
        # 这是TaskServiceClient实例，使用一个参数的方法
        # 确保task_data包含task_id
        core_task_data["task_id"] = task_id
        create_result = await asyncio.wait_for(
            core_client.create_task(core_task_data),
            timeout=120.0  # 2分钟超时
        )
```

#### 修复3：start_task调用逻辑
```python
# 启动任务
logger.info(f"🚀 开始启动Core服务任务: {task_id}")
try:
    # 两个客户端都有start_task方法，可以直接调用
    start_result = await asyncio.wait_for(
        core_client.start_task(task_id),
        timeout=60.0  # 1分钟超时
    )
```

## 客户端类型识别逻辑

### CoreClient特征
- 有`task_client`属性（指向TaskServiceClient实例）
- `create_task(task_id, task_data)` - 两个参数
- `start_task(task_id)` - 一个参数

### TaskServiceClient特征  
- 没有`task_client`属性
- `create_task(task_data)` - 一个参数（task_data中包含task_id）
- `start_task(task_id)` - 一个参数

## 修复验证

### 测试步骤
1. 创建一个指定Core服务的任务
2. 启动任务，观察日志输出
3. 确认没有"takes 2 positional arguments but 3 were given"错误
4. 确认任务在正确的Core服务上执行

### 预期结果
- ✅ 任务创建和启动不再出现参数数量错误
- ✅ 日志显示"任务指定使用Core服务: [service_id]"
- ✅ 任务在指定的Core服务上正确执行

## 影响范围

此修复影响以下功能：
- 指定Core服务的任务创建和启动
- 任务执行时的Core服务选择逻辑
- 所有使用`start_core_task_async`函数的任务启动流程

## 注意事项

1. 修复保持了向后兼容性，不影响使用默认Core服务的任务
2. 客户端类型检测逻辑简单可靠，基于对象属性存在性判断
3. 两种客户端的`start_task`方法签名相同，无需特殊处理
4. 建议在生产环境部署前进行充分测试

## 相关问题

此修复解决了以下相关问题：
- 任务执行时Core服务选择不正确
- 指定Core服务的任务无法正常启动
- 方法调用参数数量不匹配导致的TypeError
