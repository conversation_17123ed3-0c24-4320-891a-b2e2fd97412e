# 视频片头片尾处理功能

## 功能概述

视频片头片尾处理功能允许用户批量为视频添加指定的片头和片尾，支持多种转场特效，提高视频制作效率。

## 主要特性

### 🎬 批量处理
- 支持批量为文件夹中的所有视频添加片头和片尾
- 可选择只添加片头、只添加片尾，或同时添加
- 支持并发处理，提高处理效率

### ✨ 转场特效
- **淡入淡出 (fade)**: 经典的淡入淡出效果
- **溶解 (dissolve)**: 画面溶解过渡效果
- **擦除 (wipe)**: 从一边擦除到另一边
- **滑动 (slide)**: 滑动过渡效果
- **缩放 (zoom)**: 缩放过渡效果
- **无特效 (none)**: 直接拼接，无过渡效果

### 📁 灵活的输出选项
- 可选择覆盖原文件或输出到新文件
- 支持自定义输出文件夹
- 支持自定义输出文件名后缀
- 支持多种输出质量选择（高/中/低）

### 🎯 支持的格式
- **视频格式**: MP4, AVI, MOV, MKV, WMV, FLV, M4V
- **输出质量**: 高质量(CRF 18)、中等质量(CRF 23)、低质量(CRF 28)

## 使用方法

### 1. 前端操作

1. 在文件管理界面，点击"🎬 视频处理"下拉菜单
2. 选择"🎭 添加片头片尾"选项
3. 在弹出的对话框中配置参数：
   - **视频文件夹**: 自动填充当前文件夹路径
   - **片头文件**: 选择片头视频文件（可选）
   - **片尾文件**: 选择片尾视频文件（可选）
   - **转场效果**: 选择转场特效类型
   - **转场时长**: 设置转场持续时间（0-5秒）
   - **输出质量**: 选择输出视频质量
   - **并发处理数**: 设置同时处理的视频数量（1-8个）
   - **输出设置**: 选择覆盖原文件或保存到新文件
4. 点击"开始处理"按钮启动处理

### 2. API调用

```javascript
// 前端API调用示例
const request = {
  folder_path: 'H:\\PublishSystem\\videos',
  intro_path: 'H:\\PublishSystem\\intro.mp4',
  outro_path: 'H:\\PublishSystem\\outro.mp4',
  transition_effect: 'fade',
  transition_duration: 1.0,
  output_quality: 'medium',
  max_concurrent: 3,
  overwrite_original: false,
  output_suffix: '_with_intro_outro'
}

const response = await addIntroOutroBatch(request)
```

### 3. 后端API

```http
POST /api/v1/filesystem/add-intro-outro
Content-Type: application/json

{
  "folder_path": "H:\\PublishSystem\\videos",
  "intro_path": "H:\\PublishSystem\\intro.mp4",
  "outro_path": "H:\\PublishSystem\\outro.mp4",
  "transition_effect": "fade",
  "transition_duration": 1.0,
  "output_quality": "medium",
  "max_concurrent": 3,
  "overwrite_original": false,
  "output_suffix": "_with_intro_outro"
}
```

## 技术实现

### 架构组件

1. **Core服务** (`core/src/services/video_intro_outro_service.py`)
   - 视频片头片尾处理的核心逻辑
   - 使用FFmpeg进行视频处理
   - 支持异步并发处理

2. **gRPC接口** (`core/src/api/file.proto`)
   - 定义BatchAddIntroOutro RPC方法
   - 定义相关的请求和响应消息

3. **Backend API** (`backend/app/api/v1/filesystem.py`)
   - 提供HTTP REST API接口
   - 调用Core服务的gRPC接口
   - 处理请求验证和响应格式化

4. **前端界面** (`frontend/src/views/doc/Manager.vue`)
   - 用户友好的操作界面
   - 参数配置和进度显示
   - 集成到文件管理系统中

### 处理流程

1. **参数验证**: 检查输入文件夹、片头片尾文件是否存在
2. **视频扫描**: 扫描文件夹中的所有视频文件
3. **并发处理**: 使用信号量控制并发数，避免系统过载
4. **FFmpeg处理**: 使用FFmpeg的concat滤镜拼接视频
5. **转场特效**: 根据选择的特效类型添加过渡效果
6. **结果返回**: 返回处理结果和统计信息

### FFmpeg命令示例

```bash
# 添加片头和片尾的FFmpeg命令
ffmpeg -y -i intro.mp4 -i main.mp4 -i outro.mp4 \
  -filter_complex "[0:v][0:a][1:v][1:a][2:v][2:a]concat=n=3:v=1:a=1[outv][outa]" \
  -map "[outv]" -map "[outa]" \
  -c:v libx264 -preset fast -crf 23 \
  -c:a aac -b:a 128k \
  output.mp4
```

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| folder_path | string | - | 视频文件夹路径（必填） |
| intro_path | string | null | 片头文件路径（可选） |
| outro_path | string | null | 片尾文件路径（可选） |
| transition_effect | string | 'fade' | 转场效果类型 |
| transition_duration | float | 1.0 | 转场时长（秒） |
| output_quality | string | 'medium' | 输出质量 |
| max_concurrent | int | 3 | 最大并发处理数 |
| overwrite_original | bool | false | 是否覆盖原文件 |
| output_suffix | string | '_with_intro_outro' | 输出文件后缀 |

## 注意事项

1. **文件格式兼容性**: 确保片头、片尾文件与主视频文件格式兼容
2. **磁盘空间**: 处理大量视频时需要足够的磁盘空间
3. **处理时间**: 处理时间取决于视频数量、大小和输出质量
4. **并发限制**: 建议根据系统性能调整并发处理数
5. **备份建议**: 重要文件建议先备份再进行处理

## 故障排除

### 常见问题

1. **片头片尾文件不存在**
   - 检查文件路径是否正确
   - 确认文件确实存在且可访问

2. **视频格式不支持**
   - 确认视频文件格式在支持列表中
   - 尝试转换为MP4格式

3. **处理失败**
   - 检查FFmpeg是否正确安装
   - 查看日志文件获取详细错误信息
   - 确认磁盘空间充足

4. **性能问题**
   - 降低并发处理数
   - 选择较低的输出质量
   - 确保系统资源充足

## 更新日志

### v1.0.0 (2025-07-08)
- 初始版本发布
- 支持基本的片头片尾添加功能
- 支持多种转场特效
- 支持批量并发处理
- 集成到文件管理系统
