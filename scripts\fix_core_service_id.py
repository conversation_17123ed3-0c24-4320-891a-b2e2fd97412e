#!/usr/bin/env python3
"""
修复现有任务的Core服务ID脚本
用于为旧任务补充缺失的core_service_id字段
"""

import requests
import json
import sys

def fix_core_service_id(backend_url="http://localhost:8000", default_core_service_id="core-123.2"):
    """
    调用Backend API批量更新任务的Core服务ID
    
    Args:
        backend_url: Backend服务地址
        default_core_service_id: 默认的Core服务ID
    """
    try:
        print(f"🔧 开始修复任务Core服务ID...")
        print(f"Backend地址: {backend_url}")
        print(f"默认Core服务ID: {default_core_service_id}")
        
        # 调用批量更新API
        response = requests.post(
            f"{backend_url}/api/tasks/batch-update-core-service-id",
            json={
                "default_core_service_id": default_core_service_id
            },
            headers={
                "Content-Type": "application/json"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ 修复成功!")
                print(f"   更新了 {result.get('updated_count', 0)} 个任务")
                print(f"   使用的Core服务ID: {result.get('default_core_service_id')}")
                print(f"   消息: {result.get('message')}")
                return True
            else:
                print(f"❌ 修复失败: {result.get('error')}")
                return False
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 修复过程异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("任务Core服务ID修复工具")
    print("=" * 60)
    
    # 解析命令行参数
    backend_url = "http://localhost:8000"
    default_core_service_id = "core-123.2"
    
    if len(sys.argv) > 1:
        backend_url = sys.argv[1]
    if len(sys.argv) > 2:
        default_core_service_id = sys.argv[2]
    
    print(f"使用参数:")
    print(f"  Backend地址: {backend_url}")
    print(f"  默认Core服务ID: {default_core_service_id}")
    print()
    
    # 确认操作
    confirm = input("确认要执行批量更新吗？(y/N): ")
    if confirm.lower() not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 执行修复
    success = fix_core_service_id(backend_url, default_core_service_id)
    
    if success:
        print("\n🎉 任务Core服务ID修复完成!")
        print("现在新的工作流配置获取应该能正确使用指定的Core服务了。")
    else:
        print("\n💥 修复失败，请检查错误信息并重试。")
        sys.exit(1)

if __name__ == "__main__":
    main()
