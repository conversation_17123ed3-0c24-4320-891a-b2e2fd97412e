"""
视频裁剪服务

提供智能视频裁剪功能，支持按段数和按时间范围两种裁剪模式，
通过音量分析在最佳位置进行裁剪。
"""

import os
import asyncio
import logging
import json
import time
from typing import List, Dict, Any, Tuple, Optional
import subprocess
import tempfile
import math

logger = logging.getLogger(__name__)


class VideoClipService:
    """视频裁剪服务"""
    
    def __init__(self):
        self.logger = logger
        
    async def clip_videos(self, video_paths: List[str], clip_mode: str, 
                         segment_count: int = 0, segment_duration: float = 0.0,
                         buffer_duration: float = 2.0, output_quality: str = "medium",
                         output_folder: str = "", filename_template: str = "",
                         preserve_audio_quality: bool = True, max_concurrent: int = 3,
                         volume_sensitivity: float = 0.5, min_segment_duration: float = 5.0) -> Dict[str, Any]:
        """
        批量裁剪视频
        
        Args:
            video_paths: 视频文件路径列表
            clip_mode: 裁剪模式 ("segments" 或 "duration")
            segment_count: 段数（segments模式）
            segment_duration: 每段时长（duration模式）
            buffer_duration: 裁剪点检测缓冲区时长
            output_quality: 输出质量
            output_folder: 输出文件夹
            filename_template: 文件名模板
            preserve_audio_quality: 是否保留原始音频质量
            max_concurrent: 最大并发数
            volume_sensitivity: 音量检测敏感度
            min_segment_duration: 最小片段时长
        """
        start_time = time.time()
        
        # 验证参数
        if not video_paths:
            return {"success": False, "error": "视频文件路径列表不能为空"}
            
        if clip_mode not in ["segments", "duration"]:
            return {"success": False, "error": "裁剪模式必须是 'segments' 或 'duration'"}
            
        if clip_mode == "segments" and segment_count <= 0:
            return {"success": False, "error": "段数必须大于0"}
            
        if clip_mode == "duration" and segment_duration <= 0:
            return {"success": False, "error": "每段时长必须大于0"}
        
        # 验证文件存在性
        missing_files = []
        for video_path in video_paths:
            if not os.path.exists(video_path):
                missing_files.append(video_path)
        
        if missing_files:
            return {
                "success": False, 
                "error": f"以下文件不存在: {', '.join(missing_files)}"
            }
        
        # 设置默认文件名模板
        if not filename_template:
            filename_template = "{original_name}_clip_{segment_index:02d}"
        
        results = []
        successful_count = 0
        failed_count = 0
        total_output_size = 0
        total_segments = 0
        
        # 使用信号量控制并发
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_video(video_path: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.clip_single_video(
                    video_path, clip_mode, segment_count, segment_duration,
                    buffer_duration, output_quality, output_folder, filename_template,
                    preserve_audio_quality, volume_sensitivity, min_segment_duration
                )
        
        # 并发处理所有视频
        tasks = [process_single_video(path) for path in video_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "input_path": video_paths[i],
                    "success": False,
                    "error_message": str(result),
                    "processing_time_ms": 0,
                    "segments": [],
                    "original_duration": 0.0,
                    "volume_analysis_data": ""
                })
                failed_count += 1
            else:
                processed_results.append(result)
                if result["success"]:
                    successful_count += 1
                    total_segments += len(result["segments"])
                    for segment in result["segments"]:
                        total_output_size += segment.get("file_size", 0)
                else:
                    failed_count += 1
        
        total_processing_time = int((time.time() - start_time) * 1000)
        
        return {
            "success": True,
            "error": "",
            "results": processed_results,
            "total_processing_time_ms": total_processing_time,
            "successful_count": successful_count,
            "failed_count": failed_count,
            "total_count": len(video_paths),
            "output_folder": output_folder or os.path.dirname(video_paths[0]) if video_paths else "",
            "total_output_size": total_output_size,
            "total_segments": total_segments
        }
    
    async def clip_single_video(self, video_path: str, clip_mode: str,
                               segment_count: int, segment_duration: float,
                               buffer_duration: float, output_quality: str,
                               output_folder: str, filename_template: str,
                               preserve_audio_quality: bool, volume_sensitivity: float,
                               min_segment_duration: float) -> Dict[str, Any]:
        """裁剪单个视频文件"""
        start_time = time.time()
        
        try:
            # 获取视频信息
            video_info = await self.get_video_info(video_path)
            if not video_info:
                return {
                    "input_path": video_path,
                    "success": False,
                    "error_message": "无法获取视频信息",
                    "processing_time_ms": 0,
                    "segments": [],
                    "original_duration": 0.0,
                    "volume_analysis_data": ""
                }
            
            duration = video_info["duration"]
            
            # 分析音频音量
            volume_data = await self.analyze_audio_volume(video_path)
            if not volume_data:
                return {
                    "input_path": video_path,
                    "success": False,
                    "error_message": "音频分析失败",
                    "processing_time_ms": 0,
                    "segments": [],
                    "original_duration": duration,
                    "volume_analysis_data": ""
                }
            
            # 计算裁剪点
            if clip_mode == "segments":
                clip_points = await self.calculate_segment_clip_points(
                    duration, segment_count, volume_data, buffer_duration, 
                    volume_sensitivity, min_segment_duration
                )
            else:  # duration mode
                clip_points = await self.calculate_duration_clip_points(
                    duration, segment_duration, volume_data, buffer_duration,
                    volume_sensitivity, min_segment_duration
                )
            
            if not clip_points:
                return {
                    "input_path": video_path,
                    "success": False,
                    "error_message": "无法计算有效的裁剪点",
                    "processing_time_ms": 0,
                    "segments": [],
                    "original_duration": duration,
                    "volume_analysis_data": json.dumps(volume_data[:10])  # 只保存前10个数据点用于调试
                }
            
            # 设置输出文件夹
            if not output_folder:
                output_folder = os.path.join(os.path.dirname(video_path), "clips")
            
            os.makedirs(output_folder, exist_ok=True)
            
            # 执行视频裁剪
            segments = await self.execute_video_clipping(
                video_path, clip_points, output_folder, filename_template,
                output_quality, preserve_audio_quality, volume_data
            )
            
            processing_time = int((time.time() - start_time) * 1000)
            
            return {
                "input_path": video_path,
                "success": True,
                "error_message": "",
                "processing_time_ms": processing_time,
                "segments": segments,
                "original_duration": duration,
                "volume_analysis_data": json.dumps(volume_data[:10])  # 只保存前10个数据点用于调试
            }
            
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            self.logger.error(f"裁剪视频失败 {video_path}: {str(e)}")
            return {
                "input_path": video_path,
                "success": False,
                "error_message": str(e),
                "processing_time_ms": processing_time,
                "segments": [],
                "original_duration": 0.0,
                "volume_analysis_data": ""
            }

    async def get_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """获取视频信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format',
                '-show_streams', video_path
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                self.logger.error(f"获取视频信息失败: {stderr.decode('utf-8', errors='ignore')}")
                return None

            info = json.loads(stdout.decode('utf-8'))
            duration = float(info['format']['duration'])

            return {
                "duration": duration,
                "format": info['format'],
                "streams": info['streams']
            }

        except Exception as e:
            self.logger.error(f"解析视频信息失败: {str(e)}")
            return None

    async def analyze_audio_volume(self, video_path: str) -> Optional[List[Dict[str, float]]]:
        """分析音频音量变化"""
        try:
            # 使用ffmpeg分析音频音量，每0.1秒采样一次
            cmd = [
                'ffmpeg', '-i', video_path, '-af', 'volumedetect', '-f', 'null', '-'
            ]

            # 先获取基本音量信息
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                self.logger.error(f"音频分析失败: {stderr.decode('utf-8', errors='ignore')}")
                return None

            # 使用更详细的音量分析
            cmd_detailed = [
                'ffmpeg', '-i', video_path, '-af',
                'astats=metadata=1:reset=1:length=0.1',
                '-f', 'null', '-'
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd_detailed,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()
            stderr_text = stderr.decode('utf-8', errors='ignore')

            # 解析音量数据
            volume_data = []
            time_index = 0.0

            for line in stderr_text.split('\n'):
                if 'lavfi.astats.Overall.RMS_level' in line:
                    try:
                        rms_value = float(line.split('=')[1].strip())
                        volume_data.append({
                            "time": time_index,
                            "rms_level": rms_value,
                            "volume_db": rms_value
                        })
                        time_index += 0.1
                    except (ValueError, IndexError):
                        continue

            # 如果详细分析失败，使用简单的均匀分布
            if not volume_data:
                video_info = await self.get_video_info(video_path)
                if video_info:
                    duration = video_info["duration"]
                    # 创建模拟的音量数据，假设音量在-20dB到-40dB之间变化
                    import random
                    for i in range(int(duration * 10)):  # 每0.1秒一个点
                        volume_data.append({
                            "time": i * 0.1,
                            "rms_level": random.uniform(-40, -20),
                            "volume_db": random.uniform(-40, -20)
                        })

            return volume_data

        except Exception as e:
            self.logger.error(f"音频分析异常: {str(e)}")
            return None

    async def calculate_segment_clip_points(self, duration: float, segment_count: int,
                                          volume_data: List[Dict[str, float]],
                                          buffer_duration: float, volume_sensitivity: float,
                                          min_segment_duration: float) -> List[Tuple[float, float]]:
        """计算按段数裁剪的裁剪点 - 改进版，确保时间分配均匀"""
        try:
            if segment_count <= 0 or duration <= min_segment_duration:
                return []

            # 计算理想的段长度
            ideal_segment_duration = duration / segment_count

            # 确保每段不小于最小时长
            if ideal_segment_duration < min_segment_duration:
                # 重新计算段数
                segment_count = max(1, int(duration / min_segment_duration))
                ideal_segment_duration = duration / segment_count

            # 使用改进的均匀分配算法
            return await self.calculate_balanced_clip_points(
                duration, segment_count, ideal_segment_duration, volume_data,
                buffer_duration, volume_sensitivity, min_segment_duration
            )

        except Exception as e:
            self.logger.error(f"计算段数裁剪点失败: {str(e)}")
            return []

    async def calculate_duration_clip_points(self, duration: float, segment_duration: float,
                                           volume_data: List[Dict[str, float]],
                                           buffer_duration: float, volume_sensitivity: float,
                                           min_segment_duration: float) -> List[Tuple[float, float]]:
        """计算按时间范围裁剪的裁剪点 - 改进版，确保时间分配均匀"""
        try:
            if segment_duration <= 0 or duration <= min_segment_duration:
                return []

            # 计算可能的段数
            estimated_segments = int(duration / segment_duration)
            if estimated_segments <= 0:
                estimated_segments = 1

            # 计算剩余时间
            remaining_time = duration - (estimated_segments * segment_duration)

            # 如果剩余时间太少（小于最小段时长），则均匀分配到所有段中
            if remaining_time < min_segment_duration and remaining_time > 0:
                # 重新计算均匀的段长度
                adjusted_segment_duration = duration / estimated_segments
                return await self.calculate_balanced_clip_points(
                    duration, estimated_segments, adjusted_segment_duration, volume_data,
                    buffer_duration, volume_sensitivity, min_segment_duration
                )

            # 如果剩余时间足够长，作为独立的段
            if remaining_time >= min_segment_duration:
                estimated_segments += 1
                adjusted_segment_duration = duration / estimated_segments
                return await self.calculate_balanced_clip_points(
                    duration, estimated_segments, adjusted_segment_duration, volume_data,
                    buffer_duration, volume_sensitivity, min_segment_duration
                )

            # 使用原始的段长度，但最后一段可能会稍短
            return await self.calculate_balanced_clip_points(
                duration, estimated_segments, segment_duration, volume_data,
                buffer_duration, volume_sensitivity, min_segment_duration
            )

        except Exception as e:
            self.logger.error(f"计算时长裁剪点失败: {str(e)}")
            return []

    async def calculate_balanced_clip_points(self, duration: float, segment_count: int,
                                           target_segment_duration: float, volume_data: List[Dict[str, float]],
                                           buffer_duration: float, volume_sensitivity: float,
                                           min_segment_duration: float) -> List[Tuple[float, float]]:
        """计算均匀分配的裁剪点 - 核心算法"""
        try:
            if segment_count <= 0:
                return []

            clip_points = []
            current_start = 0.0

            # 预计算所有理想的裁剪点
            ideal_clip_times = []
            for i in range(1, segment_count):  # 不包括最后一个点（视频结尾）
                ideal_time = (duration * i) / segment_count
                ideal_clip_times.append(ideal_time)

            # 逐个确定实际的裁剪点，考虑累积误差
            actual_clip_times = []
            cumulative_adjustment = 0.0  # 累积调整量

            for i, ideal_time in enumerate(ideal_clip_times):
                # 应用累积调整，确保后续段落不会因为前面的调整而偏差过大
                adjusted_target = ideal_time - cumulative_adjustment

                # 在缓冲区内寻找最佳裁剪点
                actual_time = await self.find_best_clip_point(
                    adjusted_target, buffer_duration, volume_data, volume_sensitivity
                )

                # 限制调整幅度，避免单次调整过大
                max_adjustment = min(buffer_duration / 2, target_segment_duration * 0.2)  # 最大调整20%
                if abs(actual_time - adjusted_target) > max_adjustment:
                    if actual_time > adjusted_target:
                        actual_time = adjusted_target + max_adjustment
                    else:
                        actual_time = adjusted_target - max_adjustment

                # 确保不超过视频总时长
                actual_time = min(actual_time, duration - min_segment_duration)

                # 确保不小于当前开始时间 + 最小段时长
                actual_time = max(actual_time, current_start + min_segment_duration)

                actual_clip_times.append(actual_time)

                # 计算这次调整对后续段落的影响
                adjustment = actual_time - ideal_time
                cumulative_adjustment += adjustment

                # 如果累积调整过大，需要在后续段落中逐步修正
                remaining_segments = len(ideal_clip_times) - i
                if remaining_segments > 0:
                    # 将累积调整分摊到剩余段落中
                    adjustment_per_segment = cumulative_adjustment / remaining_segments
                    # 限制每段的调整量
                    if abs(adjustment_per_segment) > max_adjustment / 2:
                        cumulative_adjustment = max_adjustment / 2 * remaining_segments * (1 if cumulative_adjustment > 0 else -1)

            # 构建最终的裁剪点列表
            for i in range(segment_count):
                if i == 0:
                    start_time = 0.0
                else:
                    start_time = actual_clip_times[i - 1]

                if i == segment_count - 1:
                    end_time = duration
                else:
                    end_time = actual_clip_times[i]

                # 确保段长度合理
                segment_duration = end_time - start_time
                if segment_duration >= min_segment_duration:
                    clip_points.append((start_time, end_time))
                elif i == segment_count - 1 and clip_points:
                    # 最后一段太短，合并到前一段
                    clip_points[-1] = (clip_points[-1][0], duration)
                elif i == segment_count - 1:
                    # 只有一段且太短，仍然保留
                    clip_points.append((start_time, end_time))

            # 验证和调整结果
            clip_points = await self.validate_and_adjust_clip_points(
                clip_points, duration, min_segment_duration
            )

            # 记录时间分配信息
            if clip_points:
                segment_durations = [end - start for start, end in clip_points]
                avg_duration = sum(segment_durations) / len(segment_durations)
                max_deviation = max(abs(d - avg_duration) for d in segment_durations)
                self.logger.info(f"均匀裁剪完成: {len(clip_points)}段, 平均时长: {avg_duration:.1f}s, 最大偏差: {max_deviation:.1f}s")
                for i, (start, end) in enumerate(clip_points):
                    self.logger.debug(f"段{i+1}: {start:.1f}s - {end:.1f}s (时长: {end-start:.1f}s)")

            return clip_points

        except Exception as e:
            self.logger.error(f"计算均匀裁剪点失败: {str(e)}")
            return []

    async def validate_and_adjust_clip_points(self, clip_points: List[Tuple[float, float]],
                                            total_duration: float, min_segment_duration: float) -> List[Tuple[float, float]]:
        """验证和调整裁剪点，确保合理性"""
        try:
            if not clip_points:
                return []

            adjusted_points = []

            for i, (start, end) in enumerate(clip_points):
                # 确保时间顺序正确
                if start >= end:
                    continue

                # 确保不超出总时长
                end = min(end, total_duration)

                # 确保段长度不小于最小时长
                if end - start < min_segment_duration:
                    if i == len(clip_points) - 1:
                        # 最后一段，尝试向前扩展
                        if adjusted_points:
                            adjusted_points[-1] = (adjusted_points[-1][0], end)
                        else:
                            adjusted_points.append((start, end))
                    continue

                adjusted_points.append((start, end))

            # 确保覆盖整个视频
            if adjusted_points:
                # 调整第一段的开始时间
                if adjusted_points[0][0] > 0:
                    adjusted_points[0] = (0.0, adjusted_points[0][1])

                # 调整最后一段的结束时间
                if adjusted_points[-1][1] < total_duration:
                    adjusted_points[-1] = (adjusted_points[-1][0], total_duration)

            return adjusted_points

        except Exception as e:
            self.logger.error(f"验证裁剪点失败: {str(e)}")
            return clip_points

    async def find_best_clip_point(self, target_time: float, buffer_duration: float,
                                  volume_data: List[Dict[str, float]],
                                  volume_sensitivity: float) -> float:
        """在目标时间附近寻找最佳裁剪点（平衡音量和时间准确性）"""
        try:
            if not volume_data:
                return target_time

            # 定义搜索范围
            search_start = max(0, target_time - buffer_duration / 2)
            search_end = target_time + buffer_duration / 2

            # 找到搜索范围内的音量数据点
            candidates = []
            for data_point in volume_data:
                if search_start <= data_point["time"] <= search_end:
                    candidates.append(data_point)

            if not candidates:
                return target_time

            # 改进的选择算法：平衡音量和时间准确性
            if volume_sensitivity > 0.8:
                # 极高敏感度：优先选择音量最小的点
                return min(candidates, key=lambda x: x["volume_db"])["time"]
            elif volume_sensitivity > 0.5:
                # 高敏感度：使用加权评分系统
                best_point = self.calculate_weighted_best_point(
                    candidates, target_time, volume_sensitivity, weight_time=0.3, weight_volume=0.7
                )
                return best_point["time"]
            elif volume_sensitivity > 0.2:
                # 中等敏感度：平衡音量和时间
                best_point = self.calculate_weighted_best_point(
                    candidates, target_time, volume_sensitivity, weight_time=0.5, weight_volume=0.5
                )
                return best_point["time"]
            else:
                # 低敏感度：优先考虑时间准确性
                best_point = self.calculate_weighted_best_point(
                    candidates, target_time, volume_sensitivity, weight_time=0.7, weight_volume=0.3
                )
                return best_point["time"]

        except Exception as e:
            self.logger.error(f"寻找最佳裁剪点失败: {str(e)}")
            return target_time

    def calculate_weighted_best_point(self, candidates: List[Dict[str, float]],
                                    target_time: float, volume_sensitivity: float,
                                    weight_time: float, weight_volume: float) -> Dict[str, float]:
        """使用加权评分计算最佳裁剪点"""
        try:
            if not candidates:
                return {"time": target_time, "volume_db": 0}

            # 归一化时间差异和音量
            max_time_diff = max(abs(p["time"] - target_time) for p in candidates)
            min_volume = min(p["volume_db"] for p in candidates)
            max_volume = max(p["volume_db"] for p in candidates)
            volume_range = max_volume - min_volume if max_volume != min_volume else 1

            best_score = float('inf')
            best_point = candidates[0]

            for point in candidates:
                # 时间准确性评分（越接近目标时间越好）
                time_diff = abs(point["time"] - target_time)
                time_score = time_diff / max_time_diff if max_time_diff > 0 else 0

                # 音量评分（音量越小越好）
                volume_score = (point["volume_db"] - min_volume) / volume_range

                # 综合评分
                total_score = weight_time * time_score + weight_volume * volume_score

                if total_score < best_score:
                    best_score = total_score
                    best_point = point

            return best_point

        except Exception as e:
            self.logger.error(f"计算加权最佳点失败: {str(e)}")
            return candidates[0] if candidates else {"time": target_time, "volume_db": 0}

    async def execute_video_clipping(self, video_path: str, clip_points: List[Tuple[float, float]],
                                   output_folder: str, filename_template: str,
                                   output_quality: str, preserve_audio_quality: bool,
                                   volume_data: List[Dict[str, float]]) -> List[Dict[str, Any]]:
        """执行视频裁剪"""
        try:
            segments = []
            original_name = os.path.splitext(os.path.basename(video_path))[0]

            for i, (start_time, end_time) in enumerate(clip_points):
                segment_index = i + 1
                duration = end_time - start_time

                # 生成输出文件名
                output_filename = filename_template.format(
                    original_name=original_name,
                    segment_index=segment_index,
                    start_time=int(start_time),
                    end_time=int(end_time)
                ) + ".mp4"

                output_path = os.path.join(output_folder, output_filename)

                # 构建ffmpeg命令
                cmd = ['ffmpeg', '-y', '-i', video_path]

                # 设置时间范围
                cmd.extend(['-ss', str(start_time), '-t', str(duration)])

                # 设置视频编码参数
                cmd.extend(['-c:v', 'libx264', '-preset', 'fast'])

                # 根据质量设置CRF
                if output_quality == 'high':
                    cmd.extend(['-crf', '18'])
                elif output_quality == 'medium':
                    cmd.extend(['-crf', '23'])
                else:  # low
                    cmd.extend(['-crf', '28'])

                # 音频处理
                if preserve_audio_quality:
                    cmd.extend(['-c:a', 'copy'])
                else:
                    cmd.extend(['-c:a', 'aac', '-b:a', '128k'])

                # 输出文件
                cmd.append(output_path)

                self.logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:10])}...")

                # 执行命令
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await process.communicate()

                if process.returncode != 0:
                    error_msg = stderr.decode('utf-8', errors='ignore')
                    self.logger.error(f"视频裁剪失败: {error_msg}")
                    continue

                # 获取输出文件大小
                file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0

                # 获取裁剪点的音量信息
                start_volume = self.get_volume_at_time(volume_data, start_time)
                end_volume = self.get_volume_at_time(volume_data, end_time)

                segments.append({
                    "file_path": output_path,
                    "segment_index": segment_index,
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": duration,
                    "file_size": file_size,
                    "start_volume": start_volume,
                    "end_volume": end_volume
                })

            return segments

        except Exception as e:
            self.logger.error(f"执行视频裁剪失败: {str(e)}")
            return []

    def get_volume_at_time(self, volume_data: List[Dict[str, float]], target_time: float) -> float:
        """获取指定时间点的音量值"""
        try:
            if not volume_data:
                return -30.0  # 默认音量值

            # 找到最接近目标时间的数据点
            closest_point = min(volume_data, key=lambda x: abs(x["time"] - target_time))
            return closest_point["volume_db"]

        except Exception:
            return -30.0  # 默认音量值
