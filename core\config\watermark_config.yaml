# 视频水印检测和清除配置文件

# 水印检测配置
detection:
  # 默认检测模式：auto(自动), template(模板匹配), region(区域检测)
  default_mode: "auto"
  
  # 默认敏感度 (0.0-1.0)
  default_sensitivity: 0.7
  
  # 支持的视频格式
  supported_formats:
    - "mp4"
    - "avi"
    - "mov"
    - "mkv"
    - "wmv"
    - "flv"
    - "m4v"
  
  # 自动检测配置
  auto_detection:
    # 检测区域（四个角落和中心）
    regions:
      - name: "左上角"
        area: [0, 0, 0.25, 0.25]  # x, y, width, height (比例)
      - name: "右上角"
        area: [0.75, 0, 0.25, 0.25]
      - name: "左下角"
        area: [0, 0.75, 0.25, 0.25]
      - name: "右下角"
        area: [0.75, 0.75, 0.25, 0.25]
      - name: "中心区域"
        area: [0.33, 0.33, 0.34, 0.34]
    
    # 边缘检测阈值
    edge_threshold_low: 50
    edge_threshold_high: 150
    
    # 边缘密度阈值
    edge_density_threshold: 0.05
  
  # 模板匹配配置
  template_matching:
    # 模板文件夹
    template_folder: "templates/watermarks"
    
    # 常见平台水印模板
    platform_templates:
      - name: "抖音"
        file: "douyin_logo.png"
        confidence_threshold: 0.8
      - name: "快手"
        file: "kuaishou_logo.png"
        confidence_threshold: 0.8
      - name: "小红书"
        file: "xiaohongshu_logo.png"
        confidence_threshold: 0.8
      - name: "微博"
        file: "weibo_logo.png"
        confidence_threshold: 0.8
    
    # 匹配方法
    match_method: "TM_CCOEFF_NORMED"
    
    # 最小置信度
    min_confidence: 0.6
  
  # 帧采样配置
  frame_sampling:
    # 每个视频采样的帧数
    sample_count: 10
    
    # 采样策略：uniform(均匀), random(随机), keyframes(关键帧)
    sampling_strategy: "uniform"
    
    # 最小采样间隔（秒）
    min_interval: 1.0

# 水印清除配置
removal:
  # 默认清除模式：auto(自动), manual(手动指定区域), inpaint(修复算法)
  default_mode: "auto"
  
  # 默认修复算法：blur(模糊), median(中值滤波), inpaint(图像修复)
  default_inpaint_method: "blur"
  
  # 默认输出质量：high, medium, low
  default_output_quality: "medium"
  
  # 质量设置
  quality_settings:
    high:
      crf: 18
      preset: "slow"
      profile: "high"
    medium:
      crf: 23
      preset: "fast"
      profile: "main"
    low:
      crf: 28
      preset: "ultrafast"
      profile: "baseline"
  
  # 修复算法配置
  inpaint_methods:
    blur:
      # 模糊半径
      blur_radius: 10
      # 模糊强度
      blur_strength: 1
    
    median:
      # 中值滤波核大小
      kernel_size: 5
    
    inpaint:
      # 修复半径
      inpaint_radius: 3
      # 修复方法：TELEA, NS
      inpaint_method: "TELEA"
  
  # 编码配置
  encoding:
    # 视频编码器
    video_codec: "libx264"
    
    # 音频编码器
    audio_codec: "aac"
    
    # 音频比特率
    audio_bitrate: "128k"
    
    # 像素格式
    pixel_format: "yuv420p"
    
    # 帧率处理：keep(保持), 30, 25, 24
    framerate: "keep"

# 批量处理配置
batch_processing:
  # 默认最大并发数
  default_max_concurrent: 3
  
  # 支持的文件过滤器
  file_filters:
    - "*.mp4"
    - "*.avi"
    - "*.mov"
    - "*.mkv"
    - "*.wmv"
    - "*.flv"
    - "*.m4v"
  
  # 处理模式
  process_modes:
    - "detect_only"      # 仅检测
    - "remove_only"      # 仅清除
    - "detect_and_remove" # 检测并清除
  
  # 任务超时设置（秒）
  timeouts:
    detection_per_file: 900    # 单文件检测超时（从5分钟增加到15分钟）
    removal_per_file: 3600     # 单文件清除超时（从30分钟增加到60分钟）
    batch_total: 14400         # 批量任务总超时（从2小时增加到4小时）

# 性能配置
performance:
  # 临时文件目录
  temp_directory: "temp_watermark"
  
  # 最大内存使用（MB）
  max_memory_mb: 2048
  
  # ffmpeg线程数（0为自动）
  ffmpeg_threads: 0
  
  # OpenCV线程数（0为自动）
  opencv_threads: 0
  
  # 清理临时文件
  cleanup_temp_files: true
  
  # 保留检测结果图片
  keep_detection_images: false

# 日志配置
logging:
  # 日志级别：DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  
  # 详细模式
  verbose: false
  
  # 记录处理时间
  log_timing: true
  
  # 记录内存使用
  log_memory: false

# 输出配置
output:
  # 默认输出文件名模式
  filename_pattern: "{original_name}_no_watermark{extension}"
  
  # 可用变量：
  # {original_name} - 原始文件名（不含扩展名）
  # {extension} - 文件扩展名
  # {timestamp} - 时间戳
  # {md5} - 文件MD5值前8位
  # {platform} - 检测到的平台名称
  
  # 覆盖现有文件
  overwrite_existing: false
  
  # 保留原始文件
  keep_original: true

# 验证配置
validation:
  # 验证输出文件完整性
  verify_output: true
  
  # 最小输出文件大小（字节）
  min_output_size: 1024
  
  # 检查视频可播放性
  check_playability: true
  
  # 比较输出质量
  quality_check: false

# 错误处理配置
error_handling:
  # 最大重试次数
  max_retries: 2
  
  # 重试间隔（秒）
  retry_interval: 5
  
  # 跳过损坏的文件
  skip_corrupted_files: true
  
  # 继续处理其他文件（批量模式）
  continue_on_error: true
  
  # 错误日志详细程度
  error_detail_level: "full"  # full, summary, minimal
