# ThunderHub 视频编辑器功能规划

## 1. 剪映功能对比分析

### 1.1 剪映核心功能清单

| 功能分类 | 具体功能 | 优先级 | ThunderHub实现状态 |
|---------|---------|--------|-------------------|
| **基础编辑** | 视频剪切 | P0 | ✅ 已有Core服务 |
| | 视频分割 | P0 | ✅ 已有Core服务 |
| | 视频合并 | P0 | ✅ 已有Core服务 |
| | 视频复制 | P0 | 🔄 需要扩展 |
| | 撤销/重做 | P0 | ❌ 需要开发 |
| **时间轴** | 多轨道编辑 | P0 | ❌ 需要开发 |
| | 精确到帧 | P1 | ❌ 需要开发 |
| | 磁性吸附 | P1 | ❌ 需要开发 |
| | 关键帧动画 | P2 | ❌ 需要开发 |
| **音频处理** | 音频分离 | P0 | ✅ 已有Core服务 |
| | 音量调节 | P0 | 🔄 需要扩展 |
| | 音频淡入淡出 | P1 | ❌ 需要开发 |
| | 音频同步 | P1 | ❌ 需要开发 |
| **视觉效果** | 滤镜效果 | P1 | 🔄 基础支持 |
| | 调色调光 | P1 | ❌ 需要开发 |
| | 转场动画 | P1 | ❌ 需要开发 |
| | 画面裁剪 | P0 | ✅ 已有Core服务 |
| **文字字幕** | 文字添加 | P1 | ❌ 需要开发 |
| | 字幕生成 | P1 | ✅ 已有Core服务 |
| | 文字动画 | P2 | ❌ 需要开发 |
| | 多语言支持 | P2 | ❌ 需要开发 |
| **导出分享** | 多格式导出 | P0 | ✅ 已有Core服务 |
| | 质量选择 | P0 | ✅ 已有Core服务 |
| | 云端保存 | P1 | 🔄 基础支持 |
| | 社交分享 | P2 | ❌ 需要开发 |

### 1.2 优先级说明

- **P0 (核心功能)**: 必须实现，影响基本使用
- **P1 (重要功能)**: 应该实现，提升用户体验
- **P2 (增强功能)**: 可以实现，锦上添花

## 2. 详细功能设计

### 2.1 基础编辑功能

#### 视频剪切 (Video Trimming)
```typescript
interface TrimOperation {
  clipId: string
  startTime: number  // 新的开始时间
  endTime: number    // 新的结束时间
  preserveAudio: boolean
}

// API调用
const trimVideo = async (operation: TrimOperation) => {
  return await request.post('/api/v1/video-editor/edit/trim', operation)
}
```

#### 视频分割 (Video Splitting)
```typescript
interface SplitOperation {
  clipId: string
  splitTime: number  // 分割时间点
  createNewClip: boolean
}

// 分割后生成两个新的clip
const splitVideo = async (operation: SplitOperation) => {
  return await request.post('/api/v1/video-editor/edit/split', operation)
}
```

#### 视频合并 (Video Merging)
```typescript
interface MergeOperation {
  clipIds: string[]  // 要合并的clip列表
  transitionType?: string  // 转场类型
  transitionDuration?: number
}

const mergeVideos = async (operation: MergeOperation) => {
  return await request.post('/api/v1/video-editor/edit/merge', operation)
}
```

### 2.2 时间轴设计

#### 多轨道结构
```typescript
interface Timeline {
  id: string
  duration: number
  frameRate: number
  tracks: Track[]
  markers: Marker[]
  playhead: number
}

interface Track {
  id: string
  type: 'video' | 'audio' | 'subtitle' | 'effect'
  name: string
  height: number
  enabled: boolean
  locked: boolean
  clips: Clip[]
}

interface Clip {
  id: string
  trackId: string
  startTime: number
  duration: number
  mediaAssetId: string
  
  // 裁剪信息
  trimIn: number
  trimOut: number
  
  // 变换属性
  transform: {
    x: number
    y: number
    scaleX: number
    scaleY: number
    rotation: number
    opacity: number
  }
  
  // 效果列表
  effects: Effect[]
}
```

#### 时间轴操作
```typescript
// 拖拽操作
interface DragOperation {
  clipId: string
  newTrackId: string
  newStartTime: number
  snapToGrid: boolean
}

// 缩放操作
interface ResizeOperation {
  clipId: string
  edge: 'start' | 'end'
  newTime: number
  maintainAspectRatio: boolean
}
```

### 2.3 音频处理功能

#### 音量控制
```typescript
interface VolumeControl {
  clipId: string
  volume: number  // 0-200 (0%到200%)
  keyframes?: VolumeKeyframe[]
}

interface VolumeKeyframe {
  time: number
  volume: number
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out'
}
```

#### 音频效果
```typescript
interface AudioEffect {
  id: string
  type: 'fade-in' | 'fade-out' | 'noise-reduction' | 'equalizer'
  parameters: Record<string, any>
  enabled: boolean
}

// 淡入淡出效果
interface FadeEffect extends AudioEffect {
  type: 'fade-in' | 'fade-out'
  parameters: {
    duration: number  // 淡入淡出时长(秒)
    curve: 'linear' | 'exponential' | 'logarithmic'
  }
}
```

### 2.4 视觉效果系统

#### 滤镜系统
```typescript
interface VideoFilter {
  id: string
  name: string
  type: 'color' | 'blur' | 'sharpen' | 'vintage' | 'custom'
  parameters: FilterParameters
  intensity: number  // 0-100
}

interface ColorFilter extends VideoFilter {
  type: 'color'
  parameters: {
    brightness: number    // -100 to 100
    contrast: number      // -100 to 100
    saturation: number    // -100 to 100
    hue: number          // -180 to 180
    temperature: number   // -100 to 100
    tint: number         // -100 to 100
  }
}
```

#### 转场动画
```typescript
interface Transition {
  id: string
  type: 'fade' | 'slide' | 'zoom' | 'wipe' | 'dissolve'
  duration: number
  easing: string
  parameters: TransitionParameters
}

interface SlideTransition extends Transition {
  type: 'slide'
  parameters: {
    direction: 'left' | 'right' | 'up' | 'down'
    softness: number  // 边缘柔化程度
  }
}
```

### 2.5 文字和字幕系统

#### 文字样式
```typescript
interface TextStyle {
  fontFamily: string
  fontSize: number
  fontWeight: 'normal' | 'bold' | '100' | '200' | ... | '900'
  fontStyle: 'normal' | 'italic'
  color: string
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
  shadow?: TextShadow
  alignment: 'left' | 'center' | 'right' | 'justify'
}

interface TextShadow {
  offsetX: number
  offsetY: number
  blur: number
  color: string
}
```

#### 字幕轨道
```typescript
interface SubtitleClip extends Clip {
  text: string
  style: TextStyle
  position: {
    x: number  // 相对于视频的位置
    y: number
    width: number
    height: number
  }
  animation?: TextAnimation
}

interface TextAnimation {
  type: 'fade-in' | 'slide-in' | 'typewriter' | 'bounce'
  duration: number
  delay: number
  parameters: Record<string, any>
}
```

## 3. 用户交互设计

### 3.1 快捷键支持

| 功能 | 快捷键 | 描述 |
|------|--------|------|
| 播放/暂停 | Space | 控制视频播放 |
| 剪切 | Ctrl+X | 剪切选中片段 |
| 复制 | Ctrl+C | 复制选中片段 |
| 粘贴 | Ctrl+V | 粘贴片段 |
| 撤销 | Ctrl+Z | 撤销操作 |
| 重做 | Ctrl+Y | 重做操作 |
| 分割 | S | 在播放头位置分割 |
| 删除 | Delete | 删除选中片段 |
| 全选 | Ctrl+A | 选择所有片段 |
| 缩放时间轴 | Ctrl+滚轮 | 缩放时间轴 |

### 3.2 拖拽操作

#### 素材拖拽
- 从素材库拖拽到时间轴
- 自动创建新的clip
- 智能轨道分配

#### 时间轴拖拽
- 片段移动和重排
- 跨轨道拖拽
- 磁性吸附对齐

#### 调整大小
- 拖拽片段边缘调整长度
- 保持宽高比选项
- 实时预览效果

### 3.3 右键菜单

```typescript
interface ContextMenuItem {
  label: string
  action: string
  shortcut?: string
  disabled?: boolean
  submenu?: ContextMenuItem[]
}

// 时间轴右键菜单
const timelineContextMenu: ContextMenuItem[] = [
  { label: '剪切', action: 'cut', shortcut: 'Ctrl+X' },
  { label: '复制', action: 'copy', shortcut: 'Ctrl+C' },
  { label: '粘贴', action: 'paste', shortcut: 'Ctrl+V' },
  { label: '删除', action: 'delete', shortcut: 'Delete' },
  { label: '分割', action: 'split', shortcut: 'S' },
  { 
    label: '添加效果', 
    action: 'add-effect',
    submenu: [
      { label: '滤镜', action: 'add-filter' },
      { label: '转场', action: 'add-transition' },
      { label: '音频效果', action: 'add-audio-effect' }
    ]
  }
]
```

## 4. 性能优化策略

### 4.1 渲染优化

#### 分层渲染
```typescript
interface RenderLayer {
  id: string
  type: 'background' | 'video' | 'effect' | 'overlay' | 'ui'
  zIndex: number
  clips: Clip[]
  needsUpdate: boolean
}

// 只重新渲染需要更新的层
const renderFrame = async (timestamp: number) => {
  const layers = timeline.layers.filter(layer => layer.needsUpdate)
  for (const layer of layers) {
    await renderLayer(layer, timestamp)
    layer.needsUpdate = false
  }
}
```

#### 预览质量分级
```typescript
interface PreviewSettings {
  quality: 'low' | 'medium' | 'high' | 'full'
  resolution: { width: number, height: number }
  frameRate: number
  enableEffects: boolean
}

// 根据性能动态调整预览质量
const adaptivePreview = {
  low: { resolution: { width: 480, height: 270 }, frameRate: 15 },
  medium: { resolution: { width: 720, height: 405 }, frameRate: 24 },
  high: { resolution: { width: 1280, height: 720 }, frameRate: 30 },
  full: { resolution: { width: 1920, height: 1080 }, frameRate: 60 }
}
```

### 4.2 缓存策略

#### 智能缓存
```typescript
interface CacheEntry {
  key: string
  data: ArrayBuffer | ImageData
  timestamp: number
  accessCount: number
  size: number
}

class VideoCache {
  private cache = new Map<string, CacheEntry>()
  private maxSize = 500 * 1024 * 1024  // 500MB
  
  async getFrame(clipId: string, timestamp: number): Promise<ImageData> {
    const key = `${clipId}_${timestamp}`
    const cached = this.cache.get(key)
    
    if (cached) {
      cached.accessCount++
      return cached.data as ImageData
    }
    
    // 生成并缓存帧
    const frame = await this.generateFrame(clipId, timestamp)
    this.setCache(key, frame)
    return frame
  }
}
```

## 5. 移动端适配

### 5.1 触摸操作

#### 手势支持
```typescript
interface TouchGesture {
  type: 'tap' | 'long-press' | 'pinch' | 'pan' | 'swipe'
  target: HTMLElement
  data: any
}

// 时间轴触摸操作
const timelineTouchHandler = {
  onTap: (gesture: TouchGesture) => {
    // 移动播放头
    movePlayhead(gesture.data.x)
  },
  
  onLongPress: (gesture: TouchGesture) => {
    // 显示上下文菜单
    showContextMenu(gesture.data.x, gesture.data.y)
  },
  
  onPinch: (gesture: TouchGesture) => {
    // 缩放时间轴
    zoomTimeline(gesture.data.scale)
  },
  
  onPan: (gesture: TouchGesture) => {
    // 拖拽片段
    dragClip(gesture.data.deltaX, gesture.data.deltaY)
  }
}
```

### 5.2 响应式布局

#### 断点设计
```scss
// 响应式断点
$breakpoints: (
  mobile: 320px,
  tablet: 768px,
  desktop: 1024px,
  large: 1440px
);

// 移动端布局
@media (max-width: 767px) {
  .video-editor {
    .timeline {
      height: 120px;  // 减小时间轴高度
    }
    
    .preview {
      height: 200px;  // 减小预览窗口
    }
    
    .panels {
      display: none;  // 隐藏侧边面板
      
      &.active {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1000;
        display: block;
      }
    }
  }
}
```

这个功能规划文档详细分析了剪映的核心功能，并结合ThunderHub现有的技术基础，制定了完整的实现方案。接下来我们可以开始具体的开发工作。
