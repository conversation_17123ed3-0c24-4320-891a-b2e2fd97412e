#!/usr/bin/env python3
"""
测试工作流真实数据修复效果
"""

import requests
import json
import sys

def test_workflow_data(backend_url="http://localhost:8000", task_id=None):
    """
    测试工作流数据是否为真实数据
    
    Args:
        backend_url: Backend服务地址
        task_id: 要测试的任务ID
    """
    try:
        if not task_id:
            # 先获取任务列表
            print("🔍 获取任务列表...")
            response = requests.get(f"{backend_url}/api/tasks/running", timeout=10)
            if response.status_code == 200:
                tasks_data = response.json()
                if tasks_data.get("success") and tasks_data.get("data"):
                    tasks = tasks_data["data"]
                    if tasks:
                        task_id = tasks[0]["task_id"]
                        print(f"   使用第一个任务: {task_id}")
                    else:
                        print("❌ 没有找到任务")
                        return False
                else:
                    print("❌ 获取任务列表失败")
                    return False
            else:
                print(f"❌ 获取任务列表失败: HTTP {response.status_code}")
                return False
        
        print(f"🔧 测试任务工作流数据: {task_id}")
        
        # 调用工作流API
        response = requests.get(
            f"{backend_url}/api/tasks/{task_id}/workflow",
            timeout=30
        )
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功!")
            print(f"   成功: {result.get('success')}")
            print(f"   数据源: {result.get('source')}")
            print(f"   消息: {result.get('message')}")
            
            if result.get("data"):
                data = result["data"]
                print(f"   工作流名称: {data.get('workflow_name')}")
                print(f"   当前步骤索引: {data.get('current_step_index')}")
                print(f"   总步骤数: {data.get('total_steps')}")
                print(f"   可恢复: {data.get('can_resume')}")
                
                steps = data.get("steps", [])
                print(f"   步骤详情:")
                
                # 检查是否为假数据
                fake_data_indicators = []
                
                for i, step in enumerate(steps):
                    status_icon = {
                        'pending': '⏳',
                        'running': '🔄',
                        'completed': '✅',
                        'failed': '❌',
                        'paused': '⏸️',
                        'skipped': '⏭️'
                    }.get(step.get('status'), '❓')
                    
                    step_name = step.get('name', '')
                    print(f"     {i+1}. {status_icon} {step_name} - {step.get('status')} ({step.get('progress', 0)}%)")
                    
                    # 检查假数据指标
                    if step_name in ["准备任务", "连接设备", "启动应用", "执行操作", "完成任务"]:
                        fake_data_indicators.append(f"默认步骤名称: {step_name}")
                    
                    if step.get('error_message'):
                        print(f"        错误: {step.get('error_message')}")
                
                # 分析数据源
                source = result.get('source', '')
                print(f"\n📊 数据源分析:")
                print(f"   数据源: {source}")
                
                if source == "core_real_status":
                    print("   ✅ 这是来自Core服务的真实执行状态")
                elif source == "real_config_with_final_status":
                    print("   ✅ 这是基于真实工作流配置的最终状态")
                elif source == "default_completed_status":
                    print("   ⚠️  这是使用默认步骤的已完成状态")
                elif source == "generated_basic_workflow":
                    print("   ❌ 这是生成的基础工作流（可能是假数据）")
                else:
                    print(f"   ❓ 未知数据源: {source}")
                
                # 假数据检测
                if fake_data_indicators:
                    print(f"\n⚠️  检测到可能的假数据指标:")
                    for indicator in fake_data_indicators:
                        print(f"     - {indicator}")
                    
                    if len(fake_data_indicators) >= 3:
                        print("   ❌ 这很可能是假数据！")
                        return False
                    else:
                        print("   ⚠️  可能包含部分假数据")
                        return True
                else:
                    print("   ✅ 未检测到明显的假数据指标")
                    return True
            
            return True
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("工作流真实数据测试工具")
    print("=" * 60)
    
    # 解析命令行参数
    backend_url = "http://localhost:8000"
    task_id = None
    
    if len(sys.argv) > 1:
        backend_url = sys.argv[1]
    if len(sys.argv) > 2:
        task_id = sys.argv[2]
    
    print(f"使用参数:")
    print(f"  Backend地址: {backend_url}")
    print(f"  任务ID: {task_id or '自动获取'}")
    print()
    
    # 执行测试
    success = test_workflow_data(backend_url, task_id)
    
    if success:
        print("\n🎉 工作流数据测试完成!")
        print("请查看上面的分析结果，确认数据是否为真实数据。")
    else:
        print("\n💥 检测到假数据或测试失败！")
        print("请检查工作流API的实现，确保返回真实的工作流数据。")
        sys.exit(1)

if __name__ == "__main__":
    main()
