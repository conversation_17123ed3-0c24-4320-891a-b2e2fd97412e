<template>
  <el-dialog
    v-model="dialogVisible"
    title="对标账号详情"
    width="800px"
    @close="handleClose"
  >
    <div v-if="benchmark" class="benchmark-detail">
      <!-- 基本信息 -->
      <el-card class="info-card" style="margin-bottom: 20px;">
        <template #header>
          <div class="card-header">
            <span>📋 基本信息</span>
            <el-button type="primary" size="small" @click="openAccountUrl">
              <el-icon><Link /></el-icon>
              访问账号
            </el-button>
          </div>
        </template>
        
        <div class="account-info">
          <div class="account-header">
            <div class="account-avatar">
              <img v-if="benchmark.avatar" :src="benchmark.avatar" :alt="benchmark.account_name" />
              <div v-else class="avatar-placeholder">
                {{ getAccountInitial(benchmark.account_name) }}
              </div>
            </div>
            <div class="account-details">
              <h3 class="account-name">{{ benchmark.account_name }}</h3>
              <div class="account-meta">
                <el-tag type="info">{{ getPlatformDisplayName(benchmark.platform) }}</el-tag>
                <el-tag
                  :type="getBenchmarkTypeColor(benchmark.benchmark_type)"
                  style="margin-left: 8px"
                >
                  {{ getBenchmarkTypeText(benchmark.benchmark_type) }}
                </el-tag>
                <div class="priority-stars">
                  {{ '★'.repeat(benchmark.priority || 0) }}{{ '☆'.repeat(5 - (benchmark.priority || 0)) }}
                </div>
              </div>
              <div v-if="benchmark.description" class="account-description">
                {{ benchmark.description }}
              </div>
            </div>
          </div>
        </div>

        <el-descriptions :column="2" border style="margin-top: 20px">
          <el-descriptions-item label="账号链接">
            <a :href="benchmark.account_url" target="_blank" class="account-link">
              {{ benchmark.account_url }}
            </a>
          </el-descriptions-item>
          <el-descriptions-item label="关联账号">
            <div v-if="benchmark.ourAccount">
              {{ formatAccountName(benchmark.ourAccount) }}
            </div>
            <div v-else-if="benchmark.our_account_info && benchmark.our_account_info.status !== 'not_found'">
              {{ formatOurAccountName(benchmark.our_account_info) }}
            </div>
            <el-tag v-else type="warning" size="small">未关联</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(benchmark.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatTime(benchmark.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="标签" :span="2">
            <div v-if="benchmark.tags && benchmark.tags.length > 0">
              <el-tag
                v-for="tag in benchmark.tags"
                :key="tag"
                size="small"
                style="margin-right: 8px"
              >
                {{ tag }}
              </el-tag>
            </div>
            <span v-else class="no-data">无标签</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 数据概览 -->
      <el-card v-if="benchmark.account_data" class="info-card" style="margin-bottom: 20px;">
        <template #header>
          <div class="card-header">
            <span>📊 数据概览</span>
            <div class="data-update-time">
              <span v-if="benchmark.account_data.updated_at">
                更新时间: {{ formatTime(benchmark.account_data.updated_at) }}
              </span>
            </div>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8" v-if="benchmark.account_data.followers">
            <div class="data-item">
              <div class="data-icon">👥</div>
              <div class="data-content">
                <div class="data-value">{{ formatNumber(benchmark.account_data.followers) }}</div>
                <div class="data-label">粉丝数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8" v-if="benchmark.account_data.posts_count">
            <div class="data-item">
              <div class="data-icon">📝</div>
              <div class="data-content">
                <div class="data-value">{{ formatNumber(benchmark.account_data.posts_count) }}</div>
                <div class="data-label">作品数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8" v-if="benchmark.account_data.engagement_rate">
            <div class="data-item">
              <div class="data-icon">📈</div>
              <div class="data-content">
                <div class="data-value">{{ (benchmark.account_data.engagement_rate * 100).toFixed(1) }}%</div>
                <div class="data-label">互动率</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <div v-if="benchmark.account_data.bio" style="margin-top: 20px">
          <h4>账号简介</h4>
          <p class="account-bio">{{ benchmark.account_data.bio }}</p>
        </div>
      </el-card>

      <!-- 采集信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>🎯 采集信息</span>
            <el-button type="success" size="small" @click="createCollectTask">
              <el-icon><VideoPlay /></el-icon>
              创建采集任务
            </el-button>
          </div>
        </template>
        
        <div class="collect-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="预计下载路径">
              <div class="download-path">
                <div class="path-preview">{{ getDownloadPath() }}</div>
                <div class="path-explanation">
                  <small>基础路径/我们的平台/我们的账号/对标账号名称/发布月份/</small>
                </div>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="最后采集时间">
              <span v-if="benchmark.last_collect_time">
                {{ formatTime(benchmark.last_collect_time) }}
              </span>
              <span v-else class="no-data">从未采集</span>
            </el-descriptions-item>
            <el-descriptions-item label="采集状态">
              <el-tag :type="getCollectStatusColor(benchmark.collect_status)">
                {{ getCollectStatusText(benchmark.collect_status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="采集内容数">
              <span>{{ benchmark.collected_count || 0 }} 个</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="editBenchmark">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, VideoPlay, Edit } from '@element-plus/icons-vue'
import { format } from 'date-fns'

// Props
const props = defineProps<{
  visible: boolean
  benchmark: any
}>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const openAccountUrl = () => {
  if (props.benchmark?.account_url) {
    window.open(props.benchmark.account_url, '_blank')
  }
}

const createCollectTask = () => {
  ElMessage.info('创建采集任务功能开发中...')
  // TODO: 实现创建采集任务功能
}

const editBenchmark = () => {
  ElMessage.info('编辑功能开发中...')
  // TODO: 实现编辑功能
}

const getDownloadPath = () => {
  if (!props.benchmark) return ''
  
  const basePath = 'H:\\PublishSystem\\'
  const currentMonth = new Date().toISOString().slice(0, 7)
  
  // 这里需要从对标账号中获取关联的我们的账号信息
  const ourAccount = props.benchmark.ourAccount
  if (ourAccount) {
    return `${basePath}${ourAccount.platform}\\${ourAccount.name}\\${props.benchmark.account_name}\\${currentMonth}\\`
  }
  
  return `${basePath}平台名称\\我们的账号\\${props.benchmark.account_name}\\${currentMonth}\\`
}

// 工具方法
const formatAccountName = (account: any) => {
  if (!account) return '未知账号'

  // 获取平台名称
  const platform = account.platform_name || account.platform || '未知平台'

  // 获取账号名称，优先使用display_name，然后是name，最后是username
  let name = account.display_name || account.name
  // 修复：检查空字符串和null/undefined
  if (!name || name.trim() === '') {
    if (account.username && account.username.trim() !== '') {
      // 如果username是邮箱格式，取@前面的部分
      name = account.username.includes('@') ? account.username.split('@')[0] : account.username
    } else {
      name = '未命名账号'
    }
  }

  return `${platform}-${name}`
}

const formatOurAccountName = (accountInfo: any) => {
  if (!accountInfo) return '未知账号'

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = accountInfo.display_name
  // 修复：检查空字符串和null/undefined
  if (!name || name.trim() === '') {
    if (accountInfo.username && accountInfo.username.trim() !== '') {
      // 如果username是邮箱格式，取@前面的部分
      name = accountInfo.username.includes('@') ? accountInfo.username.split('@')[0] : accountInfo.username
    } else {
      name = '未命名账号'
    }
  }

  return name
}

const getAccountInitial = (name: string) => {
  return name ? name.charAt(0).toUpperCase() : '?'
}

const getPlatformDisplayName = (platform: string) => {
  const platformMap = {
    youtube: 'YouTube',
    tiktok: 'TikTok',
    instagram: 'Instagram',
    douyin: '抖音',
    weibo: '微博',
    xiaohongshu: '小红书',
    kuaishou: '快手',
    bilibili: 'B站'
  }
  return platformMap[platform] || platform
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type] || 'info'
}

const getBenchmarkTypeText = (type: string) => {
  const textMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return textMap[type] || type
}

const getCollectStatusColor = (status: string) => {
  const colorMap = {
    never: 'info',
    collecting: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return colorMap[status] || 'info'
}

const getCollectStatusText = (status: string) => {
  const textMap = {
    never: '从未采集',
    collecting: '采集中',
    completed: '采集完成',
    failed: '采集失败'
  }
  return textMap[status] || '未知'
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (time: string) => {
  if (!time) return '未知'
  try {
    return format(new Date(time), 'yyyy-MM-dd HH:mm:ss')
  } catch {
    return time
  }
}
</script>

<style scoped>
.benchmark-detail {
  max-height: 600px;
  overflow-y: auto;
}

.info-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.account-info {
  padding: 10px 0;
}

.account-header {
  display: flex;
  align-items: flex-start;
}

.account-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  overflow: hidden;
  border: 2px solid #e4e7ed;
}

.account-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  color: #666;
  font-size: 24px;
  font-weight: bold;
}

.account-details {
  flex: 1;
}

.account-name {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.account-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.priority-stars {
  color: #f39c12;
  font-size: 14px;
}

.account-description {
  color: #666;
  line-height: 1.5;
}

.account-link {
  color: #409eff;
  text-decoration: none;
  word-break: break-all;
}

.account-link:hover {
  text-decoration: underline;
}

.no-data {
  color: #909399;
  font-style: italic;
}

.data-update-time {
  font-size: 12px;
  color: #909399;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.data-icon {
  font-size: 24px;
  margin-right: 10px;
}

.data-content {
  flex: 1;
}

.data-value {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1;
}

.data-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.account-bio {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin: 0;
  line-height: 1.6;
  color: #666;
}

.collect-info {
  padding: 10px 0;
}

.download-path {
  font-family: monospace;
}

.path-preview {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  word-break: break-all;
}

.path-explanation {
  margin-top: 5px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
