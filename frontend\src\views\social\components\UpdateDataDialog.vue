<template>
  <el-dialog
    v-model="dialogVisible"
    title="更新账号数据"
    width="500px"
    @close="handleClose"
  >
    <div v-if="benchmark" class="update-data-form">
      <div class="account-info">
        <h4>{{ benchmark.account_name }}</h4>
        <p>{{ benchmark.platform }} - {{ getBenchmarkTypeText(benchmark.benchmark_type) }}</p>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="粉丝数" prop="followers">
          <el-input-number
            v-model="form.followers"
            :min="0"
            :max="*********"
            placeholder="输入粉丝数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="关注数" prop="following">
          <el-input-number
            v-model="form.following"
            :min="0"
            :max="*********"
            placeholder="输入关注数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="作品数" prop="posts_count">
          <el-input-number
            v-model="form.posts_count"
            :min="0"
            :max="*********"
            placeholder="输入作品数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="平均观看数" prop="avg_views">
          <el-input-number
            v-model="form.avg_views"
            :min="0"
            :max="*********"
            placeholder="输入平均观看数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="平均点赞数" prop="avg_likes">
          <el-input-number
            v-model="form.avg_likes"
            :min="0"
            :max="*********"
            placeholder="输入平均点赞数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="互动率" prop="engagement_rate">
          <el-input-number
            v-model="form.engagement_rate"
            :min="0"
            :max="1"
            :step="0.001"
            :precision="3"
            placeholder="输入互动率 (0-1)"
            style="width: 100%"
          />
          <div class="form-tip">
            互动率范围：0-1，例如 0.05 表示 5%
          </div>
        </el-form-item>

        <el-form-item label="增长率" prop="growth_rate">
          <el-input-number
            v-model="form.growth_rate"
            :min="-1"
            :max="10"
            :step="0.001"
            :precision="3"
            placeholder="输入增长率"
            style="width: 100%"
          />
          <div class="form-tip">
            增长率，例如 0.1 表示 10% 增长，-0.05 表示 5% 下降
          </div>
        </el-form-item>

        <el-form-item label="最后发布时间" prop="last_post_date">
          <el-date-picker
            v-model="form.last_post_date"
            type="datetime"
            placeholder="选择最后发布时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="简介" prop="bio">
          <el-input
            v-model="form.bio"
            type="textarea"
            :rows="3"
            placeholder="输入账号简介"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="current-data" v-if="benchmark.account_data">
        <h5>当前数据</h5>
        <div class="data-grid">
          <div class="data-item" v-if="benchmark.account_data.followers">
            <span class="label">粉丝数:</span>
            <span class="value">{{ formatNumber(benchmark.account_data.followers) }}</span>
          </div>
          <div class="data-item" v-if="benchmark.account_data.posts_count">
            <span class="label">作品数:</span>
            <span class="value">{{ formatNumber(benchmark.account_data.posts_count) }}</span>
          </div>
          <div class="data-item" v-if="benchmark.account_data.engagement_rate">
            <span class="label">互动率:</span>
            <span class="value">{{ (benchmark.account_data.engagement_rate * 100).toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="autoFillCurrentData" v-if="benchmark.account_data">
          填入当前数据
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          更新数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { updateBenchmarkAccountData } from '@/api/content'

// Props
const props = defineProps<{
  visible: boolean
  benchmark: any
}>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const form = reactive({
  followers: null,
  following: null,
  posts_count: null,
  avg_views: null,
  avg_likes: null,
  engagement_rate: null,
  growth_rate: null,
  last_post_date: null,
  bio: ''
})

// 表单验证规则
const rules: FormRules = {
  followers: [
    { type: 'number', min: 0, message: '粉丝数不能小于0', trigger: 'blur' }
  ],
  following: [
    { type: 'number', min: 0, message: '关注数不能小于0', trigger: 'blur' }
  ],
  posts_count: [
    { type: 'number', min: 0, message: '作品数不能小于0', trigger: 'blur' }
  ],
  engagement_rate: [
    { type: 'number', min: 0, max: 1, message: '互动率范围为0-1', trigger: 'blur' }
  ]
}

// 方法
const getBenchmarkTypeText = (type: string) => {
  const textMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return textMap[type] || type
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const autoFillCurrentData = () => {
  if (props.benchmark?.account_data) {
    const data = props.benchmark.account_data
    Object.assign(form, {
      followers: data.followers || null,
      following: data.following || null,
      posts_count: data.posts_count || null,
      avg_views: data.avg_views || null,
      avg_likes: data.avg_likes || null,
      engagement_rate: data.engagement_rate || null,
      growth_rate: data.growth_rate || null,
      last_post_date: data.last_post_date || null,
      bio: data.bio || ''
    })
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true

    const benchmarkId = props.benchmark._id || props.benchmark.id
    if (!benchmarkId) {
      throw new Error('对标账号ID不存在')
    }

    // 过滤掉null值
    const updateData = Object.fromEntries(
      Object.entries(form).filter(([_, value]) => value !== null && value !== '')
    )

    await updateBenchmarkAccountData(benchmarkId, updateData)

    ElMessage.success('更新账号数据成功')
    emit('success')
    handleClose()

  } catch (error) {
    console.error('更新账号数据失败:', error)
    ElMessage.error('更新账号数据失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    followers: null,
    following: null,
    posts_count: null,
    avg_views: null,
    avg_likes: null,
    engagement_rate: null,
    growth_rate: null,
    last_post_date: null,
    bio: ''
  })
}

// 监听对话框打开
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm()
  }
})
</script>

<style scoped>
.update-data-form {
  max-height: 600px;
  overflow-y: auto;
}

.account-info {
  text-align: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.account-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.account-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.current-data {
  margin-top: 20px;
  padding: 15px;
  background: #f0f2f5;
  border-radius: 8px;
}

.current-data h5 {
  margin: 0 0 10px 0;
  color: #606266;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.data-item .label {
  color: #909399;
  font-size: 12px;
}

.data-item .value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
