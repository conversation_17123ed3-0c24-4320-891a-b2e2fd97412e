import { io, Socket } from 'socket.io-client';
import { ElNotification } from 'element-plus';
import { getCurrentUser } from '@/api/auth';
import { Device, BatchOperationResult } from '@/api/device';

// 定义 Socket 事件类型
interface AuthSocketEvents {
  connect: () => void;
  connect_error: (err: Error) => void;
}

interface DeviceSocketEvents {
  connect: () => void;
  connect_error: (err: Error) => void;
  device_status: (data: { device_id: string; [key: string]: any }) => void;
  get_devices: (callback: (devices: Device[]) => void) => void;
  start_device: (id: string, callback: () => void) => void;
  stop_device: (id: string, callback: () => void) => void;
  batch_start_devices: (ids: string[], callback: (results: BatchOperationResult[]) => void) => void;
  batch_stop_devices: (ids: string[], callback: (results: BatchOperationResult[]) => void) => void;
  batch_install_apps: (data: { ids: string[]; appPath: string }, callback: (results: BatchOperationResult[]) => void) => void;
  batch_run_scripts: (data: { ids: string[]; script: string }, callback: (results: BatchOperationResult[]) => void) => void;

  // 任务相关事件
  subscribe_task: (taskId: string) => void;
  unsubscribe_task: (taskId: string) => void;
  task_status_update: (data: {
    task_id: string;
    status: string;
    progress: number;
    device_usage?: {
      cpu: number;
      memory: number;
      network: string;
    };
    logs?: Array<{
      message: string;
      level: string;
      timestamp: string;
    }>;
    [key: string]: any
  }) => void;
}

// 认证 WebSocket 通道 - 延迟初始化
let authSocket: Socket<AuthSocketEvents> | null = null;

// 获取认证 Socket 连接
export function getAuthSocket(): Socket<AuthSocketEvents> {
  if (!authSocket) {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('请先登录系统');
    }

    authSocket = io(import.meta.env.VITE_WS_URL, {
      path: '/auth-socket.io',
      transports: ['websocket'],
      auth: { token },
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 3000,
      timeout: 10000, // 连接超时 10 秒
      withCredentials: true,
      autoConnect: false // 不自动连接，需要手动连接
    });

    // 认证 Socket 事件处理
    authSocket.on('connect', () => {
      console.log('Auth socket connected successfully');
    });

    authSocket.on('connect_error', (err) => {
      console.error('Auth socket error:', err.message);
      if (err.message.includes('invalid token') || err.message.includes('需要认证')) {
        localStorage.removeItem('token');
        ElNotification.error('认证失效，请重新登录');
        window.location.href = '/login';
      } else {
        ElNotification.error('认证通道连接失败');
      }
    });
  }

  return authSocket;
}

// 设备 WebSocket 通道
export class DeviceSocketManager {
  private static instance: DeviceSocketManager;
  private socket: Socket<DeviceSocketEvents> | null = null;

  private constructor() {}

  // 单例模式
  public static getInstance(): DeviceSocketManager {
    if (!DeviceSocketManager.instance) {
      DeviceSocketManager.instance = new DeviceSocketManager();
    }
    return DeviceSocketManager.instance;
  }

  // 确保 Socket 连接
  private async ensureConnected(): Promise<void> {
    if (this.socket?.connected) return;

    // 1. 获取 token
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('请先登录系统');
    }

    // 2. 验证 token 有效性
    try {
      await getCurrentUser();
    } catch (err) {
      localStorage.removeItem('token');
      ElNotification.error('登录已过期，请重新登录');
      throw new Error('登录已过期，请重新登录');
    }

    // 3. 建立新的 Socket 连接
    if (!this.socket || !this.socket.connected) {
      this.socket = io(import.meta.env.VITE_WS_URL || window.location.origin, {
        path: '/device-socket.io', // 使用设备WebSocket路径
        transports: ['websocket', 'polling'], // 允许降级到polling
        auth: { token }, // 移除硬编码的 Bearer 前缀
        reconnection: true,
        reconnectionAttempts: 15, // 增加重试次数从10次到15次
        reconnectionDelay: 2000, // 初始重连间隔从1秒增加到2秒
        reconnectionDelayMax: 20000, // 最大重连间隔从10秒增加到20秒
        timeout: 60000, // 增加连接超时时间从20秒到60秒
        randomizationFactor: 0.5,
        autoConnect: false,
        perMessageDeflate: {
          threshold: 1024 // 1KB
        } // 启用消息压缩
      });

      // 错误处理
      this.socket.on('connect_error', (err) => {
        console.error('Device socket error:', err.message);
        if (err.message.includes('invalid token')) {
          localStorage.removeItem('token');
          ElNotification.error('认证失效，请重新登录');
          window.location.href = '/login';
        } else {
          ElNotification.error('设备通道连接失败');
        }
      });

      // 连接成功
      this.socket.on('connect', () => {
        console.log('Device socket connected successfully');
      });

      // 添加重试逻辑
      let retries = 0;
      const maxRetries = 3;

      while (retries < maxRetries) {
        try {
          await new Promise<void>((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('连接超时'));
            }, 20000); // 增加超时时间到20秒

            const connectHandler = () => {
              clearTimeout(timeout);
              this.socket!.off('connect_error', errorHandler);
              resolve();
            };

            const errorHandler = (err: Error) => {
              clearTimeout(timeout);
              this.socket!.off('connect', connectHandler);
              reject(err);
            };

            this.socket!.once('connect', connectHandler);
            this.socket!.once('connect_error', errorHandler);

            // 确保断开之前的连接
            if (this.socket!.connected) {
              this.socket!.disconnect();
            }

            // 连接
            this.socket!.connect();
          });

          // 如果成功连接，跳出循环
          console.log('WebSocket连接成功');
          break;
        } catch (err) {
          retries++;
          console.warn(`WebSocket连接失败，重试 ${retries}/${maxRetries}`, err);

          if (retries >= maxRetries) {
            console.error('WebSocket连接失败，已达到最大重试次数');
            throw err;
          }

          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
  }

  // 获取 Socket 实例
  public async getSocket(): Promise<Socket<DeviceSocketEvents>> {
    try {
      await this.ensureConnected();
      return this.socket!;
    } catch (err) {
      console.error('获取 Socket 连接失败:', err);
      throw err;
    }
  }

  // 执行 WebSocket 操作
  public async withSocket<T>(action: (socket: Socket<DeviceSocketEvents>) => Promise<T>): Promise<T> {
    try {
      const socket = await this.getSocket();
      return await action(socket);
    } catch (err) {
      console.error('WebSocket 操作失败:', err);
      throw err;
    }
  }

  // 清理 Socket 连接
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
}




// 获取设备Socket连接
export async function getDeviceSocket(): Promise<Socket<DeviceSocketEvents>> {
  return await deviceSocketManager.getSocket();
}
// 导出单例实例
export const deviceSocketManager = DeviceSocketManager.getInstance();