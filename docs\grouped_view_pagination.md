# 分组视图分页功能设计

## 🎯 设计目标

为分组视图添加合理的分页显示，既能展示完整的分组关系，又能保证良好的性能和用户体验。

## 📊 分页策略

### 1. 数据加载策略
- **分组视图**: 加载所有数据(最多1000条)用于完整的分组计算
- **列表视图**: 继续使用传统分页(每页20-100条)

### 2. 分页显示策略
- **分组视图**: 按分组进行分页，每页显示5-50个分组
- **列表视图**: 按单个对标账号分页

## 🔧 技术实现

### 1. 状态管理
```javascript
// 列表视图分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 分组视图分页
const groupCurrentPage = ref(1)
const groupPageSize = ref(10) // 每页显示10个分组
const groupTotalCount = ref(0)
```

### 2. 数据计算
```javascript
// 完整分组数据
const groupedData = computed(() => {
  // 1. 加载所有数据进行分组计算
  // 2. 更新分组总数 groupTotalCount.value
  // 3. 返回所有分组
})

// 分页后的分组数据
const paginatedGroupedData = computed(() => {
  if (viewMode.value !== 'grouped') {
    return groupedData.value
  }

  const allGroups = groupedData.value
  const start = (groupCurrentPage.value - 1) * groupPageSize.value
  const end = start + groupPageSize.value
  
  return allGroups.slice(start, end)
})
```

### 3. 分页组件
```vue
<!-- 分组视图分页 -->
<div v-if="viewMode === 'grouped'" class="grouped-pagination-container">
  <div class="grouped-stats">
    <el-text type="info">
      共 {{ groupTotalCount }} 个分组，{{ benchmarkAccounts.length }} 个对标账号
      (第 {{ groupCurrentPage }} 页，每页 {{ groupPageSize }} 个分组)
    </el-text>
  </div>
  
  <el-pagination
    v-model:current-page="groupCurrentPage"
    v-model:page-size="groupPageSize"
    :total="groupTotalCount"
    :page-sizes="[5, 10, 20, 50]"
    layout="total, sizes, prev, pager, next, jumper"
    @current-change="handleGroupPageChange"
    @size-change="handleGroupSizeChange"
  />
</div>
```

## 📈 用户体验优化

### 1. 智能分页大小
- **默认**: 每页10个分组
- **可选**: 5, 10, 20, 50个分组
- **自适应**: 根据数据量自动建议合适的分页大小

### 2. 状态保持
- 视图切换时保持各自的分页状态
- 刷新时重置到第一页
- 搜索时自动回到第一页

### 3. 性能优化
- 前端分页，避免频繁请求后端
- 虚拟滚动(未来可考虑)
- 懒加载分组内容(未来可考虑)

## 🔍 数据流程

### 1. 初始加载
```
用户打开页面 
→ 加载我们的账号(100条) 
→ 加载对标账号(1000条) 
→ 计算分组关系 
→ 显示第一页分组(10个)
```

### 2. 视图切换
```
列表视图 → 分组视图:
- 重新加载所有对标账号数据
- 计算完整分组关系
- 显示第一页分组

分组视图 → 列表视图:
- 重置到第一页
- 加载分页数据
```

### 3. 分页操作
```
分组视图分页:
- 纯前端计算，无需请求后端
- 即时响应，用户体验好

列表视图分页:
- 请求后端获取对应页数据
- 传统分页体验
```

## 📊 性能指标

### 1. 数据量限制
- **最大加载**: 1000条对标账号
- **分组上限**: 理论上无限制(取决于账号数量)
- **单页显示**: 5-50个分组

### 2. 响应时间
- **分组计算**: < 100ms (1000条数据)
- **分页切换**: < 10ms (纯前端计算)
- **视图切换**: < 500ms (包含数据加载)

### 3. 内存使用
- **数据缓存**: 适中(所有数据在内存中)
- **DOM节点**: 较少(只渲染当前页)
- **计算开销**: 较低(computed缓存)

## 🎨 UI/UX 设计

### 1. 分页信息显示
```
共 25 个分组，156 个对标账号 (第 2 页，每页 10 个分组)
```

### 2. 分页控件
- 总数显示
- 页码选择器
- 每页大小选择器
- 上一页/下一页按钮
- 跳转到指定页

### 3. 视觉区分
- 分组视图使用不同的分页样式
- 清晰的数据统计信息
- 友好的加载状态提示

## 🔮 未来扩展

### 1. 高级功能
- 分组内搜索
- 分组排序选项
- 分组筛选功能
- 批量操作支持

### 2. 性能优化
- 虚拟滚动支持
- 懒加载分组内容
- 智能预加载

### 3. 用户体验
- 分页状态记忆
- 键盘快捷键支持
- 移动端适配

## ✅ 验收标准

1. **功能完整性**
   - ✅ 分组视图支持分页
   - ✅ 分页信息准确显示
   - ✅ 分页操作响应正常

2. **数据一致性**
   - ✅ 分组关系完整准确
   - ✅ 分页前后数据一致
   - ✅ 视图切换数据同步

3. **性能表现**
   - ✅ 分页切换流畅
   - ✅ 大数据量处理正常
   - ✅ 内存使用合理

4. **用户体验**
   - ✅ 操作直观易懂
   - ✅ 状态反馈及时
   - ✅ 错误处理友好
