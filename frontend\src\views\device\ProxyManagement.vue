<template>
  <div class="proxy-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>代理IP管理</span>
          <div style="display: flex; gap: 10px; align-items: center">
            <el-button type="primary" @click="showAddDialog">添加代理IP</el-button>
            <el-button type="success" @click="showBatchImportDialog">批量导入</el-button>
            <el-button @click="refreshList">刷新</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-container" style="margin-bottom: 20px;">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filterRegion" placeholder="选择地区" clearable @change="handleFilterChange">
              <el-option label="全部地区" value="" />
              <el-option
                v-for="region in regions"
                :key="region"
                :label="region"
                :value="region"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterStatus" placeholder="选择状态" clearable @change="handleFilterChange">
              <el-option label="全部状态" value="" />
              <el-option label="活跃" value="active" />
              <el-option label="已过期" value="expired" />
              <el-option label="停用" value="inactive" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterDevice" placeholder="选择设备" clearable @change="handleFilterChange">
              <el-option label="全部设备" value="" />
              <el-option
                v-for="device in activeDevices"
                :key="device.id"
                :label="device.name"
                :value="device.id"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="warning" @click="showExpiringProxies">即将到期</el-button>
            <el-button type="info" @click="showStats">统计信息</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 代理IP列表 -->
      <el-table
        :data="filteredProxyList"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="region" label="地区" width="80" />
        <el-table-column prop="ip_address" label="IP地址" width="140" />
        <el-table-column prop="v2ray_config" label="v2rayN配置" width="300">
          <template #default="scope">
            <div v-if="scope.row.v2ray_config" class="config-cell">
              <el-input
                :value="scope.row.v2ray_config"
                readonly
                size="small"
                style="margin-bottom: 5px;"
              />
              <el-button
                size="small"
                type="primary"
                @click="copyToClipboard(scope.row.v2ray_config, 'v2rayN配置')"
              >
                复制
              </el-button>
            </div>
            <span v-else class="text-gray">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="socks_config" label="socks配置" width="250">
          <template #default="scope">
            <div v-if="scope.row.socks_config" class="config-cell">
              <el-input
                :value="scope.row.socks_config"
                readonly
                size="small"
                style="margin-bottom: 5px;"
              />
              <el-button
                size="small"
                type="primary"
                @click="copyToClipboard(scope.row.socks_config, 'socks配置')"
              >
                复制
              </el-button>
            </div>
            <span v-else class="text-gray">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" width="200">
          <template #default="scope">
            <span v-if="scope.row.notes">{{ scope.row.notes }}</span>
            <span v-else class="text-gray">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="provider" label="服务商" width="120" />
        <el-table-column prop="expire_date" label="到期时间" width="120">
          <template #default="scope">
            <span v-if="scope.row.expire_date" :class="getExpireClass(scope.row.expire_date)">
              {{ formatDate(scope.row.expire_date) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="payment_card" label="付款卡" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="associated_devices" label="关联设备" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.associated_devices.length > 0" type="info">
              {{ scope.row.associated_devices.length }}台设备
            </el-tag>
            <span v-else>未关联</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="info" @click="showDeviceAssociation(scope.row)">关联设备</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 20px; text-align: right;">
        <span>共 {{ total }} 条记录</span>
      </div>
    </el-card>

    <!-- 添加/编辑代理IP对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="地区" prop="region">
              <el-input v-model="form.region" placeholder="如：HK、US、JP" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ip_address">
              <el-input v-model="form.ip_address" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="端口" prop="port">
              <el-input-number v-model="form.port" :min="1" :max="65535" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代理类型" prop="proxy_type">
              <el-select v-model="form.proxy_type" style="width: 100%">
                <el-option label="vless" value="vless" />
                <el-option label="http" value="http" />
                <el-option label="socks5" value="socks5" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名">
              <el-input v-model="form.username" placeholder="可选" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码">
              <el-input v-model="form.password" type="password" placeholder="可选" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="v2rayN配置">
          <el-input
            v-model="form.v2ray_config"
            type="textarea"
            :rows="3"
            placeholder="如：vless://d5d80529-f6d8-4127-b81e-7b02eb032eb8@**************:16881?type=ws&security=none&path=%2F#B-US-1-2-16-KR01-HK"
          />
        </el-form-item>
        <el-form-item label="socks配置">
          <el-input
            v-model="form.socks_config"
            type="textarea"
            :rows="2"
            placeholder="如：**************:16882:a0XAHFvWEM:WNlVbQfrSi"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="到期时间">
              <el-date-picker
                v-model="form.expire_date"
                type="date"
                placeholder="选择到期时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务商">
              <el-input v-model="form.provider" placeholder="可选" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="付款卡信息">
          <el-input v-model="form.payment_card" placeholder="如：招商 6017" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" v-if="isEdit">
              <el-select v-model="form.status" style="width: 100%">
                <el-option label="活跃" value="active" />
                <el-option label="已过期" value="expired" />
                <el-option label="停用" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="2"
            placeholder="可选备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      title="批量导入代理IP"
      v-model="batchImportVisible"
      width="800px"
    >
      <div style="margin-bottom: 20px;">
        <el-alert
          title="导入格式说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>请按照以下格式输入代理IP信息，每行一个：</p>
            <p><strong>地区:IP地址:端口:类型:用户名:密码:v2ray配置:socks配置:到期时间:付款卡:服务商</strong></p>
            <p>示例：</p>
            <p>HK:**************:16881:vless:a0XAHFvWEM:WNlVbQfrSi:vless://d5d80529...#B-US-1-2-16-KR01-HK:**************:16882:a0XAHFvWEM:WNlVbQfrSi:2026-03-15:兴业 3509:某服务商</p>
          </template>
        </el-alert>
      </div>
      <el-input
        v-model="batchImportText"
        type="textarea"
        :rows="10"
        placeholder="请输入代理IP信息，每行一个"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchImportVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchImport">导入</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 设备关联对话框 -->
    <el-dialog
      title="设备关联管理"
      v-model="deviceAssociationVisible"
      width="600px"
    >
      <div v-if="currentProxy">
        <p><strong>代理IP：</strong>{{ currentProxy.ip_address }}:{{ currentProxy.port }} ({{ currentProxy.region }})</p>
        <div style="margin: 20px 0;">
          <el-button type="primary" @click="showAddDeviceDialog">添加设备关联</el-button>
        </div>
        <el-table
          :data="currentProxy.associated_devices"
          style="width: 100%"
          :empty-text="currentProxy.associated_devices.length === 0 ? '暂无关联设备' : ''"
        >
          <el-table-column prop="device_name" label="设备名称" width="200" />
          <el-table-column prop="device_id" label="设备ID" width="120" />
          <el-table-column prop="device_status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.device_status === 'running' ? 'success' : 'warning'">
                {{ scope.row.device_status === 'running' ? '运行中' : scope.row.device_status === 'starting' ? '启动中' : scope.row.device_status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="device_type" label="设备类型" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                @click="handleRemoveDeviceAssociation(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态提示 -->
        <div v-if="currentProxy.associated_devices.length === 0" style="text-align: center; padding: 20px; color: #909399;">
          <el-icon size="48" style="margin-bottom: 16px;"><Connection /></el-icon>
          <p>该代理IP暂未关联任何设备</p>
          <p style="font-size: 12px;">点击上方"添加设备关联"按钮开始关联设备</p>
        </div>
      </div>
    </el-dialog>

    <!-- 添加设备关联对话框 -->
    <el-dialog
      title="选择要关联的设备"
      v-model="addDeviceDialogVisible"
      width="700px"
    >
      <div style="margin-bottom: 20px;">
        <el-alert
          :title="`找到 ${activeDevices.length} 个关联了活跃账号的设备`"
          description="只显示关联了状态为'活跃'的社交媒体账号的设备"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <el-table
        :data="activeDevices"
        style="width: 100%"
        v-loading="loadingActiveDevices"
        @selection-change="handleDeviceSelectionChange"
        :empty-text="loadingActiveDevices ? '正在加载设备列表...' : '暂无活跃设备'"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="设备名称" width="200" />
        <el-table-column prop="id" label="设备ID" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'running' ? 'success' : 'warning'">
              {{ scope.row.status === 'running' ? '运行中' : '启动中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="设备类型" width="120" />
        <el-table-column prop="core_id" label="Core服务" />
      </el-table>

      <!-- 空状态提示 -->
      <div v-if="!loadingActiveDevices && activeDevices.length === 0" style="text-align: center; padding: 40px; color: #909399;">
        <el-icon size="64" style="margin-bottom: 16px;"><Monitor /></el-icon>
        <p style="font-size: 16px; margin-bottom: 8px;">暂无可关联的设备</p>
        <p style="font-size: 12px;">请确保有设备关联了状态为'活跃'的社交媒体账号</p>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDeviceDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleAddDeviceAssociation"
            :disabled="selectedDevices.length === 0"
          >
            关联选中设备 ({{ selectedDevices.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog
      title="代理IP统计信息"
      v-model="statsVisible"
      width="500px"
    >
      <div v-if="stats">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-statistic title="总数量" :value="stats.total" />
          </el-col>
          <el-col :span="12">
            <el-statistic title="活跃数量" :value="stats.active" />
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-statistic title="已过期" :value="stats.expired" />
          </el-col>
          <el-col :span="12">
            <el-statistic title="地区数量" :value="Object.keys(stats.regions).length" />
          </el-col>
        </el-row>
        <div style="margin-top: 20px;">
          <h4>按地区分布：</h4>
          <el-table :data="regionStatsData" style="width: 100%">
            <el-table-column prop="region" label="地区" />
            <el-table-column prop="count" label="数量" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection, Monitor } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import {
  getProxyList,
  createProxy,
  updateProxy,
  deleteProxy,
  getProxyStats,
  batchImportProxies,
  getExpiringProxies,
  associateDeviceProxy,
  disassociateDeviceProxy,
  getActiveDevices,
  type ProxyIP,
  type ProxyIPCreateRequest,
  type ProxyStats,
  type ActiveDevice
} from '@/api/proxy'

// 响应式数据
const loading = ref(false)
const proxyList = ref<ProxyIP[]>([])
const total = ref(0)
const regions = ref<string[]>([])
const selectedProxies = ref<ProxyIP[]>([])

// 筛选条件
const filterRegion = ref('')
const filterStatus = ref('')
const filterDevice = ref('')

// 路由参数
const route = useRoute()

// 对话框状态
const dialogVisible = ref(false)
const batchImportVisible = ref(false)
const deviceAssociationVisible = ref(false)
const addDeviceDialogVisible = ref(false)
const statsVisible = ref(false)

// 设备关联相关
const activeDevices = ref<ActiveDevice[]>([])
const loadingActiveDevices = ref(false)
const selectedDevices = ref<ActiveDevice[]>([])

// 表单数据
const formRef = ref<FormInstance>()
const isEdit = ref(false)
const currentProxyId = ref('')
const currentProxy = ref<ProxyIP | null>(null)

const form = ref<ProxyIPCreateRequest & { status?: string }>({
  region: '',
  ip_address: '',
  port: 80,
  proxy_type: 'vless',
  username: '',
  password: '',
  v2ray_config: '',
  socks_config: '',
  expire_date: '',
  payment_card: '',
  provider: '',
  notes: '',
  status: 'active'
})

// 批量导入
const batchImportText = ref('')

// 统计信息
const stats = ref<ProxyStats | null>(null)

// 表单验证规则
const rules: FormRules = {
  region: [{ required: true, message: '请输入地区', trigger: 'blur' }],
  ip_address: [{ required: true, message: '请输入IP地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  proxy_type: [{ required: true, message: '请选择代理类型', trigger: 'change' }]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑代理IP' : '添加代理IP')

const regionStatsData = computed(() => {
  if (!stats.value) return []
  return Object.entries(stats.value.regions).map(([region, count]) => ({
    region,
    count
  }))
})

// 筛选后的代理IP列表
const filteredProxyList = computed(() => {
  if (!filterDevice.value) {
    return proxyList.value
  }

  return proxyList.value.filter(proxy => {
    // 设备筛选：检查代理IP是否关联了指定设备
    const hasDevice = proxy.associated_devices?.some(device =>
      device.device_id === filterDevice.value
    )
    return hasDevice
  })
})

// 方法
const refreshList = async () => {
  try {
    loading.value = true
    const result = await getProxyList({
      region: filterRegion.value || undefined,
      status: filterStatus.value || undefined
    })
    proxyList.value = result.items
    total.value = result.total
    regions.value = result.regions
  } catch (error) {
    ElMessage.error('获取代理IP列表失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  refreshList()
}

const showAddDialog = () => {
  isEdit.value = false
  currentProxyId.value = ''
  resetForm()
  dialogVisible.value = true
}

const showEditDialog = (proxy: ProxyIP) => {
  isEdit.value = true
  currentProxyId.value = proxy.id
  form.value = {
    region: proxy.region,
    ip_address: proxy.ip_address,
    port: proxy.port,
    proxy_type: proxy.proxy_type,
    username: proxy.username || '',
    password: proxy.password || '',
    v2ray_config: proxy.v2ray_config || '',
    socks_config: proxy.socks_config || '',
    expire_date: proxy.expire_date || '',
    payment_card: proxy.payment_card || '',
    provider: proxy.provider || '',
    notes: proxy.notes || '',
    status: proxy.status || 'active'
  }
  dialogVisible.value = true
}

const resetForm = () => {
  form.value = {
    region: '',
    ip_address: '',
    port: 80,
    proxy_type: 'vless',
    username: '',
    password: '',
    v2ray_config: '',
    socks_config: '',
    expire_date: '',
    payment_card: '',
    provider: '',
    notes: '',
    status: 'active'
  }
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await updateProxy(currentProxyId.value, form.value)
      ElMessage.success('代理IP更新成功')
    } else {
      await createProxy(form.value)
      ElMessage.success('代理IP创建成功')
    }
    
    dialogVisible.value = false
    refreshList()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新代理IP失败' : '创建代理IP失败')
  }
}

const handleDelete = async (proxy: ProxyIP) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除代理IP ${proxy.ip_address}:${proxy.port} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteProxy(proxy.id)
    ElMessage.success('删除成功')
    refreshList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = (selection: ProxyIP[]) => {
  selectedProxies.value = selection
}

const showBatchImportDialog = () => {
  batchImportText.value = ''
  batchImportVisible.value = true
}

const handleBatchImport = async () => {
  if (!batchImportText.value.trim()) {
    ElMessage.warning('请输入代理IP信息')
    return
  }
  
  try {
    const lines = batchImportText.value.trim().split('\n')
    const proxies: ProxyIPCreateRequest[] = []
    
    for (const line of lines) {
      const parts = line.split(':')
      if (parts.length >= 4) {
        proxies.push({
          region: parts[0],
          ip_address: parts[1],
          port: parseInt(parts[2]),
          proxy_type: parts[3],
          username: parts[4] || undefined,
          password: parts[5] || undefined,
          v2ray_config: parts[6] || undefined,
          socks_config: parts[7] || undefined,
          expire_date: parts[8] || undefined,
          payment_card: parts[9] || undefined,
          provider: parts[10] || undefined
        })
      }
    }
    
    if (proxies.length === 0) {
      ElMessage.warning('没有有效的代理IP信息')
      return
    }
    
    const result = await batchImportProxies(proxies)
    ElMessage.success(result.message)
    batchImportVisible.value = false
    refreshList()
  } catch (error) {
    ElMessage.error('批量导入失败')
  }
}

const showDeviceAssociation = (proxy: ProxyIP) => {
  currentProxy.value = proxy
  deviceAssociationVisible.value = true
}

const showAddDeviceDialog = async () => {
  try {
    loadingActiveDevices.value = true
    activeDevices.value = await getActiveDevices()

    if (activeDevices.value.length === 0) {
      ElMessage.warning('当前没有关联了活跃账号的设备可供关联')
      return
    }

    addDeviceDialogVisible.value = true
    selectedDevices.value = []
  } catch (error: any) {
    console.error('获取活跃设备列表失败:', error)
    const errorMessage = error?.response?.data?.detail || error?.message || '获取活跃设备列表失败'
    ElMessage.error(errorMessage)
  } finally {
    loadingActiveDevices.value = false
  }
}

const handleDeviceSelectionChange = (devices: ActiveDevice[]) => {
  selectedDevices.value = devices
}

const handleAddDeviceAssociation = async () => {
  if (!currentProxy.value || selectedDevices.value.length === 0) return

  try {
    // 显示加载状态
    const loadingMessage = ElMessage({
      message: `正在关联 ${selectedDevices.value.length} 个设备...`,
      type: 'info',
      duration: 0
    })

    const promises = selectedDevices.value.map(device =>
      associateDeviceProxy(currentProxy.value!.id, device.id)
    )

    const results = await Promise.allSettled(promises)
    loadingMessage.close()

    // 统计成功和失败的数量
    const successCount = results.filter(r => r.status === 'fulfilled').length
    const failedCount = results.filter(r => r.status === 'rejected').length

    if (successCount > 0) {
      ElMessage.success(`成功关联 ${successCount} 个设备${failedCount > 0 ? `，${failedCount} 个失败` : ''}`)
    }

    if (failedCount > 0 && successCount === 0) {
      ElMessage.error(`关联失败，共 ${failedCount} 个设备关联失败`)
    }

    addDeviceDialogVisible.value = false
    refreshList()

    // 更新当前代理信息
    const updatedProxy = proxyList.value.find(p => p.id === currentProxy.value!.id)
    if (updatedProxy) {
      currentProxy.value = updatedProxy
    }
  } catch (error: any) {
    console.error('关联设备失败:', error)
    const errorMessage = error?.response?.data?.detail || error?.message || '关联设备失败'
    ElMessage.error(errorMessage)
  }
}

const handleRemoveDeviceAssociation = async (device: any) => {
  if (!currentProxy.value) return

  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要移除设备 "${device.device_name}" 的关联吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await disassociateDeviceProxy(currentProxy.value.id, device.device_id)
    ElMessage.success(`成功移除设备 "${device.device_name}" 的关联`)
    refreshList()

    // 更新当前代理信息
    const updatedProxy = proxyList.value.find(p => p.id === currentProxy.value!.id)
    if (updatedProxy) {
      currentProxy.value = updatedProxy
    }
  } catch (error: any) {
    if (error === 'cancel') {
      return // 用户取消操作
    }
    console.error('移除关联失败:', error)
    const errorMessage = error?.response?.data?.detail || error?.message || '移除关联失败'
    ElMessage.error(errorMessage)
  }
}

const showExpiringProxies = async () => {
  try {
    const result = await getExpiringProxies(30)
    proxyList.value = result.items
    total.value = result.total
    ElMessage.info(`找到 ${result.total} 个即将到期的代理IP`)
  } catch (error) {
    ElMessage.error('获取即将到期的代理IP失败')
  }
}

const showStats = async () => {
  try {
    stats.value = await getProxyStats()
    statsVisible.value = true
  } catch (error) {
    ElMessage.error('获取统计信息失败')
  }
}

// 加载活跃设备列表
const loadActiveDevices = async () => {
  try {
    loadingActiveDevices.value = true
    const devices = await getActiveDevices()
    activeDevices.value = devices
  } catch (error) {
    console.error('获取活跃设备列表失败:', error)
    ElMessage.error('获取活跃设备列表失败')
  } finally {
    loadingActiveDevices.value = false
  }
}

// 工具方法
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString()
}

const getExpireClass = (dateStr: string) => {
  const expireDate = new Date(dateStr)
  const now = new Date()
  const diffDays = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'expired'
  if (diffDays <= 7) return 'expiring-soon'
  if (diffDays <= 30) return 'expiring-warning'
  return ''
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'expired': return 'danger'
    case 'inactive': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'expired': return '已过期'
    case 'inactive': return '停用'
    default: return '未知'
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string, type: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success(`${type}已复制到剪贴板`)
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success(`${type}已复制到剪贴板`)
    } catch (err) {
      ElMessage.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 生命周期
onMounted(async () => {
  // 先获取活跃设备列表
  await loadActiveDevices()

  // 检查URL参数，如果有设备ID则自动筛选
  const deviceId = route.query.device_id as string
  const deviceName = route.query.device_name as string

  if (deviceId) {
    filterDevice.value = deviceId
    if (deviceName) {
      ElMessage.info(`已自动筛选设备: ${deviceName}`)
    }
  }

  refreshList()
})
</script>

<style scoped>
.proxy-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.expired) {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.expiring-soon) {
  color: #e6a23c;
  font-weight: bold;
}

:deep(.expiring-warning) {
  color: #f56c6c;
}

.config-cell {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.config-cell .el-input {
  font-size: 12px;
}

.config-cell .el-input__inner {
  font-family: 'Courier New', monospace;
}

.text-gray {
  color: #909399;
  font-style: italic;
}
</style>
