<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑对标账号"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="关联账号" prop="our_account_id">
        <el-select
          v-model="form.our_account_id"
          placeholder="选择我们的账号"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="account in ourAccounts"
            :key="account.id"
            :label="formatAccountName(account)"
            :value="account.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="平台" prop="platform">
        <el-select
          v-model="form.platform"
          placeholder="选择平台"
          style="width: 100%"
        >
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
          <el-option label="抖音" value="douyin" />
          <el-option label="微博" value="weibo" />
          <el-option label="小红书" value="xiaohongshu" />
          <el-option label="快手" value="kuaishou" />
          <el-option label="B站" value="bilibili" />
        </el-select>
      </el-form-item>

      <el-form-item label="账号名称" prop="account_name">
        <el-input
          v-model="form.account_name"
          placeholder="输入对标账号名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="账号链接" prop="account_url">
        <el-input
          v-model="form.account_url"
          placeholder="输入对标账号链接"
          type="url"
        />
        <div class="form-tip">
          请输入完整的账号链接，例如：https://www.youtube.com/@channelname
        </div>
      </el-form-item>

      <el-form-item label="对标类型" prop="benchmark_type">
        <el-radio-group v-model="form.benchmark_type">
          <el-radio value="original">原创</el-radio>
          <el-radio value="recreate">二创</el-radio>
          <el-radio value="repost">搬运</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="优先级" prop="priority">
        <el-rate
          v-model="form.priority"
          :max="5"
          show-text
          :texts="['很低', '较低', '一般', '较高', '很高']"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="active">活跃</el-radio>
          <el-radio value="inactive">非活跃</el-radio>
          <el-radio value="monitoring">监控中</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="输入对标账号的描述信息（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { updateBenchmarkAccount } from '@/api/content'

// Props
const props = defineProps<{
  visible: boolean
  benchmark: any
  ourAccounts: any[]
}>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const form = reactive({
  our_account_id: '',
  platform: '',
  account_name: '',
  account_url: '',
  benchmark_type: 'original',
  priority: 3,
  status: 'active',
  tags: [] as string[],
  description: ''
})

// 常用标签
const commonTags = [
  '热门', '优质', '同行', '竞品', '学习', '参考',
  '创意', '内容', '风格', '技巧', '趋势', '爆款'
]

// 表单验证规则
const rules: FormRules = {
  our_account_id: [
    { required: true, message: '请选择关联账号', trigger: 'change' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  account_name: [
    { required: true, message: '请输入账号名称', trigger: 'blur' },
    { min: 1, max: 100, message: '账号名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  account_url: [
    { required: true, message: '请输入账号链接', trigger: 'blur' },
    { type: 'url', message: '请输入有效的链接地址', trigger: 'blur' }
  ],
  benchmark_type: [
    { required: true, message: '请选择对标类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 工具方法
const getPlatformDisplayName = (platformId: string) => {
  // 根据数据库中的平台ObjectId映射到显示名称
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube', // 油管
    '681efeeecd836bd64b9c2a20': 'TikTok',  // TT
    '681efeeecd836bd64b9c2a22': '抖音',     // 抖音
    '6822ecaa62fd956eb6d2c071': 'Facebook', // 脸书
    '6822ebfc05340d5a3d867138': 'AWS'       // AWS
  }

  // 如果是ObjectId格式，使用映射表
  if (platformIdMap[platformId]) {
    return platformIdMap[platformId]
  }

  // 如果是传统的平台ID格式，使用原来的映射
  const platformMap = {
    youtube: 'YouTube',
    tiktok: 'TikTok',
    instagram: 'Instagram',
    weibo: '微博',
    xiaohongshu: '小红书',
    douyin: '抖音',
    kuaishou: '快手',
    bilibili: 'B站'
  }

  return platformMap[platformId as keyof typeof platformMap] || platformId
}

// 方法
const formatAccountName = (account: any) => {
  if (!account) return '未知账号'

  // 获取平台名称 - 优先使用后端提供的platform_name，否则通过platform_id映射
  let platform = account.platform_name || account.platform_display_name
  if (!platform) {
    platform = getPlatformDisplayName(account.platform_id || 'unknown')
  }

  let name = account.display_name || account.name
  // 修复：检查空字符串和null/undefined
  if (!name || name.trim() === '') {
    if (account.username && account.username.trim() !== '') {
      name = account.username.includes('@') ? account.username.split('@')[0] : account.username
    } else {
      name = '未命名账号'
    }
  }
  return `${platform}-${name}`
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true

    const benchmarkId = props.benchmark._id || props.benchmark.id
    if (!benchmarkId) {
      throw new Error('对标账号ID不存在')
    }

    // 更新对标账号
    await updateBenchmarkAccount(benchmarkId, {
      our_account_id: form.our_account_id,
      platform: form.platform,
      account_name: form.account_name,
      account_url: form.account_url,
      benchmark_type: form.benchmark_type,
      priority: form.priority,
      status: form.status,
      tags: form.tags,
      description: form.description
    })

    ElMessage.success('更新对标账号成功')
    emit('success')
    handleClose()

  } catch (error) {
    console.error('更新对标账号失败:', error)
    ElMessage.error('更新对标账号失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const initForm = () => {
  if (props.benchmark) {
    Object.assign(form, {
      our_account_id: props.benchmark.our_account_id || '',
      platform: props.benchmark.platform || '',
      account_name: props.benchmark.account_name || '',
      account_url: props.benchmark.account_url || '',
      benchmark_type: props.benchmark.benchmark_type || 'original',
      priority: props.benchmark.priority || 3,
      status: props.benchmark.status || 'active',
      tags: props.benchmark.tags || [],
      description: props.benchmark.description || ''
    })
  }
}

// 监听对话框打开
watch(() => props.visible, (visible) => {
  if (visible) {
    initForm()
  }
})

// 监听benchmark变化
watch(() => props.benchmark, () => {
  if (props.visible) {
    initForm()
  }
})
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
