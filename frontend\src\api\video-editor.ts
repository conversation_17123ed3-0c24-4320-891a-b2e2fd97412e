/**
 * 视频编辑器API接口
 */

import request from '@/utils/request'

// 类型定义
export interface VideoResolution {
  width: number
  height: number
}

export interface ProjectSettings {
  resolution: VideoResolution
  frame_rate: number
  audio_sample_rate: number
  audio_channels: number
  color_space: string
  timecode_format: string
  auto_save: boolean
}

export interface Track {
  id: string
  type: 'video' | 'audio' | 'subtitle' | 'effect'
  name: string
  enabled: boolean
  locked: boolean
  clips: any[]
}

export interface Timeline {
  tracks: Track[]
  total_duration: number
  markers: any[]
}

export interface VideoProject {
  _id?: string
  name: string
  description?: string
  owner_id?: string
  settings: ProjectSettings
  timeline: Timeline
  created_at?: string
  updated_at?: string
  collaborators?: string[]
}

export interface MediaAsset {
  _id?: string
  name: string
  type: 'video' | 'audio' | 'image' | 'subtitle'
  url: string
  thumbnail?: string
  size: number
  duration?: number
  resolution?: VideoResolution
  created_at?: string
}

export interface EditOperation {
  type: 'cut' | 'split' | 'merge' | 'move' | 'resize' | 'delete'
  clip_id: string
  parameters: Record<string, any>
}

export interface ExportSettings {
  format: string
  resolution: string
  frame_rate: string | number
  quality: number
  audio_codec: string
  audio_bitrate: string
  export_range: string
  start_time?: number
  end_time?: number
  output_path: string
  file_name: string
}

export interface ExportTask {
  _id: string
  project_id: string
  user_id: string
  settings: ExportSettings
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  output_file?: string
  file_size?: number
  error_message?: string
  created_at: string
  updated_at: string
}

// 项目管理API
export const createProject = (project: Omit<VideoProject, '_id' | 'owner_id' | 'created_at' | 'updated_at'>) => {
  return request({
    url: '/api/v1/video-editor/projects',
    method: 'post',
    data: project
  })
}

export const getProjects = (params?: { page?: number; limit?: number }) => {
  return request({
    url: '/api/v1/video-editor/projects',
    method: 'get',
    params
  })
}

export const getProject = (projectId: string) => {
  return request({
    url: `/api/v1/video-editor/projects/${projectId}`,
    method: 'get'
  })
}

export const updateProject = (projectId: string, project: Partial<VideoProject>) => {
  return request({
    url: `/api/v1/video-editor/projects/${projectId}`,
    method: 'put',
    data: project
  })
}

export const deleteProject = (projectId: string) => {
  return request({
    url: `/api/v1/video-editor/projects/${projectId}`,
    method: 'delete'
  })
}

// 素材管理API
export const uploadMedia = (formData: FormData) => {
  return request({
    url: '/api/v1/video-editor/media/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export const getMediaAssets = (params?: { type?: string }) => {
  return request({
    url: '/api/v1/video-editor/media',
    method: 'get',
    params
  })
}

export const deleteMediaAsset = (mediaId: string) => {
  return request({
    url: `/api/v1/video-editor/media/${mediaId}`,
    method: 'delete'
  })
}

// 实时编辑API
export const applyEditOperation = (projectId: string, operation: EditOperation) => {
  return request({
    url: `/api/v1/video-editor/edit/${projectId}`,
    method: 'post',
    data: operation
  })
}

// 渲染和导出API
export const generatePreview = (
  projectId: string, 
  time: number, 
  quality: string = 'medium',
  coreServiceId?: string
) => {
  return request({
    url: `/api/v1/video-editor/render/preview/${projectId}`,
    method: 'post',
    data: { time, quality },
    params: coreServiceId ? { core_service_id: coreServiceId } : undefined
  })
}

export const exportVideo = (
  projectId: string, 
  exportSettings: ExportSettings,
  coreServiceId?: string
) => {
  return request({
    url: `/api/v1/video-editor/export/${projectId}`,
    method: 'post',
    data: exportSettings,
    params: coreServiceId ? { core_service_id: coreServiceId } : undefined
  })
}

export const getExportStatus = (taskId: string) => {
  return request({
    url: `/api/v1/video-editor/export/status/${taskId}`,
    method: 'get'
  })
}

// 协作功能API
export const addCollaborator = (projectId: string, collaboratorEmail: string) => {
  return request({
    url: `/api/v1/video-editor/projects/${projectId}/collaborators`,
    method: 'post',
    data: { collaborator_email: collaboratorEmail }
  })
}

export const removeCollaborator = (projectId: string, collaboratorId: string) => {
  return request({
    url: `/api/v1/video-editor/projects/${projectId}/collaborators/${collaboratorId}`,
    method: 'delete'
  })
}

// WebSocket连接管理
export class VideoEditorWebSocket {
  private ws: WebSocket | null = null
  private projectId: string
  private callbacks: Map<string, Function> = new Map()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 3000

  constructor(projectId: string) {
    this.projectId = projectId
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `ws://localhost:8000/api/v1/video-editor/ws/preview/${this.projectId}`
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('Video editor WebSocket connected')
          this.reconnectAttempts = 0
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error)
          }
        }

        this.ws.onclose = () => {
          console.log('Video editor WebSocket disconnected')
          this.attemptReconnect()
        }

        this.ws.onerror = (error) => {
          console.error('Video editor WebSocket error:', error)
          reject(error)
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  on(event: string, callback: Function) {
    this.callbacks.set(event, callback)
  }

  off(event: string) {
    this.callbacks.delete(event)
  }

  private handleMessage(data: any) {
    const callback = this.callbacks.get(data.type)
    if (callback) {
      callback(data)
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error)
        })
      }, this.reconnectDelay)
    } else {
      console.error('Max reconnection attempts reached')
    }
  }

  // 预览控制方法
  seek(time: number) {
    this.send({
      type: 'seek',
      time: time
    })
  }

  play(time: number = 0) {
    this.send({
      type: 'play',
      time: time
    })
  }

  pause() {
    this.send({
      type: 'pause'
    })
  }
}

// 工具函数
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const frames = Math.floor((seconds % 1) * 30) // 假设30fps

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${frames.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${frames.toString().padStart(2, '0')}`
  }
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

export const calculateAspectRatio = (width: number, height: number): string => {
  const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
  const divisor = gcd(width, height)
  return `${width / divisor}:${height / divisor}`
}

export const getResolutionFromString = (resolution: string): VideoResolution => {
  const resolutionMap: Record<string, VideoResolution> = {
    '4k': { width: 3840, height: 2160 },
    '2k': { width: 2560, height: 1440 },
    '1080p': { width: 1920, height: 1080 },
    '720p': { width: 1280, height: 720 },
    '480p': { width: 854, height: 480 }
  }

  return resolutionMap[resolution] || { width: 1920, height: 1080 }
}
