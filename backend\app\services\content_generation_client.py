"""
内容生成gRPC客户端
通过gRPC调用Core服务的内容生成功能
"""

import logging
import grpc
from typing import Dict, Any, Optional, List
from app.core.grpc_client import get_core_client

logger = logging.getLogger(__name__)

class ContentGenerationClient:
    """内容生成gRPC客户端"""
    
    def __init__(self):
        self.core_client = None
        
    async def get_client(self):
        """获取Core客户端"""
        if not self.core_client:
            self.core_client = await get_core_client()
        return self.core_client
        
    async def create_generation_task(self, account_id: str, platform_id: str, title: str,
                                   task_type: str, description: str, style: str = "realistic",
                                   count: int = 1, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """创建内容生成任务"""
        try:
            client = await self.get_client()
            
            # 导入protobuf类型
            from core.src.api import content_generation_pb2
            
            # 构建配置
            pb_config = None
            if config:
                pb_config = content_generation_pb2.GenerationConfig(
                    comfyui_workflow=config.get("comfyui_workflow", ""),
                    duration=config.get("duration", 30),
                    resolution=config.get("resolution", "1920x1080"),
                    fps=config.get("fps", 30),
                    quality=config.get("quality", "high"),
                    model_settings=config.get("model_settings", {}),
                    output_settings=config.get("output_settings", {})
                )
            
            # 构建请求
            request = content_generation_pb2.CreateGenerationTaskRequest(
                account_id=account_id,
                platform_id=platform_id,
                title=title,
                type=task_type,
                description=description,
                style=style,
                count=count,
                config=pb_config
            )
            
            # 调用gRPC服务
            response = await client.content_generation_stub.CreateGenerationTask(request)
            
            return {
                "success": response.success,
                "task_id": response.task_id if response.success else None,
                "error": response.error if not response.success else None
            }
            
        except Exception as e:
            logger.error(f"创建内容生成任务失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def start_generation_task(self, task_id: str, account_id: str) -> Dict[str, Any]:
        """启动内容生成任务"""
        try:
            client = await self.get_client()
            
            from core.src.api import content_generation_pb2
            
            request = content_generation_pb2.StartGenerationTaskRequest(
                task_id=task_id,
                account_id=account_id
            )
            
            response = await client.content_generation_stub.StartGenerationTask(request)
            
            return {
                "success": response.success,
                "error": response.error if not response.success else None
            }
            
        except Exception as e:
            logger.error(f"启动内容生成任务失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def get_task_status(self, task_id: str, account_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            client = await self.get_client()
            
            from core.src.api import content_generation_pb2
            
            request = content_generation_pb2.GetGenerationTaskStatusRequest(
                task_id=task_id,
                account_id=account_id
            )
            
            response = await client.content_generation_stub.GetGenerationTaskStatus(request)
            
            if response.success:
                status = response.status
                return {
                    "success": True,
                    "status": {
                        "task_id": status.task_id,
                        "status": status.status,
                        "progress": status.progress,
                        "current_step": status.current_step,
                        "error_message": status.error_message,
                        "created_at": status.created_at,
                        "updated_at": status.updated_at,
                        "completed_at": status.completed_at
                    }
                }
            else:
                return {
                    "success": False,
                    "error": response.error
                }
                
        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def get_generation_results(self, task_id: str, account_id: str) -> Dict[str, Any]:
        """获取生成结果"""
        try:
            client = await self.get_client()
            
            from core.src.api import content_generation_pb2
            
            request = content_generation_pb2.GetGenerationResultsRequest(
                task_id=task_id,
                account_id=account_id
            )
            
            response = await client.content_generation_stub.GetGenerationResults(request)
            
            if response.success:
                results = []
                for result in response.results:
                    results.append({
                        "id": result.id,
                        "type": result.type,
                        "title": result.title,
                        "file_path": result.file_path,
                        "file_size": result.file_size,
                        "preview_url": result.preview_url,
                        "download_url": result.download_url,
                        "thumbnail_path": result.thumbnail_path,
                        "metadata": dict(result.metadata)
                    })
                
                return {
                    "success": True,
                    "results": results
                }
            else:
                return {
                    "success": False,
                    "error": response.error
                }
                
        except Exception as e:
            logger.error(f"获取生成结果失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def download_generated_file(self, result_id: str, account_id: str) -> Dict[str, Any]:
        """下载生成的文件"""
        try:
            client = await self.get_client()
            
            from core.src.api import content_generation_pb2
            
            request = content_generation_pb2.DownloadGeneratedFileRequest(
                result_id=result_id,
                account_id=account_id
            )
            
            response = await client.content_generation_stub.DownloadGeneratedFile(request)
            
            if response.success:
                return {
                    "success": True,
                    "file_data": response.file_data,
                    "filename": response.filename,
                    "content_type": response.content_type
                }
            else:
                return {
                    "success": False,
                    "error": response.error
                }
                
        except Exception as e:
            logger.error(f"下载文件失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def test_comfyui_connection(self) -> Dict[str, Any]:
        """测试ComfyUI连接"""
        try:
            client = await self.get_client()
            
            from core.src.api import content_generation_pb2
            
            request = content_generation_pb2.TestComfyUIConnectionRequest()
            
            response = await client.content_generation_stub.TestComfyUIConnection(request)
            
            return {
                "success": response.success,
                "status": response.status,
                "version": response.version,
                "queue_size": response.queue_size,
                "active_tasks": response.active_tasks,
                "error": response.error if not response.success else None
            }
            
        except Exception as e:
            logger.error(f"测试ComfyUI连接失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "status": "disconnected",
                "error": str(e)
            }
            
    async def get_available_models(self) -> Dict[str, Any]:
        """获取可用模型"""
        try:
            client = await self.get_client()
            
            from core.src.api import content_generation_pb2
            
            request = content_generation_pb2.GetAvailableModelsRequest()
            
            response = await client.content_generation_stub.GetAvailableModels(request)
            
            if response.success:
                text_models = [{"id": m.id, "name": m.name, "description": m.description, "type": m.type} for m in response.text_models]
                image_models = [{"id": m.id, "name": m.name, "description": m.description, "type": m.type} for m in response.image_models]
                video_models = [{"id": m.id, "name": m.name, "description": m.description, "type": m.type} for m in response.video_models]
                
                return {
                    "success": True,
                    "text_models": text_models,
                    "image_models": image_models,
                    "video_models": video_models
                }
            else:
                return {
                    "success": False,
                    "error": response.error
                }
                
        except Exception as e:
            logger.error(f"获取可用模型失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }

# 全局客户端实例
_content_generation_client = None

def get_content_generation_client() -> ContentGenerationClient:
    """获取内容生成客户端实例"""
    global _content_generation_client
    
    if _content_generation_client is None:
        _content_generation_client = ContentGenerationClient()
        
    return _content_generation_client
