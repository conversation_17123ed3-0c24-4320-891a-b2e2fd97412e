# 文件操作功能的Core服务选择支持

## 概述

为了确保文件管理功能的Core服务选择机制完整性，我们为预发布和归档功能添加了Core服务选择支持。

## 修改的API

### 1. 预发布功能API

**API路径**：`POST /api/v1/filesystem/pre-publish`

**后端修改**：`backend/app/api/v1/filesystem.py`

```python
@router.post("/pre-publish", response_model=PrePublishResponse)
async def pre_publish_files(
    request: PrePublishRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    预发布功能：将选中的视频文件剪切到根目录下的publishing文件夹

    - **file_paths**: 要预发布的文件路径列表
    - **current_folder**: 当前文件夹路径，用于确定根目录
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
```

**Core服务选择逻辑**：
```python
# 根据core_service_id选择Core服务
if core_service_id:
    core_client = get_core_client_by_service_id(core_service_id)
    logger.info(f"使用指定的Core服务进行预发布: {core_service_id}")
else:
    from app.core.grpc_client import get_core_client
    core_client = get_core_client()
    logger.info("使用默认Core服务进行预发布")
```

**前端修改**：`frontend/src/api/social.ts`
```typescript
export const prePublishFiles = (data: PrePublishRequest, coreServiceId?: string) => {
  return request<PrePublishResponse>({
    url: '/api/v1/filesystem/pre-publish',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

**文件管理页面集成**：`frontend/src/views/doc/Manager.vue`
```typescript
const response = await prePublishFiles(requestData, selectedCoreService.value || undefined)
```

### 2. 归档已发布文件API

**API路径**：`POST /api/v1/filesystem/archive-published`

**后端修改**：
```python
@router.post("/archive-published", response_model=ArchivePublishedFilesResponse)
async def archive_published_files(
    request: ArchivePublishedFilesRequest, 
    db_request: Request,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    """
    一键归档已发布的文件到子文件夹

    - **folder_path**: 文件夹路径
    - **archive_folder_name**: 归档文件夹名称（默认为"已发布"）
    - **platforms**: 可选，指定要检查的平台列表。如果不提供，则检查所有平台
    - **core_service_id**: 可选的Core服务ID，如果指定则通过该Core服务处理
    """
```

**Core服务选择逻辑**：
```python
# 根据core_service_id选择Core服务
if core_service_id:
    core_client = get_core_client_by_service_id(core_service_id)
    logger.info(f"使用指定的Core服务进行归档: {core_service_id}")
else:
    from app.core.grpc_client import get_core_client
    core_client = get_core_client()
    logger.info("使用默认Core服务进行归档")
```

**前端修改**：
```typescript
export const archivePublishedFiles = (data: ArchivePublishedFilesRequest, coreServiceId?: string) => {
  return request<ArchivePublishedFilesResponse>({
    url: '/api/v1/filesystem/archive-published',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

**文件管理页面集成**：
```typescript
const response = await archivePublishedFiles(requestData, selectedCoreService.value || undefined)
```

## 功能说明

### 预发布功能

预发布功能将选中的视频文件从当前位置剪切到账号根目录下的 `publishing` 文件夹：

1. **路径解析**：自动识别账号根目录（通过PublishSystem路径结构）
2. **文件移动**：通过Core服务执行文件移动操作
3. **冲突处理**：如果目标文件已存在，自动添加时间戳后缀
4. **降级处理**：如果Core服务不可用，使用本地文件操作作为降级方案

### 归档已发布文件功能

归档功能将已发布的文件移动到指定的归档文件夹：

1. **发布状态检查**：检查文件夹中每个文件的发布状态
2. **MD5匹配**：通过MD5哈希值匹配数据库中的发布记录
3. **平台过滤**：可以指定特定平台进行发布状态检查
4. **批量归档**：通过Core服务批量移动已发布的文件

## 技术实现细节

### 统一的Core服务选择模式

所有文件操作API都遵循相同的Core服务选择模式：

1. **参数检查**：检查是否提供了 `core_service_id` 参数
2. **服务选择**：
   - 如果提供了 `core_service_id`，使用 `get_core_client_by_service_id()` 获取指定服务
   - 如果没有提供，使用 `get_core_client()` 获取默认服务
3. **日志记录**：记录使用的Core服务信息
4. **错误处理**：如果无法获取Core服务，返回503错误

### 前端集成

1. **API函数更新**：所有文件操作API函数都添加了可选的 `coreServiceId` 参数
2. **参数传递**：通过查询参数 `core_service_id` 传递给后端
3. **文件管理集成**：所有文件操作都使用用户选择的Core服务

## 使用方式

### 在文件管理页面

用户选择Core服务后，所有文件操作都会使用选择的Core服务：

1. 用户在下拉菜单中选择Core服务
2. 系统自动将选择的服务ID传递给所有文件操作API
3. 预发布、归档等功能都使用选择的Core服务

### 降级处理

当指定的Core服务不可用时：

1. **预发布功能**：降级到本地文件移动操作
2. **归档功能**：返回错误信息，提示Core服务不可用

## 向后兼容性

所有修改都保持向后兼容：

- `core_service_id` 参数都是可选的
- 如果不提供该参数，系统使用默认Core服务
- 现有的API调用不需要修改即可继续工作

## 日志记录

所有API都添加了详细的日志记录：

- 记录使用的Core服务ID
- 记录Core服务选择结果
- 记录文件操作状态和结果

## 测试建议

1. **预发布功能**：测试选择不同Core服务时的预发布操作
2. **归档功能**：测试归档已发布文件的功能
3. **错误处理**：测试Core服务不可用时的错误处理
4. **降级处理**：测试预发布功能的本地降级处理
5. **默认行为**：测试不指定Core服务时的默认行为

## 完整的Core服务支持列表

现在文件管理功能中支持Core服务选择的操作包括：

### 文件基础操作
- ✅ 文件列表获取
- ✅ 文件删除
- ✅ 预发布到publishing文件夹
- ✅ 归档已发布文件

### 视频处理操作
- ✅ 视频旋转
- ✅ 视频裁剪
- ✅ 视频加速
- ✅ 三拼视频制作

### 视频预览操作
- ✅ 视频缩略图生成
- ✅ 视频预览信息获取
- ✅ 视频预览片段生成

### 音频处理操作
- 🔄 音频分离（待添加）
- 🔄 人声分离（待添加）
- 🔄 音频替换（待添加）

## 影响范围

这次修改主要影响：

1. **预发布功能**：现在使用选择的Core服务进行文件移动
2. **归档功能**：现在使用选择的Core服务进行文件归档
3. **API一致性**：所有文件操作API都支持Core服务选择
4. **用户体验**：用户选择的Core服务现在应用到所有文件操作

## 后续改进

可以考虑为以下功能添加Core服务选择支持：

1. 音频处理相关API
2. 批量重命名功能
3. 其他文件管理功能

这样可以确保整个文件管理系统的Core服务选择机制完全一致。
