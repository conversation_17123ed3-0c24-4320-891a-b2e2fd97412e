# 任务执行Core服务选择修复

## 问题描述

用户反馈任务执行时没有严格使用任务创建时指定的Core服务，而是使用了默认的服务发现机制。这导致任务可能在错误的Core服务上执行，影响系统的可靠性和预期行为。

## 根本原因分析

经过代码分析，发现问题出现在以下几个环节：

1. **任务创建时**：前端虽然选择了Core服务，但没有将`core_service_id`正确传递到后端API
2. **任务数据存储**：后端API没有将`core_service_id`保存到任务数据中
3. **任务启动时**：任务启动逻辑没有从任务数据中读取`core_service_id`，而是使用默认服务发现

## 修复方案

### 1. 前端修复

**文件**: `frontend/src/api/social.ts`
- 修改`createTask`函数，添加可选的`coreServiceId`参数
- 将`core_service_id`添加到任务数据中

```typescript
export const createTask = (data: PublishTask, coreServiceId?: string) => {
  // 🔧 重要修复：添加core_service_id到任务数据中
  const taskData = { ...data }
  if (coreServiceId) {
    taskData.core_service_id = coreServiceId
  }
  
  return request({
    url: '/api/social/tasks',
    method: 'post',
    data: taskData
  })
}
```

**文件**: `frontend/src/views/social/components/PublishTaskForm.vue`
- 修改API调用，传递`form.coreService`作为`coreServiceId`参数

```typescript
// 🔧 重要修复：传递core_service_id参数到API调用
const response = await createTask(taskData, form.coreService)
```

### 2. 后端API修复

**文件**: `backend/app/api/social.py`
- 在创建任务时获取并保存`core_service_id`
- 确保主任务、子任务和单任务都包含`core_service_id`信息

```python
# 🔧 重要修复：获取并保存core_service_id
core_service_id = task_data.get("core_service_id")
if core_service_id:
    logger.info(f"任务指定使用Core服务: {core_service_id}")
else:
    logger.info("任务将使用默认Core服务")

# 保存到主任务
if core_service_id:
    main_task_data["core_service_id"] = core_service_id

# 保存到子任务
if core_service_id:
    subtask_data["core_service_id"] = core_service_id

# 保存到单任务
if core_service_id:
    task_record["core_service_id"] = core_service_id
```

### 3. 任务启动逻辑修复

**文件**: `backend/app/api/task.py`
- 修改`start_core_task_async`函数，从任务数据中读取`core_service_id`
- 根据`core_service_id`选择对应的Core服务客户端

```python
# 🔧 重要修复：从任务数据中获取core_service_id
core_service_id = task_data.get("core_service_id")
if core_service_id:
    logger.info(f"任务指定使用Core服务: {core_service_id}")
    # 使用指定的Core服务
    core_client = await get_task_client(core_service_id)
    if not core_client:
        logger.error(f"无法连接到指定的Core服务: {core_service_id}")
        raise Exception(f"无法连接到指定的Core服务: {core_service_id}")
else:
    logger.info("任务使用默认Core服务")
    # 获取Core客户端 - 自动从 Consul 发现服务
    core_client = get_core_client()
```

### 4. Core服务任务创建修复

**文件**: `backend/app/core/client.py`
- 在向Core服务发送任务创建请求时，包含`core_service_id`参数

```python
# 🔧 重要修复：添加core_service_id参数
if "core_service_id" in task_data and task_data["core_service_id"]:
    request.params["core_service_id"] = str(task_data["core_service_id"])
    logger.info(f"添加core_service_id参数: {task_data['core_service_id']}")
```

**文件**: `core/src/api/task_service.py`
- 在Core服务中保存`core_service_id`信息

```python
# 🔧 重要修复：特别记录core_service_id参数
if "core_service_id" in request.params:
    task_data["core_service_id"] = request.params["core_service_id"]
    logger.info(f"📋 任务指定使用Core服务: {request.params['core_service_id']}")
```

## 修复验证

### 测试步骤

1. 在发布管理界面创建任务时选择特定的Core服务
2. 检查任务数据库记录是否包含正确的`core_service_id`
3. 启动任务，检查日志确认使用了指定的Core服务
4. 验证任务在正确的Core服务上执行

### 预期结果

- 任务创建时`core_service_id`被正确保存到数据库
- 任务启动时从数据库读取`core_service_id`并使用指定的Core服务
- 日志中显示"任务指定使用Core服务: [service_id]"而不是"任务使用默认Core服务"

## 影响范围

此修复影响以下功能：
- 发布管理中的任务创建和执行
- 主任务和子任务的Core服务选择
- 任务重启功能的Core服务一致性

## 现有任务修复

对于在修复之前创建的任务（如任务`93badfee-12f3-4093-bd74-29ccf672986b`），需要手动补充`core_service_id`信息。

### 方法1：使用API修复

**后端API**: `POST /api/tasks/{task_id}/fix-core-service`

```json
{
  "core_service_id": "thunderhub-core-server1"
}
```

**前端API函数**:
```typescript
import { fixTaskCoreService } from '@/api/social'

// 修复任务的Core服务配置
await fixTaskCoreService('93badfee-12f3-4093-bd74-29ccf672986b', 'thunderhub-core-server1')
```

### 方法2：使用脚本修复

```bash
# 检查任务
python quick_fix_task.py check 93badfee-12f3-4093-bd74-29ccf672986b

# 修复任务
python quick_fix_task.py fix 93badfee-12f3-4093-bd74-29ccf672986b thunderhub-core-server1
```

### 方法3：直接数据库修复

```javascript
// MongoDB命令
db.social_tasks.updateOne(
  { "task_id": "93badfee-12f3-4093-bd74-29ccf672986b" },
  { "$set": { "core_service_id": "thunderhub-core-server1" } }
)
```

## 增强的日志记录

修复后的系统会在日志中明确显示Core服务选择情况：

- ✅ `任务指定使用Core服务: thunderhub-core-server1` - 使用指定服务
- ⚠️ `任务缺少core_service_id，使用默认Core服务` - 使用默认服务（需要修复）

## 注意事项

1. 此修复确保了任务执行的一致性，但不影响现有的服务发现机制
2. 如果任务数据中没有`core_service_id`，系统仍会使用默认的服务发现机制作为降级方案，但会记录警告日志
3. 建议在生产环境部署前进行充分测试，确保所有任务类型都能正确选择Core服务
4. 对于现有的缺少`core_service_id`的任务，建议及时修复以确保执行一致性
