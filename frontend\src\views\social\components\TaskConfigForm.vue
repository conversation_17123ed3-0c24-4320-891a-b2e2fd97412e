<template>
  <div class="task-config">
    <div class="config-header">
      <h2>📝 配置任务</h2>
      <p class="config-description">请配置视频的基本信息和发布设置</p>
    </div>

    <div class="config-content">
      <el-form :model="config" label-width="100px" class="config-form">

        <!-- 基础配置 - 使用紧凑布局 -->
        <el-card class="config-section" shadow="never">
          <template #header>
            <span class="section-title">📝 基础配置</span>
          </template>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="内容类型" size="small">
                <el-select v-model="config.contentType" placeholder="选择内容类型" style="width: 100%">
                  <el-option label="📱 短视频" value="shorts" />
                  <el-option label="🎬 普通视频" value="video" />
                  <el-option label="📺 直播" value="live" />
                  <el-option label="📄 帖子" value="post" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="隐私设置" size="small">
                <el-select v-model="config.privacyStatus" style="width: 100%">
                  <el-option label="🌍 公开" value="public" />
                  <el-option label="🔗 不公开" value="unlisted" />
                  <el-option label="🔒 私有" value="private" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="标题模板" size="small">
            <el-input v-model="config.titleTemplate" placeholder="{filename}" />
          </el-form-item>

          <el-form-item label="视频描述" size="small">
            <el-input type="textarea" v-model="config.description" :rows="3" placeholder="请输入视频描述..." />
          </el-form-item>

          <!-- 标签配置 - 紧凑显示 -->
          <el-form-item label="标签" size="small">
            <div class="tags-container">
              <el-tag
                v-for="tag in config.tags"
                :key="tag"
                closable
                @close="removeTag(tag)"
                class="tag-item"
                size="small"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-if="inputTagVisible"
                ref="tagInput"
                v-model="inputTagValue"
                size="small"
                class="tag-input"
                @keyup.enter="addTag"
                @blur="addTag"
                placeholder="输入标签"
              />
              <el-button v-else size="small" @click="showTagInput" text>+ 添加标签</el-button>
            </div>
          </el-form-item>
        </el-card>

        <!-- 发布设置 -->
        <el-card class="config-section" shadow="never">
          <template #header>
            <span class="section-title">⏰ 发布设置</span>
          </template>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="发布时间" size="small">
                <el-radio-group v-model="config.scheduleType" size="small">
                  <el-radio label="immediate">立即发布</el-radio>
                  <el-radio label="scheduled">定时发布</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12" v-if="config.scheduleType === 'scheduled'">
              <el-form-item label="发布时间" size="small">
                <el-date-picker
                  v-model="config.scheduleTime"
                  type="datetime"
                  placeholder="选择发布时间"
                  style="width: 100%"
                  size="small"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 高级设置 - 使用折叠面板 -->
        <el-collapse v-model="activeCollapse" class="config-collapse">
          <el-collapse-item title="⚙️ 高级设置" name="advanced">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="分类" size="small">
                  <el-select v-model="config.category" placeholder="选择分类" style="width: 100%" size="small">
                    <el-option
                      v-for="item in categories"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="语言" size="small">
                  <el-select v-model="config.language" placeholder="选择语言" style="width: 100%" size="small">
                    <el-option label="中文" value="zh" />
                    <el-option label="英文" value="en" />
                    <el-option label="日文" value="ja" />
                    <el-option label="韩文" value="ko" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="许可证" size="small">
                  <el-select v-model="config.license" placeholder="选择许可证" style="width: 100%" size="small">
                    <el-option label="标准 YouTube 许可证" value="youtube" />
                    <el-option label="知识共享" value="creativeCommon" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>

          <!-- 短视频专用设置 -->
          <el-collapse-item v-if="config.contentType === 'shorts'" title="🎵 短视频音频设置" name="audio">
            <!-- 音乐库选择 -->
            <div class="music-section">
              <MusicLibrary
                :platform="'youtube'"
                @music-selected="handleMusicSelected"
              />
            </div>

            <!-- 音频设置 -->
            <div class="audio-settings">
              <el-form-item label="保留原声" size="small">
                <el-switch
                  v-model="config.keepOriginalAudio"
                  active-text="保留"
                  inactive-text="不保留"
                  @change="handleKeepOriginalAudioChange"
                  size="small"
                />
                <div class="setting-hint">
                  <small>开启后可以保留视频的原始音频，与背景音乐混合播放</small>
                </div>
              </el-form-item>

              <!-- 音量调节（仅在保留原声时显示） -->
              <div v-if="config.keepOriginalAudio" class="volume-controls">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="原声音量" size="small">
                      <el-slider
                        v-model="config.originalAudioPercentage"
                        :min="0"
                        :max="100"
                        :step="5"
                        show-input
                        :format-tooltip="formatVolumeTooltip"
                        size="small"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="背景音乐音量" size="small">
                      <el-slider
                        v-model="config.musicVolumePercentage"
                        :min="0"
                        :max="100"
                        :step="5"
                        show-input
                        :format-tooltip="formatVolumeTooltip"
                        size="small"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 音量预设 -->
                <el-form-item label="快速预设" size="small">
                  <el-button-group>
                    <el-button
                      size="small"
                      @click="applyVolumePreset('original-main')"
                      :type="isCurrentPreset('original-main') ? 'primary' : 'default'"
                    >
                      原声为主
                    </el-button>
                    <el-button
                      size="small"
                      @click="applyVolumePreset('balanced')"
                      :type="isCurrentPreset('balanced') ? 'primary' : 'default'"
                    >
                      平衡混音
                    </el-button>
                    <el-button
                      size="small"
                      @click="applyVolumePreset('music-main')"
                      :type="isCurrentPreset('music-main') ? 'primary' : 'default'"
                    >
                      音乐为主
                    </el-button>
                  </el-button-group>
                </el-form-item>
              </div>

              <!-- 音频设置提示 -->
              <el-alert
                v-if="config.selectedMusic && config.selectedMusic.length > 0"
                title="💡 音频配置预览"
                type="info"
                :closable="false"
                size="small"
              >
                <template #default>
                  <div class="audio-preview">
                    <p><strong>已选择音乐：</strong>{{ config.selectedMusic.length }} 首</p>
                    <p v-if="config.keepOriginalAudio">
                      <strong>音频混合：</strong>原声 {{ config.originalAudioPercentage }}% + 背景音乐 {{ config.musicVolumePercentage }}%
                    </p>
                    <p v-else>
                      <strong>音频模式：</strong>仅使用背景音乐，不保留原声
                    </p>
                  </div>
                </template>
              </el-alert>
            </div>
          </el-collapse-item>
        </el-collapse>

      </el-form>
    </div>

    <!-- 固定底部按钮区域 -->
    <div class="config-footer">
      <!-- 配置状态提示 -->
      <el-alert
        :title="getConfigStatusTitle"
        :type="getConfigStatusType"
        :description="getConfigStatusDescription"
        :closable="false"
        size="small"
        style="margin-bottom: 10px;"
      />

      <!-- 简化的按钮区域 -->
      <div class="button-group">
        <el-button
          type="primary"
          @click="saveConfigAndNext"
          :loading="loading"
          size="large"
          class="primary-button"
          :icon="Check"
        >
          💾 保存配置并继续
        </el-button>
        <el-button
          @click="saveConfig"
          :loading="loading"
          class="secondary-button"
          :icon="DocumentCopy"
        >
          📄 暂存配置
        </el-button>
      </div>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, nextTick, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, DocumentCopy } from '@element-plus/icons-vue'
import MusicLibrary from './MusicLibrary.vue'
import { getAccounts } from '@/api/social'

const props = defineProps({
  taskId: {
    type: String,
    required: true
  },
  existingConfig: {
    type: Object,
    default: null
  },
  taskData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['config-completed', 'config-changed'])

// 标签输入相关
const inputTagVisible = ref(false)
const inputTagValue = ref('')
const tagInput = ref(null)

// 加载状态
const loading = ref(false)

// 是否正在恢复配置（避免恢复时触发保存）
const isRestoring = ref(false)

// 折叠面板状态 - 默认展开高级设置，确保有足够内容触发滚动
const activeCollapse = ref(['advanced'])

// 配置数据
const config = reactive({
  titleTemplate: '{filename}',  // 默认标题模版为 {filename}
  description: '',
  tags: [] as string[],
  contentType: 'shorts',  // 默认内容类型为短视频
  privacyStatus: 'unlisted',  // 默认隐私类型为不公开
  scheduleType: 'immediate',
  scheduleTime: '',
  category: '',
  language: 'zh',
  license: 'youtube',
  selectedMusic: [] as any[],  // 新增选中的音乐列表
  // 音频设置
  keepOriginalAudio: false,  // 是否保留原声
  originalAudioPercentage: 50,  // 原声音量百分比
  musicVolumePercentage: 50   // 背景音乐音量百分比
})

// 分类选项
const categories = [
  { value: 'entertainment', label: '娱乐' },
  { value: 'music', label: '音乐' },
  { value: 'sports', label: '体育' },
  { value: 'gaming', label: '游戏' },
  { value: 'education', label: '教育' }
]

// 配置状态提示
const getConfigStatusTitle = computed(() => {
  if (config.contentType === 'shorts') {
    return '📱 短视频配置'
  }
  return '🎬 普通视频配置'
})

const getConfigStatusType = computed(() => {
  if (!config.titleTemplate || !config.description) {
    return 'warning'
  }
  return 'success'
})

const getConfigStatusDescription = computed(() => {
  const issues = []
  if (!config.titleTemplate) issues.push('标题模板')
  if (!config.description) issues.push('视频描述')

  if (issues.length > 0) {
    return `请完善以下必填项：${issues.join('、')}`
  }

  if (config.contentType === 'shorts') {
    return '短视频配置已就绪，点击"保存配置并继续"开始执行任务'
  }
  return '普通视频配置已就绪，点击"保存配置并继续"开始执行任务'
})

// 获取账号信息并设置默认描述
const fetchAccountInfo = async () => {
  if (!props.taskData?.account_id) {
    console.log('没有账号ID，跳过获取账号信息')
    return
  }

  try {
    console.log('获取账号信息，账号ID:', props.taskData.account_id)
    const response = await getAccounts()

    if (response?.data?.data && Array.isArray(response.data.data)) {
      const account = response.data.data.find(acc => acc.id === props.taskData.account_id || acc._id === props.taskData.account_id)

      if (account && account.description) {
        console.log('找到账号备注:', account.description)
        // 只有在描述为空时才设置默认值
        if (!config.description) {
          config.description = account.description
          console.log('设置默认视频描述为账号备注:', account.description)
        }
      } else {
        console.log('账号没有备注信息或未找到账号')
      }
    }
  } catch (error) {
    console.error('获取账号信息失败:', error)
  }
}

// 初始化加载
onMounted(async () => {
  // 如果有已存在的配置，优先使用
  if (props.existingConfig) {
    restoreConfig(props.existingConfig)
  } else {
    await fetchTaskConfig()
  }

  // 获取账号信息并设置默认描述
  await fetchAccountInfo()
})

// 监听existingConfig的变化
watch(() => props.existingConfig, (newConfig) => {
  if (newConfig) {
    console.log('检测到existingConfig变化，恢复配置:', newConfig)
    restoreConfig(newConfig)
  }
}, { deep: true })

// 监听taskData的变化
watch(() => props.taskData, async (newTaskData) => {
  if (newTaskData) {
    console.log('检测到taskData变化，获取账号信息:', newTaskData)
    await fetchAccountInfo()
  }
}, { deep: true })

// 防抖函数
let configChangeTimer: NodeJS.Timeout | null = null

// 监听config的变化，实时保存配置状态（防抖）
watch(config, (newConfig) => {
  // 如果正在恢复配置，不触发保存
  if (isRestoring.value) {
    return
  }

  // 清除之前的定时器
  if (configChangeTimer) {
    clearTimeout(configChangeTimer)
  }

  // 设置新的定时器，500ms后执行
  configChangeTimer = setTimeout(() => {
    console.log('📝 配置实时保存:', newConfig.contentType || '未设置')
    // 将Proxy对象转换为普通对象
    const configToSend = JSON.parse(JSON.stringify(newConfig))
    emit('config-changed', configToSend)
  }, 500)
}, { deep: true })

// 恢复配置数据
const restoreConfig = (existingConfig: any) => {
  console.log('🔄 恢复配置:', existingConfig.contentType || '未设置')

  // 设置恢复标志，避免触发保存
  isRestoring.value = true

  // 恢复所有配置字段
  Object.keys(existingConfig).forEach(key => {
    if (config.hasOwnProperty(key)) {
      config[key] = existingConfig[key]
    }
  })

  ElMessage.success('✅ 配置数据已恢复')

  // 恢复完成后，重置标志
  setTimeout(() => {
    isRestoring.value = false
  }, 100)
}

// 获取任务配置
const fetchTaskConfig = async () => {
  try {
    loading.value = true
    // 这里应该调用API获取任务配置
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟数据 - 保持默认值，不覆盖已设置的值
    const defaultConfig = {
      tags: ['视频', '精彩', 'ThunderHub'],
      scheduleType: 'immediate',
      scheduleTime: '',
      category: 'entertainment',
      language: 'zh',
      license: 'youtube'
    }

    // 只更新不存在的字段，保持用户已有的选择和默认值
    Object.keys(defaultConfig).forEach(key => {
      if (config[key] === undefined || (Array.isArray(config[key]) && config[key].length === 0) || config[key] === '') {
        config[key] = defaultConfig[key]
      }
    })

    console.log('配置加载完成，当前contentType:', config.contentType)
    console.log('配置加载完成，当前titleTemplate:', config.titleTemplate)
    console.log('配置加载完成，当前privacyStatus:', config.privacyStatus)
    ElMessage.success('配置加载成功')
  } catch (error) {
    console.error('获取任务配置失败:', error)
    ElMessage.error('获取任务配置失败')
  } finally {
    loading.value = false
  }
}

// 显示标签输入框
const showTagInput = () => {
  inputTagVisible.value = true
  nextTick(() => {
    tagInput.value.focus()
  })
}

// 添加标签
const addTag = () => {
  if (inputTagValue.value) {
    if (!config.tags.includes(inputTagValue.value)) {
      config.tags.push(inputTagValue.value)
    }
    inputTagValue.value = ''
  }
  inputTagVisible.value = false
}

// 移除标签
const removeTag = (tag: string) => {
  const index = config.tags.indexOf(tag)
  if (index > -1) {
    config.tags.splice(index, 1)
  }
}

// 处理音乐选择
const handleMusicSelected = (selectedMusic: any[]) => {
  config.selectedMusic = selectedMusic
  console.log('选中的音乐:', selectedMusic)
}

// 处理保留原声开关变化
const handleKeepOriginalAudioChange = (value: boolean) => {
  console.log('保留原声设置变化:', value)

  if (value) {
    // 开启保留原声时，设置默认音量比例
    if (config.originalAudioPercentage === 0) {
      config.originalAudioPercentage = 50
    }
    if (config.musicVolumePercentage === 0) {
      config.musicVolumePercentage = 50
    }
    ElMessage.info('已开启原声保留，可以调节音量比例')
  } else {
    ElMessage.info('已关闭原声保留，将仅使用背景音乐')
  }
}

// 格式化音量提示
const formatVolumeTooltip = (value: number) => {
  return `${value}%`
}

// 应用音量预设
const applyVolumePreset = (preset: string) => {
  switch (preset) {
    case 'original-main':
      config.originalAudioPercentage = 100
      config.musicVolumePercentage = 15
      ElMessage.success('已应用"原声为主"预设：原声100%，背景音乐15%')
      break
    case 'balanced':
      config.originalAudioPercentage = 50
      config.musicVolumePercentage = 50
      ElMessage.success('已应用"平衡混音"预设：原声50%，背景音乐50%')
      break
    case 'music-main':
      config.originalAudioPercentage = 15
      config.musicVolumePercentage = 100
      ElMessage.success('已应用"音乐为主"预设：原声15%，背景音乐100%')
      break
  }
}

// 检查当前是否为指定预设
const isCurrentPreset = (preset: string) => {
  switch (preset) {
    case 'original-main':
      return config.originalAudioPercentage === 100 && config.musicVolumePercentage === 15
    case 'balanced':
      return config.originalAudioPercentage === 50 && config.musicVolumePercentage === 50
    case 'music-main':
      return config.originalAudioPercentage === 15 && config.musicVolumePercentage === 100
    default:
      return false
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    loading.value = true

    console.log('准备保存任务配置:', config)

    // 将Proxy对象转换为普通对象，确保序列化正确
    const configToSend = JSON.parse(JSON.stringify(config))
    console.log('转换后的配置对象:', configToSend)

    // 直接触发事件，让父组件处理API调用
    // 这样可以确保配置数据正确传递
    emit('config-completed', configToSend)

    ElMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存任务配置失败:', error)
    ElMessage.error('保存任务配置失败')
  } finally {
    loading.value = false
  }
}

// 保存配置并继续下一步
const saveConfigAndNext = async () => {
  try {
    loading.value = true

    console.log('=== 保存配置并继续 ===')
    console.log('当前完整配置:', config)
    console.log('特别检查contentType:', config.contentType)
    console.log('特别检查selectedMusic:', config.selectedMusic)
    console.log('selectedMusic类型:', typeof config.selectedMusic)
    console.log('selectedMusic长度:', config.selectedMusic ? config.selectedMusic.length : 'undefined')
    console.log('音频设置检查:')
    console.log('  - keepOriginalAudio:', config.keepOriginalAudio)
    console.log('  - originalAudioPercentage:', config.originalAudioPercentage)
    console.log('  - musicVolumePercentage:', config.musicVolumePercentage)

    if (config.selectedMusic && config.selectedMusic.length > 0) {
      console.log('✅ 检测到选中的音乐:')
      config.selectedMusic.forEach((music, index) => {
        console.log(`  音乐${index + 1}:`, music)
      })
    } else {
      console.log('❌ 未检测到选中的音乐')
    }

    // 将Proxy对象转换为普通对象，确保序列化正确
    const configToSend = JSON.parse(JSON.stringify(config))
    console.log('转换后的配置对象:', configToSend)
    console.log('转换后的selectedMusic:', configToSend.selectedMusic)

    // 触发配置完成事件，父组件会处理保存并自动跳转到下一步
    emit('config-completed', configToSend)

    ElMessage.success('配置保存成功，即将进入下一步')
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.task-config {
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto; /* 头部 内容 底部 */
  overflow: hidden;
}

.config-header {
  padding: 12px 15px 8px 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  grid-row: 1;
}

.config-header h2 {
  margin: 0 0 4px 0;
  color: #409EFF;
  font-size: 1.3rem;
}

.config-description {
  margin: 0;
  color: #606266;
  font-size: 13px;
}

.config-content {
  grid-row: 2;
  overflow-y: scroll; /* 强制显示滚动条 */
  padding: 10px 15px;
  min-height: 0;
  max-height: 500px; /* 限制最大高度，确保滚动条显示 */
}

/* 自定义滚动条样式 */
.config-content::-webkit-scrollbar {
  width: 8px; /* 增加宽度让滚动条更明显 */
}

.config-content::-webkit-scrollbar-thumb {
  background-color: #c0c4cc; /* 更深的颜色 */
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.config-content::-webkit-scrollbar-thumb:hover {
  background-color: #a8abb2;
}

.config-content::-webkit-scrollbar-track {
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.config-form {
  max-width: 100%;
}

/* 表单项间距优化 */
.config-form :deep(.el-form-item) {
  margin-bottom: 12px;
}

.config-form :deep(.el-form-item__label) {
  padding-bottom: 4px;
  font-size: 13px;
}

.config-form :deep(.el-form-item__content) {
  line-height: 1.4;
}

/* 配置区块样式 */
.config-section {
  margin-bottom: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.config-section :deep(.el-card__header) {
  padding: 8px 12px;
  background-color: #f8f9fa;
}

.config-section :deep(.el-card__body) {
  padding: 12px;
}

.section-title {
  font-weight: 600;
  color: #409EFF;
  font-size: 14px;
}

/* 折叠面板样式 */
.config-collapse {
  margin-bottom: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.config-collapse :deep(.el-collapse-item__header) {
  padding: 8px 12px;
  background-color: #f8f9fa;
  font-weight: 600;
  color: #409EFF;
  font-size: 14px;
  height: auto;
  min-height: 40px;
}

.config-collapse :deep(.el-collapse-item__content) {
  padding: 12px;
}

.config-collapse :deep(.el-collapse-item__wrap) {
  border-bottom: none;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.config-footer {
  padding: 10px 15px;
  background-color: #fafafa;
  border-top: 1px solid #ebeef5;
  grid-row: 3;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 100px;
  margin-right: 8px;
  vertical-align: bottom;
}

.schedule-time {
  margin-top: 10px;
}

/* 音频设置样式 */
.music-section {
  margin-bottom: 12px;
}

.audio-settings {
  margin-top: 12px;
}

.volume-controls {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
  margin-top: 6px;
}

.audio-preview {
  font-size: 13px;
  line-height: 1.5;
}

.audio-preview p {
  margin: 4px 0;
}

.setting-hint {
  margin-top: 5px;
  color: #909399;
}

.setting-hint small {
  font-size: 12px;
  line-height: 1.4;
}

/* 高级设置卡片样式 */
.advanced-settings-card {
  margin: 20px 0;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.advanced-settings-card .card-header {
  font-weight: bold;
  color: #409EFF;
}

/* 按钮样式 */
.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 8px;
}

.primary-button {
  font-weight: bold;
  padding: 12px 30px;
  border-radius: 6px;
}

.secondary-button {
  padding: 10px 20px;
  border-radius: 6px;
}

.help-text {
  text-align: center;
}

.help-text p {
  color: #909399;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
}

.content-type-highlight {
  color: #409EFF;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-header {
    padding: 15px 15px 10px 15px;
  }

  .config-content {
    padding: 15px;
  }

  .config-footer {
    padding: 15px;
  }

  .button-group {
    flex-direction: column;
    gap: 10px;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
  }
}
</style>
