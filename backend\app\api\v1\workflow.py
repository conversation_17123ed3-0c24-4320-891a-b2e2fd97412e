"""
工作流管理API
提供工作流状态查询和控制功能
"""

import logging
import redis
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from motor.motor_asyncio import AsyncIOMotorClient

from app.config.database import DatabaseConfig
from app.services.workflow_state_service import WorkflowStateService
from app.core.schemas.content_models import WorkflowContext

logger = logging.getLogger(__name__)

router = APIRouter(tags=["工作流管理"])

# 响应模型
class WorkflowResponse(BaseModel):
    """工作流响应"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# 依赖注入
async def get_workflow_service() -> WorkflowStateService:
    """获取工作流状态服务"""
    db_config = DatabaseConfig()
    client = AsyncIOMotorClient(db_config.mongodb_url)
    db = client[db_config.mongodb_name]
    return WorkflowStateService(db)

def get_redis_client():
    """获取Redis客户端"""
    return redis.Redis(host='***************', port=6379, db=1, decode_responses=True)

async def get_real_platform_id(db, platform_object_id: str) -> str:
    """将平台ObjectId转换为真实的平台ID

    Args:
        db: 数据库连接
        platform_object_id: 平台的MongoDB ObjectId

    Returns:
        str: 真实的平台ID（如"youtube", "tiktok"）
    """
    try:
        from bson import ObjectId

        # 如果已经是真实的平台ID，直接返回
        if platform_object_id in ["youtube", "tiktok", "douyin", "facebook", "instagram"]:
            return platform_object_id

        # 查询平台信息
        if len(platform_object_id) == 24:
            platform = await db.social_platforms.find_one({"_id": ObjectId(platform_object_id)})
        else:
            platform = await db.social_platforms.find_one({"_id": platform_object_id})

        if platform and "id" in platform:
            return platform["id"]
        else:
            logger.warning(f"未找到平台信息: {platform_object_id}")
            # 使用硬编码映射作为降级方案
            platform_mapping = {
                "681efeeecd836bd64b9c2a1e": "youtube",
                "681efeeecd836bd64b9c2a20": "tiktok",
                "681efeeecd836bd64b9c2a22": "douyin"
            }
            return platform_mapping.get(platform_object_id, "youtube")  # 默认返回youtube

    except Exception as e:
        logger.error(f"转换平台ID失败: {str(e)}")
        return "youtube"  # 默认返回youtube

async def get_workflow_config_from_core(platform_id: str, content_type: str) -> Optional[Dict[str, Any]]:
    """从Core服务获取工作流配置

    Args:
        platform_id: 平台ID
        content_type: 内容类型

    Returns:
        工作流配置字典，如果获取失败返回None
    """
    try:
        logger.info(f"开始从Core服务获取工作流配置: platform_id={platform_id}, content_type={content_type}")

        from app.core.grpc_client import get_task_client

        # 获取Core服务的任务客户端
        logger.info("正在获取Core服务任务客户端...")
        task_client = await get_task_client()
        if not task_client:
            logger.error("无法获取Core服务任务客户端")
            return None

        logger.info("成功获取Core服务任务客户端")

        # 调用Core服务的GetWorkflowConfig接口
        workflow_config = await task_client.get_workflow_config(platform_id, content_type)

        if workflow_config and workflow_config.get("success"):
            logger.info(f"从Core服务获取工作流配置成功: {workflow_config.get('workflow_name')}")

            # 转换步骤格式
            steps = []
            for step in workflow_config.get("steps", []):
                step_data = {
                    "id": step.get("id", ""),
                    "name": step.get("name", ""),
                    "description": step.get("description", ""),
                    "action": step.get("action", ""),
                    "status": "pending",
                    "progress": 0,
                    "required": step.get("required", True),
                    "timeout": step.get("timeout", 30),
                    "retry_count": step.get("retry_count", 3),
                    "wait_after": step.get("wait_after", 0),
                    "condition": step.get("condition", ""),
                    "element": step.get("element", ""),
                    "notes": step.get("notes", ""),
                    "logs": []
                }

                # 解析参数
                parameters_str = step.get("parameters", "")
                if parameters_str:
                    try:
                        import json
                        step_data["parameters"] = json.loads(parameters_str)
                    except json.JSONDecodeError:
                        step_data["parameters"] = {}
                else:
                    step_data["parameters"] = {}

                steps.append(step_data)

            # 解析配置
            config_str = workflow_config.get("config", "")
            config = {}
            if config_str:
                try:
                    import json
                    config = json.loads(config_str)
                except json.JSONDecodeError:
                    config = {}

            return {
                "workflow_id": workflow_config.get("workflow_id", ""),
                "workflow_name": workflow_config.get("workflow_name", ""),
                "workflow_description": workflow_config.get("workflow_description", ""),
                "workflow_version": workflow_config.get("workflow_version", "1.0"),
                "steps": steps,
                "config": config
            }
        else:
            logger.warning(f"Core服务返回工作流配置失败: {workflow_config.get('error', '未知错误')}")
            return None

    except Exception as e:
        logger.error(f"从Core服务获取工作流配置异常: {str(e)}", exc_info=True)
        return None

async def merge_workflow_config_and_state(
    config: Dict[str, Any],
    context: Optional[Any],
    task_id: str
) -> Dict[str, Any]:
    """合并工作流配置和执行状态

    Args:
        config: 工作流配置
        context: 工作流执行上下文（可能为None）
        task_id: 任务ID

    Returns:
        合并后的工作流数据
    """
    try:
        # 基础工作流数据
        workflow_data = {
            "workflow_id": config.get("workflow_id", f"workflow_{task_id}"),
            "workflow_name": config.get("workflow_name", "未知工作流"),
            "workflow_version": config.get("workflow_version", "1.0"),
            "current_step_index": 0,
            "total_steps": len(config.get("steps", [])),
            "can_resume": True,
            "context_data": {},
            "checkpoint_data": {},
            "config": config.get("config", {})
        }

        # 从配置获取步骤定义
        config_steps = config.get("steps", [])

        # 如果有执行上下文，合并执行状态
        if context:
            logger.info(f"合并执行状态: task_id={task_id}")
            context_dict = context.dict() if hasattr(context, 'dict') else context

            # 更新工作流级别的状态
            workflow_data["current_step_index"] = context_dict.get("current_step_index", 0)
            workflow_data["context_data"] = context_dict.get("context_data", {})
            workflow_data["checkpoint_data"] = context_dict.get("checkpoint_data", {})
            workflow_data["can_resume"] = context_dict.get("can_resume", True)

            # 合并步骤状态
            context_steps = context_dict.get("steps", [])
            context_steps_dict = {step.get("id"): step for step in context_steps}

            # 更新步骤状态
            merged_steps = []
            for config_step in config_steps:
                step_id = config_step.get("id")
                merged_step = config_step.copy()

                # 如果执行上下文中有该步骤的状态，合并状态信息
                if step_id in context_steps_dict:
                    context_step = context_steps_dict[step_id]
                    merged_step.update({
                        "status": context_step.get("status", "pending"),
                        "progress": context_step.get("progress", 0),
                        "start_time": context_step.get("start_time"),
                        "end_time": context_step.get("end_time"),
                        "duration": context_step.get("duration"),
                        "error_message": context_step.get("error_message"),
                        "retry_count": context_step.get("retry_count", 0),
                        "logs": context_step.get("logs", [])
                    })
                else:
                    # 确保步骤有默认状态
                    merged_step.setdefault("status", "pending")
                    merged_step.setdefault("progress", 0)
                    merged_step.setdefault("retry_count", 0)
                    merged_step.setdefault("logs", [])

                merged_steps.append(merged_step)

            workflow_data["steps"] = merged_steps
            logger.info(f"成功合并工作流状态: {len(merged_steps)}个步骤")
        else:
            # 没有执行上下文，使用配置中的默认状态
            logger.info(f"使用配置默认状态: task_id={task_id}")
            steps = []
            for step in config_steps:
                step_copy = step.copy()
                step_copy.setdefault("status", "pending")
                step_copy.setdefault("progress", 0)
                step_copy.setdefault("retry_count", 0)
                step_copy.setdefault("logs", [])
                steps.append(step_copy)
            workflow_data["steps"] = steps

        return workflow_data

    except Exception as e:
        logger.error(f"合并工作流配置和状态失败: {str(e)}", exc_info=True)
        # 返回基础配置
        return {
            "workflow_id": config.get("workflow_id", f"workflow_{task_id}"),
            "workflow_name": config.get("workflow_name", "未知工作流"),
            "workflow_version": config.get("workflow_version", "1.0"),
            "current_step_index": 0,
            "total_steps": len(config.get("steps", [])),
            "steps": config.get("steps", []),
            "can_resume": True,
            "context_data": {},
            "checkpoint_data": {},
            "config": config.get("config", {})
        }

# 已删除直接文件读取函数 - 违反微服务架构原则
# Backend服务只能通过gRPC与Core服务通信，不允许直接访问Core服务的文件系统

async def generate_basic_workflow(task_id: str, workflow_name: str, content_type: str) -> Dict[str, Any]:
    """根据任务信息生成基础工作流结构"""

    # 根据内容类型定义不同的工作流步骤
    if content_type == "shorts" or "短视频" in workflow_name:
        steps = [
            {
                "id": "step_1",
                "name": "初始化环境",
                "description": "启动浏览器并初始化上传环境",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_2",
                "name": "登录验证",
                "description": "验证YouTube账号登录状态",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_3",
                "name": "选择视频文件",
                "description": "选择要上传的短视频文件",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_4",
                "name": "上传视频",
                "description": "将短视频文件上传到YouTube",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_5",
                "name": "填写信息",
                "description": "填写视频标题、描述等信息",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_6",
                "name": "发布视频",
                "description": "确认发布短视频",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            }
        ]
    else:
        # 默认工作流
        steps = [
            {
                "id": "step_1",
                "name": "任务初始化",
                "description": "初始化任务执行环境",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_2",
                "name": "执行任务",
                "description": "执行主要任务逻辑",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_3",
                "name": "完成任务",
                "description": "完成任务并清理资源",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            }
        ]

    return {
        "workflow_id": f"workflow_{task_id}",
        "workflow_name": workflow_name,
        "workflow_version": "1.0",
        "current_step_index": 0,
        "total_steps": len(steps),
        "steps": steps,
        "can_resume": True,
        "context_data": {},
        "checkpoint_data": {}
    }

@router.get("/{task_id}/workflow")
async def get_task_workflow(
    task_id: str,
    workflow_service: WorkflowStateService = Depends(get_workflow_service)
):
    """获取任务工作流详情"""
    try:
        logger.info(f"获取任务工作流详情: {task_id}")

        # 1. 首先尝试从Redis获取实时状态
        redis_client = get_redis_client()
        try:
            workflow_key = f"workflow_state:{task_id}"
            workflow_data = redis_client.get(workflow_key)
            if workflow_data:
                try:
                    workflow_dict = json.loads(workflow_data)
                    logger.info(f"从Redis获取到工作流状态: task_id={task_id}")
                    return {
                        "success": True,
                        "message": "获取工作流详情成功",
                        "data": workflow_dict,
                        "source": "redis"
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Redis中的工作流数据格式错误")
        except Exception as redis_error:
            logger.warning(f"Redis查询失败: {redis_error}")

        # 2. 从MongoDB中查找工作流状态
        workflow_context = await workflow_service.get_workflow_context(task_id)
        if workflow_context:
            logger.info(f"从MongoDB获取到工作流状态: task_id={task_id}")
            return {
                "success": True,
                "message": "获取工作流详情成功",
                "data": workflow_context.dict(),
                "source": "workflow_states"
            }

        # 3. 尝试获取工作流配置
        db_config = DatabaseConfig()
        client = AsyncIOMotorClient(db_config.mongodb_url)
        db = client[db_config.mongodb_name]

        # 查找任务信息
        task = await db.social_tasks.find_one({"task_id": task_id})
        if task:
            # 获取任务的平台ObjectId
            platform_object_id = task.get("platform_id", "")
            content_type = task.get("metadata", {}).get("contentType", "")

            # 将平台ObjectId转换为真实的平台ID
            platform_id = await get_real_platform_id(db, platform_object_id)
            logger.info(f"平台ID映射: {platform_object_id} -> {platform_id}")

            # 如果content_type为空，尝试从其他字段获取
            if not content_type:
                content_type = task.get("metadata", {}).get("content_type", "")

                # 如果仍然为空，且是子任务，从父任务继承
                if not content_type and task.get("task_type") == "subtask" and task.get("parent_task_id"):
                    try:
                        parent_task = await db.social_tasks.find_one({"task_id": task.get("parent_task_id")})
                        if parent_task and parent_task.get("metadata"):
                            parent_metadata = parent_task.get("metadata", {})
                            content_type = parent_metadata.get("contentType", "")
                            logger.info(f"🔧 从父任务继承contentType: {content_type}")
                    except Exception as e:
                        logger.warning(f"从父任务获取contentType失败: {str(e)}")

                # 如果还是为空，根据工作流名称推断内容类型
                if not content_type:
                    workflow_name = task.get("workflow_name", "").lower()
                    if "shorts" in workflow_name or "短视频" in workflow_name:
                        content_type = "shorts"
                    elif "video" in workflow_name or "视频" in workflow_name:
                        content_type = "video"
                    else:
                        content_type = "video"  # 默认值

            logger.info(f"最终确定的contentType: {content_type}")

            logger.info(f"通过gRPC从Core服务获取工作流配置: platform_id={platform_id}, content_type={content_type}")

            # 只通过gRPC从Core服务获取工作流配置，不允许直接读取文件
            core_workflow = await get_workflow_config_from_core(platform_id, content_type)

            if core_workflow:
                logger.info(f"✅ 从Core服务获取工作流配置成功: {core_workflow.get('workflow_name')}")
                # 构建工作流数据
                workflow_data = {
                    "workflow_id": core_workflow.get("workflow_id", f"workflow_{task_id}"),
                    "workflow_name": core_workflow.get("workflow_name", "未知工作流"),
                    "workflow_version": core_workflow.get("workflow_version", "1.0"),
                    "current_step_index": 0,
                    "total_steps": len(core_workflow.get("steps", [])),
                    "steps": core_workflow.get("steps", []),
                    "can_resume": True,
                    "context_data": {},
                    "checkpoint_data": {},
                    "config": core_workflow.get("config", {})
                }

                return {
                    "success": True,
                    "message": "获取工作流详情成功",
                    "data": workflow_data,
                    "source": "core_grpc"
                }
            else:
                logger.error(f"❌ 无法从Core服务获取工作流配置: platform_id={platform_id}, content_type={content_type}")
                # 不再降级到文件读取，直接返回错误或生成基础工作流

        # 1. 如果配置文件读取失败，从MongoDB中查找工作流状态
        workflow_context = await workflow_service.get_workflow_context(task_id)

        if workflow_context:
            logger.info(f"从MongoDB获取到工作流状态: task_id={task_id}")
            return {
                "success": True,
                "message": "获取工作流详情成功",
                "data": workflow_context.dict(),
                "source": "workflow_states"
            }

        # 2. 如果MongoDB中没有，尝试从Redis获取实时状态（优先使用标准键名）
        redis_client = get_redis_client()
        try:
            # 首先尝试标准的工作流状态键
            workflow_key = f"workflow_state:{task_id}"
            logger.info(f"尝试从Redis获取工作流状态: {workflow_key}")

            workflow_data = redis_client.get(workflow_key)
            if workflow_data:
                try:
                    workflow_dict = json.loads(workflow_data)
                    logger.info(f"✅ 从Redis获取到工作流数据: task_id={task_id}")
                    logger.info(f"✅ Redis数据: workflow_name={workflow_dict.get('workflow_name')}, total_steps={workflow_dict.get('total_steps')}")

                    return {
                        "success": True,
                        "message": "获取工作流详情成功",
                        "data": workflow_dict,
                        "source": "redis"
                    }
                except json.JSONDecodeError as json_error:
                    logger.warning(f"Redis中的工作流数据格式错误: {json_error}")
            else:
                logger.info(f"Redis中没有找到工作流状态: {workflow_key}")

                # 降级方案：查找任务相关的Redis键
                task_keys = redis_client.keys(f"*{task_id}*")
                if task_keys:
                    logger.info(f"从Redis找到任务相关键: {task_keys}")

                    # 尝试获取工作流状态
                    workflow_key = None
                    for key in task_keys:
                        if 'workflow' in key.lower():
                            workflow_key = key
                            break

                    if workflow_key:
                        workflow_data = redis_client.get(workflow_key)
                        if workflow_data:
                            try:
                                workflow_dict = json.loads(workflow_data)
                                logger.info(f"从Redis获取到工作流数据: task_id={task_id}")
                                return {
                                    "success": True,
                                    "message": "获取工作流详情成功",
                                    "data": workflow_dict,
                                    "source": "redis"
                                }
                            except json.JSONDecodeError:
                                logger.warning(f"Redis中的工作流数据格式错误: {workflow_data}")

        except Exception as redis_error:
            logger.warning(f"Redis查询失败: {redis_error}")

        # 4. 如果没有找到任务，返回错误
        raise HTTPException(status_code=404, detail="任务不存在")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工作流详情失败: task_id={task_id}, error={str(e)}")

        # 最后的降级方案：生成基础工作流
        try:
            basic_workflow = await generate_basic_workflow(task_id, "未知工作流", "")
            return {
                "success": True,
                "message": "获取工作流详情成功（使用基础结构）",
                "data": basic_workflow,
                "source": "generated"
            }
        except:
            raise HTTPException(status_code=500, detail="获取工作流详情失败")


@router.post("/{task_id}/workflow/state")
async def save_workflow_state(task_id: str, workflow_data: dict):
    """保存工作流状态（Core服务调用）"""
    try:
        logger.info(f"Core服务保存工作流状态: {task_id}")

        # 获取数据库连接
        db_config = DatabaseConfig()
        client = AsyncIOMotorClient(db_config.mongodb_url)
        db = client[db_config.mongodb_name]

        # 保存到workflow_states集合
        workflow_data['task_id'] = task_id
        workflow_data['updated_at'] = datetime.now()

        result = await db.workflow_states.update_one(
            {"task_id": task_id},
            {"$set": workflow_data},
            upsert=True
        )

        logger.info(f"工作流状态保存成功: task_id={task_id}, modified_count={result.modified_count}")

        # 同时保存到Redis
        try:
            import redis.asyncio as redis
            redis_client = redis.Redis.from_url("redis://***************:6379/1")

            redis_key = f"workflow_state:{task_id}"
            import json
            await redis_client.set(redis_key, json.dumps(workflow_data, default=str), ex=3600)

            logger.info(f"工作流状态已同步到Redis: {redis_key}")

        except Exception as redis_error:
            logger.warning(f"同步工作流状态到Redis失败: {str(redis_error)}")

        return {
            "success": True,
            "message": "工作流状态保存成功"
        }

    except Exception as e:
        logger.error(f"保存工作流状态失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"保存工作流状态失败: {str(e)}")

@router.get("/{task_id}/workflow/steps/{step_id}/logs")
async def get_workflow_step_logs(task_id: str, step_id: str):
    """获取工作流步骤日志"""
    # 返回模拟日志数据
    mock_logs = [
        {
            "timestamp": "2024-01-01 10:00:00",
            "level": "info",
            "message": f"步骤 {step_id} 开始执行"
        },
        {
            "timestamp": "2024-01-01 10:00:30",
            "level": "info",
            "message": f"步骤 {step_id} 执行中..."
        }
    ]

    return {
        "success": True,
        "message": "获取步骤日志成功",
        "data": {"logs": mock_logs}
    }

@router.post("/{task_id}/workflow/control")
async def control_workflow(task_id: str, request: dict):
    """控制工作流执行"""
    action = request.get("action", "").lower()

    # 暂时返回成功响应
    action_messages = {
        "pause": "工作流已暂停",
        "resume": "工作流已恢复",
        "retry": "工作流重试已启动",
        "skip": "当前步骤已跳过"
    }

    message = action_messages.get(action, f"执行动作: {action}")

    return {
        "success": True,
        "message": message
    }

@router.post("/{task_id}/workflow/resume")
async def resume_workflow_from_step(task_id: str, request: dict):
    """从指定步骤恢复工作流"""
    step_index = request.get("step_index", 0)
    return {
        "success": True,
        "message": f"工作流将从第 {step_index + 1} 步开始恢复执行"
    }

@router.post("/{task_id}/workflow/steps/{step_id}/retry")
async def retry_workflow_step(task_id: str, step_id: str):
    """重试工作流步骤"""
    return {
        "success": True,
        "message": "步骤重试已启动"
    }

@router.post("/{task_id}/workflow/steps/{step_id}/skip")
async def skip_workflow_step(task_id: str, step_id: str):
    """跳过工作流步骤"""
    return {
        "success": True,
        "message": "步骤已跳过"
    }

