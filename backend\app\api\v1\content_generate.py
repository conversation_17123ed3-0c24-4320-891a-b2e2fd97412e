"""
内容生成API路由
支持AI内容生成，包括文案、分镜、图片、视频等
集成ComfyUI等AI工具
"""

import logging
import asyncio
import os
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query, Request, BackgroundTasks
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
from bson import ObjectId
from app.core.schemas.social_repository import SocialDatabaseService
# 移除本地服务导入，改为通过gRPC调用Core服务

# 获取数据库连接
def get_db(request: Request):
    """获取数据库连接"""
    try:
        return request.app.state.mongo_db
    except AttributeError:
        # 如果没有数据库连接，返回None
        return None

def get_core_client():
    """获取Core客户端"""
    # 简化版本，暂时返回None
    return None

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/content-generate", tags=["内容生成"])

# 数据模型
class GenerationConfig(BaseModel):
    comfyui_workflow: Optional[str] = None
    model_settings: Optional[Dict[str, Any]] = None
    output_settings: Optional[Dict[str, Any]] = None
    duration: Optional[int] = Field(30, description="视频时长(秒)")
    resolution: Optional[str] = Field("1920x1080", description="分辨率")
    fps: Optional[int] = Field(30, description="帧率")
    quality: Optional[str] = Field("high", description="质量: low, medium, high")

class CreateTaskRequest(BaseModel):
    title: str = Field(..., description="任务名称")
    type: str = Field(..., description="生成类型: text, storyboard, image, video, full_pipeline")
    description: str = Field(..., description="内容描述")
    style: Optional[str] = Field("realistic", description="生成风格")
    count: int = Field(1, ge=1, le=10, description="生成数量")
    config: Optional[GenerationConfig] = None
    # 直接关联社交账号
    account_id: Optional[str] = Field(None, description="目标社交账号ID")
    platform_id: Optional[str] = Field(None, description="目标平台ID")
    auto_publish: bool = Field(False, description="生成完成后自动发布")

class GenerationResult(BaseModel):
    id: str
    type: str
    title: str
    content: Optional[str] = None
    preview_url: Optional[str] = None
    download_url: Optional[str] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None

class GenerationTask(BaseModel):
    id: str
    title: str
    type: str
    description: str
    style: Optional[str] = None
    count: int
    status: str  # pending, processing, completed, failed, paused
    progress: float = 0.0
    current_step: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    results: Optional[List[GenerationResult]] = None
    config: Optional[GenerationConfig] = None
    # 直接关联社交账号
    account_id: Optional[str] = None
    platform_id: Optional[str] = None
    account_name: Optional[str] = None  # 账号显示名称，用于前端显示
    auto_publish: bool = False
    error_message: Optional[str] = None

class TaskListResponse(BaseModel):
    tasks: List[GenerationTask]
    total: int
    page: int
    page_size: int

class TaskProgressResponse(BaseModel):
    task_id: str
    status: str
    progress: float
    current_step: Optional[str] = None
    error_message: Optional[str] = None

class UpdateTaskRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    progress: Optional[float] = None
    current_step: Optional[str] = None
    error_message: Optional[str] = None

# 辅助函数
def format_task_from_db(task_doc: dict) -> GenerationTask:
    """将数据库文档转换为GenerationTask对象"""
    # 移除MongoDB的_id字段
    if '_id' in task_doc:
        del task_doc['_id']

    # 转换日期字段
    for date_field in ['created_at', 'updated_at', 'completed_at']:
        if date_field in task_doc and task_doc[date_field]:
            if isinstance(task_doc[date_field], str):
                task_doc[date_field] = datetime.fromisoformat(task_doc[date_field].replace('Z', '+00:00'))

    return GenerationTask(**task_doc)

# API端点

@router.get("/tasks", response_model=TaskListResponse)
async def get_generation_tasks(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="任务状态过滤"),
    type: Optional[str] = Query(None, description="任务类型过滤"),
    account_id: Optional[str] = Query(None, description="按社交账号过滤"),
    platform_id: Optional[str] = Query(None, description="按平台过滤")
):
    """获取生成任务列表"""
    try:
        logger.info(f"获取内容生成任务列表: page={page}, page_size={page_size}, status={status}, type={type}, account_id={account_id}, platform_id={platform_id}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 构建查询条件
        query = {}
        if status:
            query["status"] = status
        if type:
            query["type"] = type
        if account_id:
            query["account_id"] = account_id
        if platform_id:
            query["platform_id"] = platform_id

        # 计算偏移量
        skip = (page - 1) * page_size

        # 查询任务总数
        total = await db_service.db.content_generation_tasks.count_documents(query)

        # 查询任务列表
        cursor = db_service.db.content_generation_tasks.find(query).sort("created_at", -1).skip(skip).limit(page_size)
        task_docs = await cursor.to_list(length=page_size)

        # 转换为响应格式
        tasks = [format_task_from_db(doc) for doc in task_docs]

        logger.info(f"找到 {len(tasks)} 个内容生成任务，总计 {total} 个")

        return TaskListResponse(
            tasks=tasks,
            total=total,
            page=page,
            page_size=page_size
        )

    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.post("/tasks", response_model=GenerationTask)
async def create_generation_task(
    request: Request,
    task_data: CreateTaskRequest,
    background_tasks: BackgroundTasks
):
    """创建生成任务"""
    try:
        logger.info(f"创建内容生成任务: {task_data.title}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 获取账号信息（如果指定了账号）
        account_name = None
        if task_data.account_id:
            try:
                account = await db_service.get_account(task_data.account_id)
                if account:
                    account_name = account.get("display_name") or account.get("username")
                    # 验证平台ID是否匹配
                    if task_data.platform_id and account.get("platform_id") != task_data.platform_id:
                        raise HTTPException(status_code=400, detail="账号与平台不匹配")
                else:
                    raise HTTPException(status_code=400, detail="指定的社交账号不存在")
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取账号信息失败: {str(e)}")
                raise HTTPException(status_code=500, detail="获取账号信息失败")

        # 创建任务文档
        now = datetime.now()
        task_doc = {
            "id": task_id,
            "title": task_data.title,
            "type": task_data.type,
            "description": task_data.description,
            "style": task_data.style or "realistic",
            "count": task_data.count,
            "status": "pending",
            "progress": 0.0,
            "current_step": "任务已创建，等待处理",
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "config": task_data.config.dict() if task_data.config else {},
            "results": [],
            # 直接关联账号信息
            "account_id": task_data.account_id,
            "platform_id": task_data.platform_id,
            "account_name": account_name,
            "auto_publish": task_data.auto_publish
        }

        # 保存到数据库
        await db_service.db.content_generation_tasks.insert_one(task_doc)

        # 创建任务对象
        new_task = format_task_from_db(task_doc.copy())

        # 启动后台任务处理
        background_tasks.add_task(process_generation_task, task_id, task_data, db_service)

        logger.info(f"创建内容生成任务成功: {task_id}")
        return new_task

    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@router.get("/tasks/{task_id}", response_model=GenerationTask)
async def get_generation_task(
    request: Request,
    task_id: str
):
    """获取任务详情"""
    try:
        logger.info(f"获取内容生成任务详情: {task_id}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 查询任务
        task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        if not task_doc:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 转换为响应格式
        task = format_task_from_db(task_doc)

        logger.info(f"找到内容生成任务: {task.title}")
        return task

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务详情失败: {str(e)}")

# 后台任务处理函数
async def process_generation_task(task_id: str, task_data: CreateTaskRequest, db_service: SocialDatabaseService):
    """处理内容生成任务 - 通过gRPC调用Core服务"""
    try:
        logger.info(f"通过Core服务处理内容生成任务: {task_id}")

        # TODO: 通过gRPC调用Core服务创建和启动任务
        # 这里暂时模拟处理过程
        await update_task_status(db_service, task_id, "processing", 0.1, "正在连接Core服务...")

        # 模拟处理步骤
        steps = [
            ("正在初始化生成环境...", 0.2),
            ("正在连接ComfyUI服务...", 0.3),
            ("正在生成内容...", 0.7),
            ("正在保存结果...", 0.9),
            ("生成完成", 1.0)
        ]

        for step_name, progress in steps:
            await update_task_status(db_service, task_id, "processing", progress, step_name)
            await asyncio.sleep(2)  # 模拟处理时间

        # 生成模拟结果
        results = []
        for i in range(task_data.count):
            result = {
                "id": f"{task_id}_result_{i+1}",
                "type": task_data.type,
                "title": f"{task_data.title} - 结果 {i+1}",
                "file_path": f"content_generation/{task_id}/result_{i+1}.mp4",
                "file_size": 1024 * 1024 * 10,  # 10MB
                "preview_url": f"/api/v1/content-generate/results/{task_id}_result_{i+1}/preview",
                "download_url": f"/api/v1/content-generate/results/{task_id}_result_{i+1}/download",
                "metadata": {
                    "style": task_data.style,
                    "generated_at": datetime.now().isoformat()
                }
            }
            results.append(result)

        # 更新任务为完成状态
        await db_service.db.content_generation_tasks.update_one(
            {"id": task_id},
            {
                "$set": {
                    "status": "completed",
                    "progress": 1.0,
                    "current_step": "生成完成",
                    "completed_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "results": results
                }
            }
        )

        logger.info(f"内容生成任务完成: {task_id}")

    except Exception as e:
        logger.error(f"处理内容生成任务失败: {task_id}, 错误: {str(e)}", exc_info=True)

        # 更新任务为失败状态
        await update_task_status(db_service, task_id, "failed", None, None, str(e))

async def update_task_status(db_service: SocialDatabaseService, task_id: str, status: str,
                           progress: Optional[float] = None, current_step: Optional[str] = None,
                           error_message: Optional[str] = None):
    """更新任务状态"""
    update_data = {
        "status": status,
        "updated_at": datetime.now().isoformat()
    }

    if progress is not None:
        update_data["progress"] = progress
    if current_step is not None:
        update_data["current_step"] = current_step
    if error_message is not None:
        update_data["error_message"] = error_message

    await db_service.db.content_generation_tasks.update_one(
        {"id": task_id},
        {"$set": update_data}
    )

@router.put("/tasks/{task_id}", response_model=GenerationTask)
async def update_generation_task(
    request: Request,
    task_id: str,
    update_data: UpdateTaskRequest
):
    """更新任务"""
    try:
        logger.info(f"更新内容生成任务: {task_id}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 检查任务是否存在
        task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        if not task_doc:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 构建更新数据
        update_fields = {"updated_at": datetime.now().isoformat()}

        if update_data.title is not None:
            update_fields["title"] = update_data.title
        if update_data.description is not None:
            update_fields["description"] = update_data.description
        if update_data.status is not None:
            update_fields["status"] = update_data.status
        if update_data.progress is not None:
            update_fields["progress"] = update_data.progress
        if update_data.current_step is not None:
            update_fields["current_step"] = update_data.current_step
        if update_data.error_message is not None:
            update_fields["error_message"] = update_data.error_message

        # 更新数据库
        await db_service.db.content_generation_tasks.update_one(
            {"id": task_id},
            {"$set": update_fields}
        )

        # 返回更新后的任务
        updated_task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        task = format_task_from_db(updated_task_doc)

        logger.info(f"更新内容生成任务成功: {task_id}")
        return task

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新任务失败: {str(e)}")

@router.delete("/tasks/{task_id}")
async def delete_generation_task(
    request: Request,
    task_id: str
):
    """删除任务"""
    try:
        logger.info(f"删除内容生成任务: {task_id}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 检查任务是否存在
        task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        if not task_doc:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 删除任务
        await db_service.db.content_generation_tasks.delete_one({"id": task_id})

        logger.info(f"删除内容生成任务成功: {task_id}")
        return {"message": "任务删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")

@router.post("/tasks/{task_id}/start")
async def start_generation_task(
    request: Request,
    task_id: str
):
    """开始生成任务"""
    try:
        logger.info(f"开始内容生成任务: {task_id}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 检查任务是否存在
        task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        if not task_doc:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 检查任务状态
        if task_doc.get("status") not in ["pending", "paused", "failed"]:
            raise HTTPException(status_code=400, detail="任务状态不允许启动")

        # 更新任务状态
        await update_task_status(db_service, task_id, "processing", 0.0, "正在启动任务...")

        # 创建任务数据
        task_data = CreateTaskRequest(
            title=task_doc["title"],
            type=task_doc["type"],
            description=task_doc["description"],
            style=task_doc.get("style"),
            count=task_doc["count"],
            config=GenerationConfig(**task_doc.get("config", {})) if task_doc.get("config") else None
        )

        # 启动后台处理
        asyncio.create_task(process_generation_task(task_id, task_data, db_service))

        return {"message": "任务已启动"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

@router.post("/tasks/{task_id}/pause")
async def pause_generation_task(
    request: Request,
    task_id: str
):
    """暂停生成任务"""
    try:
        logger.info(f"暂停内容生成任务: {task_id}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 更新任务状态
        await update_task_status(db_service, task_id, "paused", None, "任务已暂停")

        return {"message": "任务已暂停"}

    except Exception as e:
        logger.error(f"暂停任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"暂停任务失败: {str(e)}")

@router.post("/tasks/{task_id}/stop")
async def stop_generation_task(
    request: Request,
    task_id: str
):
    """停止生成任务"""
    try:
        logger.info(f"停止内容生成任务: {task_id}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 更新任务状态
        await update_task_status(db_service, task_id, "canceled", None, "任务已取消")

        return {"message": "任务已停止"}

    except Exception as e:
        logger.error(f"停止任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"停止任务失败: {str(e)}")

@router.get("/tasks/{task_id}/progress", response_model=TaskProgressResponse)
async def get_task_progress(
    request: Request,
    task_id: str
):
    """获取任务进度"""
    try:
        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 查询任务
        task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        if not task_doc:
            raise HTTPException(status_code=404, detail="任务不存在")

        return TaskProgressResponse(
            task_id=task_id,
            status=task_doc.get("status", "unknown"),
            progress=task_doc.get("progress", 0.0),
            current_step=task_doc.get("current_step"),
            error_message=task_doc.get("error_message")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务进度失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务进度失败: {str(e)}")

@router.get("/models")
async def get_available_models():
    """获取可用的生成模型"""
    return {
        "text_models": [
            {"id": "gpt-4", "name": "GPT-4", "description": "OpenAI GPT-4 文本生成模型"},
            {"id": "claude-3", "name": "Claude-3", "description": "Anthropic Claude-3 文本生成模型"}
        ],
        "image_models": [
            {"id": "stable-diffusion", "name": "Stable Diffusion", "description": "开源图像生成模型"},
            {"id": "midjourney", "name": "Midjourney", "description": "高质量艺术图像生成"}
        ],
        "video_models": [
            {"id": "runway", "name": "Runway ML", "description": "专业视频生成平台"},
            {"id": "pika", "name": "Pika Labs", "description": "AI视频生成工具"}
        ]
    }

@router.get("/comfyui/test")
async def test_comfyui_connection_api():
    """测试ComfyUI连接 - 通过Core服务"""
    try:
        # TODO: 通过gRPC调用Core服务测试ComfyUI连接
        # 暂时返回模拟数据
        return {
            "status": "connected",
            "version": "1.0.0",
            "queue_size": 0,
            "active_tasks": 0,
            "message": "通过Core服务连接ComfyUI"
        }

    except Exception as e:
        logger.error(f"测试ComfyUI连接失败: {str(e)}")
        return {
            "status": "disconnected",
            "error": str(e)
        }

@router.get("/workflows/templates")
async def get_workflow_templates(
    category: Optional[str] = None,
    tags: Optional[str] = None,
    active_only: bool = True
):
    """获取工作流模板列表"""
    try:
        # TODO: 通过gRPC调用Core服务获取模板
        # 暂时返回模拟数据
        templates = [
            {
                "id": "sdxl_image",
                "name": "SDXL图片生成",
                "description": "使用Stable Diffusion XL生成高质量图片",
                "category": "image",
                "version": "1.0.0",
                "author": "system",
                "tags": ["sdxl", "image", "generation"],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "is_active": True
            },
            {
                "id": "svd_video",
                "name": "SVD视频生成",
                "description": "使用Stable Video Diffusion生成视频",
                "category": "video",
                "version": "1.0.0",
                "author": "system",
                "tags": ["svd", "video", "generation"],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "is_active": True
            },
            {
                "id": "text_generation",
                "name": "文本生成",
                "description": "AI文本内容生成",
                "category": "text",
                "version": "1.0.0",
                "author": "system",
                "tags": ["text", "llm", "generation"],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "is_active": True
            }
        ]

        # 应用筛选
        if category:
            templates = [t for t in templates if t["category"] == category]

        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            templates = [t for t in templates if any(tag in t["tags"] for tag in tag_list)]

        if active_only:
            templates = [t for t in templates if t["is_active"]]

        return {
            "success": True,
            "templates": templates,
            "total": len(templates)
        }

    except Exception as e:
        logger.error(f"获取工作流模板失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/workflows/templates/{template_id}")
async def get_workflow_template(template_id: str):
    """获取单个工作流模板详情"""
    try:
        # TODO: 通过gRPC调用Core服务获取模板详情
        # 暂时返回模拟数据
        if template_id == "sdxl_image":
            template = {
                "id": "sdxl_image",
                "name": "SDXL图片生成",
                "description": "使用Stable Diffusion XL生成高质量图片",
                "category": "image",
                "version": "1.0.0",
                "author": "system",
                "tags": ["sdxl", "image", "generation"],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "is_active": True,
                "workflow_data": {
                    "3": {
                        "inputs": {
                            "seed": -1,
                            "steps": 20,
                            "cfg": 7.0,
                            "sampler_name": "euler",
                            "scheduler": "normal",
                            "denoise": 1.0,
                            "model": ["4", 0],
                            "positive": ["6", 0],
                            "negative": ["7", 0],
                            "latent_image": ["5", 0]
                        },
                        "class_type": "KSampler"
                    },
                    "4": {
                        "inputs": {
                            "ckpt_name": "sd_xl_base_1.0.safetensors"
                        },
                        "class_type": "CheckpointLoaderSimple"
                    }
                }
            }
            return {
                "success": True,
                "template": template
            }
        else:
            return {
                "success": False,
                "error": "模板不存在"
            }

    except Exception as e:
        logger.error(f"获取工作流模板详情失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/workflows/templates")
async def create_workflow_template(
    name: str,
    description: str,
    category: str,
    workflow_data: Dict[str, Any],
    tags: Optional[List[str]] = None
):
    """创建新的工作流模板"""
    try:
        # TODO: 通过gRPC调用Core服务创建模板
        # 暂时返回模拟数据
        template_id = str(uuid.uuid4())
        template = {
            "id": template_id,
            "name": name,
            "description": description,
            "category": category,
            "workflow_data": workflow_data,
            "version": "1.0.0",
            "author": "user",
            "tags": tags or [],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_active": True
        }

        return {
            "success": True,
            "template": template,
            "message": "工作流模板创建成功"
        }

    except Exception as e:
        logger.error(f"创建工作流模板失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.put("/workflows/templates/{template_id}")
async def update_workflow_template(
    template_id: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    workflow_data: Optional[Dict[str, Any]] = None,
    tags: Optional[List[str]] = None
):
    """更新工作流模板"""
    try:
        # TODO: 通过gRPC调用Core服务更新模板
        # 暂时返回模拟数据
        return {
            "success": True,
            "message": "工作流模板更新成功"
        }

    except Exception as e:
        logger.error(f"更新工作流模板失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.delete("/workflows/templates/{template_id}")
async def delete_workflow_template(template_id: str):
    """删除工作流模板"""
    try:
        # TODO: 通过gRPC调用Core服务删除模板
        # 暂时返回模拟数据
        return {
            "success": True,
            "message": "工作流模板删除成功"
        }

    except Exception as e:
        logger.error(f"删除工作流模板失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/workflows/templates/{template_id}/duplicate")
async def duplicate_workflow_template(template_id: str, new_name: Optional[str] = None):
    """复制工作流模板"""
    try:
        # TODO: 通过gRPC调用Core服务复制模板
        # 暂时返回模拟数据
        new_template_id = str(uuid.uuid4())
        return {
            "success": True,
            "new_template_id": new_template_id,
            "message": "工作流模板复制成功"
        }

    except Exception as e:
        logger.error(f"复制工作流模板失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/comfyui/models")
async def get_comfyui_models():
    """获取ComfyUI可用模型列表"""
    try:
        # TODO: 通过gRPC调用Core服务获取模型列表
        # 暂时返回模拟数据
        return {
            "success": True,
            "models": {
                "checkpoints": [
                    "sd_xl_base_1.0.safetensors",
                    "sd_xl_refiner_1.0.safetensors",
                    "dreamshaper_8.safetensors",
                    "realisticVisionV60_v60B1VAE.safetensors"
                ],
                "loras": [
                    "add_detail.safetensors",
                    "film_grain.safetensors",
                    "more_art.safetensors"
                ],
                "vae": [
                    "sdxl_vae.safetensors",
                    "vae-ft-mse-840000-ema-pruned.safetensors"
                ],
                "controlnet": [
                    "control_v11p_sd15_canny.pth",
                    "control_v11p_sd15_openpose.pth",
                    "control_v11p_sd15_depth.pth"
                ]
            }
        }

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/stats")
async def get_generation_stats(request: Request):
    """获取生成统计信息"""
    try:
        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 统计各状态的任务数量
        total_tasks = await db_service.db.content_generation_tasks.count_documents({})
        completed_tasks = await db_service.db.content_generation_tasks.count_documents({"status": "completed"})
        failed_tasks = await db_service.db.content_generation_tasks.count_documents({"status": "failed"})
        processing_tasks = await db_service.db.content_generation_tasks.count_documents({"status": "processing"})
        pending_tasks = await db_service.db.content_generation_tasks.count_documents({"status": "pending"})

        # 统计结果数量
        pipeline = [
            {"$match": {"results": {"$exists": True}}},
            {"$project": {"result_count": {"$size": "$results"}}},
            {"$group": {"_id": None, "total_results": {"$sum": "$result_count"}}}
        ]
        result = await db_service.db.content_generation_tasks.aggregate(pipeline).to_list(1)
        total_results = result[0]["total_results"] if result else 0

        return {
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "processing_tasks": processing_tasks,
            "pending_tasks": pending_tasks,
            "total_results": total_results
        }

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

# 文件管理相关API

@router.get("/results/{result_id}/preview")
async def get_result_preview(
    request: Request,
    result_id: str
):
    """获取生成结果预览"""
    try:
        # 解析result_id获取task_id
        if "_result_" in result_id:
            task_id = result_id.split("_result_")[0]
        else:
            raise HTTPException(status_code=400, detail="无效的结果ID")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 查询任务
        task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        if not task_doc:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 查找对应的结果
        results = task_doc.get("results", [])
        result = None
        for r in results:
            if r["id"] == result_id:
                result = r
                break

        if not result:
            raise HTTPException(status_code=404, detail="结果不存在")

        # 返回预览信息
        return {
            "result_id": result_id,
            "title": result.get("title", ""),
            "type": result.get("type", ""),
            "preview_url": result.get("preview_url", ""),
            "metadata": result.get("metadata", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取结果预览失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取结果预览失败: {str(e)}")

@router.get("/results/{result_id}/download")
async def download_result(
    request: Request,
    result_id: str
):
    """下载生成结果"""
    try:
        # 解析result_id获取task_id
        if "_result_" in result_id:
            task_id = result_id.split("_result_")[0]
        else:
            raise HTTPException(status_code=400, detail="无效的结果ID")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 查询任务
        task_doc = await db_service.db.content_generation_tasks.find_one({"id": task_id})
        if not task_doc:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 查找对应的结果
        results = task_doc.get("results", [])
        result = None
        for r in results:
            if r["id"] == result_id:
                result = r
                break

        if not result:
            raise HTTPException(status_code=404, detail="结果不存在")

        # 获取文件路径
        file_path = result.get("file_path")
        if not file_path:
            raise HTTPException(status_code=404, detail="文件不存在")

        # TODO: 实现实际的文件下载
        # 这里应该返回文件流或重定向到文件URL
        return {
            "download_url": f"/files/content_generation/{file_path}",
            "filename": os.path.basename(file_path),
            "file_size": result.get("file_size", 0)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载结果失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"下载结果失败: {str(e)}")

@router.delete("/results/{result_id}")
async def delete_result(
    request: Request,
    result_id: str
):
    """删除生成结果"""
    try:
        # 解析result_id获取task_id
        if "_result_" in result_id:
            task_id = result_id.split("_result_")[0]
        else:
            raise HTTPException(status_code=400, detail="无效的结果ID")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 从任务中移除结果
        await db_service.db.content_generation_tasks.update_one(
            {"id": task_id},
            {"$pull": {"results": {"id": result_id}}}
        )

        # TODO: 删除实际文件

        logger.info(f"删除生成结果成功: {result_id}")
        return {"message": "结果删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除结果失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除结果失败: {str(e)}")

@router.get("/available-accounts", response_model=Dict[str, Any])
async def get_available_accounts(request: Request):
    """获取可用的社交媒体账号列表"""
    try:
        logger.info("获取可用的社交媒体账号列表")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 查询活跃的社交账号
        accounts_cursor = db_service.db.social_accounts.find({
            "status": "active"
        }).sort("platform_id", 1)

        accounts = await accounts_cursor.to_list(length=None)

        # 按平台分组
        platforms_dict = {}
        for account in accounts:
            platform_id = account.get("platform_id", "unknown")
            if platform_id not in platforms_dict:
                platforms_dict[platform_id] = []

            # 格式化账号信息
            account_info = {
                "id": str(account.get("_id", account.get("id", ""))),
                "username": account.get("username", ""),
                "display_name": account.get("display_name", account.get("username", "")),
                "platform_id": platform_id,
                "status": account.get("status", "unknown"),
                "avatar": account.get("avatar"),
                "description": account.get("description")
            }
            platforms_dict[platform_id].append(account_info)

        # 获取平台信息
        platforms_cursor = db_service.db.social_platforms.find({
            "status": "active"
        })
        platforms = await platforms_cursor.to_list(length=None)

        # 构建平台信息映射
        platform_info = {}
        for platform in platforms:
            platform_info[platform.get("id", "")] = {
                "name": platform.get("name", ""),
                "icon": platform.get("icon", ""),
                "color": platform.get("color", "")
            }

        return {
            "platforms": platforms_dict,
            "platform_info": platform_info,
            "total_accounts": sum(len(accounts) for accounts in platforms_dict.values())
        }

    except Exception as e:
        logger.error(f"获取可用账号列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取可用账号列表失败: {str(e)}")

@router.get("/tasks/by-account/{account_id}", response_model=Dict[str, Any])
async def get_tasks_by_account(
    account_id: str,
    request: Request,
    status: Optional[str] = None
):
    """获取指定账号的任务列表"""
    try:
        logger.info(f"获取账号 {account_id} 的任务列表")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 构建查询条件
        query = {"account_id": account_id}
        if status:
            query["status"] = status

        # 查询任务
        cursor = db_service.db.content_generation_tasks.find(query).sort("created_at", -1)
        task_docs = await cursor.to_list(length=None)

        # 转换为响应格式
        tasks = [format_task_from_db(doc) for doc in task_docs]

        # 获取账号信息
        account = await db_service.get_account(account_id)
        account_info = None
        if account:
            account_info = {
                "id": str(account.get("_id", account.get("id", ""))),
                "username": account.get("username", ""),
                "display_name": account.get("display_name", account.get("username", "")),
                "platform_id": account.get("platform_id", ""),
                "status": account.get("status", "")
            }

        return {
            "tasks": tasks,
            "total": len(tasks),
            "account_info": account_info
        }

    except Exception as e:
        logger.error(f"获取账号任务列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取账号任务列表失败: {str(e)}")

@router.delete("/tasks/batch")
async def batch_delete_tasks(
    task_ids: List[str],
    request: Request
):
    """批量删除任务"""
    try:
        logger.info(f"批量删除任务: {task_ids}")

        # 获取数据库连接
        db = get_db(request)
        if not db:
            raise HTTPException(status_code=500, detail="数据库连接失败")

        db_service = SocialDatabaseService(db)

        # 批量删除任务
        result = await db_service.db.content_generation_tasks.delete_many({
            "id": {"$in": task_ids}
        })

        logger.info(f"成功删除 {result.deleted_count} 个任务")

        return {
            "message": f"成功删除 {result.deleted_count} 个任务",
            "deleted_count": result.deleted_count
        }

    except Exception as e:
        logger.error(f"批量删除任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量删除任务失败: {str(e)}")

@router.get("/workflows/parameters/{category}")
async def get_workflow_parameters(category: str):
    """获取指定类别的工作流参数定义"""
    try:
        # TODO: 通过gRPC调用Core服务获取参数定义
        # 暂时返回模拟数据
        if category == "image":
            parameters = {
                "positive_prompt": {
                    "name": "positive_prompt",
                    "type": "string",
                    "label": "正向提示词",
                    "description": "描述想要生成的内容",
                    "required": True,
                    "group": "prompt"
                },
                "negative_prompt": {
                    "name": "negative_prompt",
                    "type": "string",
                    "label": "负向提示词",
                    "description": "描述不想要的内容",
                    "default_value": "low quality, blurry, bad anatomy",
                    "group": "prompt"
                },
                "width": {
                    "name": "width",
                    "type": "integer",
                    "label": "宽度",
                    "description": "图片宽度（像素）",
                    "default_value": 1024,
                    "min_value": 512,
                    "max_value": 2048,
                    "group": "size"
                },
                "height": {
                    "name": "height",
                    "type": "integer",
                    "label": "高度",
                    "description": "图片高度（像素）",
                    "default_value": 1024,
                    "min_value": 512,
                    "max_value": 2048,
                    "group": "size"
                },
                "steps": {
                    "name": "steps",
                    "type": "integer",
                    "label": "生成步数",
                    "description": "扩散模型的采样步数",
                    "default_value": 20,
                    "min_value": 1,
                    "max_value": 100,
                    "group": "generation"
                },
                "cfg_scale": {
                    "name": "cfg_scale",
                    "type": "float",
                    "label": "CFG Scale",
                    "description": "分类器自由引导强度",
                    "default_value": 7.0,
                    "min_value": 1.0,
                    "max_value": 20.0,
                    "group": "generation"
                },
                "sampler": {
                    "name": "sampler",
                    "type": "select",
                    "label": "采样器",
                    "description": "选择采样算法",
                    "default_value": "euler",
                    "options": [
                        {"value": "euler", "label": "Euler"},
                        {"value": "euler_a", "label": "Euler Ancestral"},
                        {"value": "dpm_2", "label": "DPM++ 2M"},
                        {"value": "dpm_2_a", "label": "DPM++ 2M Karras"},
                        {"value": "ddim", "label": "DDIM"}
                    ],
                    "group": "generation"
                }
            }
        elif category == "video":
            parameters = {
                "prompt": {
                    "name": "prompt",
                    "type": "string",
                    "label": "视频描述",
                    "description": "描述想要生成的视频内容",
                    "required": True,
                    "group": "prompt"
                },
                "duration": {
                    "name": "duration",
                    "type": "integer",
                    "label": "视频时长",
                    "description": "视频时长（秒）",
                    "default_value": 4,
                    "min_value": 2,
                    "max_value": 10,
                    "group": "video"
                },
                "fps": {
                    "name": "fps",
                    "type": "integer",
                    "label": "帧率",
                    "description": "每秒帧数",
                    "default_value": 8,
                    "min_value": 6,
                    "max_value": 30,
                    "group": "video"
                }
            }
        else:
            parameters = {}

        return {
            "success": True,
            "category": category,
            "parameters": parameters
        }

    except Exception as e:
        logger.error(f"获取工作流参数失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/workflows/parameters/validate")
async def validate_workflow_parameters(
    category: str,
    parameters: Dict[str, Any]
):
    """验证工作流参数"""
    try:
        # TODO: 通过gRPC调用Core服务验证参数
        # 暂时返回模拟验证结果
        errors = []
        validated_params = {}

        # 简单的验证逻辑
        if category == "image":
            if "positive_prompt" not in parameters or not parameters["positive_prompt"]:
                errors.append("正向提示词不能为空")
            else:
                validated_params["positive_prompt"] = parameters["positive_prompt"]

            if "width" in parameters:
                width = parameters["width"]
                if not isinstance(width, int) or width < 512 or width > 2048:
                    errors.append("宽度必须是512-2048之间的整数")
                else:
                    validated_params["width"] = width

        return {
            "success": len(errors) == 0,
            "parameters": validated_params,
            "errors": errors
        }

    except Exception as e:
        logger.error(f"验证工作流参数失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/models")
async def get_models(
    model_type: Optional[str] = None,
    status: Optional[str] = None,
    tags: Optional[str] = None
):
    """获取模型列表"""
    try:
        # TODO: 通过gRPC调用Core服务获取模型列表
        # 暂时返回模拟数据
        models = [
            {
                "id": "checkpoint_sdxl_base",
                "name": "SDXL Base 1.0",
                "type": "checkpoint",
                "version": "1.0.0",
                "description": "Stable Diffusion XL基础模型",
                "author": "Stability AI",
                "file_size": 6738734080,  # ~6.3GB
                "status": "active",
                "tags": ["sdxl", "base", "general"],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "download_count": 1250
            },
            {
                "id": "lora_add_detail",
                "name": "Add Detail LoRA",
                "type": "lora",
                "version": "1.0.0",
                "description": "增加图片细节的LoRA模型",
                "author": "community",
                "file_size": 143654912,  # ~137MB
                "status": "active",
                "tags": ["detail", "enhancement"],
                "created_at": "2024-01-02T00:00:00",
                "updated_at": "2024-01-02T00:00:00",
                "download_count": 856
            },
            {
                "id": "vae_sdxl",
                "name": "SDXL VAE",
                "type": "vae",
                "version": "1.0.0",
                "description": "SDXL专用VAE模型",
                "author": "Stability AI",
                "file_size": 334643200,  # ~319MB
                "status": "active",
                "tags": ["sdxl", "vae"],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "download_count": 743
            }
        ]

        # 应用筛选
        if model_type:
            models = [m for m in models if m["type"] == model_type]

        if status:
            models = [m for m in models if m["status"] == status]

        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            models = [m for m in models if any(tag in m["tags"] for tag in tag_list)]

        return {
            "success": True,
            "models": models,
            "total": len(models)
        }

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/models/{model_id}")
async def get_model_detail(model_id: str):
    """获取模型详情"""
    try:
        # TODO: 通过gRPC调用Core服务获取模型详情
        # 暂时返回模拟数据
        if model_id == "checkpoint_sdxl_base":
            model = {
                "id": "checkpoint_sdxl_base",
                "name": "SDXL Base 1.0",
                "type": "checkpoint",
                "version": "1.0.0",
                "description": "Stable Diffusion XL基础模型，支持1024x1024高分辨率图像生成",
                "author": "Stability AI",
                "file_path": "/models/checkpoints/sd_xl_base_1.0.safetensors",
                "file_size": 6738734080,
                "file_hash": "31e35c80fc4829d14f90153f4c74cd59c90b779f6afe05a74cd6120b893f7e5b",
                "status": "active",
                "tags": ["sdxl", "base", "general"],
                "metadata": {
                    "resolution": "1024x1024",
                    "architecture": "SDXL",
                    "training_steps": 500000,
                    "base_model": "sd_xl_base_1.0"
                },
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "download_count": 1250
            }
            return {
                "success": True,
                "model": model
            }
        else:
            return {
                "success": False,
                "error": "模型不存在"
            }

    except Exception as e:
        logger.error(f"获取模型详情失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/models/upload")
async def upload_model(
    name: str,
    model_type: str,
    version: str = "1.0.0",
    description: str = "",
    tags: Optional[List[str]] = None
):
    """上传模型文件"""
    try:
        # TODO: 实现文件上传逻辑
        # 这里需要处理文件上传，暂时返回模拟响应
        model_id = f"{model_type}_{name}_{uuid.uuid4().hex[:8]}"

        return {
            "success": True,
            "model_id": model_id,
            "message": "模型上传成功"
        }

    except Exception as e:
        logger.error(f"上传模型失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.delete("/models/{model_id}")
async def delete_model(model_id: str, remove_file: bool = False):
    """删除模型"""
    try:
        # TODO: 通过gRPC调用Core服务删除模型
        # 暂时返回模拟响应
        return {
            "success": True,
            "message": "模型删除成功"
        }

    except Exception as e:
        logger.error(f"删除模型失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/models/statistics")
async def get_model_statistics():
    """获取模型统计信息"""
    try:
        # TODO: 通过gRPC调用Core服务获取统计信息
        # 暂时返回模拟数据
        stats = {
            "total_models": 15,
            "by_type": {
                "checkpoint": 5,
                "lora": 6,
                "vae": 2,
                "controlnet": 2
            },
            "by_status": {
                "active": 14,
                "inactive": 1
            },
            "total_size": 45678901234,  # ~42.5GB
            "total_downloads": 8456
        }

        return {
            "success": True,
            "statistics": stats
        }

    except Exception as e:
        logger.error(f"获取模型统计失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/queue/status")
async def get_queue_status():
    """获取任务队列状态"""
    try:
        # TODO: 通过gRPC调用Core服务获取队列状态
        # 暂时返回模拟数据
        stats = {
            "total_submitted": 156,
            "total_completed": 142,
            "total_failed": 8,
            "current_pending": 6,
            "current_running": 3,
            "total_batches": 12,
            "completed_batches": 10,
            "workers": {
                "total": 5,
                "active": 3,
                "idle": 2
            },
            "queue_length": 6
        }

        return {
            "success": True,
            "stats": stats
        }

    except Exception as e:
        logger.error(f"获取队列状态失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/queue/submit")
async def submit_task_to_queue(
    task_type: str,
    payload: Dict[str, Any],
    priority: int = 2,
    max_retries: int = 3,
    timeout: int = 300
):
    """提交任务到队列"""
    try:
        # TODO: 通过gRPC调用Core服务提交任务
        # 暂时返回模拟数据
        task_id = str(uuid.uuid4())

        return {
            "success": True,
            "task_id": task_id,
            "message": "任务已提交到队列"
        }

    except Exception as e:
        logger.error(f"提交任务到队列失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/queue/submit-batch")
async def submit_batch_to_queue(
    batch_name: str,
    tasks: List[Dict[str, Any]],
    description: str = "",
    priority: int = 2
):
    """批量提交任务到队列"""
    try:
        # TODO: 通过gRPC调用Core服务批量提交任务
        # 暂时返回模拟数据
        batch_id = str(uuid.uuid4())

        return {
            "success": True,
            "batch_id": batch_id,
            "task_count": len(tasks),
            "message": f"批量任务已提交，包含 {len(tasks)} 个任务"
        }

    except Exception as e:
        logger.error(f"批量提交任务失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/queue/task/{task_id}")
async def get_task_status_from_queue(task_id: str):
    """获取队列中任务状态"""
    try:
        # TODO: 通过gRPC调用Core服务获取任务状态
        # 暂时返回模拟数据
        task_status = {
            "id": task_id,
            "type": "image_generation",
            "status": "running",
            "priority": 2,
            "created_at": time.time() - 300,
            "started_at": time.time() - 60,
            "retry_count": 0,
            "worker_id": "worker_1",
            "progress": 0.65
        }

        return {
            "success": True,
            "task": task_status
        }

    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/queue/batch/{batch_id}")
async def get_batch_status_from_queue(batch_id: str):
    """获取批处理状态"""
    try:
        # TODO: 通过gRPC调用Core服务获取批处理状态
        # 暂时返回模拟数据
        batch_status = {
            "id": batch_id,
            "name": "批量图片生成",
            "description": "生成产品宣传图片",
            "status": "running",
            "total_tasks": 10,
            "completed_tasks": 6,
            "failed_tasks": 1,
            "progress": 0.7,
            "created_at": time.time() - 600,
            "started_at": time.time() - 500
        }

        return {
            "success": True,
            "batch": batch_status
        }

    except Exception as e:
        logger.error(f"获取批处理状态失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.delete("/queue/task/{task_id}")
async def cancel_task_in_queue(task_id: str):
    """取消队列中的任务"""
    try:
        # TODO: 通过gRPC调用Core服务取消任务
        # 暂时返回模拟数据
        return {
            "success": True,
            "message": "任务已取消"
        }

    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.delete("/queue/batch/{batch_id}")
async def cancel_batch_in_queue(batch_id: str):
    """取消批处理任务"""
    try:
        # TODO: 通过gRPC调用Core服务取消批处理
        # 暂时返回模拟数据
        return {
            "success": True,
            "message": "批处理任务已取消"
        }

    except Exception as e:
        logger.error(f"取消批处理失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/results")
async def get_results(
    task_id: Optional[str] = None,
    batch_id: Optional[str] = None,
    result_type: Optional[str] = None,
    status: Optional[str] = None,
    tags: Optional[str] = None,
    limit: int = 20
):
    """获取结果列表"""
    try:
        # TODO: 通过gRPC调用Core服务获取结果列表
        # 暂时返回模拟数据
        results = [
            {
                "id": "result_001",
                "task_id": "task_001",
                "batch_id": "batch_001",
                "type": "image",
                "status": "completed",
                "title": "产品宣传图",
                "description": "AI生成的产品宣传图片",
                "file_name": "product_image.jpg",
                "file_size": 2048576,
                "mime_type": "image/jpeg",
                "dimensions": {"width": 1024, "height": 1024},
                "tags": ["product", "marketing"],
                "created_at": "2024-01-01T10:00:00",
                "created_by": "user_001"
            },
            {
                "id": "result_002",
                "task_id": "task_002",
                "type": "video",
                "status": "completed",
                "title": "宣传视频",
                "description": "AI生成的产品宣传视频",
                "file_name": "promo_video.mp4",
                "file_size": 15728640,
                "mime_type": "video/mp4",
                "duration": 30.0,
                "tags": ["video", "promo"],
                "created_at": "2024-01-01T11:00:00",
                "created_by": "user_001"
            }
        ]

        # 应用筛选
        if task_id:
            results = [r for r in results if r.get("task_id") == task_id]

        if result_type:
            results = [r for r in results if r["type"] == result_type]

        if status:
            results = [r for r in results if r["status"] == status]

        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            results = [r for r in results if any(tag in r.get("tags", []) for tag in tag_list)]

        # 应用限制
        results = results[:limit]

        return {
            "success": True,
            "results": results,
            "total": len(results)
        }

    except Exception as e:
        logger.error(f"获取结果列表失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/results/{result_id}")
async def get_result_detail(result_id: str):
    """获取结果详情"""
    try:
        # TODO: 通过gRPC调用Core服务获取结果详情
        # 暂时返回模拟数据
        if result_id == "result_001":
            result = {
                "id": "result_001",
                "task_id": "task_001",
                "batch_id": "batch_001",
                "type": "image",
                "status": "completed",
                "title": "产品宣传图",
                "description": "AI生成的产品宣传图片",
                "file_path": "/data/results/image/originals/result_001.jpg",
                "file_name": "product_image.jpg",
                "file_size": 2048576,
                "file_hash": "abc123def456",
                "mime_type": "image/jpeg",
                "dimensions": {"width": 1024, "height": 1024},
                "tags": ["product", "marketing"],
                "metadata": {
                    "model": "SDXL",
                    "prompt": "professional product photo",
                    "steps": 20
                },
                "version": 1,
                "created_at": "2024-01-01T10:00:00",
                "updated_at": "2024-01-01T10:00:00",
                "created_by": "user_001"
            }
            return {
                "success": True,
                "result": result
            }
        else:
            return {
                "success": False,
                "error": "结果不存在"
            }

    except Exception as e:
        logger.error(f"获取结果详情失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/results/{result_id}/thumbnail")
async def get_result_thumbnail(result_id: str):
    """获取结果缩略图"""
    try:
        # TODO: 通过gRPC调用Core服务获取缩略图
        # 暂时返回模拟响应
        return {
            "success": True,
            "thumbnail_url": f"/api/v1/files/thumbnails/{result_id}_thumb.jpg",
            "thumbnail_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
        }

    except Exception as e:
        logger.error(f"获取缩略图失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/results/{result_id}/edit")
async def edit_result(
    result_id: str,
    operation: str,
    parameters: Dict[str, Any]
):
    """编辑结果"""
    try:
        # TODO: 通过gRPC调用Core服务编辑结果
        # 暂时返回模拟响应
        return {
            "success": True,
            "processed_file_path": f"/data/results/processed/{result_id}_{operation}.jpg",
            "message": f"应用 {operation} 操作成功"
        }

    except Exception as e:
        logger.error(f"编辑结果失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/results/export")
async def export_results(
    result_ids: List[str],
    format: str = "zip",
    include_metadata: bool = True,
    include_thumbnails: bool = False,
    custom_filename: Optional[str] = None
):
    """导出结果"""
    try:
        # TODO: 通过gRPC调用Core服务导出结果
        # 暂时返回模拟响应
        export_id = str(uuid.uuid4())

        return {
            "success": True,
            "export_id": export_id,
            "message": f"导出任务已创建，包含 {len(result_ids)} 个结果"
        }

    except Exception as e:
        logger.error(f"导出结果失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/exports/{export_id}")
async def get_export_status(export_id: str):
    """获取导出状态"""
    try:
        # TODO: 通过gRPC调用Core服务获取导出状态
        # 暂时返回模拟数据
        export_status = {
            "id": export_id,
            "status": "completed",
            "progress": 1.0,
            "output_path": f"/tmp/export_{export_id}.zip",
            "download_url": f"/api/v1/downloads/{export_id}",
            "created_at": "2024-01-01T12:00:00",
            "completed_at": "2024-01-01T12:05:00"
        }

        return {
            "success": True,
            "export": export_status
        }

    except Exception as e:
        logger.error(f"获取导出状态失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/results/{result_id}/distribute")
async def distribute_result(
    result_id: str,
    platform: str,
    account_id: str,
    title: str,
    description: str = "",
    tags: List[str] = None,
    privacy: str = "public",
    schedule_time: Optional[str] = None
):
    """分发结果到社交媒体"""
    try:
        # TODO: 通过gRPC调用Core服务分发结果
        # 暂时返回模拟响应
        distribution_id = str(uuid.uuid4())

        return {
            "success": True,
            "distribution_id": distribution_id,
            "message": f"分发任务已创建，将发布到 {platform}"
        }

    except Exception as e:
        logger.error(f"分发结果失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/distributions/{distribution_id}")
async def get_distribution_status(distribution_id: str):
    """获取分发状态"""
    try:
        # TODO: 通过gRPC调用Core服务获取分发状态
        # 暂时返回模拟数据
        distribution_status = {
            "id": distribution_id,
            "result_id": "result_001",
            "platform": "youtube",
            "status": "completed",
            "progress": 1.0,
            "platform_post_id": "youtube_abc123",
            "platform_url": "https://youtube.com/watch?v=abc123",
            "created_at": "2024-01-01T13:00:00",
            "completed_at": "2024-01-01T13:10:00"
        }

        return {
            "success": True,
            "distribution": distribution_status
        }

    except Exception as e:
        logger.error(f"获取分发状态失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
