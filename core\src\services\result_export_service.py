"""
结果导出服务
提供生成结果的多格式下载和批量导出功能
"""

import logging
import os
import json
import zipfile
import tempfile
import shutil
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import mimetypes

logger = logging.getLogger(__name__)

class ExportFormat(Enum):
    """导出格式"""
    ORIGINAL = "original"
    ZIP = "zip"
    TAR = "tar"
    JSON = "json"
    CSV = "csv"
    PDF = "pdf"

class ExportType(Enum):
    """导出类型"""
    SINGLE = "single"
    BATCH = "batch"
    TASK = "task"
    FILTERED = "filtered"

@dataclass
class ExportConfig:
    """导出配置"""
    format: ExportFormat
    include_metadata: bool = True
    include_thumbnails: bool = False
    include_processed: bool = False
    compression_level: int = 6
    custom_filename: Optional[str] = None

@dataclass
class ExportJob:
    """导出任务"""
    id: str
    type: ExportType
    config: ExportConfig
    result_ids: List[str]
    status: str = "pending"
    progress: float = 0.0
    output_path: Optional[str] = None
    created_at: str = ""
    completed_at: Optional[str] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

class ResultExportService:
    """结果导出服务"""
    
    def __init__(self, storage_service=None, temp_dir: str = None):
        self.storage_service = storage_service
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.export_jobs: Dict[str, ExportJob] = {}
        self.supported_formats = {
            ExportFormat.ORIGINAL: self._export_original,
            ExportFormat.ZIP: self._export_zip,
            ExportFormat.TAR: self._export_tar,
            ExportFormat.JSON: self._export_json,
            ExportFormat.CSV: self._export_csv,
            ExportFormat.PDF: self._export_pdf
        }
    
    def create_export_job(self, result_ids: List[str], config: ExportConfig,
                         export_type: ExportType = ExportType.BATCH) -> str:
        """创建导出任务"""
        try:
            import uuid
            job_id = str(uuid.uuid4())
            
            job = ExportJob(
                id=job_id,
                type=export_type,
                config=config,
                result_ids=result_ids
            )
            
            self.export_jobs[job_id] = job
            
            # 异步执行导出
            import asyncio
            asyncio.create_task(self._execute_export_job(job_id))
            
            logger.info(f"创建导出任务: {job_id} ({len(result_ids)} 个结果)")
            return job_id
            
        except Exception as e:
            logger.error(f"创建导出任务失败: {str(e)}")
            raise
    
    async def _execute_export_job(self, job_id: str):
        """执行导出任务"""
        job = self.export_jobs.get(job_id)
        if not job:
            return
        
        try:
            job.status = "running"
            
            # 获取导出处理器
            export_handler = self.supported_formats.get(job.config.format)
            if not export_handler:
                raise ValueError(f"不支持的导出格式: {job.config.format}")
            
            # 执行导出
            output_path = await export_handler(job)
            
            job.output_path = output_path
            job.status = "completed"
            job.progress = 1.0
            job.completed_at = datetime.now().isoformat()
            
            logger.info(f"导出任务完成: {job_id}")
            
        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            logger.error(f"导出任务失败: {job_id} - {str(e)}")
    
    async def _export_original(self, job: ExportJob) -> str:
        """导出原始文件"""
        try:
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            if len(job.result_ids) == 1:
                # 单个文件直接返回原路径
                result = self.storage_service.get_result(job.result_ids[0])
                if result and os.path.exists(result.file_path):
                    return result.file_path
                else:
                    raise FileNotFoundError("结果文件不存在")
            else:
                # 多个文件打包
                return await self._create_archive(job, "zip")
                
        except Exception as e:
            logger.error(f"导出原始文件失败: {str(e)}")
            raise
    
    async def _export_zip(self, job: ExportJob) -> str:
        """导出为ZIP格式"""
        return await self._create_archive(job, "zip")
    
    async def _export_tar(self, job: ExportJob) -> str:
        """导出为TAR格式"""
        return await self._create_archive(job, "tar")
    
    async def _create_archive(self, job: ExportJob, archive_type: str) -> str:
        """创建压缩包"""
        try:
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if job.config.custom_filename:
                filename = f"{job.config.custom_filename}.{archive_type}"
            else:
                filename = f"export_{timestamp}.{archive_type}"
            
            output_path = os.path.join(self.temp_dir, filename)
            
            if archive_type == "zip":
                await self._create_zip_archive(job, output_path)
            elif archive_type == "tar":
                await self._create_tar_archive(job, output_path)
            
            return output_path
            
        except Exception as e:
            logger.error(f"创建压缩包失败: {str(e)}")
            raise
    
    async def _create_zip_archive(self, job: ExportJob, output_path: str):
        """创建ZIP压缩包"""
        try:
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED, 
                               compresslevel=job.config.compression_level) as zipf:
                
                total_files = len(job.result_ids)
                processed_files = 0
                
                for result_id in job.result_ids:
                    result = self.storage_service.get_result(result_id)
                    if not result or not os.path.exists(result.file_path):
                        logger.warning(f"跳过不存在的结果: {result_id}")
                        continue
                    
                    # 添加原始文件
                    archive_name = f"{result.type.value}/{result.file_name}"
                    zipf.write(result.file_path, archive_name)
                    
                    # 添加缩略图（如果需要）
                    if job.config.include_thumbnails:
                        thumbnail_path = self._get_thumbnail_path(result)
                        if thumbnail_path and os.path.exists(thumbnail_path):
                            thumb_name = f"thumbnails/{result.id}_thumb.jpg"
                            zipf.write(thumbnail_path, thumb_name)
                    
                    # 添加处理文件（如果需要）
                    if job.config.include_processed:
                        processed_files_list = self._get_processed_files(result)
                        for processed_file in processed_files_list:
                            if os.path.exists(processed_file):
                                processed_name = f"processed/{os.path.basename(processed_file)}"
                                zipf.write(processed_file, processed_name)
                    
                    # 添加元数据（如果需要）
                    if job.config.include_metadata:
                        metadata_content = json.dumps(result.to_dict(), 
                                                    ensure_ascii=False, indent=2)
                        metadata_name = f"metadata/{result.id}.json"
                        zipf.writestr(metadata_name, metadata_content)
                    
                    processed_files += 1
                    job.progress = processed_files / total_files
                
                # 添加导出信息
                export_info = {
                    "export_id": job.id,
                    "export_date": datetime.now().isoformat(),
                    "total_files": total_files,
                    "config": {
                        "format": job.config.format.value,
                        "include_metadata": job.config.include_metadata,
                        "include_thumbnails": job.config.include_thumbnails,
                        "include_processed": job.config.include_processed
                    }
                }
                zipf.writestr("export_info.json", 
                            json.dumps(export_info, ensure_ascii=False, indent=2))
                
        except Exception as e:
            logger.error(f"创建ZIP压缩包失败: {str(e)}")
            raise
    
    async def _create_tar_archive(self, job: ExportJob, output_path: str):
        """创建TAR压缩包"""
        try:
            import tarfile
            
            with tarfile.open(output_path, 'w:gz') as tarf:
                total_files = len(job.result_ids)
                processed_files = 0
                
                for result_id in job.result_ids:
                    result = self.storage_service.get_result(result_id)
                    if not result or not os.path.exists(result.file_path):
                        continue
                    
                    # 添加原始文件
                    archive_name = f"{result.type.value}/{result.file_name}"
                    tarf.add(result.file_path, arcname=archive_name)
                    
                    # 添加其他文件（缩略图、处理文件、元数据）
                    # 类似ZIP的处理逻辑
                    
                    processed_files += 1
                    job.progress = processed_files / total_files
                    
        except Exception as e:
            logger.error(f"创建TAR压缩包失败: {str(e)}")
            raise
    
    async def _export_json(self, job: ExportJob) -> str:
        """导出为JSON格式"""
        try:
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            # 收集所有结果的元数据
            results_data = []
            for result_id in job.result_ids:
                result = self.storage_service.get_result(result_id)
                if result:
                    results_data.append(result.to_dict())
            
            # 生成输出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = job.config.custom_filename or f"export_{timestamp}.json"
            if not filename.endswith('.json'):
                filename += '.json'
            
            output_path = os.path.join(self.temp_dir, filename)
            
            export_data = {
                "export_info": {
                    "export_id": job.id,
                    "export_date": datetime.now().isoformat(),
                    "total_results": len(results_data),
                    "format": "json"
                },
                "results": results_data
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            job.progress = 1.0
            return output_path
            
        except Exception as e:
            logger.error(f"导出JSON失败: {str(e)}")
            raise
    
    async def _export_csv(self, job: ExportJob) -> str:
        """导出为CSV格式"""
        try:
            import csv
            
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            # 生成输出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = job.config.custom_filename or f"export_{timestamp}.csv"
            if not filename.endswith('.csv'):
                filename += '.csv'
            
            output_path = os.path.join(self.temp_dir, filename)
            
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['id', 'task_id', 'type', 'status', 'title', 'description',
                            'file_name', 'file_size', 'mime_type', 'created_at', 'tags']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                
                for result_id in job.result_ids:
                    result = self.storage_service.get_result(result_id)
                    if result:
                        row = {
                            'id': result.id,
                            'task_id': result.task_id,
                            'type': result.type.value,
                            'status': result.status.value,
                            'title': result.title,
                            'description': result.description,
                            'file_name': result.file_name,
                            'file_size': result.file_size,
                            'mime_type': result.mime_type,
                            'created_at': result.created_at,
                            'tags': ','.join(result.tags)
                        }
                        writer.writerow(row)
            
            job.progress = 1.0
            return output_path
            
        except Exception as e:
            logger.error(f"导出CSV失败: {str(e)}")
            raise
    
    async def _export_pdf(self, job: ExportJob) -> str:
        """导出为PDF格式"""
        try:
            # 这里应该使用reportlab或其他PDF库
            # 暂时创建一个简单的文本文件
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = job.config.custom_filename or f"export_{timestamp}.txt"
            output_path = os.path.join(self.temp_dir, filename)
            
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("导出报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"导出时间: {datetime.now().isoformat()}\n")
                f.write(f"导出ID: {job.id}\n")
                f.write(f"结果数量: {len(job.result_ids)}\n\n")
                
                for i, result_id in enumerate(job.result_ids):
                    result = self.storage_service.get_result(result_id)
                    if result:
                        f.write(f"{i+1}. {result.title}\n")
                        f.write(f"   ID: {result.id}\n")
                        f.write(f"   类型: {result.type.value}\n")
                        f.write(f"   状态: {result.status.value}\n")
                        f.write(f"   文件: {result.file_name}\n")
                        f.write(f"   大小: {result.file_size} bytes\n")
                        f.write(f"   创建时间: {result.created_at}\n")
                        if result.description:
                            f.write(f"   描述: {result.description}\n")
                        f.write("\n")
            
            job.progress = 1.0
            return output_path
            
        except Exception as e:
            logger.error(f"导出PDF失败: {str(e)}")
            raise
    
    def _get_thumbnail_path(self, result) -> Optional[str]:
        """获取缩略图路径"""
        try:
            thumbnail_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "thumbnails")
            )
            thumbnail_path = os.path.join(thumbnail_dir, f"{result.id}_thumb.jpg")
            return thumbnail_path if os.path.exists(thumbnail_path) else None
        except:
            return None
    
    def _get_processed_files(self, result) -> List[str]:
        """获取处理文件列表"""
        try:
            processed_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "processed")
            )
            if not os.path.exists(processed_dir):
                return []
            
            processed_files = []
            for file in os.listdir(processed_dir):
                if file.startswith(result.id):
                    processed_files.append(os.path.join(processed_dir, file))
            
            return processed_files
        except:
            return []
    
    def get_export_job(self, job_id: str) -> Optional[ExportJob]:
        """获取导出任务"""
        return self.export_jobs.get(job_id)
    
    def get_export_jobs(self, status: str = None) -> List[ExportJob]:
        """获取导出任务列表"""
        jobs = list(self.export_jobs.values())
        
        if status:
            jobs = [job for job in jobs if job.status == status]
        
        # 按创建时间倒序排序
        jobs.sort(key=lambda j: j.created_at, reverse=True)
        return jobs
    
    def cleanup_completed_jobs(self, max_age_hours: int = 24):
        """清理已完成的导出任务"""
        try:
            from datetime import timedelta
            
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            jobs_to_remove = []
            for job_id, job in self.export_jobs.items():
                if job.status in ["completed", "failed"] and job.completed_at:
                    completed_time = datetime.fromisoformat(job.completed_at)
                    if completed_time < cutoff_time:
                        # 删除输出文件
                        if job.output_path and os.path.exists(job.output_path):
                            os.remove(job.output_path)
                        jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                del self.export_jobs[job_id]
            
            logger.info(f"清理了 {len(jobs_to_remove)} 个过期导出任务")
            
        except Exception as e:
            logger.error(f"清理导出任务失败: {str(e)}")

# 全局导出服务实例
_export_service = None

def get_export_service() -> ResultExportService:
    """获取导出服务实例"""
    global _export_service
    
    if _export_service is None:
        _export_service = ResultExportService()
    
    return _export_service
