# 对标账号管理界面重新设计

## 设计目标

原有的对标账号管理界面存在以下问题：
1. **关联性不明显**：对标账号和我们的账号之间的关联关系不够直观
2. **查找不便**：需要分别搜索对标账号和关联账号，操作繁琐
3. **信息分散**：关键信息分布在不同列中，不利于快速理解关联关系
4. **缺乏层次感**：没有体现出"我们的账号 -> 对标账号"的层级关系

## 新设计方案

### 核心理念
**以关联关系为核心**，清晰展示我们的账号对标哪些频道，一目了然。

### 主要特点

#### 1. 双视图模式
- **分组视图**：以我们的账号为主体，展示其对标的所有账号
- **列表视图**：传统表格形式，便于快速浏览和操作

#### 2. 层级清晰的信息架构
```
我们的账号 (主卡片)
├── 对标账号1 (子卡片)
├── 对标账号2 (子卡片)
└── 对标账号3 (子卡片)
```

#### 3. 直观的统计信息
- 我们的账号数量
- 总对标账号数量
- 活跃关联数量
- 平均对标数量

#### 4. 便捷的操作体验
- 一键为特定账号添加对标
- 快速展开/收起账号组
- 统一的搜索和筛选
- 批量操作支持

## 技术实现

### 文件结构
```
frontend/src/views/social/
├── BenchmarkAccountsNew.vue          # 主界面
└── components/
    ├── AddBenchmarkDialog.vue        # 添加对标账号对话框
    └── BenchmarkDetailDialog.vue     # 对标账号详情对话框
```

### 核心组件

#### 1. BenchmarkAccountsNew.vue
- **功能**：主界面，包含分组视图和列表视图
- **特点**：
  - 响应式设计，支持移动端
  - 实时搜索和筛选
  - 智能分组和排序
  - 丰富的交互反馈

#### 2. AddBenchmarkDialog.vue
- **功能**：添加新的对标账号
- **特点**：
  - 支持预选关联账号
  - URL重复检查
  - 表单验证和提示
  - 常用标签快速选择

#### 3. BenchmarkDetailDialog.vue
- **功能**：查看对标账号详细信息
- **特点**：
  - 完整的账号信息展示
  - 数据概览和趋势
  - 采集信息和路径预览
  - 快速操作入口

### 数据流设计

#### 1. 数据加载
```javascript
// 并行加载数据
await Promise.all([
  loadBenchmarkAccounts(),  // 加载对标账号
  loadOurAccounts()         // 加载我们的账号
])
```

#### 2. 数据分组
```javascript
// 按我们的账号ID分组对标账号
const groupedData = computed(() => {
  const groups = new Map()
  
  // 初始化所有我们的账号
  ourAccounts.value.forEach(account => {
    groups.set(account.id, {
      ourAccount: account,
      benchmarks: [],
      expanded: expandedGroups.value.has(account.id)
    })
  })
  
  // 将对标账号分组
  filteredBenchmarks.value.forEach(benchmark => {
    const group = groups.get(benchmark.our_account_id)
    if (group) {
      benchmark.ourAccount = group.ourAccount
      group.benchmarks.push(benchmark)
    }
  })
  
  return Array.from(groups.values())
})
```

#### 3. 智能筛选
```javascript
// 支持多维度筛选
const filteredBenchmarks = computed(() => {
  let filtered = benchmarkAccounts.value

  // 关键词搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.account_name.toLowerCase().includes(query) ||
      (item.ourAccount && item.ourAccount.name.toLowerCase().includes(query))
    )
  }

  // 平台筛选
  if (filterPlatform.value) {
    filtered = filtered.filter(item => item.platform === filterPlatform.value)
  }

  // 类型筛选
  if (filterType.value) {
    filtered = filtered.filter(item => item.benchmark_type === filterType.value)
  }

  return filtered
})
```

## 用户体验改进

### 1. 视觉层次
- 使用渐变色卡片突出我们的账号
- 对标账号使用简洁的白色卡片
- 清晰的图标和标签系统

### 2. 交互反馈
- 悬停效果和动画过渡
- 加载状态和错误提示
- 操作确认和成功反馈

### 3. 响应式设计
- 移动端友好的布局
- 自适应的卡片网格
- 触摸友好的操作按钮

### 4. 性能优化
- 虚拟滚动支持大量数据
- 智能的数据缓存
- 按需加载详细信息

## 部署说明

### 1. 路由配置
已更新 `frontend/src/router/index.ts`，将对标账号管理路由指向新组件：
```javascript
{
  path: 'benchmark',
  name: 'BenchmarkAccounts',
  component: () => import('@/views/social/BenchmarkAccountsNew.vue'),
  meta: {
    requiresAuth: true,
    menuItem: true,
    title: '对标账号',
    icon: 'TrendCharts'
  }
}
```

### 2. API兼容性
新界面完全兼容现有的API接口：
- `getBenchmarkAccounts()` - 获取对标账号列表
- `getAccounts()` - 获取我们的账号列表
- `deleteBenchmarkAccount()` - 删除对标账号
- `createBenchmarkAccount()` - 创建对标账号

### 3. 数据处理
新界面能够处理多种数据格式，确保与现有系统的兼容性：
- 支持 `response.items` 和 `response.data` 格式
- 自动处理 ObjectId 和普通 ID
- 智能关联账号信息

## 后续优化计划

1. **批量操作**：支持批量删除、批量更新对标账号
2. **数据导入导出**：支持Excel格式的批量导入导出
3. **高级筛选**：添加更多筛选维度和保存筛选条件
4. **数据可视化**：添加对标账号数据的图表展示
5. **自动化建议**：基于AI的对标账号推荐功能

## 总结

新的对标账号管理界面通过以关联关系为核心的设计理念，显著提升了用户体验：

- **关联性更明显**：清晰的层级结构让关联关系一目了然
- **查找更便捷**：统一的搜索入口和智能筛选
- **操作更高效**：针对性的快速操作和批量处理
- **界面更美观**：现代化的设计语言和流畅的交互

这个重新设计的界面将大大提高对标账号管理的效率，让用户能够更好地理解和管理账号之间的对标关系。
