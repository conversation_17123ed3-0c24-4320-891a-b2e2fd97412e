"""
错误处理与重试服务
提供任务失败的错误分析、重试策略和故障恢复机制
"""

import logging
import asyncio
import time
import traceback
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
import re

logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """错误类型"""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    RESOURCE_ERROR = "resource_error"
    VALIDATION_ERROR = "validation_error"
    SYSTEM_ERROR = "system_error"
    UNKNOWN_ERROR = "unknown_error"

class RetryStrategy(Enum):
    """重试策略"""
    IMMEDIATE = "immediate"          # 立即重试
    LINEAR_BACKOFF = "linear"        # 线性退避
    EXPONENTIAL_BACKOFF = "exponential"  # 指数退避
    FIXED_DELAY = "fixed_delay"      # 固定延迟
    NO_RETRY = "no_retry"           # 不重试

@dataclass
class ErrorPattern:
    """错误模式"""
    pattern: str                    # 错误消息正则模式
    error_type: ErrorType          # 错误类型
    retry_strategy: RetryStrategy  # 重试策略
    max_retries: int = 3           # 最大重试次数
    base_delay: float = 1.0        # 基础延迟时间
    max_delay: float = 60.0        # 最大延迟时间
    description: str = ""          # 错误描述

@dataclass
class ErrorRecord:
    """错误记录"""
    task_id: str
    error_type: ErrorType
    error_message: str
    stack_trace: str
    timestamp: float
    retry_count: int
    next_retry_at: Optional[float] = None
    resolved: bool = False

class ErrorHandlerService:
    """错误处理服务"""
    
    def __init__(self):
        self.error_patterns: List[ErrorPattern] = []
        self.error_records: Dict[str, List[ErrorRecord]] = {}
        self.retry_handlers: Dict[RetryStrategy, Callable] = {}
        self._init_default_patterns()
        self._init_retry_handlers()
    
    def _init_default_patterns(self):
        """初始化默认错误模式"""
        patterns = [
            # 网络错误
            ErrorPattern(
                pattern=r"(connection|network|timeout|unreachable)",
                error_type=ErrorType.NETWORK_ERROR,
                retry_strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
                max_retries=5,
                base_delay=2.0,
                description="网络连接错误"
            ),
            
            # 超时错误
            ErrorPattern(
                pattern=r"(timeout|timed out|deadline exceeded)",
                error_type=ErrorType.TIMEOUT_ERROR,
                retry_strategy=RetryStrategy.LINEAR_BACKOFF,
                max_retries=3,
                base_delay=5.0,
                description="请求超时"
            ),
            
            # 资源错误
            ErrorPattern(
                pattern=r"(out of memory|disk full|resource|quota)",
                error_type=ErrorType.RESOURCE_ERROR,
                retry_strategy=RetryStrategy.FIXED_DELAY,
                max_retries=2,
                base_delay=30.0,
                description="资源不足"
            ),
            
            # 验证错误
            ErrorPattern(
                pattern=r"(validation|invalid|bad request|400)",
                error_type=ErrorType.VALIDATION_ERROR,
                retry_strategy=RetryStrategy.NO_RETRY,
                max_retries=0,
                description="参数验证错误"
            ),
            
            # 系统错误
            ErrorPattern(
                pattern=r"(internal server error|500|system error)",
                error_type=ErrorType.SYSTEM_ERROR,
                retry_strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
                max_retries=3,
                base_delay=10.0,
                description="系统内部错误"
            )
        ]
        
        self.error_patterns.extend(patterns)
    
    def _init_retry_handlers(self):
        """初始化重试处理器"""
        self.retry_handlers = {
            RetryStrategy.IMMEDIATE: self._immediate_retry,
            RetryStrategy.LINEAR_BACKOFF: self._linear_backoff_retry,
            RetryStrategy.EXPONENTIAL_BACKOFF: self._exponential_backoff_retry,
            RetryStrategy.FIXED_DELAY: self._fixed_delay_retry,
            RetryStrategy.NO_RETRY: self._no_retry
        }
    
    def analyze_error(self, task_id: str, error_message: str, stack_trace: str = "") -> ErrorRecord:
        """分析错误并创建错误记录"""
        error_type = ErrorType.UNKNOWN_ERROR
        
        # 匹配错误模式
        for pattern in self.error_patterns:
            if re.search(pattern.pattern, error_message, re.IGNORECASE):
                error_type = pattern.error_type
                break
        
        # 创建错误记录
        error_record = ErrorRecord(
            task_id=task_id,
            error_type=error_type,
            error_message=error_message,
            stack_trace=stack_trace,
            timestamp=time.time(),
            retry_count=0
        )
        
        # 保存错误记录
        if task_id not in self.error_records:
            self.error_records[task_id] = []
        self.error_records[task_id].append(error_record)
        
        logger.error(f"任务 {task_id} 发生错误: {error_type.value} - {error_message}")
        
        return error_record
    
    def should_retry(self, task_id: str, current_retry_count: int) -> bool:
        """判断是否应该重试"""
        if task_id not in self.error_records or not self.error_records[task_id]:
            return False
        
        latest_error = self.error_records[task_id][-1]
        error_pattern = self._get_error_pattern(latest_error.error_type)
        
        if not error_pattern:
            return current_retry_count < 3  # 默认重试3次
        
        return current_retry_count < error_pattern.max_retries
    
    def calculate_retry_delay(self, task_id: str, retry_count: int) -> float:
        """计算重试延迟时间"""
        if task_id not in self.error_records or not self.error_records[task_id]:
            return 1.0  # 默认延迟1秒
        
        latest_error = self.error_records[task_id][-1]
        error_pattern = self._get_error_pattern(latest_error.error_type)
        
        if not error_pattern:
            return min(2.0 ** retry_count, 60.0)  # 默认指数退避
        
        retry_handler = self.retry_handlers.get(error_pattern.retry_strategy)
        if retry_handler:
            return retry_handler(error_pattern, retry_count)
        
        return error_pattern.base_delay
    
    def _get_error_pattern(self, error_type: ErrorType) -> Optional[ErrorPattern]:
        """获取错误模式"""
        for pattern in self.error_patterns:
            if pattern.error_type == error_type:
                return pattern
        return None
    
    def _immediate_retry(self, pattern: ErrorPattern, retry_count: int) -> float:
        """立即重试"""
        return 0.0
    
    def _linear_backoff_retry(self, pattern: ErrorPattern, retry_count: int) -> float:
        """线性退避重试"""
        delay = pattern.base_delay * (retry_count + 1)
        return min(delay, pattern.max_delay)
    
    def _exponential_backoff_retry(self, pattern: ErrorPattern, retry_count: int) -> float:
        """指数退避重试"""
        delay = pattern.base_delay * (2 ** retry_count)
        return min(delay, pattern.max_delay)
    
    def _fixed_delay_retry(self, pattern: ErrorPattern, retry_count: int) -> float:
        """固定延迟重试"""
        return pattern.base_delay
    
    def _no_retry(self, pattern: ErrorPattern, retry_count: int) -> float:
        """不重试"""
        return -1.0  # 返回负值表示不重试
    
    def mark_error_resolved(self, task_id: str):
        """标记错误已解决"""
        if task_id in self.error_records:
            for error_record in self.error_records[task_id]:
                error_record.resolved = True
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        stats = {
            "total_errors": 0,
            "by_type": {},
            "by_task": {},
            "resolved_errors": 0,
            "unresolved_errors": 0
        }
        
        for task_id, error_list in self.error_records.items():
            stats["by_task"][task_id] = len(error_list)
            
            for error_record in error_list:
                stats["total_errors"] += 1
                
                error_type = error_record.error_type.value
                stats["by_type"][error_type] = stats["by_type"].get(error_type, 0) + 1
                
                if error_record.resolved:
                    stats["resolved_errors"] += 1
                else:
                    stats["unresolved_errors"] += 1
        
        return stats
    
    def get_task_errors(self, task_id: str) -> List[ErrorRecord]:
        """获取任务的错误记录"""
        return self.error_records.get(task_id, [])
    
    def add_error_pattern(self, pattern: ErrorPattern):
        """添加自定义错误模式"""
        self.error_patterns.append(pattern)
        logger.info(f"添加错误模式: {pattern.description}")
    
    def remove_error_pattern(self, pattern_str: str):
        """移除错误模式"""
        self.error_patterns = [
            p for p in self.error_patterns 
            if p.pattern != pattern_str
        ]
    
    async def handle_task_error(self, task_id: str, error_message: str, 
                               stack_trace: str = "") -> Dict[str, Any]:
        """处理任务错误"""
        try:
            # 分析错误
            error_record = self.analyze_error(task_id, error_message, stack_trace)
            
            # 获取当前重试次数
            retry_count = len([e for e in self.error_records[task_id] if not e.resolved])
            
            # 判断是否应该重试
            should_retry = self.should_retry(task_id, retry_count)
            
            result = {
                "should_retry": should_retry,
                "error_type": error_record.error_type.value,
                "retry_count": retry_count,
                "retry_delay": 0.0
            }
            
            if should_retry:
                # 计算重试延迟
                retry_delay = self.calculate_retry_delay(task_id, retry_count)
                if retry_delay >= 0:
                    result["retry_delay"] = retry_delay
                    error_record.next_retry_at = time.time() + retry_delay
                    logger.info(f"任务 {task_id} 将在 {retry_delay:.2f} 秒后重试")
                else:
                    result["should_retry"] = False
            
            return result
            
        except Exception as e:
            logger.error(f"处理任务错误失败: {str(e)}")
            return {
                "should_retry": False,
                "error_type": ErrorType.SYSTEM_ERROR.value,
                "retry_count": 0,
                "retry_delay": 0.0
            }

# 全局错误处理服务实例
_error_handler_service = None

def get_error_handler_service() -> ErrorHandlerService:
    """获取错误处理服务实例"""
    global _error_handler_service
    
    if _error_handler_service is None:
        _error_handler_service = ErrorHandlerService()
    
    return _error_handler_service
