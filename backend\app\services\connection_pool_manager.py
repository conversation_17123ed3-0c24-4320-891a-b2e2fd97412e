"""
连接池管理器
优化数据库和Redis连接，减少连接开销
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Any
import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class ConnectionPoolManager:
    """连接池管理器"""
    
    def __init__(self):
        # Redis连接池
        self.redis_pools: Dict[str, redis.ConnectionPool] = {}
        self.redis_clients: Dict[str, redis.Redis] = {}
        
        # MongoDB连接池
        self.mongo_clients: Dict[str, AsyncIOMotorClient] = {}
        
        # 连接统计
        self.connection_stats = {
            'redis_pools': 0,
            'mongo_clients': 0,
            'active_connections': 0
        }
        
        logger.info("连接池管理器初始化完成")
    
    async def get_redis_client(self, 
                              redis_url: str,
                              pool_size: int = 20,
                              timeout: int = 5) -> redis.Redis:
        """获取Redis客户端（带连接池）
        
        Args:
            redis_url: Redis连接URL
            pool_size: 连接池大小
            timeout: 连接超时时间
            
        Returns:
            Redis客户端实例
        """
        pool_key = f"{redis_url}_{pool_size}_{timeout}"
        
        if pool_key not in self.redis_clients:
            try:
                # 创建连接池
                pool = redis.ConnectionPool.from_url(
                    redis_url,
                    max_connections=pool_size,
                    socket_timeout=timeout,
                    socket_connect_timeout=timeout,
                    retry_on_timeout=True,
                    health_check_interval=30  # 30秒健康检查
                )
                
                # 创建Redis客户端
                client = redis.Redis(connection_pool=pool)
                
                # 测试连接
                await client.ping()
                
                self.redis_pools[pool_key] = pool
                self.redis_clients[pool_key] = client
                self.connection_stats['redis_pools'] += 1
                
                logger.info(f"创建Redis连接池: {redis_url} (大小: {pool_size})")
                
            except Exception as e:
                logger.error(f"创建Redis连接池失败: {str(e)}")
                raise
        
        return self.redis_clients[pool_key]
    
    async def get_mongo_client(self, 
                              mongo_url: str,
                              max_pool_size: int = 50,
                              min_pool_size: int = 5) -> AsyncIOMotorClient:
        """获取MongoDB客户端（带连接池）
        
        Args:
            mongo_url: MongoDB连接URL
            max_pool_size: 最大连接池大小
            min_pool_size: 最小连接池大小
            
        Returns:
            MongoDB客户端实例
        """
        pool_key = f"{mongo_url}_{max_pool_size}_{min_pool_size}"
        
        if pool_key not in self.mongo_clients:
            try:
                # 创建MongoDB客户端（带连接池配置）
                client = AsyncIOMotorClient(
                    mongo_url,
                    maxPoolSize=max_pool_size,
                    minPoolSize=min_pool_size,
                    maxIdleTimeMS=30000,  # 30秒空闲超时
                    waitQueueTimeoutMS=5000,  # 5秒等待超时
                    serverSelectionTimeoutMS=5000,  # 5秒服务器选择超时
                    connectTimeoutMS=5000,  # 5秒连接超时
                    socketTimeoutMS=10000,  # 10秒socket超时
                )
                
                # 测试连接
                await client.admin.command('ping')
                
                self.mongo_clients[pool_key] = client
                self.connection_stats['mongo_clients'] += 1
                
                logger.info(f"创建MongoDB连接池: {mongo_url} (大小: {min_pool_size}-{max_pool_size})")
                
            except Exception as e:
                logger.error(f"创建MongoDB连接池失败: {str(e)}")
                raise
        
        return self.mongo_clients[pool_key]
    
    @asynccontextmanager
    async def get_redis_connection(self, redis_url: str, **kwargs):
        """获取Redis连接的上下文管理器"""
        client = await self.get_redis_client(redis_url, **kwargs)
        try:
            yield client
        finally:
            # Redis连接池会自动管理连接，这里不需要手动关闭
            pass
    
    @asynccontextmanager
    async def get_mongo_connection(self, mongo_url: str, db_name: str, **kwargs):
        """获取MongoDB连接的上下文管理器"""
        client = await self.get_mongo_client(mongo_url, **kwargs)
        try:
            yield client[db_name]
        finally:
            # MongoDB连接池会自动管理连接，这里不需要手动关闭
            pass
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查所有连接池"""
        health_status = {
            'redis_pools': {},
            'mongo_clients': {},
            'overall_healthy': True
        }
        
        # 检查Redis连接池
        for pool_key, client in self.redis_clients.items():
            try:
                start_time = time.time()
                await client.ping()
                response_time = (time.time() - start_time) * 1000  # 毫秒
                
                health_status['redis_pools'][pool_key] = {
                    'healthy': True,
                    'response_time_ms': round(response_time, 2)
                }
            except Exception as e:
                health_status['redis_pools'][pool_key] = {
                    'healthy': False,
                    'error': str(e)
                }
                health_status['overall_healthy'] = False
        
        # 检查MongoDB连接池
        for pool_key, client in self.mongo_clients.items():
            try:
                start_time = time.time()
                await client.admin.command('ping')
                response_time = (time.time() - start_time) * 1000  # 毫秒
                
                health_status['mongo_clients'][pool_key] = {
                    'healthy': True,
                    'response_time_ms': round(response_time, 2)
                }
            except Exception as e:
                health_status['mongo_clients'][pool_key] = {
                    'healthy': False,
                    'error': str(e)
                }
                health_status['overall_healthy'] = False
        
        return health_status
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        stats = self.connection_stats.copy()
        
        # 添加详细的连接池信息
        redis_pool_details = {}
        for pool_key, pool in self.redis_pools.items():
            redis_pool_details[pool_key] = {
                'max_connections': pool.max_connections,
                'created_connections': pool.created_connections,
                'available_connections': len(pool._available_connections),
                'in_use_connections': len(pool._in_use_connections)
            }
        
        mongo_client_details = {}
        for pool_key, client in self.mongo_clients.items():
            # MongoDB连接池信息较难获取，这里提供基本信息
            mongo_client_details[pool_key] = {
                'connected': client.is_mongos or client.is_primary,
                'topology_description': str(client.topology_description)
            }
        
        stats.update({
            'redis_pool_details': redis_pool_details,
            'mongo_client_details': mongo_client_details,
            'timestamp': time.time()
        })
        
        return stats
    
    async def close_all(self):
        """关闭所有连接池"""
        logger.info("正在关闭所有连接池...")
        
        # 关闭Redis连接池
        for pool_key, client in self.redis_clients.items():
            try:
                await client.close()
                logger.info(f"Redis连接池已关闭: {pool_key}")
            except Exception as e:
                logger.error(f"关闭Redis连接池失败: {pool_key}, 错误: {str(e)}")
        
        # 关闭MongoDB客户端
        for pool_key, client in self.mongo_clients.items():
            try:
                client.close()
                logger.info(f"MongoDB客户端已关闭: {pool_key}")
            except Exception as e:
                logger.error(f"关闭MongoDB客户端失败: {pool_key}, 错误: {str(e)}")
        
        # 清空缓存
        self.redis_pools.clear()
        self.redis_clients.clear()
        self.mongo_clients.clear()
        
        logger.info("所有连接池已关闭")


# 全局连接池管理器实例
connection_pool_manager = ConnectionPoolManager()
