import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import autoprefixer from 'autoprefixer'
import nested from 'postcss-nested'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
        // 移除rewrite规则，保持原始路径
      }
    }
  },
  build: {
    chunkSizeWarningLimit: 2000,
    target: ['chrome90', 'edge90', 'firefox88', 'safari14'],
    // 确保构建输出的一致性
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        // 确保文件名的一致性
        entryFileNames: 'assets/[name].[hash].js',
        chunkFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]',
        manualChunks(id) {
          if (id.includes('node_modules')) {
            if (id.includes('element-plus')) {
              return 'element-plus'
            }
            if (id.includes('echarts')) {
              return 'echarts'
            }
            if (id.includes('axios')) {
              return 'axios'
            }
            if (id.includes('socket.io-client')) {
              return 'socket-io'
            }
            if (id.includes('vue')) {
              return 'vue-vendor'
            }
            return 'vendor'
          }
          if (id.includes('PublishManagement')) {
            return 'publish-management'
          }
          if (id.includes('src/views/social')) {
            return 'social-module'
          }
          if (id.includes('src/views/device')) {
            return 'device-module'
          }
          if (id.includes('src/api')) {
            return 'api-module'
          }
        }
      }
    }
  },
  css: {
    postcss: {
      plugins: [
        nested,
        autoprefixer,
        {
          postcssPlugin: 'internal:charset-removal',
          AtRule: {
            charset: (atRule) => {
              if (atRule.name === 'charset') {
                atRule.remove();
              }
            }
          }
        }
      ]
    }
  }
})
