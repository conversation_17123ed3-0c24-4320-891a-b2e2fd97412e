#!/usr/bin/env python3
"""
更新依赖包脚本
用于安装性能优化相关的新依赖包
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 成功")
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误: {e.stderr.strip()}")
        return False

def main():
    """主函数"""
    print("🚀 Backend 性能优化依赖更新脚本")
    print("=" * 50)
    
    # 检查是否在正确的目录
    backend_dir = Path(__file__).parent.parent
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print(f"❌ 找不到 requirements.txt 文件: {requirements_file}")
        sys.exit(1)
    
    print(f"📁 Backend 目录: {backend_dir}")
    print(f"📄 Requirements 文件: {requirements_file}")
    
    # 切换到 backend 目录
    os.chdir(backend_dir)
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 检测到虚拟环境")
    else:
        print("⚠️  警告: 未检测到虚拟环境，建议在虚拟环境中运行")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            print("❌ 用户取消操作")
            sys.exit(1)
    
    # 升级 pip
    if not run_command("python -m pip install --upgrade pip", "升级 pip"):
        print("⚠️  pip 升级失败，但继续安装依赖")
    
    # 安装新的依赖包
    new_packages = [
        "psutil==6.1.0",
        "redis[hiredis]==5.2.1"
    ]
    
    print(f"\n📦 准备安装新的依赖包:")
    for package in new_packages:
        print(f"  - {package}")
    
    # 逐个安装新包
    success_count = 0
    for package in new_packages:
        if run_command(f"pip install {package}", f"安装 {package}"):
            success_count += 1
    
    # 从 requirements.txt 安装所有依赖
    if run_command("pip install -r requirements.txt", "安装 requirements.txt 中的所有依赖"):
        success_count += 1
    
    # 验证关键包是否安装成功
    print(f"\n🔍 验证关键包安装状态...")
    
    verification_commands = [
        ("python -c \"import psutil; print(f'psutil 版本: {psutil.__version__}')\"", "验证 psutil"),
        ("python -c \"import redis; print(f'redis 版本: {redis.__version__}')\"", "验证 redis"),
        ("python -c \"import motor; print(f'motor 版本: {motor.version}')\"", "验证 motor"),
        ("python -c \"import fastapi; print(f'fastapi 版本: {fastapi.__version__}')\"", "验证 fastapi"),
    ]
    
    verification_success = 0
    for command, description in verification_commands:
        if run_command(command, description):
            verification_success += 1
    
    # 总结
    print(f"\n📊 安装总结:")
    print(f"  - 新包安装成功: {success_count}/{len(new_packages) + 1}")
    print(f"  - 验证成功: {verification_success}/{len(verification_commands)}")
    
    if verification_success == len(verification_commands):
        print(f"\n🎉 所有依赖包安装成功！")
        print(f"💡 现在可以启动 Backend 服务来体验性能优化功能")
        print(f"   运行命令: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        print(f"\n📊 性能监控 API 地址:")
        print(f"   - 性能指标: http://localhost:8000/api/v1/performance/metrics")
        print(f"   - 健康检查: http://localhost:8000/api/v1/performance/health")
        print(f"   - 任务队列统计: http://localhost:8000/api/v1/performance/task-queue/stats")
    else:
        print(f"\n⚠️  部分依赖包安装可能有问题，请检查上述错误信息")
        print(f"💡 建议手动运行: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
