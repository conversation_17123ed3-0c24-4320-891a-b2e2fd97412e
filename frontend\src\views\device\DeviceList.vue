<template>
  <div class="device-list">
    <div class="header-container">
      <h1>设备列表</h1>
      <div class="filter-container">
        <el-select
          v-model="selectedCoreId"
          placeholder="选择Core服务"
          clearable
          @change="handleCoreChange"
          style="width: 180px;"
        >
          <el-option
            v-for="core in coreServices"
            :key="core.id"
            :label="core.name"
            :value="core.id"
          />
        </el-select>
        <el-button type="primary" @click="refreshDevices" style="margin-left: 10px;">刷新</el-button>
      </div>
    </div>
    <el-table :data="paginatedDevices" style="width: 100%">
      <el-table-column prop="name" label="设备名称" />
      <el-table-column prop="type" label="类别" />
      <el-table-column prop="status" label="状态" />
      <el-table-column prop="resolution" label="分辨率" />
      <el-table-column prop="cpu" label="CPU" />
      <el-table-column prop="memory" label="内存" />
      <el-table-column prop="network" label="网络" />
      <el-table-column label="代理IP" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.proxy_info" type="success" size="small">
            {{ scope.row.proxy_info.region }}
          </el-tag>
          <el-tag v-else type="info" size="small">
            未关联
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleControl(scope.row)">
            设置
          </el-button>
          <el-button size="small" type="primary" @click="handleIPAssociation(scope.row)">
            IP关联
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="fetchDevices"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- IP关联对话框 -->
    <el-dialog
      v-model="ipAssociationVisible"
      title="设备IP关联设置"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentDevice">
        <div class="device-info">
          <h4>设备信息</h4>
          <p><strong>设备名称：</strong>{{ currentDevice.name }}</p>
          <p><strong>设备状态：</strong>{{ currentDevice.status }}</p>
          <p><strong>当前代理：</strong>
            <el-tag v-if="currentDevice.proxy_info" type="success">
              {{ currentDevice.proxy_info.region }} - {{ currentDevice.proxy_info.ip_address }}
            </el-tag>
            <el-tag v-else type="info">未关联代理IP</el-tag>
          </p>
        </div>

        <el-divider />

        <div class="proxy-selection">
          <div class="section-header">
            <h4>可用代理IP列表</h4>
            <el-button type="primary" size="small" @click="refreshProxyList">刷新</el-button>
          </div>

          <el-table
            :data="proxyList"
            style="width: 100%"
            v-loading="loadingProxies"
            @selection-change="handleProxySelectionChange"
            :empty-text="loadingProxies ? '正在加载代理列表...' : '暂无可用代理IP'"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="region" label="地区" width="80" />
            <el-table-column prop="ip_address" label="IP地址" width="140" />
            <el-table-column prop="port" label="端口" width="80" />
            <el-table-column prop="proxy_type" label="类型" width="100" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'warning'" size="small">
                  {{ scope.row.status === 'active' ? '可用' : '不可用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="关联状态" width="100">
              <template #default="scope">
                <el-tag v-if="isProxyAssociated(scope.row.id)" type="success" size="small">
                  已关联
                </el-tag>
                <el-tag v-else type="info" size="small">
                  未关联
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="dialog-footer" style="margin-top: 20px;">
          <el-button @click="ipAssociationVisible = false">取消</el-button>
          <el-button type="danger" @click="handleRemoveAssociation" v-if="currentDevice.proxy_info">
            取消关联
          </el-button>
          <el-button type="primary" @click="handleAssociateProxy" :disabled="selectedProxies.length === 0">
            关联选中代理
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getDevices, getCoreServices } from '@/api/rest/device'
import { getProxyList, associateDeviceProxy, disassociateDeviceProxy } from '@/api/proxy'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Device {
  id: string
  name: string
  type: string
  status: string
  resolution: string
  cpu: string
  memory: string
  network: string
  proxy_info?: {
    id: string
    region: string
    ip_address: string
    port: number
  }
}

interface ProxyIP {
  id: string
  region: string
  ip_address: string
  port: number
  proxy_type: string
  status: string
  associated_devices?: any[]
}

interface CoreService {
  id: string
  name: string
}

const router = useRouter()
const devices = ref<Device[]>([])
const coreServices = ref<CoreService[]>([])
const selectedCoreId = ref<string | null>(null)

// 分页数据
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

// IP关联相关数据
const ipAssociationVisible = ref(false)
const currentDevice = ref<Device | null>(null)
const proxyList = ref<ProxyIP[]>([])
const selectedProxies = ref<ProxyIP[]>([])
const loadingProxies = ref(false)

const typeMap: Record<string, string> = {
  'ldplayer': '雷电模拟器',
  'android': '安卓设备',
  'ios': 'iOS设备',
  'windows': 'Windows设备'
}

const getTypeName = (type?: string) => {
  return type ? typeMap[type] || type : '未知'
}
import { ElPagination } from 'element-plus'

const paginatedDevices = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return devices.value.slice(start, end)
})

// 获取设备列表
const fetchDevices = async () => {
  try {
    console.log('开始获取设备数据...')

    // 如果没有选择Core服务，则不显示任何设备
    if (!selectedCoreId.value) {
      devices.value = []
      pagination.value.total = 0
      return
    }

    const res = await getDevices({
      include_config: true,
      core_id: selectedCoreId.value
    })
    console.log('API响应:', res)

    if (!res) {
      console.warn('API返回空响应，使用空数组')
      devices.value = []
      pagination.value.total = 0
      return
    }

    if (!Array.isArray(res)) {
      console.warn(`无效的设备数据格式，期望数组，实际得到: ${typeof res}，使用空数组`)
      devices.value = []
      pagination.value.total = 0
      return
    }

    // 安全地处理设备数据
    devices.value = res.map(device => {
      // 确保每个字段都有默认值
      const deviceId = device.id || 'unknown'

      // 调试输出设备数据
      console.debug('设备数据:', device)

      // 处理显示信息
      let resolution = '未知'
      if (device.display_info) {
        const width = device.display_info.width || 0
        const height = device.display_info.height || 0
        if (width > 0 && height > 0) {
          resolution = `${width}x${height}`
        }
      }

      // 处理状态信息
      let statusText = '未知'
      if (device.status === 'running') {
        statusText = '运行中'
      } else if (device.status === 'stopped') {
        statusText = '已停止'
      } else if (device.status === 'starting') {
        statusText = '启动中'
      } else if (device.status === 'stopping') {
        statusText = '停止中'
      } else {
        statusText = device.status || '未知'
      }

      return {
        id: deviceId,
        name: device.name || `设备-${deviceId}`,
        type: getTypeName(device.type),
        status: statusText,
        resolution: resolution,
        cpu: device.cpu || '0%',
        memory: device.memory || '0%',
        network: device.network || '未知',
        proxy_info: device.proxy_info || undefined
      }
    })

    pagination.value.total = res.length
    console.log(`成功获取 ${res.length} 个设备`)
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败，请检查网络连接和后端服务')
    // 设置为空数组而不是保持之前的状态
    devices.value = []
    pagination.value.total = 0
  }
}

// 获取Core服务列表
const fetchCoreServices = async () => {
  try {
    const cores = await getCoreServices()
    coreServices.value = cores
    console.log('获取到Core服务列表:', cores)
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    ElMessage.error('获取Core服务列表失败')
  }
}

// 处理Core服务筛选变更
const handleCoreChange = () => {
  console.log('选择的Core服务ID:', selectedCoreId.value)
  pagination.value.page = 1 // 重置到第一页
  fetchDevices()
}

onMounted(() => {
  fetchCoreServices()
  fetchDevices()
})

const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.page = 1
  fetchDevices()
}

const handleControl = (device: any) => {
  router.push({ name: 'DeviceControl', params: { id: device.name } })
}

// IP关联相关方法
const handleIPAssociation = async (device: Device) => {
  currentDevice.value = device
  ipAssociationVisible.value = true
  await refreshProxyList()
}

const refreshProxyList = async () => {
  try {
    loadingProxies.value = true
    const response = await getProxyList()
    proxyList.value = response.items || []
  } catch (error: any) {
    console.error('获取代理IP列表失败:', error)
    ElMessage.error('获取代理IP列表失败: ' + (error?.message || '未知错误'))
    proxyList.value = []
  } finally {
    loadingProxies.value = false
  }
}

const handleProxySelectionChange = (proxies: ProxyIP[]) => {
  selectedProxies.value = proxies
}

const isProxyAssociated = (proxyId: string): boolean => {
  if (!currentDevice.value) return false
  return currentDevice.value.proxy_info?.id === proxyId
}

const handleAssociateProxy = async () => {
  if (!currentDevice.value || selectedProxies.value.length === 0) return

  try {
    const proxy = selectedProxies.value[0] // 只关联第一个选中的代理
    await associateDeviceProxy(proxy.id, currentDevice.value.id)

    ElMessage.success('代理IP关联成功')

    // 更新设备的代理信息
    currentDevice.value.proxy_info = {
      id: proxy.id,
      region: proxy.region,
      ip_address: proxy.ip_address,
      port: proxy.port
    }

    // 更新设备列表中的数据
    const deviceIndex = devices.value.findIndex(d => d.id === currentDevice.value!.id)
    if (deviceIndex !== -1) {
      devices.value[deviceIndex].proxy_info = currentDevice.value.proxy_info
    }

    ipAssociationVisible.value = false
    selectedProxies.value = []
  } catch (error: any) {
    console.error('关联代理IP失败:', error)
    ElMessage.error('关联代理IP失败: ' + (error?.response?.data?.detail || error?.message || '未知错误'))
  }
}

const handleRemoveAssociation = async () => {
  if (!currentDevice.value || !currentDevice.value.proxy_info) return

  try {
    await ElMessageBox.confirm('确定要取消设备与代理IP的关联吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await disassociateDeviceProxy(currentDevice.value.proxy_info.id, currentDevice.value.id)

    ElMessage.success('已取消代理IP关联')

    // 清除设备的代理信息
    currentDevice.value.proxy_info = undefined

    // 更新设备列表中的数据
    const deviceIndex = devices.value.findIndex(d => d.id === currentDevice.value!.id)
    if (deviceIndex !== -1) {
      devices.value[deviceIndex].proxy_info = undefined
    }

    ipAssociationVisible.value = false
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('取消关联失败:', error)
      ElMessage.error('取消关联失败: ' + (error?.response?.data?.detail || error?.message || '未知错误'))
    }
  }
}
</script>

<style scoped>
.device-list {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  align-items: center;
}

.table-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.el-table {
  margin-top: 20px;
}

.el-table :deep(.el-table__header) th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: bold;
}

.el-table :deep(.el-table__row) td {
  padding: 12px 0;
  text-align: center !important;
}

/* 表头居中 */
.el-table :deep(.el-table__header) th {
  text-align: center;
}

.el-table :deep(.el-button) {
  padding: 7px 15px;
}
</style>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  background: #fff;
  border-top: 1px solid #ebeef5;
}

.el-pagination {
  padding: 0 10px;
}

/* IP关联对话框样式 */
.device-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.device-info h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.device-info p {
  margin: 5px 0;
  color: #666;
}

.proxy-selection {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}
</style>