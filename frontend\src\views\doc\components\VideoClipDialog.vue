<template>
  <el-dialog
    v-model="dialogVisible"
    title="智能视频裁剪"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="video-clip-dialog">
      <!-- 文件信息 -->
      <div class="file-info-section">
        <h4>待裁剪文件 ({{ props.selectedFiles.length }} 个)</h4>
        <div class="file-list">
          <div v-for="file in props.selectedFiles" :key="file.path" class="file-item">
            <el-icon><VideoPlay /></el-icon>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-duration" v-if="file.media_info">
              {{ formatDuration(file.media_info.duration) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 裁剪模式选择 -->
      <div class="mode-section">
        <h4>裁剪模式</h4>
        <el-radio-group v-model="clipMode" @change="onModeChange">
          <el-radio value="segments">
            <div class="mode-option">
              <div class="mode-title">按段数裁剪</div>
              <div class="mode-desc">将视频分割成指定数量的片段</div>
            </div>
          </el-radio>
          <el-radio value="duration">
            <div class="mode-option">
              <div class="mode-title">按时长裁剪</div>
              <div class="mode-desc">按照指定的时间长度进行分割</div>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 裁剪参数设置 -->
      <div class="params-section">
        <h4>裁剪参数</h4>
        <el-form :model="clipParams" label-width="140px" size="default">
          <!-- 按段数模式参数 -->
          <el-form-item v-if="clipMode === 'segments'" label="分割段数">
            <el-input-number
              v-model="clipParams.segment_count"
              :min="2"
              :max="20"
              :step="1"
              placeholder="请输入分割段数"
            />
            <span class="param-tip">建议2-10段，段数过多可能影响处理速度</span>
          </el-form-item>

          <!-- 按时长模式参数 -->
          <el-form-item v-if="clipMode === 'duration'" label="每段时长(秒)">
            <el-input-number
              v-model="clipParams.segment_duration"
              :min="10"
              :max="600"
              :step="5"
              placeholder="请输入每段时长"
            />
            <span class="param-tip">建议30-300秒，时长过短可能无法找到合适的裁剪点</span>
          </el-form-item>

          <!-- 通用参数 -->
          <el-form-item label="裁剪点缓冲区(秒)">
            <el-input-number
              v-model="clipParams.buffer_duration"
              :min="0.5"
              :max="10"
              :step="0.5"
              :precision="1"
            />
            <span class="param-tip">在目标时间点前后的范围内寻找最佳裁剪点</span>
          </el-form-item>

          <el-form-item label="音量检测敏感度">
            <el-slider
              v-model="clipParams.volume_sensitivity"
              :min="0"
              :max="1"
              :step="0.1"
              :format-tooltip="formatSensitivity"
              show-stops
            />
            <span class="param-tip">敏感度越高，越倾向于在音量最小处裁剪</span>
          </el-form-item>

          <el-form-item label="最小片段时长(秒)">
            <el-input-number
              v-model="clipParams.min_segment_duration"
              :min="3"
              :max="60"
              :step="1"
            />
            <span class="param-tip">避免生成过短的片段</span>
          </el-form-item>

          <el-form-item label="输出质量">
            <el-select v-model="clipParams.output_quality">
              <el-option label="高质量 (较慢)" value="high" />
              <el-option label="中等质量 (推荐)" value="medium" />
              <el-option label="低质量 (较快)" value="low" />
            </el-select>
          </el-form-item>

          <el-form-item label="输出文件夹">
            <el-input
              v-model="clipParams.output_folder"
              placeholder="留空则在原文件同目录下创建clips文件夹"
            >
              <template #append>
                <el-button @click="selectOutputFolder">选择</el-button>
              </template>
            </el-input>
            <div class="param-tip">
              <span v-if="pathInfo?.account && pathInfo.account !== '未识别账号'">
                💡 建议输出到账号根目录：{{ `H:\\PublishSystem\\${pathInfo.platform}\\${pathInfo.account}\\clips` }}
              </span>
              <span v-else>
                💡 留空将在原文件同目录下创建 clips 文件夹
              </span>
            </div>
          </el-form-item>

          <el-form-item label="文件名模板">
            <el-input
              v-model="clipParams.filename_template"
              placeholder="{original_name}_clip_{segment_index:02d}"
            />
            <div class="template-help">
              <span>可用变量：</span>
              <el-tag size="small">{original_name}</el-tag>
              <el-tag size="small">{segment_index}</el-tag>
              <el-tag size="small">{start_time}</el-tag>
              <el-tag size="small">{end_time}</el-tag>
            </div>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="clipParams.preserve_audio_quality">
              保留原始音频质量
            </el-checkbox>
          </el-form-item>

          <el-form-item label="处理模式">
            <el-radio-group v-model="backgroundProcessing">
              <el-radio :label="false">同步处理</el-radio>
              <el-radio :label="true">后台处理</el-radio>
            </el-radio-group>
            <div class="param-tip">
              <span v-if="!backgroundProcessing">同步处理：立即执行并等待结果</span>
              <span v-else>后台处理：提交到后台队列，可在任务管理中查看进度</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 高级设置 -->
      <el-collapse v-model="advancedCollapse">
        <el-collapse-item title="高级设置" name="advanced">
          <el-form :model="clipParams" label-width="140px" size="default">
            <el-form-item label="最大并发数">
              <el-input-number
                v-model="clipParams.max_concurrent"
                :min="1"
                :max="8"
                :step="1"
              />
              <span class="param-tip">同时处理的视频数量，建议根据电脑性能调整</span>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="startClipping" :loading="processing">
          {{ processing ? '处理中...' : '开始裁剪' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 进度对话框 -->
  <el-dialog
    v-model="progressVisible"
    title="视频裁剪进度"
    width="600px"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div class="progress-content">
      <div class="progress-info">
        <p>正在处理 {{ props.selectedFiles.length }} 个视频文件...</p>
        <p>预计生成 {{ estimatedSegments }} 个片段</p>
      </div>
      
      <el-progress
        :percentage="progressPercentage"
        :status="progressStatus"
        :stroke-width="8"
      />
      
      <div class="progress-details" v-if="progressDetails">
        <p>{{ progressDetails }}</p>
      </div>
    </div>

    <template #footer>
      <el-button @click="cancelClipping" :disabled="!processing">
        取消处理
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { VideoPlay } from '@element-plus/icons-vue'
import { clipVideos, getVideoClipTaskStatus, type VideoClipRequest, type FileInfo } from '@/api/filesystem'

interface Props {
  modelValue: boolean
  selectedFiles: FileInfo[]
  currentPath?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'clip-completed': [result: any]
}>()

// 响应式数据
const dialogVisible = ref(false)
const progressVisible = ref(false)
const processing = ref(false)
const advancedCollapse = ref<string[]>([])

// 裁剪模式和参数
const clipMode = ref<'segments' | 'duration'>('duration')  // 默认按时长裁剪
const clipParams = reactive({
  segment_count: 3,
  segment_duration: 45,  // 默认45秒
  buffer_duration: 10.0,  // 默认10秒缓冲区
  volume_sensitivity: 0.5,  // 默认中等敏感度
  min_segment_duration: 35.0,  // 默认35秒最小片段时长
  output_quality: 'high' as 'high' | 'medium' | 'low',  // 默认高质量
  output_folder: '',
  filename_template: '{original_name}_{segment_index:02d}',  // 简化文件名模板
  preserve_audio_quality: true,
  max_concurrent: 3
})

// 进度相关
const progressPercentage = ref(0)
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressDetails = ref('')

// 后台处理选项
const backgroundProcessing = ref(false)

// 计算属性
const estimatedSegments = computed(() => {
  if (!props.selectedFiles || props.selectedFiles.length === 0) {
    return 0
  }

  if (clipMode.value === 'segments') {
    return props.selectedFiles.length * clipParams.segment_count
  } else {
    // 估算按时长裁剪的片段数
    const avgDuration = props.selectedFiles.reduce((sum, file) => {
      return sum + (file.media_info?.duration || 60)
    }, 0) / props.selectedFiles.length
    return Math.ceil(avgDuration / clipParams.segment_duration) * props.selectedFiles.length
  }
})

// 安全的路径信息计算属性
const pathInfo = computed(() => {
  const result = extractPathInfo(props.currentPath)
  // 确保返回值始终有效
  return result || { platform: 'unknown', account: '未识别账号' }
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  if (val !== undefined) {
    dialogVisible.value = val
  }
}, { immediate: true })

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 组件卸载时清理
onUnmounted(() => {
  processing.value = false
  progressVisible.value = false
})

// 方法
const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatSensitivity = (value: number): string => {
  if (value <= 0.3) return '低敏感度'
  if (value <= 0.7) return '中等敏感度'
  return '高敏感度'
}

const onModeChange = () => {
  // 模式切换时重置相关参数
  if (clipMode.value === 'segments') {
    clipParams.segment_count = 3
  } else {
    clipParams.segment_duration = 45  // 使用45秒作为默认值
  }
}

const selectOutputFolder = () => {
  // 获取当前路径的账号根目录
  const currentPathInfo = pathInfo.value
  let defaultPath = props.currentPath || 'H:\\PublishSystem'

  if (currentPathInfo?.platform !== 'unknown' && currentPathInfo?.account !== '未识别账号') {
    // 构建账号根目录路径
    defaultPath = `H:\\PublishSystem\\${currentPathInfo.platform}\\${currentPathInfo.account}`
  }

  // 使用系统文件选择器（这里需要调用后端API或使用electron的dialog）
  ElMessage.info(`请在文件管理器中选择输出文件夹，建议选择账号根目录：${defaultPath}`)
}

// 提取路径信息的辅助函数
const extractPathInfo = (path: string | undefined) => {
  // 处理 undefined 或空字符串的情况
  if (!path || typeof path !== 'string') {
    return { platform: 'unknown', account: '未识别账号' }
  }

  const parts = path.split('\\')
  let platform = 'unknown'
  let account = '未识别账号'

  const publishSystemIndex = parts.findIndex(part => part === 'PublishSystem')
  if (publishSystemIndex >= 0 && parts.length > publishSystemIndex + 2) {
    platform = parts[publishSystemIndex + 1]
    account = parts[publishSystemIndex + 2]
  }

  return { platform, account }
}

const startClipping = async () => {
  try {
    // 验证参数
    if (clipMode.value === 'segments' && clipParams.segment_count < 2) {
      ElMessage.error('分割段数至少为2')
      return
    }
    
    if (clipMode.value === 'duration' && clipParams.segment_duration < 30) {
      ElMessage.error('每段时长至少为30秒')
      return
    }

    processing.value = true
    progressVisible.value = true
    progressPercentage.value = 0
    progressStatus.value = ''
    progressDetails.value = '正在准备裁剪任务...'

    // 构建请求参数
    const request: VideoClipRequest = {
      video_paths: props.selectedFiles.map(f => f.path),
      clip_mode: clipMode.value,
      background_processing: backgroundProcessing.value,
      ...clipParams
    }

    // 调用API
    const response = await clipVideos(request)

    if (backgroundProcessing.value) {
      // 后台处理模式
      progressPercentage.value = 100
      progressStatus.value = 'success'
      progressDetails.value = `任务已提交到后台处理！任务ID: ${response.task_id}`

      setTimeout(() => {
        progressVisible.value = false
        dialogVisible.value = false
        processing.value = false

        ElMessage.success(`视频裁剪任务已提交到后台处理，任务ID: ${response.task_id}`)
      }, 2000)
    } else {
      // 同步处理模式
      progressPercentage.value = 100
      progressStatus.value = 'success'
      progressDetails.value = `裁剪完成！成功处理 ${response.successful_count} 个文件，生成 ${response.total_segments} 个片段`

      setTimeout(() => {
        progressVisible.value = false
        dialogVisible.value = false
        processing.value = false

        emit('clip-completed', response)

        ElMessage.success(`视频裁剪完成！生成了 ${response.total_segments} 个片段`)
      }, 2000)
    }

  } catch (error: any) {
    progressStatus.value = 'exception'
    progressDetails.value = `裁剪失败: ${error.message || '未知错误'}`
    
    setTimeout(() => {
      progressVisible.value = false
      processing.value = false
    }, 3000)
    
    ElMessage.error(`视频裁剪失败: ${error.message || '未知错误'}`)
  }
}

const cancelClipping = () => {
  // TODO: 实现取消功能
  ElMessage.info('取消功能开发中...')
}

const handleClose = () => {
  if (processing.value) {
    ElMessageBox.confirm('正在处理中，确定要关闭吗？', '确认关闭', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      dialogVisible.value = false
      processing.value = false
      progressVisible.value = false
    }).catch(() => {
      // 用户取消关闭
    })
  } else {
    dialogVisible.value = false
  }
}
</script>

<style scoped>
.video-clip-dialog {
  max-height: 70vh;
  overflow-y: auto;
}

.file-info-section {
  margin-bottom: 24px;
}

.file-info-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.file-list {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.file-item .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.file-name {
  flex: 1;
  color: #606266;
}

.file-duration {
  color: #909399;
  font-size: 12px;
}

.mode-section {
  margin-bottom: 24px;
}

.mode-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.mode-option {
  margin-left: 8px;
}

.mode-title {
  font-weight: 500;
  color: #303133;
}

.mode-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.params-section {
  margin-bottom: 24px;
}

.params-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.param-tip {
  margin-left: 12px;
  font-size: 12px;
  color: #909399;
}

.template-help {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.template-help .el-tag {
  margin-left: 8px;
  margin-right: 4px;
}

.progress-content {
  text-align: center;
}

.progress-info {
  margin-bottom: 20px;
}

.progress-info p {
  margin: 8px 0;
  color: #606266;
}

.progress-details {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 13px;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-clip-dialog {
    max-height: 60vh;
  }

  .el-form--default .el-form-item__label {
    width: 120px !important;
  }
}
</style>
