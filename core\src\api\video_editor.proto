syntax = "proto3";

package video_editor;

// 视频编辑器服务定义
service VideoEditorService {
    // 生成预览帧
    rpc GeneratePreviewFrame(PreviewFrameRequest) returns (PreviewFrameResponse);
    
    // 应用编辑操作
    rpc ApplyEditOperation(EditOperationRequest) returns (EditOperationResponse);
    
    // 导出视频
    rpc ExportVideo(ExportVideoRequest) returns (ExportVideoResponse);
    
    // 获取服务状态
    rpc GetServiceStatus(ServiceStatusRequest) returns (ServiceStatusResponse);
    
    // 清除缓存
    rpc ClearCache(ClearCacheRequest) returns (ClearCacheResponse);
}

// 预览帧请求
message PreviewFrameRequest {
    string project_data = 1;  // JSON格式的项目数据
    double timestamp = 2;     // 时间戳（秒）
    string quality = 3;       // 预览质量 (low/medium/high/full)
}

// 预览帧响应
message PreviewFrameResponse {
    bool success = 1;
    string frame_data = 2;    // base64编码的图像数据
    double timestamp = 3;
    string quality = 4;
    Resolution resolution = 5;
    int32 processing_time_ms = 6;
    string error = 7;
}

// 分辨率信息
message Resolution {
    int32 width = 1;
    int32 height = 2;
}

// 编辑操作请求
message EditOperationRequest {
    string project_id = 1;
    EditOperation operation = 2;
}

// 编辑操作
message EditOperation {
    string type = 1;          // 操作类型 (cut/split/merge/move/resize/delete)
    string clip_id = 2;       // 片段ID
    string parameters = 3;    // JSON格式的参数
}

// 编辑操作响应
message EditOperationResponse {
    bool success = 1;
    string clip_id = 2;
    string operation = 3;
    string result = 4;        // JSON格式的结果
    string error = 5;
}

// 导出视频请求
message ExportVideoRequest {
    string project_data = 1;     // JSON格式的项目数据
    ExportSettings export_settings = 2;
}

// 导出设置
message ExportSettings {
    string format = 1;            // 导出格式
    string resolution = 2;        // 导出分辨率
    string frame_rate = 3;        // 导出帧率
    int32 quality = 4;            // 导出质量 (1-100)
    string audio_codec = 5;       // 音频编码
    string audio_bitrate = 6;     // 音频比特率
    string export_range = 7;      // 导出范围
    double start_time = 8;        // 开始时间
    double end_time = 9;          // 结束时间
    string output_path = 10;      // 输出路径
    string file_name = 11;        // 文件名
}

// 导出视频响应
message ExportVideoResponse {
    bool success = 1;
    string output_file = 2;       // 输出文件路径
    int64 file_size = 3;          // 文件大小
    int32 processing_time_ms = 4; // 处理时间
    string format = 5;            // 格式
    string resolution = 6;        // 分辨率
    int32 quality = 7;            // 质量
    string error = 8;
}

// 服务状态请求
message ServiceStatusRequest {
    // 空请求
}

// 服务状态响应
message ServiceStatusResponse {
    bool success = 1;
    string status_data = 2;       // JSON格式的状态数据
    string error = 3;
}

// 清除缓存请求
message ClearCacheRequest {
    string cache_type = 1;        // 缓存类型 (preview/all)
}

// 清除缓存响应
message ClearCacheResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
}
