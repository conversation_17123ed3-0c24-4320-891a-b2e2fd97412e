# 分布式锁解决方案

## 问题描述

当多个 Backend 服务同时运行时，Redis 自动任务调度会导致多个服务同时启动下一个任务，造成冲突。这是因为：

1. 所有 Backend 服务都监听 Redis 的任务状态变更
2. 当一个子任务完成时，所有服务都会收到通知
3. 多个服务同时尝试启动下一个子任务，导致重复启动或冲突

## 解决方案

实现了一个优雅的分布式锁机制，确保同一时间只有一个 Backend 服务能够处理特定主任务的子任务启动。

### 核心特性

1. **原子性锁操作**：使用 Redis 的 `SET NX EX` 命令确保锁的原子性获取
2. **唯一锁标识**：每个锁包含主机名、进程ID和时间戳，确保唯一性
3. **自动过期**：锁设置60秒过期时间，防止死锁
4. **安全释放**：使用 Lua 脚本确保只有锁的持有者才能释放锁
5. **重试机制**：支持带延迟的重试机制，提高成功率
6. **详细日志**：完整的日志记录，便于调试和监控

### 实现细节

#### 1. 分布式锁获取

```python
async def _try_acquire_distributed_lock(self, lock_key: str, expire_time: int = 60) -> bool:
    # 生成唯一锁值
    hostname = socket.gethostname()
    pid = os.getpid()
    lock_value = f"backend_{hostname}_{pid}_{int(time.time())}"
    
    # 原子性获取锁
    result = await self.redis_client.set(lock_key, lock_value, nx=True, ex=expire_time)
    return result is not None
```

#### 2. 安全锁释放

```python
async def _release_distributed_lock(self, lock_key: str) -> bool:
    # 使用 Lua 脚本确保原子性
    lua_script = """
    if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
    else
        return 0
    end
    """
    result = await self.redis_client.eval(lua_script, 1, lock_key, expected_lock_value)
    return result == 1
```

#### 3. 重试机制

```python
async def _acquire_distributed_lock_with_retry(self, lock_key: str, callback_func, *args, 
                                             max_retries: int = 3, retry_delay: float = 1.0) -> bool:
    for attempt in range(max_retries + 1):
        if await self._try_acquire_distributed_lock(lock_key):
            try:
                await callback_func(*args)
                return True
            finally:
                await self._release_distributed_lock(lock_key)
        else:
            if attempt < max_retries:
                await asyncio.sleep(retry_delay)
    return False
```

### 使用方式

在 `_handle_task_completion` 方法中，使用分布式锁保护子任务启动逻辑：

```python
success = await self._acquire_distributed_lock_with_retry(
    f"subtask_start_lock:{parent_task_id}",
    self._process_next_subtask_logic,
    parent_task_id,
    max_retries=3,
    retry_delay=1.0
)
```

### 锁的生命周期

1. **锁键格式**：`subtask_start_lock:{parent_task_id}`
2. **锁值格式**：`backend_{hostname}_{pid}_{timestamp}`
3. **过期时间**：60秒（防止死锁）
4. **重试策略**：最多3次重试，每次间隔1秒

### 优势

1. **防止重复启动**：确保同一主任务的子任务不会被多个服务同时启动
2. **高可用性**：锁自动过期，避免单点故障导致的死锁
3. **性能优化**：使用重试机制，提高锁获取成功率
4. **易于监控**：详细的日志记录，便于问题排查
5. **扩展性好**：支持任意数量的 Backend 服务实例

### 测试

提供了 `test_distributed_lock.py` 脚本用于测试分布式锁功能：

```bash
cd backend
python test_distributed_lock.py
```

测试包括：
- 单个锁的获取和释放
- 多个进程的并发访问测试
- 锁的自动过期验证

### 监控和调试

日志中会记录以下关键信息：
- 锁的获取和释放状态
- 锁的持有者信息
- 重试次数和结果
- 任务处理进度

通过这些日志可以监控分布式锁的工作状态和性能。

## 部署注意事项

1. 确保所有 Backend 服务都连接到同一个 Redis 实例
2. Redis 服务器需要支持 Lua 脚本执行
3. 建议监控 Redis 的内存使用情况，及时清理过期的锁
4. 在高并发场景下，可以适当调整重试次数和延迟时间
