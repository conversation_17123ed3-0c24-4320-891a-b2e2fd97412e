<template>
  <div class="ai-generate-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>AI生成功能测试</span>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            创建AI生成任务
          </el-button>
        </div>
      </template>

      <!-- 任务列表 -->
      <div class="task-list">
        <div v-if="tasks.length === 0" class="empty-state">
          <el-empty description="暂无生成任务" />
        </div>
        
        <div v-else>
          <div v-for="task in tasks" :key="task.id" class="task-item">
            <div class="task-header">
              <h3>{{ task.title }}</h3>
              <el-tag :type="getStatusType(task.status)">{{ getStatusText(task.status) }}</el-tag>
            </div>
            <p class="task-description">{{ task.description }}</p>
            <div class="task-meta">
              <span>类型: {{ getTypeText(task.type) }}</span>
              <span>创建时间: {{ formatTime(task.created_at) }}</span>
            </div>
            <div v-if="task.status === 'running'" class="task-progress">
              <el-progress :percentage="task.progress || 0" />
            </div>
            <div class="task-actions">
              <el-button size="small" @click="viewTask(task)">查看详情</el-button>
              <el-button v-if="task.status === 'running'" size="small" type="warning" @click="pauseTask(task.id)">暂停</el-button>
              <el-button v-if="task.status === 'paused'" size="small" type="success" @click="resumeTask(task.id)">继续</el-button>
              <el-button size="small" type="danger" @click="deleteTask(task.id)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 创建任务对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建AI生成任务" width="600px">
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="任务标题" required>
          <el-input v-model="createForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        
        <el-form-item label="生成类型" required>
          <el-radio-group v-model="createForm.type" @change="handleTypeChange">
            <el-radio-button value="image">图片生成</el-radio-button>
            <el-radio-button value="video">视频生成</el-radio-button>
            <el-radio-button value="text">文本生成</el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="描述内容" required>
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请描述您想要生成的内容"
          />
        </el-form-item>

        <!-- 工作流模板选择 -->
        <el-form-item label="工作流模板">
          <el-select 
            v-model="createForm.workflow_template" 
            placeholder="选择工作流模板"
            style="width: 100%"
            @change="handleWorkflowChange"
          >
            <el-option 
              v-for="template in filteredTemplates" 
              :key="template.id"
              :label="template.name" 
              :value="template.id"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ template.name }}</span>
                <span style="color: #8492a6; font-size: 13px;">{{ template.category }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 动态参数配置 -->
        <div v-if="workflowParameters.length > 0" class="parameters-section">
          <h4>生成参数</h4>
          <div class="parameter-groups">
            <div v-for="group in parameterGroups" :key="group.name" class="parameter-group">
              <h5>{{ getGroupLabel(group.name) }}</h5>
              <div class="parameter-items">
                <el-form-item 
                  v-for="param in group.parameters" 
                  :key="param.name"
                  :label="param.label"
                  style="margin-bottom: 12px;"
                >
                  <!-- 字符串输入 -->
                  <el-input 
                    v-if="param.type === 'string'"
                    v-model="createForm.parameters[param.name]"
                    :placeholder="param.description"
                    :type="param.name.includes('prompt') ? 'textarea' : 'text'"
                    :rows="param.name.includes('prompt') ? 3 : 1"
                  />
                  
                  <!-- 数字输入 -->
                  <el-input-number 
                    v-else-if="param.type === 'integer' || param.type === 'float'"
                    v-model="createForm.parameters[param.name]"
                    :min="param.min_value"
                    :max="param.max_value"
                    :step="param.type === 'float' ? 0.1 : 1"
                    :precision="param.type === 'float' ? 1 : 0"
                    controls-position="right"
                    style="width: 100%;"
                  />
                  
                  <!-- 选择器 -->
                  <el-select 
                    v-else-if="param.type === 'select'"
                    v-model="createForm.parameters[param.name]"
                    style="width: 100%;"
                  >
                    <el-option 
                      v-for="option in param.options" 
                      :key="option.value"
                      :label="option.label" 
                      :value="option.value"
                    />
                  </el-select>
                  
                  <!-- 布尔值 -->
                  <el-switch 
                    v-else-if="param.type === 'boolean'"
                    v-model="createForm.parameters[param.name]"
                  />
                  
                  <div v-if="param.description" class="param-description">
                    {{ param.description }}
                  </div>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createTask" :loading="creating">
          {{ creating ? '创建中...' : '创建任务' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 模拟数据
const mockTemplates = [
  {
    id: 'sdxl_image',
    name: 'SDXL 图片生成',
    category: 'image',
    description: '使用Stable Diffusion XL生成高质量图片'
  },
  {
    id: 'svd_video',
    name: 'SVD 视频生成',
    category: 'video',
    description: '使用Stable Video Diffusion生成视频'
  },
  {
    id: 'llm_text',
    name: 'LLM 文本生成',
    category: 'text',
    description: '使用大语言模型生成文本内容'
  }
]

const mockParameters = {
  image: [
    {
      name: 'positive_prompt',
      type: 'string',
      label: '正向提示词',
      description: '描述想要生成的内容',
      required: true,
      group: 'prompt'
    },
    {
      name: 'negative_prompt',
      type: 'string',
      label: '负向提示词',
      description: '描述不想要的内容',
      default_value: 'low quality, blurry',
      group: 'prompt'
    },
    {
      name: 'width',
      type: 'integer',
      label: '宽度',
      description: '图片宽度（像素）',
      default_value: 1024,
      min_value: 512,
      max_value: 2048,
      group: 'size'
    },
    {
      name: 'height',
      type: 'integer',
      label: '高度',
      description: '图片高度（像素）',
      default_value: 1024,
      min_value: 512,
      max_value: 2048,
      group: 'size'
    },
    {
      name: 'steps',
      type: 'integer',
      label: '生成步数',
      description: '扩散模型的采样步数',
      default_value: 20,
      min_value: 1,
      max_value: 100,
      group: 'generation'
    },
    {
      name: 'cfg_scale',
      type: 'float',
      label: 'CFG Scale',
      description: '分类器自由引导强度',
      default_value: 7.0,
      min_value: 1.0,
      max_value: 20.0,
      group: 'generation'
    },
    {
      name: 'sampler',
      type: 'select',
      label: '采样器',
      description: '选择采样算法',
      default_value: 'euler',
      options: [
        { value: 'euler', label: 'Euler' },
        { value: 'euler_a', label: 'Euler Ancestral' },
        { value: 'dpm_2', label: 'DPM++ 2M' },
        { value: 'ddim', label: 'DDIM' }
      ],
      group: 'generation'
    }
  ]
}

// 响应式数据
const showCreateDialog = ref(false)
const creating = ref(false)
const tasks = ref([])
const workflowTemplates = ref(mockTemplates)
const workflowParameters = ref([])

const createForm = reactive({
  title: '',
  type: 'image',
  description: '',
  workflow_template: '',
  parameters: {}
})

// 计算属性
const filteredTemplates = computed(() => {
  return workflowTemplates.value.filter(t => t.category === createForm.type)
})

const parameterGroups = computed(() => {
  const groups = {}
  workflowParameters.value.forEach(param => {
    if (!groups[param.group]) {
      groups[param.group] = {
        name: param.group,
        parameters: []
      }
    }
    groups[param.group].parameters.push(param)
  })
  return Object.values(groups)
})

// 方法
const handleTypeChange = () => {
  createForm.workflow_template = ''
  workflowParameters.value = []
  createForm.parameters = {}
}

const handleWorkflowChange = (templateId) => {
  if (!templateId) {
    workflowParameters.value = []
    createForm.parameters = {}
    return
  }

  const template = workflowTemplates.value.find(t => t.id === templateId)
  if (template) {
    // 加载对应类型的参数
    workflowParameters.value = mockParameters[template.category] || []
    
    // 初始化默认值
    const defaultParams = {}
    workflowParameters.value.forEach(param => {
      if (param.default_value !== undefined) {
        defaultParams[param.name] = param.default_value
      }
    })
    createForm.parameters = defaultParams
  }
}

const getGroupLabel = (groupName) => {
  const labels = {
    'prompt': '提示词设置',
    'size': '尺寸设置',
    'generation': '生成设置'
  }
  return labels[groupName] || groupName
}

const createTask = async () => {
  if (!createForm.title || !createForm.description) {
    ElMessage.warning('请填写任务标题和描述')
    return
  }

  creating.value = true
  
  try {
    // 模拟创建任务
    const newTask = {
      id: Date.now().toString(),
      title: createForm.title,
      type: createForm.type,
      description: createForm.description,
      workflow_template: createForm.workflow_template,
      parameters: { ...createForm.parameters },
      status: 'running',
      progress: 0,
      created_at: new Date().toISOString()
    }

    tasks.value.unshift(newTask)
    
    // 模拟进度更新
    simulateProgress(newTask.id)
    
    ElMessage.success('任务创建成功！')
    showCreateDialog.value = false
    
    // 重置表单
    Object.assign(createForm, {
      title: '',
      type: 'image',
      description: '',
      workflow_template: '',
      parameters: {}
    })
    workflowParameters.value = []
    
  } catch (error) {
    ElMessage.error('创建任务失败')
  } finally {
    creating.value = false
  }
}

const simulateProgress = (taskId) => {
  const task = tasks.value.find(t => t.id === taskId)
  if (!task) return

  const interval = setInterval(() => {
    task.progress += Math.random() * 20
    if (task.progress >= 100) {
      task.progress = 100
      task.status = 'completed'
      clearInterval(interval)
      ElMessage.success(`任务 "${task.title}" 已完成！`)
    }
  }, 1000)
}

const getStatusType = (status) => {
  const types = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'paused': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '等待中',
    'running': '生成中',
    'completed': '已完成',
    'failed': '失败',
    'paused': '已暂停'
  }
  return texts[status] || status
}

const getTypeText = (type) => {
  const texts = {
    'image': '图片生成',
    'video': '视频生成',
    'text': '文本生成'
  }
  return texts[type] || type
}

const formatTime = (timeStr) => {
  return new Date(timeStr).toLocaleString()
}

const viewTask = (task) => {
  ElMessage.info(`查看任务: ${task.title}`)
}

const pauseTask = (taskId) => {
  const task = tasks.value.find(t => t.id === taskId)
  if (task) {
    task.status = 'paused'
    ElMessage.success('任务已暂停')
  }
}

const resumeTask = (taskId) => {
  const task = tasks.value.find(t => t.id === taskId)
  if (task) {
    task.status = 'running'
    simulateProgress(taskId)
    ElMessage.success('任务已继续')
  }
}

const deleteTask = (taskId) => {
  const index = tasks.value.findIndex(t => t.id === taskId)
  if (index > -1) {
    tasks.value.splice(index, 1)
    ElMessage.success('任务已删除')
  }
}

onMounted(() => {
  // 初始化时可以加载一些示例任务
})
</script>

<style scoped>
.ai-generate-test {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-list {
  min-height: 400px;
}

.task-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
  transition: all 0.3s;
}

.task-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.task-description {
  color: #606266;
  margin-bottom: 12px;
  line-height: 1.5;
}

.task-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #909399;
}

.task-progress {
  margin-bottom: 12px;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.parameters-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.parameters-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.parameter-groups {
  max-height: 400px;
  overflow-y: auto;
}

.parameter-group {
  margin-bottom: 20px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.parameter-group h5 {
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.parameter-items {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.param-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}
</style>
