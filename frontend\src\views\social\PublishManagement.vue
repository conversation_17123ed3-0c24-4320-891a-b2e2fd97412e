<template>
  <div class="publish-management">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <el-steps :active="activeStep" finish-status="success" simple>
        <el-step title="创建任务" />
        <el-step title="配置任务" />
        <el-step title="执行任务" />
        <el-step title="执行结果" />
      </el-steps>
    </div>

    <!-- 主要内容区域 -->
    <div class="step-content">
      <!-- 步骤1: 创建任务 -->
      <div v-if="activeStep === 1" class="step-wrapper">
        <PublishTaskForm
          :existing-task-id="currentTaskId"
          :existing-task-data="taskData"
          @task-created="onTaskCreated"
        />
      </div>

      <!-- 步骤2: 配置任务 -->
      <div v-if="activeStep === 2" class="step-wrapper">

        <TaskConfigForm
          :task-id="currentTaskId"
          :existing-config="configData"
          :task-data="taskData"
          @config-changed="onConfigChanged"
          @config-completed="onConfigCompleted"
        />
      </div>

      <!-- 步骤3: 执行任务 -->
      <div v-if="activeStep === 3" class="step-wrapper">
        <TaskExecution
          :task-id="currentTaskId"
          @execution-completed="onExecutionCompleted"
          @execution-started="onExecutionStarted"
        />
      </div>

      <!-- 步骤4: 执行结果 -->
      <div v-if="activeStep === 4" class="step-wrapper">
        <PublishResultsView :task-id="currentTaskId" @create-new-task="resetFlow" />
      </div>
    </div>

    <!-- 简化的步骤操作区域 -->
    <div class="step-actions" v-if="showStepActions">
      <el-button v-if="activeStep > 1 && !isTaskExecuting" @click="prevStep" :icon="ArrowLeft">
        返回上一步
      </el-button>

      <!-- 只在步骤4显示重新开始按钮 -->
      <el-button v-if="activeStep === 4" type="primary" @click="resetFlow" :icon="Refresh">
        创建新任务
      </el-button>
    </div>

    <!-- 上传状态标签页保留为单独入口 -->
    <div class="upload-status-link">
      <el-link type="primary" @click="showUploadStatus = true">查看上传状态</el-link>
    </div>

    <!-- 上传状态对话框 -->
    <el-dialog
      v-model="showUploadStatus"
      title="上传状态"
      width="80%"
      destroy-on-close
    >
      <UploadStatus />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh } from '@element-plus/icons-vue'
import { updateTaskConfig } from '@/api/social'

const PublishTaskForm = defineAsyncComponent(() => import('./components/PublishTaskForm.vue'))
const TaskConfigForm = defineAsyncComponent(() => import('./components/TaskConfigForm.vue'))
const TaskExecution = defineAsyncComponent(() => import('./components/TaskExecution.vue'))
const PublishResultsView = defineAsyncComponent(() => import('./components/PublishResultsView.vue'))
const UploadStatus = defineAsyncComponent(() => import('./UploadStatus.vue'))

// 当前步骤
const activeStep = ref(1)

// 当前任务ID
const currentTaskId = ref('')

// 任务数据缓存
const taskData = ref(null)

// 配置数据缓存
const configData = ref(null)

// 任务执行状态
const isTaskExecuting = ref(false)

// 上传状态对话框
const showUploadStatus = ref(false)

// 是否显示步骤操作按钮
const showStepActions = computed(() => {
  // 步骤1和步骤2不显示全局操作按钮，由组件内部处理
  // 步骤3执行中不允许返回
  // 步骤4显示重新开始按钮
  return activeStep.value > 1
})

// 任务创建成功回调
const onTaskCreated = (taskId, createdTaskData = null) => {
  currentTaskId.value = taskId

  // 保存任务数据，用于返回时恢复状态
  if (createdTaskData) {
    taskData.value = createdTaskData
  }

  ElMessage.success('任务创建成功，请继续配置任务')
  nextStep()
}

// 任务配置变化回调（实时保存）
const onConfigChanged = (config: any) => {
  console.log('配置实时变化，保存到缓存:', config)
  configData.value = { ...config }
}

// 任务配置完成回调
const onConfigCompleted = async (config: any) => {
  try {
    console.log('=== 任务配置保存开始 ===')
    console.log('保存任务配置:', config)
    console.log('任务ID:', currentTaskId.value)
    console.log('配置数据类型:', typeof config)
    console.log('配置数据键:', Object.keys(config))

    // 保存配置数据，用于返回时恢复状态
    configData.value = { ...config }
    console.log('✅ 配置数据已保存到缓存:', configData.value)

    // 特别检查contentType字段
    if (config.contentType) {
      console.log('✅ 检测到contentType字段:', config.contentType)
    } else {
      console.log('❌ 未检测到contentType字段')
    }

    // 调用API保存任务配置
    console.log('正在调用updateTaskConfig API...')
    const result = await updateTaskConfig(currentTaskId.value, config)
    console.log('API调用结果:', result)

    ElMessage.success('任务配置保存成功，请继续执行任务')
    console.log('=== 任务配置保存完成 ===')
    nextStep()
  } catch (error) {
    console.error('=== 任务配置保存失败 ===')
    console.error('保存任务配置失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    ElMessage.error('保存任务配置失败，请重试')
  }
}

// 任务执行开始回调
const onExecutionStarted = () => {
  isTaskExecuting.value = true
  ElMessage.info('任务已开始执行，无法返回上一步')
}

// 任务执行完成回调
const onExecutionCompleted = (result) => {
  isTaskExecuting.value = false
  ElMessage.success(`任务执行${result.status === 'completed' ? '成功' : '失败'}，请查看执行结果`)
  nextStep()
}

// 下一步
const nextStep = () => {
  if (activeStep.value < 4) {
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 1) {
    activeStep.value--
  }
}

// 重置流程
const resetFlow = () => {
  activeStep.value = 1
  currentTaskId.value = ''
  taskData.value = null
  configData.value = null
  isTaskExecuting.value = false
  ElMessage.info('开始创建新任务')
}
</script>

<style scoped>
.publish-management {
  height: 100%; /* 让它占满content-wrapper的可用空间 */
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.steps-container {
  padding: 20px 20px 10px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.step-wrapper {
  height: 100%;
  overflow: hidden; /* 让子组件处理滚动 */
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  flex-shrink: 0;
}

.upload-status-link {
  text-align: right;
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  flex-shrink: 0;
}

.config-step-hint {
  margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .steps-container {
    padding: 15px 10px 10px 10px;
  }

  .step-actions {
    padding: 10px 15px;
    flex-direction: column;
    gap: 10px;
  }

  .upload-status-link {
    padding: 10px 15px;
  }
}
</style>