<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="600px"
    @close="handleClose"
  >
    <div class="batch-operation">
      <!-- 选中账号列表 -->
      <div class="selected-accounts">
        <h4>选中的对标账号 ({{ selectedBenchmarks.length }}个)</h4>
        <div class="account-list">
          <div 
            v-for="benchmark in selectedBenchmarks" 
            :key="benchmark._id || benchmark.id"
            class="account-item"
          >
            <div class="account-info">
              <span class="account-name">{{ benchmark.account_name }}</span>
              <el-tag size="small" type="info">{{ benchmark.platform }}</el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 批量更新数据 -->
      <div v-if="operationType === 'update-data'" class="operation-content">
        <h4>批量更新数据</h4>
        <el-form
          ref="formRef"
          :model="updateForm"
          label-width="100px"
        >
          <el-form-item label="粉丝数">
            <el-input-number
              v-model="updateForm.followers"
              :min="0"
              placeholder="留空表示不更新"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="关注数">
            <el-input-number
              v-model="updateForm.following"
              :min="0"
              placeholder="留空表示不更新"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="作品数">
            <el-input-number
              v-model="updateForm.posts_count"
              :min="0"
              placeholder="留空表示不更新"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="互动率">
            <el-input-number
              v-model="updateForm.engagement_rate"
              :min="0"
              :max="1"
              :step="0.001"
              :precision="3"
              placeholder="留空表示不更新"
              style="width: 100%"
            />
            <div class="form-tip">
              互动率范围：0-1，例如 0.05 表示 5%
            </div>
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="updateForm.status"
              placeholder="选择状态（留空表示不更新）"
              style="width: 100%"
              clearable
            >
              <el-option label="活跃" value="active" />
              <el-option label="非活跃" value="inactive" />
              <el-option label="监控中" value="monitoring" />
            </el-select>
          </el-form-item>

          <el-form-item label="优先级">
            <el-rate
              v-model="updateForm.priority"
              :max="5"
              show-text
              :texts="['很低', '较低', '一般', '较高', '很高']"
              clearable
            />
            <div class="form-tip">
              点击清除按钮表示不更新优先级
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 批量导出 -->
      <div v-else-if="operationType === 'export'" class="operation-content">
        <h4>批量导出设置</h4>
        <el-form label-width="100px">
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportForm.format">
              <el-radio value="excel">Excel (.xlsx)</el-radio>
              <el-radio value="csv">CSV (.csv)</el-radio>
              <el-radio value="json">JSON (.json)</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="包含字段">
            <el-checkbox-group v-model="exportForm.fields">
              <el-checkbox value="basic">基本信息</el-checkbox>
              <el-checkbox value="data">数据统计</el-checkbox>
              <el-checkbox value="tags">标签</el-checkbox>
              <el-checkbox value="description">描述</el-checkbox>
              <el-checkbox value="timestamps">时间戳</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="文件名">
            <el-input
              v-model="exportForm.filename"
              placeholder="输入文件名（不含扩展名）"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ getSubmitButtonText() }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

// Props
const props = defineProps<{
  visible: boolean
  selectedBenchmarks: any[]
  operationType: string
}>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 批量更新表单
const updateForm = reactive({
  followers: null,
  following: null,
  posts_count: null,
  engagement_rate: null,
  status: '',
  priority: null
})

// 导出表单
const exportForm = reactive({
  format: 'excel',
  fields: ['basic', 'data'],
  filename: `benchmark_accounts_${new Date().toISOString().slice(0, 10)}`
})

// 方法
const getDialogTitle = () => {
  const typeMap = {
    'update-data': '批量更新数据',
    'export': '批量导出数据'
  }
  return typeMap[props.operationType] || '批量操作'
}

const getSubmitButtonText = () => {
  const textMap = {
    'update-data': '更新数据',
    'export': '导出数据'
  }
  return textMap[props.operationType] || '确定'
}

const handleSubmit = async () => {
  submitting.value = true

  try {
    if (props.operationType === 'update-data') {
      await handleBatchUpdate()
    } else if (props.operationType === 'export') {
      await handleBatchExport()
    }

    emit('success')
    handleClose()

  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  } finally {
    submitting.value = false
  }
}

const handleBatchUpdate = async () => {
  // 过滤掉空值
  const updateData = Object.fromEntries(
    Object.entries(updateForm).filter(([_, value]) => 
      value !== null && value !== '' && value !== undefined
    )
  )

  if (Object.keys(updateData).length === 0) {
    ElMessage.warning('请至少选择一个要更新的字段')
    return
  }

  // TODO: 实现批量更新API调用
  console.log('批量更新数据:', {
    benchmarks: props.selectedBenchmarks.map(b => b._id || b.id),
    updateData
  })

  ElMessage.success(`成功更新 ${props.selectedBenchmarks.length} 个对标账号`)
}

const handleBatchExport = async () => {
  if (exportForm.fields.length === 0) {
    ElMessage.warning('请至少选择一个要导出的字段')
    return
  }

  // TODO: 实现批量导出功能
  console.log('批量导出数据:', {
    benchmarks: props.selectedBenchmarks.map(b => b._id || b.id),
    format: exportForm.format,
    fields: exportForm.fields,
    filename: exportForm.filename
  })

  ElMessage.success(`开始导出 ${props.selectedBenchmarks.length} 个对标账号的数据`)
}

const handleClose = () => {
  dialogVisible.value = false
  resetForms()
}

const resetForms = () => {
  Object.assign(updateForm, {
    followers: null,
    following: null,
    posts_count: null,
    engagement_rate: null,
    status: '',
    priority: null
  })

  Object.assign(exportForm, {
    format: 'excel',
    fields: ['basic', 'data'],
    filename: `benchmark_accounts_${new Date().toISOString().slice(0, 10)}`
  })
}
</script>

<style scoped>
.batch-operation {
  max-height: 600px;
  overflow-y: auto;
}

.selected-accounts {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.selected-accounts h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.account-list {
  max-height: 150px;
  overflow-y: auto;
}

.account-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
}

.account-item:last-child {
  border-bottom: none;
}

.account-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.account-name {
  font-weight: 600;
  color: #2c3e50;
}

.operation-content {
  margin-top: 20px;
}

.operation-content h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
