<template>
  <div class="ai-role-templates-page">
    <div class="page-header">
      <div class="header-left">
        <h1>AI角色模板</h1>
        <p>配置AI角色和提示词模板，用于不同场景的AI对话</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加模板
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <el-select
        v-model="categoryFilter"
        placeholder="选择分类"
        clearable
        style="width: 200px; margin-right: 10px;"
        @change="fetchTemplates"
      >
        <el-option
          v-for="category in categories"
          :key="category"
          :label="category"
          :value="category"
        />
      </el-select>
      
      <el-switch
        v-model="enabledFilter"
        active-text="仅显示启用"
        @change="fetchTemplates"
      />
    </div>

    <!-- 模板列表 -->
    <div class="templates-list">
      <el-table
        :data="templates"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column label="模板名称" width="200">
          <template #default="{ row }">
            <div class="template-name">
              <el-tag size="small" :type="getCategoryColor(row.category)">
                {{ row.category }}
              </el-tag>
              <span style="margin-left: 8px;">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="描述" prop="description" show-overflow-tooltip />
        
        <el-table-column label="标签" width="200">
          <template #default="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              size="small"
              style="margin-right: 4px;"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              @change="toggleTemplate(row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="previewTemplate(row)">
              预览
            </el-button>
            <el-button size="small" @click="testTemplate(row)">
              测试
            </el-button>
            <el-button size="small" @click="editTemplate(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteTemplate(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="dialogMode === 'create' ? '添加AI角色模板' : '编辑AI角色模板'"
      v-model="dialogVisible"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" />
        </el-form-item>
        
        <el-form-item label="分类" prop="category">
          <el-select
            v-model="form.category"
            placeholder="请选择或输入分类"
            filterable
            allow-create
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="2"
            placeholder="请输入模板描述"
          />
        </el-form-item>
        
        <el-form-item label="系统提示词" prop="system_prompt">
          <el-input
            v-model="form.system_prompt"
            type="textarea"
            :rows="4"
            placeholder="请输入系统提示词，定义AI的角色和行为"
          />
        </el-form-item>
        
        <el-form-item label="用户提示模板" prop="user_prompt_template">
          <el-input
            v-model="form.user_prompt_template"
            type="textarea"
            :rows="4"
            placeholder="请输入用户提示词模板，可使用 {变量名} 作为占位符"
          />
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="启用状态">
          <el-switch v-model="form.enabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ dialogMode === 'create' ? '创建' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      title="模板预览"
      v-model="previewVisible"
      width="600px"
    >
      <div v-if="previewTemplate">
        <h4>系统提示词：</h4>
        <div class="prompt-preview">{{ selectedTemplate?.system_prompt }}</div>
        
        <h4>用户提示模板：</h4>
        <div class="prompt-preview">{{ selectedTemplate?.user_prompt_template }}</div>
      </div>
    </el-dialog>

    <!-- 测试对话框 -->
    <el-dialog
      title="测试模板"
      v-model="testVisible"
      width="600px"
    >
      <el-form label-width="100px">
        <el-form-item label="测试输入">
          <el-input
            v-model="testInput"
            type="textarea"
            :rows="4"
            placeholder="请输入测试内容"
          />
        </el-form-item>
      </el-form>
      
      <div v-if="testResult" class="test-result">
        <h4>AI响应：</h4>
        <div class="response-content">{{ testResult }}</div>
      </div>
      
      <template #footer>
        <el-button @click="testVisible = false">关闭</el-button>
        <el-button type="primary" @click="runTest" :loading="testing">
          运行测试
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  getAIRoleTemplates,
  createAIRoleTemplate,
  updateAIRoleTemplate,
  deleteAIRoleTemplate,
  testAIRoleTemplate,
  getTemplateCategories,
  type AIRoleTemplate,
  type CreateAIRoleTemplateRequest
} from '@/api/system-config'

// 响应式数据
const loading = ref(false)
const templates = ref<AIRoleTemplate[]>([])
const categories = ref<string[]>([])
const dialogVisible = ref(false)
const previewVisible = ref(false)
const testVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const submitting = ref(false)
const testing = ref(false)
const formRef = ref()

// 筛选器
const categoryFilter = ref('')
const enabledFilter = ref(false)

// 表单数据
const form = reactive<CreateAIRoleTemplateRequest>({
  name: '',
  description: '',
  system_prompt: '',
  user_prompt_template: '',
  category: '',
  tags: [],
  enabled: true
})

const currentEditId = ref('')
const selectedTemplate = ref<AIRoleTemplate | null>(null)
const testInput = ref('')
const testResult = ref('')

// 常用标签
const commonTags = ref(['内容创作', '客服', '翻译', '总结', '分析', '教育', '娱乐'])

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  system_prompt: [
    { required: true, message: '请输入系统提示词', trigger: 'blur' }
  ],
  user_prompt_template: [
    { required: true, message: '请输入用户提示模板', trigger: 'blur' }
  ]
}

// 获取模板列表
const fetchTemplates = async () => {
  try {
    loading.value = true
    const params = {
      category: categoryFilter.value || undefined,
      enabled: enabledFilter.value || undefined
    }
    const response = await getAIRoleTemplates(params)
    if (response.data) {
      templates.value = response.data.items
    }
  } catch (error) {
    ElMessage.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getTemplateCategories()
    if (response.data) {
      categories.value = response.data
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 显示创建对话框
const showCreateDialog = () => {
  dialogMode.value = 'create'
  dialogVisible.value = true
  resetForm()
}

// 编辑模板
const editTemplate = (template: AIRoleTemplate) => {
  dialogMode.value = 'edit'
  currentEditId.value = template.id
  
  // 填充表单数据
  Object.assign(form, {
    name: template.name,
    description: template.description,
    system_prompt: template.system_prompt,
    user_prompt_template: template.user_prompt_template,
    category: template.category,
    tags: [...template.tags],
    enabled: template.enabled
  })
  
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (dialogMode.value === 'create') {
      await createAIRoleTemplate(form)
      ElMessage.success('创建成功')
    } else {
      await updateAIRoleTemplate(currentEditId.value, form)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    fetchTemplates()
    fetchCategories() // 刷新分类列表
  } catch (error) {
    ElMessage.error(dialogMode.value === 'create' ? '创建失败' : '更新失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    system_prompt: '',
    user_prompt_template: '',
    category: '',
    tags: [],
    enabled: true
  })
  currentEditId.value = ''
  formRef.value?.clearValidate()
}

// 切换模板状态
const toggleTemplate = async (template: AIRoleTemplate) => {
  try {
    await updateAIRoleTemplate(template.id, { enabled: template.enabled })
    ElMessage.success(template.enabled ? '已启用' : '已禁用')
  } catch (error) {
    template.enabled = !template.enabled // 回滚状态
    ElMessage.error('状态更新失败')
  }
}

// 预览模板
const previewTemplate = (template: AIRoleTemplate) => {
  selectedTemplate.value = template
  previewVisible.value = true
}

// 测试模板
const testTemplate = (template: AIRoleTemplate) => {
  selectedTemplate.value = template
  testInput.value = ''
  testResult.value = ''
  testVisible.value = true
}

// 运行测试
const runTest = async () => {
  if (!selectedTemplate.value || !testInput.value.trim()) {
    ElMessage.warning('请输入测试内容')
    return
  }
  
  try {
    testing.value = true
    const response = await testAIRoleTemplate(selectedTemplate.value.id, testInput.value)
    if (response.data) {
      testResult.value = response.data.response
    }
  } catch (error) {
    ElMessage.error('测试失败')
  } finally {
    testing.value = false
  }
}

// 删除模板
const deleteTemplate = async (template: AIRoleTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    await deleteAIRoleTemplate(template.id)
    ElMessage.success('删除成功')
    fetchTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 工具函数
const getCategoryColor = (category: string) => {
  const colors = ['primary', 'success', 'warning', 'danger', 'info']
  const index = category.length % colors.length
  return colors[index]
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 初始化
onMounted(() => {
  fetchTemplates()
  fetchCategories()
})
</script>

<style scoped>
.ai-role-templates-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.filters {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.templates-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.template-name {
  display: flex;
  align-items: center;
}

.prompt-preview {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  white-space: pre-wrap;
  font-family: monospace;
}

.test-result {
  margin-top: 20px;
}

.response-content {
  background: #f0f9ff;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  white-space: pre-wrap;
}
</style>
