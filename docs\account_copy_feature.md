# 账号复制功能文档

## 功能概述

账号复制功能允许用户快速复制现有的社交媒体账号，创建一个具有相同配置但不同用户名的新账号。这个功能在需要创建相似账号或备份账号时非常有用。

## 功能特性

### 1. 后端API

**接口地址**: `POST /api/v1/social/accounts/{account_id}/copy`

**功能**: 复制指定ID的社交媒体账号

**参数**:
- `account_id` (路径参数): 要复制的账号ID

**返回值**: 复制后的新账号信息

### 2. 复制逻辑

复制账号时会执行以下操作：

1. **验证原账号**: 检查要复制的账号是否存在
2. **数据准备**: 复制原账号的所有数据
3. **字段清理**: 移除不应复制的字段：
   - `_id`: MongoDB内部ID
   - `id`: 账号唯一标识符
   - `created_at`: 创建时间
   - `updated_at`: 更新时间
4. **用户名修改**: 生成唯一的新用户名
   - 格式: `{原用户名}_copy_{时间戳}`
   - 例如: `john_doe_copy_1642567890`
5. **显示名称修改**: 
   - 如果原账号有显示名称: `{原显示名称} (副本)`
   - 如果没有显示名称: `{原用户名} (副本)`
6. **时间戳更新**: 设置新的创建和更新时间
7. **创建新账号**: 将处理后的数据保存为新账号

### 3. 前端界面

在账号管理页面的操作列中添加了"复制"按钮：

- **位置**: 编辑按钮和删除按钮之间
- **样式**: 绿色按钮，表示安全操作
- **确认**: 点击后会弹出确认对话框
- **反馈**: 操作成功后显示成功消息并刷新列表

## 使用方法

### 1. 通过前端界面

1. 打开账号管理页面
2. 在账号列表中找到要复制的账号
3. 点击该账号行的"复制"按钮
4. 在确认对话框中点击"确定"
5. 等待操作完成，新账号将出现在列表中

### 2. 通过API调用

```javascript
// 使用前端API方法
import { copyAccount } from '@/api/social'

try {
  const copiedAccount = await copyAccount('account_id_here')
  console.log('复制成功:', copiedAccount)
} catch (error) {
  console.error('复制失败:', error)
}
```

```bash
# 使用curl命令
curl -X POST "http://localhost:8000/api/v1/social/accounts/{account_id}/copy" \
     -H "Content-Type: application/json"
```

## 错误处理

### 常见错误

1. **404 - 原账号不存在**
   - 原因: 指定的账号ID不存在
   - 解决: 检查账号ID是否正确

2. **500 - 创建账号失败**
   - 原因: 数据库操作失败或数据验证失败
   - 解决: 检查数据库连接和数据完整性

### 前端错误处理

- 显示用户友好的错误消息
- 记录详细错误信息到控制台
- 不会因为取消操作而显示错误

## 技术实现

### 后端实现

- **框架**: FastAPI
- **数据库**: MongoDB
- **验证**: Pydantic模型验证
- **日志**: 详细的操作日志记录

### 前端实现

- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **HTTP客户端**: Axios
- **状态管理**: 组合式API

## 注意事项

1. **用户名唯一性**: 系统会自动生成唯一的用户名，避免冲突
2. **数据完整性**: 复制的账号包含原账号的所有配置信息
3. **权限控制**: 确保用户有权限复制指定的账号
4. **性能考虑**: 复制操作是异步的，不会阻塞其他操作

## 未来改进

1. **批量复制**: 支持同时复制多个账号
2. **自定义复制**: 允许用户选择要复制的字段
3. **复制历史**: 记录账号的复制关系和历史
4. **模板功能**: 将常用账号设置为模板，方便快速创建
