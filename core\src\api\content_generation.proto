syntax = "proto3";

package content_generation;

// 内容生成服务
service ContentGenerationService {
    // 创建内容生成任务
    rpc CreateGenerationTask(CreateGenerationTaskRequest) returns (CreateGenerationTaskResponse);
    
    // 启动内容生成任务
    rpc StartGenerationTask(StartGenerationTaskRequest) returns (StartGenerationTaskResponse);
    
    // 获取任务状态
    rpc GetGenerationTaskStatus(GetGenerationTaskStatusRequest) returns (GetGenerationTaskStatusResponse);
    
    // 取消任务
    rpc CancelGenerationTask(CancelGenerationTaskRequest) returns (CancelGenerationTaskResponse);
    
    // 获取任务结果
    rpc GetGenerationResults(GetGenerationResultsRequest) returns (GetGenerationResultsResponse);
    
    // 下载生成的文件
    rpc DownloadGeneratedFile(DownloadGeneratedFileRequest) returns (DownloadGeneratedFileResponse);
    
    // 测试ComfyUI连接
    rpc TestComfyUIConnection(TestComfyUIConnectionRequest) returns (TestComfyUIConnectionResponse);
    
    // 获取可用模型
    rpc GetAvailableModels(GetAvailableModelsRequest) returns (GetAvailableModelsResponse);
}

// 生成配置
message GenerationConfig {
    string comfyui_workflow = 1;
    map<string, string> model_settings = 2;
    map<string, string> output_settings = 3;
    int32 duration = 4;
    string resolution = 5;
    int32 fps = 6;
    string quality = 7;
}

// 创建任务请求
message CreateGenerationTaskRequest {
    string account_id = 1;          // 账号ID
    string platform_id = 2;         // 平台ID
    string title = 3;               // 任务标题
    string type = 4;                // 生成类型: text, image, video, full_pipeline
    string description = 5;         // 内容描述
    string style = 6;               // 生成风格
    int32 count = 7;                // 生成数量
    GenerationConfig config = 8;    // 生成配置
}

// 创建任务响应
message CreateGenerationTaskResponse {
    bool success = 1;
    string task_id = 2;
    string error = 3;
}

// 启动任务请求
message StartGenerationTaskRequest {
    string task_id = 1;
    string account_id = 2;
}

// 启动任务响应
message StartGenerationTaskResponse {
    bool success = 1;
    string error = 2;
}

// 获取任务状态请求
message GetGenerationTaskStatusRequest {
    string task_id = 1;
    string account_id = 2;
}

// 任务状态
message GenerationTaskStatus {
    string task_id = 1;
    string status = 2;              // pending, processing, completed, failed, canceled
    float progress = 3;             // 0.0 - 1.0
    string current_step = 4;
    string error_message = 5;
    string created_at = 6;
    string updated_at = 7;
    string completed_at = 8;
}

// 获取任务状态响应
message GetGenerationTaskStatusResponse {
    bool success = 1;
    GenerationTaskStatus status = 2;
    string error = 3;
}

// 取消任务请求
message CancelGenerationTaskRequest {
    string task_id = 1;
    string account_id = 2;
}

// 取消任务响应
message CancelGenerationTaskResponse {
    bool success = 1;
    string error = 2;
}

// 生成结果
message GenerationResult {
    string id = 1;
    string type = 2;
    string title = 3;
    string file_path = 4;
    int64 file_size = 5;
    string preview_url = 6;
    string download_url = 7;
    string thumbnail_path = 8;
    map<string, string> metadata = 9;
}

// 获取结果请求
message GetGenerationResultsRequest {
    string task_id = 1;
    string account_id = 2;
}

// 获取结果响应
message GetGenerationResultsResponse {
    bool success = 1;
    repeated GenerationResult results = 2;
    string error = 3;
}

// 下载文件请求
message DownloadGeneratedFileRequest {
    string result_id = 1;
    string account_id = 2;
}

// 下载文件响应
message DownloadGeneratedFileResponse {
    bool success = 1;
    bytes file_data = 2;
    string filename = 3;
    string content_type = 4;
    string error = 5;
}

// 测试ComfyUI连接请求
message TestComfyUIConnectionRequest {
    // 空请求
}

// 测试ComfyUI连接响应
message TestComfyUIConnectionResponse {
    bool success = 1;
    string status = 2;              // connected, disconnected
    string version = 3;
    int32 queue_size = 4;
    int32 active_tasks = 5;
    string error = 6;
}

// 模型信息
message ModelInfo {
    string id = 1;
    string name = 2;
    string description = 3;
    string type = 4;
}

// 获取可用模型请求
message GetAvailableModelsRequest {
    // 空请求
}

// 获取可用模型响应
message GetAvailableModelsResponse {
    bool success = 1;
    repeated ModelInfo text_models = 2;
    repeated ModelInfo image_models = 3;
    repeated ModelInfo video_models = 4;
    string error = 5;
}
