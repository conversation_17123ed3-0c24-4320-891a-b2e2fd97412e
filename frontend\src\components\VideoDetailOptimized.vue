<template>
  <div class="video-detail-optimized">
    <!-- 基础信息加载 -->
    <div v-loading="loadingBasic" class="basic-info-section">
      <div v-if="videoInfo" class="video-header">
        <div class="video-title">
          <h2>{{ videoInfo.title }}</h2>
          <div class="video-meta">
            <el-tag :type="getPlatformTagType(videoInfo.platform)">
              {{ getPlatformIcon(videoInfo.platform) }} {{ videoInfo.platform }}
            </el-tag>
            <span class="author">{{ videoInfo.author?.name }}</span>
            <span class="date">{{ formatDate(videoInfo.created_at) }}</span>
          </div>
        </div>
        
        <div class="video-actions">
          <el-button @click="loadMediaInfo" :loading="loadingMedia">
            <el-icon><Monitor /></el-icon>
            媒体信息
          </el-button>
          <el-button @click="loadAnalysis" :loading="loadingAnalysis">
            <el-icon><DataAnalysis /></el-icon>
            分析数据
          </el-button>
          <el-button @click="refreshAll" :loading="refreshing">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <!-- 基础信息展示 -->
      <div v-if="videoInfo" class="basic-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件路径">
            {{ videoInfo.file_info?.local_path }}
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(videoInfo.file_info?.file_size || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="文件格式">
            {{ videoInfo.file_info?.file_format }}
          </el-descriptions-item>
          <el-descriptions-item label="时长">
            {{ formatDuration(videoInfo.file_info?.duration || 0) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    
    <!-- 媒体信息（按需加载） -->
    <div v-if="showMediaInfo" class="media-info-section">
      <el-card header="媒体信息">
        <div v-loading="loadingMedia">
          <div v-if="mediaInfo && Object.keys(mediaInfo).length > 0">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="分辨率">
                {{ mediaInfo.resolution || '未知' }}
              </el-descriptions-item>
              <el-descriptions-item label="视频编码">
                {{ mediaInfo.video_codec || '未知' }}
              </el-descriptions-item>
              <el-descriptions-item label="音频编码">
                {{ mediaInfo.audio_codec || '无音频' }}
              </el-descriptions-item>
              <el-descriptions-item label="帧率">
                {{ mediaInfo.frame_rate?.toFixed(2) || '未知' }} fps
              </el-descriptions-item>
              <el-descriptions-item label="比特率">
                {{ formatBitrate(mediaInfo.bitrate || 0) }}
              </el-descriptions-item>
              <el-descriptions-item label="缓存状态">
                <el-tag :type="mediaFromCache ? 'success' : 'warning'">
                  {{ mediaFromCache ? '已缓存' : '实时获取' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div v-else-if="!loadingMedia">
            <el-empty description="暂无媒体信息" />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 分析信息（按需加载） -->
    <div v-if="showAnalysis" class="analysis-section">
      <el-card header="分析数据">
        <div v-loading="loadingAnalysis">
          <div v-if="analysisInfo && Object.keys(analysisInfo).length > 0">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="情感分析">
                {{ analysisInfo.sentiment || '未分析' }}
              </el-descriptions-item>
              <el-descriptions-item label="关键词">
                <div v-if="analysisInfo.keywords && analysisInfo.keywords.length > 0">
                  <el-tag 
                    v-for="keyword in analysisInfo.keywords" 
                    :key="keyword"
                    class="keyword-tag"
                  >
                    {{ keyword }}
                  </el-tag>
                </div>
                <span v-else>暂无关键词</span>
              </el-descriptions-item>
              <el-descriptions-item label="主题">
                <div v-if="analysisInfo.topics && analysisInfo.topics.length > 0">
                  <el-tag 
                    v-for="topic in analysisInfo.topics" 
                    :key="topic"
                    type="success"
                    class="topic-tag"
                  >
                    {{ topic }}
                  </el-tag>
                </div>
                <span v-else>暂无主题</span>
              </el-descriptions-item>
              <el-descriptions-item label="互动率">
                {{ (analysisInfo.engagement_rate * 100).toFixed(2) }}%
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div v-else-if="!loadingAnalysis">
            <el-empty description="暂无分析数据" />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 元数据信息 -->
    <div v-if="videoInfo?.metadata" class="metadata-section">
      <el-card header="元数据">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="观看数">
            {{ formatNumber(videoInfo.metadata.view_count || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="点赞数">
            {{ formatNumber(videoInfo.metadata.like_count || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="评论数">
            {{ formatNumber(videoInfo.metadata.comment_count || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="发布时间">
            {{ formatDate(videoInfo.metadata.publish_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="语言">
            {{ videoInfo.metadata.language || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="分类">
            {{ videoInfo.metadata.category || '未分类' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 标签 -->
        <div v-if="videoInfo.metadata.tags && videoInfo.metadata.tags.length > 0" class="tags-section">
          <h4>标签</h4>
          <el-tag 
            v-for="tag in videoInfo.metadata.tags" 
            :key="tag"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor, DataAnalysis, Refresh } from '@element-plus/icons-vue'
import {
  getVideoDetail,
  getVideoMediaInfo,
  getVideoAnalysis,
  formatDuration,
  formatFileSize,
  formatBitrate,
  formatNumber,
  getPlatformIcon,
  type VideoInfo
} from '@/api/video-details'

interface Props {
  videoId: string
  autoLoadMedia?: boolean
  autoLoadAnalysis?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoadMedia: false,
  autoLoadAnalysis: false
})

// 响应式数据
const loadingBasic = ref(false)
const loadingMedia = ref(false)
const loadingAnalysis = ref(false)
const refreshing = ref(false)

const videoInfo = ref<VideoInfo | null>(null)
const mediaInfo = ref<any>(null)
const analysisInfo = ref<any>(null)

const showMediaInfo = ref(false)
const showAnalysis = ref(false)
const mediaFromCache = ref(false)

// 计算属性
const getPlatformTagType = (platform: string) => {
  const types: Record<string, string> = {
    youtube: 'danger',
    tiktok: 'warning',
    instagram: 'success',
    twitter: 'info',
    bilibili: 'primary'
  }
  return types[platform?.toLowerCase()] || 'default'
}

// 方法
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const loadBasicInfo = async () => {
  if (!props.videoId) return
  
  loadingBasic.value = true
  try {
    const response = await getVideoDetail(props.videoId, {
      include_media_info: props.autoLoadMedia,
      include_analysis: props.autoLoadAnalysis
    })
    
    if (response.success) {
      videoInfo.value = response.video_info
      
      // 如果自动加载了媒体信息
      if (props.autoLoadMedia && response.media_info) {
        mediaInfo.value = response.media_info
        showMediaInfo.value = true
        mediaFromCache.value = response.from_cache
      }
      
      // 如果自动加载了分析信息
      if (props.autoLoadAnalysis && response.analysis_info) {
        analysisInfo.value = response.analysis_info
        showAnalysis.value = true
      }
    } else {
      ElMessage.error('获取视频信息失败')
    }
  } catch (error) {
    console.error('加载视频基础信息失败:', error)
    ElMessage.error('加载视频信息失败')
  } finally {
    loadingBasic.value = false
  }
}

const loadMediaInfo = async (forceRefresh = false) => {
  if (!props.videoId) return
  
  loadingMedia.value = true
  showMediaInfo.value = true
  
  try {
    const response = await getVideoMediaInfo(props.videoId, forceRefresh)
    
    if (response.success) {
      mediaInfo.value = response.media_info
      mediaFromCache.value = response.from_cache
      
      if (response.from_cache) {
        ElMessage.success('媒体信息已从缓存加载')
      } else {
        ElMessage.success('媒体信息获取成功')
      }
    } else {
      ElMessage.error(response.error || '获取媒体信息失败')
    }
  } catch (error) {
    console.error('加载媒体信息失败:', error)
    ElMessage.error('加载媒体信息失败')
  } finally {
    loadingMedia.value = false
  }
}

const loadAnalysis = async () => {
  if (!props.videoId) return
  
  loadingAnalysis.value = true
  showAnalysis.value = true
  
  try {
    const response = await getVideoAnalysis(props.videoId)
    
    if (response.success) {
      analysisInfo.value = response.analysis
      ElMessage.success('分析数据加载成功')
    } else {
      ElMessage.error(response.error || '获取分析数据失败')
    }
  } catch (error) {
    console.error('加载分析数据失败:', error)
    ElMessage.error('加载分析数据失败')
  } finally {
    loadingAnalysis.value = false
  }
}

const refreshAll = async () => {
  refreshing.value = true
  
  try {
    // 重新加载基础信息
    await loadBasicInfo()
    
    // 如果已经显示媒体信息，强制刷新
    if (showMediaInfo.value) {
      await loadMediaInfo(true)
    }
    
    // 如果已经显示分析信息，重新加载
    if (showAnalysis.value) {
      await loadAnalysis()
    }
    
    ElMessage.success('刷新完成')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 生命周期
onMounted(() => {
  loadBasicInfo()
})
</script>

<style scoped>
.video-detail-optimized {
  padding: 20px;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.video-title h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.video-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #606266;
  font-size: 14px;
}

.video-actions {
  display: flex;
  gap: 8px;
}

.basic-info-section,
.media-info-section,
.analysis-section,
.metadata-section {
  margin-bottom: 20px;
}

.keyword-tag,
.topic-tag,
.tag-item {
  margin-right: 8px;
  margin-bottom: 4px;
}

.tags-section {
  margin-top: 16px;
}

.tags-section h4 {
  margin: 0 0 8px 0;
  color: #303133;
}
</style>
