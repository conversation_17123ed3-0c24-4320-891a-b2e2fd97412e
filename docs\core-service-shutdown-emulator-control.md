# Core服务关闭时模拟器控制修复

## 问题描述

用户反馈Core服务关闭时不需要关闭模拟器。原有逻辑在Core服务关闭时会自动停止所有模拟器设备，这可能导致以下问题：

1. **服务恢复慢**：重启Core服务时需要重新启动模拟器，增加启动时间
2. **资源浪费**：模拟器启动需要较长时间和系统资源
3. **任务中断**：可能中断其他服务正在使用的模拟器
4. **用户体验差**：频繁的模拟器启停影响系统稳定性

## 解决方案

### 1. 修改默认行为

**文件**: `core/src/devices/ldplayer/manager.py`

修改`shutdown()`方法，默认不停止模拟器设备：

```python
async def shutdown(self, stop_devices: bool = False) -> None:
    """关闭管理器
    
    Args:
        stop_devices: 是否停止模拟器设备，默认为False（保持运行）
    """
    logger.info("正在关闭雷电模拟器管理器...")

    if stop_devices:
        # 停止所有设备
        logger.info("正在停止所有模拟器设备...")
        try:
            await self.stop_all_devices()
            logger.info("所有设备已停止")
        except Exception as e:
            logger.error(f"停止所有设备异常: {str(e)}", exc_info=True)
    else:
        # 🔧 重要修改：Core服务关闭时不关闭模拟器
        # 模拟器可能被其他服务使用，或需要保持运行状态以便快速恢复
        logger.info("Core服务关闭时保持模拟器运行状态")

    # 清理内存中的设备引用
    device_count = len(self.devices)
    self.devices.clear()
    
    if stop_devices:
        logger.info(f"雷电模拟器管理器已关闭，已停止 {device_count} 个模拟器")
    else:
        logger.info(f"雷电模拟器管理器已关闭，保持 {device_count} 个模拟器运行")
```

### 2. 添加配置选项

**文件**: `core/src/config/settings.py`

添加配置选项控制关闭行为：

```python
# 🔧 新增：控制Core服务关闭时是否停止模拟器
self.stop_devices_on_shutdown = os.environ.get("STOP_DEVICES_ON_SHUTDOWN", "false").lower() == "true"
```

**环境变量配置**：
```bash
# 设置为true时，Core服务关闭时会停止模拟器
STOP_DEVICES_ON_SHUTDOWN=false  # 默认值，保持模拟器运行

# 设置为true时，Core服务关闭时会停止模拟器
STOP_DEVICES_ON_SHUTDOWN=true   # 停止模拟器
```

### 3. 主服务集成

**文件**: `core/src/main_service.py`

在主服务关闭时使用配置选项：

```python
if self.ldplayer_manager:
    try:
        # 🔧 重要修改：根据配置决定是否停止模拟器设备
        stop_devices = self.settings.stop_devices_on_shutdown if hasattr(self.settings, 'stop_devices_on_shutdown') else False
        if stop_devices:
            logger.info("配置为关闭时停止模拟器设备")
        else:
            logger.info("配置为关闭时保持模拟器设备运行")
        await self.ldplayer_manager.shutdown(stop_devices=stop_devices)
    except Exception as e:
        logger.error(f"关闭雷电模拟器管理器异常: {str(e)}", exc_info=True)
```

### 4. 新增API控制

**文件**: `core/src/api/device_service.py`

添加手动控制模拟器的API方法：

```python
async def StopAllDevices(self, request, context):
    """停止所有模拟器设备"""
    # 实现停止所有设备的逻辑

async def StartAllDevices(self, request, context):
    """启动所有模拟器设备"""
    # 实现启动所有设备的逻辑
```

## 使用方法

### 1. 默认行为（推荐）

默认情况下，Core服务关闭时不会停止模拟器：

```bash
# 启动Core服务
python main.py

# 关闭Core服务（模拟器保持运行）
Ctrl+C 或 发送SIGTERM信号
```

### 2. 配置强制停止模拟器

如果需要在Core服务关闭时停止模拟器，设置环境变量：

```bash
# Windows
set STOP_DEVICES_ON_SHUTDOWN=true
python main.py

# Linux/Mac
export STOP_DEVICES_ON_SHUTDOWN=true
python main.py
```

### 3. 手动控制模拟器

通过gRPC API手动控制模拟器：

```python
# 停止所有模拟器
await device_service.StopAllDevices(request, context)

# 启动所有模拟器
await device_service.StartAllDevices(request, context)
```

## 日志输出

### 保持模拟器运行（默认）
```
2025-07-22 10:00:00 - INFO - 正在关闭雷电模拟器管理器...
2025-07-22 10:00:00 - INFO - 配置为关闭时保持模拟器设备运行
2025-07-22 10:00:00 - INFO - Core服务关闭时保持模拟器运行状态
2025-07-22 10:00:00 - INFO - 雷电模拟器管理器已关闭，保持 3 个模拟器运行
```

### 停止模拟器
```
2025-07-22 10:00:00 - INFO - 正在关闭雷电模拟器管理器...
2025-07-22 10:00:00 - INFO - 配置为关闭时停止模拟器设备
2025-07-22 10:00:00 - INFO - 正在停止所有模拟器设备...
2025-07-22 10:00:05 - INFO - 所有设备已停止
2025-07-22 10:00:05 - INFO - 雷电模拟器管理器已关闭，已停止 3 个模拟器
```

## 优势

1. **快速恢复**：Core服务重启时模拟器已经运行，无需等待启动
2. **资源节约**：避免频繁的模拟器启停，节省系统资源
3. **服务连续性**：其他服务可以继续使用模拟器
4. **灵活配置**：可根据需要选择不同的关闭策略
5. **手动控制**：提供API接口手动控制模拟器状态

## 注意事项

1. **内存清理**：虽然不停止模拟器，但会清理Core服务中的设备引用
2. **状态同步**：Core服务重启后会重新发现和同步模拟器状态
3. **资源监控**：长期运行的模拟器需要监控资源使用情况
4. **异常处理**：模拟器异常时仍需要重启机制

## 兼容性

- ✅ 向后兼容：默认行为改为不停止模拟器
- ✅ 配置灵活：可通过环境变量控制行为
- ✅ API扩展：新增手动控制接口
- ✅ 日志完整：提供详细的操作日志
