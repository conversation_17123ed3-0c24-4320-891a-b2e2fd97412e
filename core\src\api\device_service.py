"""
设备服务gRPC实现
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional

from src.main_service import CoreMainService
from src.devices.base import DeviceStatus, DeviceInfo
from src.api import device_pb2
from src.api import device_pb2_grpc

logger = logging.getLogger(__name__)

class DeviceServiceImpl(device_pb2_grpc.DeviceServiceServicer):
    """设备服务gRPC实现类"""
    
    def __init__(self, main_service: CoreMainService):
        """初始化设备服务
        
        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        logger.info("设备服务gRPC实现初始化")
    
    async def GetDeviceList(self, request, context):
        """获取设备列表"""
        try:
            logger.info("收到获取设备列表请求")
            
            device_list = await self.main_service.get_device_list()
            
            response = device_pb2.DeviceListResponse()
            for device_info in device_list:
                device = response.devices.add()
                device.device_id = device_info.device_id
                device.name = device_info.name
                device.status = device_info.status.value
                device.device_type = device_info.device_type
                
                # 添加窗口信息
                if device_info.window_info:
                    for key, value in device_info.window_info.items():
                        device.window_info[key] = str(value)
                
                # 添加进程信息
                if device_info.process_info:
                    for key, value in device_info.process_info.items():
                        device.process_info[key] = str(value)
                
                # 添加显示信息
                if device_info.display_info:
                    for key, value in device_info.display_info.items():
                        device.display_info[key] = str(value)
            
            logger.info(f"返回设备列表，共{len(device_list)}个设备")
            return response
            
        except Exception as e:
            logger.error(f"获取设备列表异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取设备列表失败: {str(e)}")
            return device_pb2.DeviceListResponse()
    
    async def StartDevice(self, request, context):
        """启动设备"""
        try:
            device_id = request.device_id
            logger.info(f"收到启动设备请求: {device_id}")
            
            success = await self.main_service.start_device(device_id)
            
            response = device_pb2.DeviceResponse()
            response.success = success
            if not success:
                response.error = f"启动设备{device_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"启动设备异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"启动设备失败: {str(e)}")
            return device_pb2.DeviceResponse(success=False, error=str(e))
    
    async def StopDevice(self, request, context):
        """停止设备"""
        try:
            device_id = request.device_id
            logger.info(f"收到停止设备请求: {device_id}")
            
            success = await self.main_service.stop_device(device_id)
            
            response = device_pb2.DeviceResponse()
            response.success = success
            if not success:
                response.error = f"停止设备{device_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"停止设备异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"停止设备失败: {str(e)}")
            return device_pb2.DeviceResponse(success=False, error=str(e))
    
    async def RestartDevice(self, request, context):
        """重启设备"""
        try:
            device_id = request.device_id
            logger.info(f"收到重启设备请求: {device_id}")
            
            success = await self.main_service.restart_device(device_id)
            
            response = device_pb2.DeviceResponse()
            response.success = success
            if not success:
                response.error = f"重启设备{device_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"重启设备异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"重启设备失败: {str(e)}")
            return device_pb2.DeviceResponse(success=False, error=str(e))
    
    async def GetDeviceInfo(self, request, context):
        """获取设备信息"""
        try:
            device_id = request.device_id
            logger.info(f"收到获取设备信息请求: {device_id}")
            
            device_info = await self.main_service.get_device_info(device_id)
            
            if not device_info:
                context.set_code(5)  # NOT_FOUND
                context.set_details(f"设备{device_id}不存在")
                return device_pb2.DeviceInfoResponse()
            
            response = device_pb2.DeviceInfoResponse()
            response.device.device_id = device_info.device_id
            response.device.name = device_info.name
            response.device.status = device_info.status.value
            response.device.device_type = device_info.device_type
            
            # 添加窗口信息
            if device_info.window_info:
                for key, value in device_info.window_info.items():
                    response.device.window_info[key] = str(value)
            
            # 添加进程信息
            if device_info.process_info:
                for key, value in device_info.process_info.items():
                    response.device.process_info[key] = str(value)
            
            # 添加显示信息
            if device_info.display_info:
                for key, value in device_info.display_info.items():
                    response.device.display_info[key] = str(value)
            
            return response
            
        except Exception as e:
            logger.error(f"获取设备信息异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取设备信息失败: {str(e)}")
            return device_pb2.DeviceInfoResponse()
    
    async def CreateDevice(self, request, context):
        """创建新设备"""
        try:
            logger.info(f"收到创建设备请求: {request.name}")
            
            # 构建设备配置
            config = {
                'name': request.name
            }
            
            # 添加可选配置
            if request.resolution:
                config['resolution'] = request.resolution
            
            if request.cpu_count > 0:
                config['cpu_count'] = request.cpu_count
            
            if request.memory_size > 0:
                config['memory_size'] = request.memory_size
            
            if request.manufacturer:
                config['manufacturer'] = request.manufacturer
            
            if request.model:
                config['model'] = request.model
            
            device_id = await self.main_service.create_device(config)
            
            response = device_pb2.CreateDeviceResponse()
            if device_id:
                response.success = True
                response.device_id = device_id
            else:
                response.success = False
                response.error = "创建设备失败"
            
            return response
            
        except Exception as e:
            logger.error(f"创建设备异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"创建设备失败: {str(e)}")
            return device_pb2.CreateDeviceResponse(success=False, error=str(e))
    
    async def RemoveDevice(self, request, context):
        """删除设备"""
        try:
            device_id = request.device_id
            logger.info(f"收到删除设备请求: {device_id}")
            
            success = await self.main_service.remove_device(device_id)
            
            response = device_pb2.DeviceResponse()
            response.success = success
            if not success:
                response.error = f"删除设备{device_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"删除设备异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"删除设备失败: {str(e)}")
            return device_pb2.DeviceResponse(success=False, error=str(e))
    
    async def ExecuteCommand(self, request, context):
        """执行设备命令"""
        try:
            device_id = request.device_id
            command = request.command
            
            # 解析参数
            params = {}
            for key, value in request.params.items():
                params[key] = value
            
            logger.info(f"收到执行命令请求: 设备={device_id}, 命令={command}, 参数={params}")
            
            result = await self.main_service.execute_device_command(device_id, command, params)
            
            response = device_pb2.CommandResponse()
            response.success = result.get('success', False)
            
            if not response.success:
                response.error = result.get('error', '未知错误')
            else:
                response.output = result.get('output', '')
            
            return response
            
        except Exception as e:
            logger.error(f"执行命令异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"执行命令失败: {str(e)}")
            return device_pb2.CommandResponse(success=False, error=str(e))

    async def StopAllDevices(self, request, context):
        """停止所有模拟器设备"""
        try:
            logger.info("收到停止所有设备请求")

            # 获取雷电模拟器管理器
            if not self.main_service.ldplayer_manager:
                response = device_pb2.DeviceOperationResponse()
                response.success = False
                response.message = "雷电模拟器管理器未初始化"
                return response

            # 停止所有设备
            result = await self.main_service.ldplayer_manager.stop_all_devices()

            # 统计结果
            success_count = sum(1 for success in result.values() if success)
            total_count = len(result)

            response = device_pb2.DeviceOperationResponse()
            response.success = success_count > 0
            response.message = f"停止设备完成，成功: {success_count}/{total_count}"

            logger.info(f"停止所有设备完成，成功: {success_count}/{total_count}")
            return response

        except Exception as e:
            logger.error(f"停止所有设备异常: {str(e)}", exc_info=True)
            response = device_pb2.DeviceOperationResponse()
            response.success = False
            response.message = f"停止所有设备失败: {str(e)}"
            return response

    async def StartAllDevices(self, request, context):
        """启动所有模拟器设备"""
        try:
            logger.info("收到启动所有设备请求")

            # 获取雷电模拟器管理器
            if not self.main_service.ldplayer_manager:
                response = device_pb2.DeviceOperationResponse()
                response.success = False
                response.message = "雷电模拟器管理器未初始化"
                return response

            # 启动所有设备
            result = await self.main_service.ldplayer_manager.start_all_devices()

            # 统计结果
            success_count = sum(1 for success in result.values() if success)
            total_count = len(result)

            response = device_pb2.DeviceOperationResponse()
            response.success = success_count > 0
            response.message = f"启动设备完成，成功: {success_count}/{total_count}"

            logger.info(f"启动所有设备完成，成功: {success_count}/{total_count}")
            return response

        except Exception as e:
            logger.error(f"启动所有设备异常: {str(e)}", exc_info=True)
            response = device_pb2.DeviceOperationResponse()
            response.success = False
            response.message = f"启动所有设备失败: {str(e)}"
            return response
