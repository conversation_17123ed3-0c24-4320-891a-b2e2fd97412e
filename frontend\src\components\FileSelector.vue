<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="file-selector">
      <!-- 路径导航 -->
      <div class="path-navigation">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item 
            v-for="(segment, index) in pathSegments" 
            :key="index"
            @click="navigateToPath(index)"
            class="breadcrumb-item"
          >
            {{ segment }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 文件列表 -->
      <div class="file-list-container">
        <el-table
          :data="filteredFiles"
          v-loading="loading"
          @row-dblclick="handleRowDoubleClick"
          @row-click="handleRowClick"
          highlight-current-row
          height="400px"
        >
          <el-table-column width="40">
            <template #default="{ row }">
              <el-icon v-if="row.is_directory" color="#409EFF">
                <Folder />
              </el-icon>
              <el-icon v-else-if="isVideoFile(row)" color="#67C23A">
                <VideoPlay />
              </el-icon>
              <el-icon v-else color="#909399">
                <Document />
              </el-icon>
            </template>
          </el-table-column>
          
          <el-table-column prop="name" label="名称" min-width="200">
            <template #default="{ row }">
              <span :class="{ 'directory-name': row.is_directory }">
                {{ row.name }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="size" label="大小" width="100" align="right">
            <template #default="{ row }">
              <span v-if="!row.is_directory">{{ formatFileSize(row.size) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="last_modified" label="修改时间" width="150">
            <template #default="{ row }">
              {{ formatTime(row.last_modified) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 文件过滤 -->
      <div class="filter-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索文件..."
          style="width: 200px"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-checkbox v-model="showOnlyVideos" style="margin-left: 10px">
          只显示视频文件
        </el-checkbox>
      </div>

      <!-- 选中的文件 -->
      <div class="selected-file" v-if="selectedFile">
        <el-alert
          :title="`已选择: ${selectedFile.name}`"
          type="success"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="navigateUp" :disabled="currentPath === '/'">
          <el-icon><ArrowUp /></el-icon>
          上级目录
        </el-button>
        <el-button 
          type="primary" 
          @click="confirmSelection"
          :disabled="!selectedFile || selectedFile.is_directory"
        >
          确定选择
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Folder, 
  VideoPlay, 
  Document, 
  Search, 
  ArrowUp 
} from '@element-plus/icons-vue'
import { listFolderContents, type FolderListResponse, type FileInfo } from '@/api/social'

interface Props {
  title?: string
  initialPath?: string
  fileFilter?: 'video' | 'all'
}

const props = withDefaults(defineProps<Props>(), {
  title: '选择文件',
  initialPath: 'H:\\PublishSystem',
  fileFilter: 'all'
})

const emit = defineEmits<{
  confirm: [file: FileInfo]
  cancel: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const currentPath = ref(props.initialPath)
const fileList = ref<FileInfo[]>([])
const selectedFile = ref<FileInfo | null>(null)
const searchQuery = ref('')
const showOnlyVideos = ref(props.fileFilter === 'video')

// 计算属性
const pathSegments = computed(() => {
  // 根据路径格式选择正确的分隔符
  const separator = currentPath.value.includes('\\') ? '\\' : '/'
  return currentPath.value.split(separator).filter(segment => segment !== '')
})

const filteredFiles = computed(() => {
  let files = fileList.value

  // 搜索过滤
  if (searchQuery.value) {
    files = files.filter(file =>
      file.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 视频文件过滤
  if (showOnlyVideos.value) {
    const beforeFilter = files.length
    files = files.filter(file => file.is_directory || isVideoFile(file))
    console.log(`FileSelector: 视频过滤 - 过滤前: ${beforeFilter}, 过滤后: ${files.length}, showOnlyVideos: ${showOnlyVideos.value}`)
  }

  // 排序：目录在前，文件在后
  return files.sort((a, b) => {
    if (a.is_directory && !b.is_directory) return -1
    if (!a.is_directory && b.is_directory) return 1
    return a.name.localeCompare(b.name)
  })
})

// 方法
const isVideoFile = (file: FileInfo): boolean => {
  if (file.is_directory) return false

  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'asf', 'rm', 'rmvb']

  // 优先使用 extension 字段，如果没有则从文件名提取
  let extension = file.extension
  if (!extension) {
    const dotIndex = file.name.lastIndexOf('.')
    if (dotIndex > 0) {
      extension = file.name.substring(dotIndex + 1)
    }
  }

  if (!extension) return false

  const isVideo = videoExtensions.includes(extension.toLowerCase())

  // 添加调试信息
  if (isVideo) {
    console.log(`FileSelector: 识别到视频文件: ${file.name} (扩展名: ${extension})`)
  }

  return isVideo
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (dateString?: string): string => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString()
}

const loadFileList = async (path: string) => {
  loading.value = true
  console.log(`FileSelector: 开始加载文件列表 - 路径: ${path}`)

  try {
    const response = await listFolderContents(
      path,
      undefined, // filter_extensions
      false,     // include_md5
      false,     // include_media_info
      undefined  // core_service_id
    )

    console.log(`FileSelector: API 响应:`, response)
    console.log(`FileSelector: 文件数量: ${response.data?.files?.length || 0}`)

    fileList.value = response.data?.files || []
    currentPath.value = path
    selectedFile.value = null

    // 输出文件列表详情
    if (response.data?.files && response.data.files.length > 0) {
      console.log(`FileSelector: 文件列表:`, response.data.files.map(f => ({
        name: f.name,
        is_directory: f.is_directory,
        extension: f.extension
      })))
    } else {
      console.log(`FileSelector: 文件夹为空或无法访问`)
    }

  } catch (error: any) {
    console.error(`FileSelector: 加载文件列表失败:`, error)
    ElMessage.error(`加载文件列表失败: ${error.message || error}`)
    fileList.value = []
  } finally {
    loading.value = false
  }
}

const handleRowClick = (row: FileInfo) => {
  if (!row.is_directory) {
    selectedFile.value = row
  }
}

const handleRowDoubleClick = (row: FileInfo) => {
  if (row.is_directory) {
    // 使用正确的路径分隔符
    const separator = currentPath.value.includes('\\') ? '\\' : '/'
    const newPath = currentPath.value.endsWith(separator)
      ? currentPath.value + row.name
      : currentPath.value + separator + row.name
    loadFileList(newPath)
  } else {
    selectedFile.value = row
    confirmSelection()
  }
}

const navigateToPath = (segmentIndex: number) => {
  const separator = currentPath.value.includes('\\') ? '\\' : '/'
  const segments = pathSegments.value.slice(0, segmentIndex + 1)

  if (separator === '\\') {
    // Windows 路径：保留盘符格式
    const newPath = segments.join('\\')
    loadFileList(newPath)
  } else {
    // Unix 路径
    const newPath = '/' + segments.join('/')
    loadFileList(newPath)
  }
}

const navigateUp = () => {
  const separator = currentPath.value.includes('\\') ? '\\' : '/'
  const segments = pathSegments.value

  if (segments.length > 1) {
    if (separator === '\\') {
      // Windows 路径
      const newPath = segments.slice(0, -1).join('\\')
      loadFileList(newPath)
    } else {
      // Unix 路径
      const newPath = '/' + segments.slice(0, -1).join('/')
      loadFileList(newPath)
    }
  } else if (separator === '/') {
    loadFileList('/')
  }
}

const confirmSelection = () => {
  if (selectedFile.value && !selectedFile.value.is_directory) {
    emit('confirm', selectedFile.value)
    handleClose()
  } else {
    ElMessage.warning('请选择一个文件')
  }
}

const handleClose = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 暴露方法
const open = (initialPath?: string) => {
  if (initialPath) {
    currentPath.value = initialPath
  }
  // 重置文件过滤状态
  showOnlyVideos.value = props.fileFilter === 'video'
  console.log(`FileSelector: 打开文件选择器 - 路径: ${currentPath.value}, fileFilter: ${props.fileFilter}, showOnlyVideos: ${showOnlyVideos.value}`)

  // 清空搜索和选择
  searchQuery.value = ''
  selectedFile.value = null

  dialogVisible.value = true
  loadFileList(currentPath.value)
}

defineExpose({
  open
})

// 监听对话框打开
watch(dialogVisible, (visible) => {
  if (visible) {
    loadFileList(currentPath.value)
  }
})
</script>

<style scoped>
.file-selector {
  min-height: 500px;
}

.path-navigation {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.breadcrumb-item {
  cursor: pointer;
}

.breadcrumb-item:hover {
  color: #409EFF;
}

.file-list-container {
  margin-bottom: 15px;
}

.directory-name {
  font-weight: bold;
  color: #409EFF;
}

.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.selected-file {
  margin-top: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
