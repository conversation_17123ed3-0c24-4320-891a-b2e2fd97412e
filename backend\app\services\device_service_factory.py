"""
设备服务工厂
用于创建与Core服务通信的客户端
"""

import logging
from typing import Optional
from app.core.client import DeviceServiceClient
from app.core.service_discovery import get_service_discovery
from app.config.settings import get_settings

logger = logging.getLogger(__name__)

class DeviceServiceFactory:
    """设备服务工厂类"""

    @staticmethod
    async def create_client() -> Optional[DeviceServiceClient]:
        """创建设备服务客户端

        Returns:
            设备服务客户端实例，如果创建失败则返回None
        """
        try:
            # 首先尝试从Consul服务发现获取Core服务地址
            service_discovery = get_service_discovery()
            service_info = service_discovery.discover_core_service()

            if service_info:
                host, port = service_info
                logger.info(f"从Consul发现Core服务，创建设备服务客户端，连接到: {host}:{port}")
            else:
                # 如果服务发现失败，使用默认配置
                settings = get_settings()
                host = settings.core_default_host
                port = settings.core_default_port
                logger.warning(f"Consul服务发现失败，使用默认Core服务地址: {host}:{port}")

            # 创建客户端
            client = DeviceServiceClient(host=host, port=port)
            return client

        except Exception as e:
            logger.error(f"创建设备服务客户端异常: {str(e)}", exc_info=True)
            return None