<template>
  <div class="benchmark-manager">
    <!-- 简化的工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索对标账号..."
          style="width: 250px"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-input
          v-model="ourAccountSearchQuery"
          placeholder="搜索关联账号..."
          style="width: 200px; margin-left: 10px"
          @input="handleOurAccountSearch"
          clearable
        >
          <template #prefix>
            <el-icon><User /></el-icon>
          </template>
        </el-input>

        <el-select
          v-model="filterPlatform"
          placeholder="平台筛选"
          style="width: 120px; margin-left: 10px"
          @change="loadBenchmarkAccounts"
          clearable
        >
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
          <el-option label="抖音" value="douyin" />
          <el-option label="微博" value="weibo" />
          <el-option label="小红书" value="xiaohongshu" />
          <el-option label="快手" value="kuaishou" />
          <el-option label="B站" value="bilibili" />
        </el-select>

        <el-select
          v-model="filterType"
          placeholder="对标类型"
          style="width: 120px; margin-left: 10px"
          @change="loadBenchmarkAccounts"
          clearable
        >
          <el-option label="原创" value="original" />
          <el-option label="二创" value="recreate" />
          <el-option label="搬运" value="repost" />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加对标账号
        </el-button>

        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 简化的统计信息 -->
    <div class="stats-bar" v-if="stats">
      <div class="stat-item">
        <span class="stat-label">总计:</span>
        <span class="stat-value">{{ stats.total_count }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">原创:</span>
        <span class="stat-value">{{ stats.by_type?.original || 0 }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">二创:</span>
        <span class="stat-value">{{ stats.by_type?.recreate || 0 }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">搬运:</span>
        <span class="stat-value">{{ stats.by_type?.repost || 0 }}</span>
      </div>
    </div>

    <!-- 简化的对标账号列表 -->
    <div class="account-list">
      <el-table
        :data="benchmarkAccounts"
        v-loading="loading"
        stripe
        style="width: 100%"
        :empty-text="loading ? '加载中...' : '暂无对标账号'"
      >
        <el-table-column label="对标账号信息" min-width="350">
          <template #default="{ row }">
            <div class="account-card">
              <div class="account-header">
                <div class="account-avatar-placeholder">
                  {{ row.account_name.charAt(0).toUpperCase() }}
                </div>
                <div class="account-details">
                  <div class="account-name">
                    <a :href="row.account_url" target="_blank" class="account-link">
                      {{ row.account_name }}
                    </a>
                  </div>
                  <div class="account-meta">
                    <el-tag size="small" type="info">{{ row.platform }}</el-tag>
                    <el-tag
                      :type="getBenchmarkTypeColor(row.benchmark_type)"
                      size="small"
                      style="margin-left: 4px"
                    >
                      {{ getBenchmarkTypeText(row.benchmark_type) }}
                    </el-tag>
                    <span class="priority-stars">
                      {{ '★'.repeat(row.priority) }}{{ '☆'.repeat(5 - row.priority) }}
                    </span>
                  </div>
                  <div v-if="row.description" class="account-description">
                    {{ row.description }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="关联账号" width="200">
          <template #default="{ row }">
            <div class="our-account-info">
              <div v-if="row.our_account_info && row.our_account_info.status !== 'not_found'">
                <div class="account-name">{{ formatOurAccountName(row.our_account_info) }}</div>
                <el-tag size="small">{{ getPlatformDisplayName(row.our_account_info.platform_id) }}</el-tag>
              </div>
              <div v-else class="no-account">
                <el-tag type="warning" size="small">未关联</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="数据概览" width="180">
          <template #default="{ row }">
            <div class="account-data">
              <div v-if="hasAccountData(row)" class="data-summary">
                <div v-if="row.account_data.followers" class="data-item">
                  👥 {{ formatNumber(row.account_data.followers) }}
                </div>
                <div v-if="row.account_data.posts_count" class="data-item">
                  📝 {{ formatNumber(row.account_data.posts_count) }}
                </div>
                <div v-if="row.account_data.engagement_rate" class="data-item">
                  📈 {{ (row.account_data.engagement_rate * 100).toFixed(1) }}%
                </div>
              </div>
              <div v-else class="no-data">
                <el-button size="small" type="primary" link @click="updateData(row)">
                  获取数据
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="标签" width="120">
          <template #default="{ row }">
            <div class="tags">
              <el-tag
                v-for="tag in row.tags.slice(0, 2)"
                :key="tag"
                size="small"
                style="margin-bottom: 2px"
              >
                {{ tag }}
              </el-tag>
              <div v-if="row.tags.length > 2" class="more-tags">
                +{{ row.tags.length - 2 }}个
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click="viewAccount(row)" type="primary" link>
                详情
              </el-button>
              <el-button size="small" @click="editAccount(row)" type="primary" link>
                编辑
              </el-button>
              <el-button size="small" @click="updateData(row)" type="primary" link>
                更新
              </el-button>
              <el-popconfirm
                title="确定删除这个对标账号吗？"
                @confirm="deleteAccount(row)"
              >
                <template #reference>
                  <el-button size="small" type="danger" link>
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalCount"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="loadBenchmarkAccounts"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 创建对标账号对话框 -->
    <CreateBenchmarkDialog 
      v-model="showCreateDialog"
      :our-accounts="ourAccounts"
      @success="handleCreateSuccess"
    />

    <!-- 编辑对标账号对话框 -->
    <EditBenchmarkDialog 
      v-model="showEditDialog"
      :account="selectedAccount"
      @success="handleEditSuccess"
    />

    <!-- 更新数据对话框 -->
    <UpdateDataDialog 
      v-model="showUpdateDataDialog"
      :account="selectedAccount"
      @success="handleUpdateDataSuccess"
    />

    <!-- 账号详情对话框 -->
    <AccountDetailDialog 
      v-model="showDetailDialog"
      :account="selectedAccount"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Refresh, User } from '@element-plus/icons-vue'

// API导入
import {
  getBenchmarkAccounts,
  getBenchmarkStats,
  updateBenchmarkAccount,
  deleteBenchmarkAccount,
  type BenchmarkAccount,
  type BenchmarkAccountStats
} from '@/api/content'
import { getAccounts } from '@/api/social'

// 组件导入
import CreateBenchmarkDialog from './CreateBenchmarkDialog.vue'
import EditBenchmarkDialog from './EditBenchmarkDialog.vue'
import UpdateDataDialog from './UpdateDataDialog.vue'
import AccountDetailDialog from './AccountDetailDialog.vue'

const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const loading = ref(false)
const benchmarkAccounts = ref<BenchmarkAccount[]>([])
const stats = ref<BenchmarkAccountStats | null>(null)
const ourAccounts = ref<any[]>([])

// 筛选和搜索
const selectedOurAccount = ref('')
const filterPlatform = ref('')
const filterType = ref('')
const searchQuery = ref('')
const ourAccountSearchQuery = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showUpdateDataDialog = ref(false)
const showDetailDialog = ref(false)
const selectedAccount = ref<BenchmarkAccount | null>(null)

// 方法
const loadOurAccounts = async () => {
  try {
    // 调用获取我们账号列表的API，只获取活跃状态的账号
    const response = await getAccounts({ status: 'active' })
    ourAccounts.value = response.data?.data || []
    console.log('加载的账号列表:', ourAccounts.value)
  } catch (error) {
    console.error('加载账号列表失败:', error)
    ElMessage.error('加载账号列表失败')
  }
}

const loadBenchmarkAccounts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      our_account_id: selectedOurAccount.value || undefined,
      platform: filterPlatform.value || undefined,
      benchmark_type: filterType.value || undefined,
      search: searchQuery.value || undefined,
      our_account_search: ourAccountSearchQuery.value || undefined
    }

    const response = await getBenchmarkAccounts(params)
    benchmarkAccounts.value = response.items
    totalCount.value = response.total
  } catch (error) {
    console.error('加载对标账号失败:', error)
    ElMessage.error('加载对标账号失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await getBenchmarkStats(selectedOurAccount.value || undefined)
    stats.value = response
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const refreshData = async () => {
  await Promise.all([
    loadBenchmarkAccounts(),
    loadStats()
  ])
  ElMessage.success('数据刷新成功')
}

// 工具方法
const getPlatformDisplayName = (platformId: string) => {
  // 根据数据库中的平台ObjectId映射到显示名称
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube', // 油管
    '681efeeecd836bd64b9c2a20': 'TikTok',  // TT
    '681efeeecd836bd64b9c2a22': '抖音',     // 抖音
    '6822ecaa62fd956eb6d2c071': 'Facebook', // 脸书
    '6822ebfc05340d5a3d867138': 'AWS'       // AWS
  }

  // 如果是ObjectId格式，使用映射表
  if (platformIdMap[platformId]) {
    return platformIdMap[platformId]
  }

  // 如果是传统的平台ID格式，使用原来的映射
  const platformMap = {
    youtube: 'YouTube',
    tiktok: 'TikTok',
    instagram: 'Instagram',
    weibo: '微博',
    xiaohongshu: '小红书',
    douyin: '抖音',
    kuaishou: '快手',
    bilibili: 'B站'
  }

  return platformMap[platformId as keyof typeof platformMap] || platformId
}

const formatOurAccountName = (accountInfo: any) => {
  if (!accountInfo) return '未知账号'

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = accountInfo.display_name
  if (!name && accountInfo.username) {
    // 如果username是邮箱格式，取@前面的部分
    name = accountInfo.username.includes('@') ? accountInfo.username.split('@')[0] : accountInfo.username
  }
  if (!name) {
    name = '未命名账号'
  }

  return name
}

const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    monitoring: '监控中'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getStatusColor = (status: string) => {
  const colorMap = {
    active: 'success',
    inactive: 'danger',
    monitoring: 'warning'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleDateString('zh-CN')
}

const hasAccountData = (account: BenchmarkAccount) => {
  const data = account.account_data
  return data.followers || data.posts_count || data.engagement_rate
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 事件处理
const handleSearch = () => {
  setTimeout(() => {
    currentPage.value = 1
    loadBenchmarkAccounts()
  }, 500)
}

const handleOurAccountSearch = () => {
  setTimeout(() => {
    currentPage.value = 1
    loadBenchmarkAccounts()
  }, 500)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadBenchmarkAccounts()
}

const updatePriority = async (account: BenchmarkAccount) => {
  try {
    await updateBenchmarkAccount(account._id!, { priority: account.priority })
    ElMessage.success('优先级更新成功')
  } catch (error) {
    console.error('更新优先级失败:', error)
    ElMessage.error('更新优先级失败')
  }
}

const viewAccount = (account: BenchmarkAccount) => {
  selectedAccount.value = account
  showDetailDialog.value = true
}

const editAccount = (account: BenchmarkAccount) => {
  selectedAccount.value = account
  showEditDialog.value = true
}

const updateData = (account: BenchmarkAccount) => {
  selectedAccount.value = account
  showUpdateDataDialog.value = true
}

const deleteAccount = async (account: BenchmarkAccount) => {
  try {
    await deleteBenchmarkAccount(account._id!)
    ElMessage.success('删除成功')
    loadBenchmarkAccounts()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  refreshData()
}

const handleEditSuccess = () => {
  showEditDialog.value = false
  refreshData()
}

const handleUpdateDataSuccess = () => {
  showUpdateDataDialog.value = false
  refreshData()
}

// 生命周期
onMounted(() => {
  loadOurAccounts()
  refreshData()
})
</script>

<style scoped>
.benchmark-manager {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-bar {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 12px 16px;
  background: #f0f2f5;
  border-radius: 6px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.account-list {
  flex: 1;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.account-card {
  padding: 4px 0;
}

.account-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.account-avatar-placeholder {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.account-details {
  flex: 1;
  min-width: 0;
}

.account-name {
  margin-bottom: 6px;
}

.account-link {
  font-weight: 500;
  color: #1890ff;
  text-decoration: none;
  font-size: 15px;
}

.account-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.account-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.priority-stars {
  font-size: 12px;
  color: #faad14;
  margin-left: 4px;
}

.account-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.our-account-info .account-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.no-account {
  text-align: center;
}

.account-data {
  padding: 4px 0;
}

.data-summary {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.data-item {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-data {
  text-align: center;
  padding: 8px 0;
}

.tags {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.more-tags {
  font-size: 11px;
  color: #999;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-start;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  margin-top: 16px;
}

/* 表格行高调整 */
:deep(.el-table .el-table__row) {
  height: auto;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

/* 链接按钮样式 */
:deep(.el-button--small.is-link) {
  padding: 2px 4px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .stats-bar {
    flex-wrap: wrap;
    gap: 12px;
  }
}
</style>
