"""
音频处理服务
提供批量音频处理功能，包括字幕生成、音频分离、人声提取和音频替换
"""

import os
import sys
import asyncio
import logging
import time
import json
import tempfile
import shutil
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import subprocess

logger = logging.getLogger(__name__)

class AudioProcessingService:
    """音频处理服务类"""

    def __init__(self):
        self.supported_audio_extensions = ['mp3', 'wav', 'aac', 'flac', 'm4a', 'ogg']
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        
    def is_supported_file(self, file_path: str) -> bool:
        """检查文件是否支持音频处理"""
        extension = Path(file_path).suffix.lower().lstrip('.')
        return extension in self.supported_audio_extensions or extension in self.supported_video_extensions

    async def generate_subtitles_batch(self, folder_path: str, output_format: str = 'srt',
                                     language: str = 'auto', model_size: str = 'base',
                                     max_concurrent: int = 2) -> Dict[str, Any]:
        """批量生成字幕文件
        
        Args:
            folder_path: 输入文件夹路径
            output_format: 输出格式 (srt, vtt, txt)
            language: 语言代码 (auto, zh, en, etc.)
            model_size: Whisper模型大小 (tiny, base, small, medium, large)
            max_concurrent: 最大并发处理数
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量生成字幕: {folder_path}")
            
            # 扫描支持的文件
            files = self._scan_media_files(folder_path)
            if not files:
                return {"success": False, "error": "未找到支持的媒体文件", "processed_files": 0}
            
            logger.info(f"找到 {len(files)} 个媒体文件")
            
            # 创建输出目录
            output_dir = os.path.join(folder_path, "subtitles")
            os.makedirs(output_dir, exist_ok=True)
            
            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0
            
            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._generate_single_subtitle(
                        file_path, output_dir, output_format, language, model_size
                    )
            
            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1
            
            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "results": [r for r in results if not isinstance(r, Exception)]
            }
            
        except Exception as e:
            logger.error(f"批量生成字幕失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    async def _generate_single_subtitle(self, file_path: str, output_dir: str,
                                      output_format: str, language: str, model_size: str) -> Dict[str, Any]:
        """生成单个文件的字幕"""
        start_time = time.time()
        file_name = Path(file_path).stem

        try:
            logger.info(f"开始生成字幕: {file_name}")

            # 构建输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}.{output_format}")

            # 检查是否已存在
            if os.path.exists(output_file):
                logger.info(f"字幕文件已存在，跳过: {output_file}")
                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "字幕文件已存在"
                }

            # 尝试使用Python API生成字幕
            try:
                result = await self._generate_subtitle_with_python_api(
                    file_path, output_dir, output_format, language, model_size
                )
                if result["success"]:
                    return result
                else:
                    logger.warning(f"Python API生成字幕失败，尝试命令行方式: {result.get('error_message', '')}")
            except Exception as api_error:
                logger.warning(f"Python API生成字幕异常，尝试命令行方式: {str(api_error)}")

            # 回退到命令行方式
            cmd = ['whisper', file_path, '--output_dir', output_dir, '--output_format', output_format]

            if language != 'auto':
                cmd.extend(['--language', language])

            cmd.extend(['--model', model_size])
            cmd.extend(['--verbose', 'False'])

            logger.debug(f"执行whisper命令: {' '.join(cmd[:5])}...")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                processing_time = int((time.time() - start_time) * 1000)
                logger.info(f"字幕生成成功: {file_name} ({processing_time}ms)")

                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": processing_time,
                    "message": "字幕生成成功"
                }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"字幕生成失败 {file_name}: {error_msg}")

                return {
                    "success": False,
                    "input_file": file_path,
                    "output_file": "",
                    "error_message": error_msg,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"生成字幕异常 {file_name}: {error_msg}")

            return {
                "success": False,
                "input_file": file_path,
                "output_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }

    async def _generate_subtitle_with_python_api(self, file_path: str, output_dir: str,
                                               output_format: str, language: str, model_size: str) -> Dict[str, Any]:
        """使用Python API生成字幕"""
        import asyncio
        import concurrent.futures

        def _transcribe_with_whisper():
            """在线程中执行whisper转录"""
            try:
                import whisper

                # 加载模型
                model = whisper.load_model(model_size)

                # 转录音频
                if language == 'auto':
                    result = model.transcribe(file_path)
                else:
                    result = model.transcribe(file_path, language=language)

                return result
            except Exception as e:
                raise e

        try:
            # 在线程池中执行whisper转录（因为whisper是CPU密集型任务）
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                result = await loop.run_in_executor(executor, _transcribe_with_whisper)

            # 构建输出文件路径
            file_name = Path(file_path).stem
            output_file = os.path.join(output_dir, f"{file_name}.{output_format}")

            # 保存字幕文件
            if output_format == 'srt':
                self._save_srt_file(result, output_file)
            elif output_format == 'vtt':
                self._save_vtt_file(result, output_file)
            elif output_format == 'txt':
                self._save_txt_file(result, output_file)
            else:
                raise ValueError(f"不支持的输出格式: {output_format}")

            return {
                "success": True,
                "input_file": file_path,
                "output_file": output_file,
                "processing_time_ms": 0,  # 会在调用方计算
                "message": "字幕生成成功 (Python API)"
            }

        except Exception as e:
            return {
                "success": False,
                "input_file": file_path,
                "output_file": "",
                "error_message": f"Python API生成字幕失败: {str(e)}",
                "processing_time_ms": 0
            }

    def _save_srt_file(self, result, output_file: str):
        """保存SRT格式字幕文件"""
        def format_timestamp(seconds):
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            millisecs = int((seconds % 1) * 1000)
            return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

        with open(output_file, 'w', encoding='utf-8') as f:
            for i, segment in enumerate(result['segments'], 1):
                start_time = format_timestamp(segment['start'])
                end_time = format_timestamp(segment['end'])
                text = segment['text'].strip()

                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{text}\n\n")

    def _save_vtt_file(self, result, output_file: str):
        """保存VTT格式字幕文件"""
        def format_timestamp(seconds):
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = seconds % 60
            return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("WEBVTT\n\n")
            for segment in result['segments']:
                start_time = format_timestamp(segment['start'])
                end_time = format_timestamp(segment['end'])
                text = segment['text'].strip()

                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{text}\n\n")

    def _save_txt_file(self, result, output_file: str):
        """保存TXT格式字幕文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result['text'])

    async def extract_audio_batch(self, folder_path: str, output_format: str = 'wav',
                                quality: str = 'high', max_concurrent: int = 3) -> Dict[str, Any]:
        """批量分离音频
        
        Args:
            folder_path: 输入文件夹路径
            output_format: 输出音频格式 (wav, mp3, aac)
            quality: 音频质量 (high, medium, low)
            max_concurrent: 最大并发处理数
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量分离音频: {folder_path}")
            
            # 扫描视频文件
            files = self._scan_video_files(folder_path)
            if not files:
                return {"success": False, "error": "未找到支持的视频文件", "processed_files": 0}
            
            logger.info(f"找到 {len(files)} 个视频文件")
            
            # 创建输出目录
            output_dir = os.path.join(folder_path, "extracted_audio")
            os.makedirs(output_dir, exist_ok=True)
            
            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0
            
            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._extract_single_audio(
                        file_path, output_dir, output_format, quality
                    )
            
            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1
            
            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "results": [r for r in results if not isinstance(r, Exception)]
            }
            
        except Exception as e:
            logger.error(f"批量分离音频失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    def _scan_media_files(self, folder_path: str) -> List[str]:
        """扫描媒体文件"""
        files = []
        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                if os.path.isfile(file_path) and self.is_supported_file(file_path):
                    files.append(file_path)
            return sorted(files)
        except Exception as e:
            logger.error(f"扫描媒体文件失败: {str(e)}")
            return []

    def _scan_video_files(self, folder_path: str) -> List[str]:
        """扫描视频文件"""
        files = []
        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                if os.path.isfile(file_path):
                    extension = Path(file_path).suffix.lower().lstrip('.')
                    if extension in self.supported_video_extensions:
                        files.append(file_path)
            return sorted(files)
        except Exception as e:
            logger.error(f"扫描视频文件失败: {str(e)}")
            return []

    async def _extract_single_audio(self, file_path: str, output_dir: str,
                                  output_format: str, quality: str) -> Dict[str, Any]:
        """分离单个文件的音频"""
        start_time = time.time()
        file_name = Path(file_path).stem

        try:
            logger.info(f"开始分离音频: {file_name}")

            # 构建输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}.{output_format}")

            # 检查是否已存在
            if os.path.exists(output_file):
                logger.info(f"音频文件已存在，跳过: {output_file}")
                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "音频文件已存在"
                }

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y', '-i', file_path]

            # 设置音频质量参数
            if output_format == 'mp3':
                if quality == 'high':
                    cmd.extend(['-b:a', '320k'])
                elif quality == 'medium':
                    cmd.extend(['-b:a', '192k'])
                else:  # low
                    cmd.extend(['-b:a', '128k'])
            elif output_format == 'wav':
                if quality == 'high':
                    cmd.extend(['-ar', '48000', '-ac', '2'])
                elif quality == 'medium':
                    cmd.extend(['-ar', '44100', '-ac', '2'])
                else:  # low
                    cmd.extend(['-ar', '22050', '-ac', '1'])
            elif output_format == 'aac':
                if quality == 'high':
                    cmd.extend(['-b:a', '256k'])
                elif quality == 'medium':
                    cmd.extend(['-b:a', '128k'])
                else:  # low
                    cmd.extend(['-b:a', '96k'])

            # 只提取音频流
            cmd.extend(['-vn', output_file])

            logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:5])}...")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                processing_time = int((time.time() - start_time) * 1000)

                # 获取文件大小
                input_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                output_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0

                logger.info(f"音频分离成功: {file_name} ({processing_time}ms)")

                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": processing_time,
                    "input_file_size": input_size,
                    "output_file_size": output_size,
                    "message": "音频分离成功"
                }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"音频分离失败 {file_name}: {error_msg}")

                return {
                    "success": False,
                    "input_file": file_path,
                    "output_file": "",
                    "error_message": error_msg,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"分离音频异常 {file_name}: {error_msg}")

            return {
                "success": False,
                "input_file": file_path,
                "output_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }

    async def separate_vocals_batch(self, folder_path: str, output_type: str = 'vocals',
                                  separation_method: str = 'ffmpeg', output_quality: str = 'high',
                                  max_concurrent: int = 2, selected_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """批量人声分离（从视频中分离人声和背景音乐）

        Args:
            folder_path: 输入视频文件夹路径
            output_type: 输出类型 (vocals: 保留人声, instrumental: 保留伴奏, both: 同时输出两种)
            separation_method: 分离方法 (ffmpeg, librosa)
            output_quality: 输出质量 (high, medium, low)
            max_concurrent: 最大并发处理数
            selected_files: 选中的文件列表（可选，如果不提供则处理整个文件夹）

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量人声分离: {folder_path}")

            # 确定要处理的文件列表
            if selected_files and len(selected_files) > 0:
                # 使用选中的文件
                files = []
                for file_name in selected_files:
                    file_path = os.path.join(folder_path, file_name)
                    if os.path.exists(file_path) and os.path.isfile(file_path) and self.is_supported_file(file_path):
                        files.append(file_path)
                    else:
                        logger.warning(f"跳过不支持或不存在的文件: {file_name}")
                logger.info(f"处理选中的 {len(files)} 个文件")
            else:
                # 扫描整个文件夹的媒体文件（支持音频和视频）
                files = self._scan_media_files(folder_path)
                logger.info(f"找到 {len(files)} 个媒体文件")

            if not files:
                return {"success": False, "error": "未找到支持的媒体文件", "processed_files": 0}

            # 创建输出目录
            output_dir = os.path.join(folder_path, "vocals_separated")
            os.makedirs(output_dir, exist_ok=True)

            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._separate_single_vocal(
                        file_path, output_dir, output_type, separation_method, output_quality
                    )

            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1

            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "results": [r for r in results if not isinstance(r, Exception)]
            }

        except Exception as e:
            logger.error(f"批量人声分离失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    def _scan_audio_files(self, folder_path: str) -> List[str]:
        """扫描音频文件"""
        files = []
        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                if os.path.isfile(file_path):
                    extension = Path(file_path).suffix.lower().lstrip('.')
                    if extension in self.supported_audio_extensions:
                        files.append(file_path)
            return sorted(files)
        except Exception as e:
            logger.error(f"扫描音频文件失败: {str(e)}")
            return []

    async def _separate_single_vocal(self, file_path: str, output_dir: str, output_type: str,
                                   separation_method: str, output_quality: str) -> Dict[str, Any]:
        """分离单个视频文件的人声"""
        start_time = time.time()
        file_name = Path(file_path).stem
        file_ext = Path(file_path).suffix

        try:
            logger.info(f"开始视频人声分离: {file_name}, 输出类型: {output_type}")

            # 构建输出视频文件路径
            vocals_file = os.path.join(output_dir, f"{file_name}_vocals{file_ext}")
            instrumental_file = os.path.join(output_dir, f"{file_name}_instrumental{file_ext}")

            # 检查输出文件是否已存在
            skip_processing = False
            if output_type == 'vocals' and os.path.exists(vocals_file):
                skip_processing = True
                logger.info(f"人声视频文件已存在，跳过: {vocals_file}")
            elif output_type == 'instrumental' and os.path.exists(instrumental_file):
                skip_processing = True
                logger.info(f"伴奏视频文件已存在，跳过: {instrumental_file}")
            elif output_type == 'both' and os.path.exists(vocals_file) and os.path.exists(instrumental_file):
                skip_processing = True
                logger.info(f"人声和伴奏视频文件都已存在，跳过")

            if skip_processing:
                return {
                    "success": True,
                    "input_file": file_path,
                    "vocals_file": vocals_file if output_type in ['vocals', 'both'] else "",
                    "instrumental_file": instrumental_file if output_type in ['instrumental', 'both'] else "",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "输出文件已存在"
                }

            if separation_method == 'ffmpeg':
                # 使用ffmpeg进行视频人声分离
                success = await self._separate_vocals_video_ffmpeg(
                    file_path, vocals_file, instrumental_file, output_type, output_quality
                )
            else:
                # 使用librosa进行更高质量的视频人声分离
                success = await self._separate_vocals_video_librosa(
                    file_path, vocals_file, instrumental_file, output_type, output_quality
                )

            if success:
                processing_time = int((time.time() - start_time) * 1000)

                # 获取文件大小
                input_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                vocals_size = 0
                instrumental_size = 0

                # 根据输出类型计算文件大小
                if output_type in ['vocals', 'both'] and os.path.exists(vocals_file):
                    vocals_size = os.path.getsize(vocals_file)
                if output_type in ['instrumental', 'both'] and os.path.exists(instrumental_file):
                    instrumental_size = os.path.getsize(instrumental_file)

                logger.info(f"视频人声分离成功: {file_name} ({output_type}) ({processing_time}ms)")

                return {
                    "success": True,
                    "input_file": file_path,
                    "vocals_file": vocals_file if output_type in ['vocals', 'both'] else "",
                    "instrumental_file": instrumental_file if output_type in ['instrumental', 'both'] else "",
                    "processing_time_ms": processing_time,
                    "input_file_size": input_size,
                    "vocals_file_size": vocals_size,
                    "instrumental_file_size": instrumental_size,
                    "message": f"视频人声分离成功 ({output_type})"
                }
            else:
                return {
                    "success": False,
                    "input_file": file_path,
                    "vocals_file": "",
                    "instrumental_file": "",
                    "error_message": "人声分离失败",
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"人声分离异常 {file_name}: {error_msg}")

            return {
                "success": False,
                "input_file": file_path,
                "vocals_file": "",
                "instrumental_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }

    async def _separate_vocals_video_ffmpeg(self, input_file: str, vocals_file: str,
                                          instrumental_file: str, output_type: str, output_quality: str) -> bool:
        """使用ffmpeg进行视频人声分离"""
        try:
            # 根据输出类型决定要生成的文件
            tasks = []

            if output_type in ['vocals', 'both']:
                # 人声版本：保留中央声道（人声），提取左右声道的平均值
                vocals_cmd = [
                    'ffmpeg', '-y', '-i', input_file,
                    '-c:v', 'copy',  # 复制视频流
                    '-af', 'pan=stereo|c0=0.5*c0+0.5*c1|c1=0.5*c0+0.5*c1',  # 人声增强：提取中央声道
                    '-c:a', 'aac', '-b:a', '128k',  # 音频编码
                    vocals_file
                ]
                tasks.append(('vocals', vocals_cmd))

            if output_type in ['instrumental', 'both']:
                # 伴奏版本：去除中央声道（人声），保留左右声道差值（背景音乐）
                instrumental_cmd = [
                    'ffmpeg', '-y', '-i', input_file,
                    '-c:v', 'copy',  # 复制视频流
                    '-af', 'pan=stereo|c0=c0-c1|c1=c1-c0',  # 去除中央声道：左右声道相减
                    '-c:a', 'aac', '-b:a', '128k',  # 音频编码
                    instrumental_file
                ]
                tasks.append(('instrumental', instrumental_cmd))

            # 执行所有任务
            for task_name, cmd in tasks:
                logger.info(f"开始生成{task_name}版本视频")
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                _, stderr = await process.communicate()

                if process.returncode != 0:
                    error_msg = stderr.decode('utf-8', errors='ignore')
                    logger.error(f"{task_name}版本生成失败: {error_msg}")
                    return False
                else:
                    logger.info(f"{task_name}版本生成成功")

            return True

        except Exception as e:
            logger.error(f"ffmpeg视频人声分离异常: {str(e)}")
            return False

    async def _separate_vocals_video_librosa(self, input_file: str, vocals_file: str,
                                           instrumental_file: str, output_type: str, output_quality: str) -> bool:
        """使用AI模型进行高质量视频人声分离"""
        try:
            logger.info(f"使用AI模型进行人声分离: {input_file}")

            # 创建临时目录用于处理
            with tempfile.TemporaryDirectory() as temp_dir:
                # 1. 先提取音频
                temp_audio = os.path.join(temp_dir, "temp_audio.wav")
                extract_cmd = [
                    'ffmpeg', '-y', '-i', input_file,
                    '-vn', '-acodec', 'pcm_s16le', '-ar', '44100', '-ac', '2',
                    temp_audio
                ]

                process = await asyncio.create_subprocess_exec(
                    *extract_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                _, stderr = await process.communicate()

                if process.returncode != 0:
                    logger.error(f"音频提取失败: {stderr.decode('utf-8', errors='ignore')}")
                    return False

                # 2. 使用Demucs进行AI人声分离
                success = await self._separate_audio_with_demucs(
                    temp_audio, temp_dir, output_type
                )

                if not success:
                    logger.warning("Demucs分离失败，回退到传统方法")
                    return await self._separate_vocals_video_ffmpeg(input_file, vocals_file, instrumental_file, output_type, output_quality)

                # 3. 将分离后的音频与原视频合并
                # 传递临时音频文件名而不是原视频文件名
                temp_audio_name = os.path.splitext(os.path.basename(temp_audio))[0]
                return await self._merge_separated_audio_with_video(
                    input_file, temp_dir, vocals_file, instrumental_file, output_type, temp_audio_name
                )

        except Exception as e:
            logger.error(f"AI人声分离异常: {str(e)}")
            # 回退到传统方法
            logger.warning("AI分离失败，回退到传统FFmpeg方法")
            return await self._separate_vocals_video_ffmpeg(input_file, vocals_file, instrumental_file, output_type, output_quality)

    async def _separate_vocals_ffmpeg(self, input_file: str, vocals_file: str,
                                    instrumental_file: str, output_quality: str) -> bool:
        """使用ffmpeg进行音频人声分离（保留原有功能）"""
        try:
            # 人声提取（中央声道）
            vocals_cmd = [
                'ffmpeg', '-y', '-i', input_file,
                '-af', 'pan=mono|c0=0.5*c0+0.5*c1',
                vocals_file
            ]

            # 伴奏提取（左右声道差值）
            instrumental_cmd = [
                'ffmpeg', '-y', '-i', input_file,
                '-af', 'pan=mono|c0=c0-c1',
                instrumental_file
            ]

            # 执行人声提取
            process = await asyncio.create_subprocess_exec(
                *vocals_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            _, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"人声提取失败: {stderr.decode('utf-8', errors='ignore')}")
                return False

            # 执行伴奏提取
            process = await asyncio.create_subprocess_exec(
                *instrumental_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            _, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"伴奏提取失败: {stderr.decode('utf-8', errors='ignore')}")
                return False

            return True

        except Exception as e:
            logger.error(f"ffmpeg人声分离异常: {str(e)}")
            return False

    async def _separate_vocals_librosa(self, input_file: str, vocals_file: str,
                                     instrumental_file: str, output_quality: str) -> bool:
        """使用AI模型进行高质量音频人声分离（保留原有功能）"""
        try:
            logger.info(f"使用AI模型进行音频人声分离: {input_file}")

            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                # 使用Demucs进行AI人声分离
                success = await self._separate_audio_with_demucs(
                    input_file, temp_dir, 'both'
                )

                if not success:
                    logger.warning("Demucs分离失败，回退到传统方法")
                    return await self._separate_vocals_ffmpeg(input_file, vocals_file, instrumental_file, output_quality)

                # 动态查找分离结果
                base_name = os.path.splitext(os.path.basename(input_file))[0]

                # 查找vocals和instrumental文件
                demucs_vocals = None
                demucs_instrumental = None

                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if file.startswith('vocals.') and (file.endswith('.mp3') or file.endswith('.wav')):
                            demucs_vocals = file_path
                        elif file.startswith('no_vocals.') and (file.endswith('.mp3') or file.endswith('.wav')):
                            demucs_instrumental = file_path

                if demucs_vocals and os.path.exists(demucs_vocals):
                    shutil.copy2(demucs_vocals, vocals_file)
                    logger.info(f"AI人声分离成功: {vocals_file}")
                else:
                    logger.error(f"未找到vocals文件")
                    return False

                if demucs_instrumental and os.path.exists(demucs_instrumental):
                    shutil.copy2(demucs_instrumental, instrumental_file)
                    logger.info(f"AI伴奏分离成功: {instrumental_file}")
                else:
                    logger.error(f"未找到instrumental文件")
                    return False

                return True

        except Exception as e:
            logger.error(f"AI人声分离异常: {str(e)}")
            # 回退到传统方法
            logger.warning("AI分离失败，回退到传统FFmpeg方法")
            return await self._separate_vocals_ffmpeg(input_file, vocals_file, instrumental_file, output_quality)

    async def replace_audio_batch(self, folder_path: str, new_audio_path: str,
                                audio_volume: float = 1.0, fade_duration: float = 0.5,
                                max_concurrent: int = 3) -> Dict[str, Any]:
        """批量替换音频

        Args:
            folder_path: 输入视频文件夹路径
            new_audio_path: 新音频文件路径
            audio_volume: 音频音量 (0.0-2.0)
            fade_duration: 淡入淡出时长（秒）
            max_concurrent: 最大并发处理数

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量替换音频: {folder_path}")

            # 检查新音频文件是否存在
            if not os.path.exists(new_audio_path):
                return {"success": False, "error": "新音频文件不存在", "processed_files": 0}

            # 扫描视频文件
            files = self._scan_video_files(folder_path)
            if not files:
                return {"success": False, "error": "未找到支持的视频文件", "processed_files": 0}

            logger.info(f"找到 {len(files)} 个视频文件")

            # 创建输出目录
            output_dir = os.path.join(folder_path, "audio_replaced")
            os.makedirs(output_dir, exist_ok=True)

            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._replace_single_audio(
                        file_path, new_audio_path, output_dir, audio_volume, fade_duration
                    )

            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1

            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "new_audio_file": new_audio_path,
                "results": [r for r in results if not isinstance(r, Exception)]
            }

        except Exception as e:
            logger.error(f"批量替换音频失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    async def _replace_single_audio(self, video_path: str, new_audio_path: str, output_dir: str,
                                  audio_volume: float, fade_duration: float) -> Dict[str, Any]:
        """替换单个视频的音频"""
        start_time = time.time()
        file_name = Path(video_path).stem

        try:
            logger.info(f"开始替换音频: {file_name}")

            # 构建输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}_new_audio.mp4")

            # 检查是否已存在
            if os.path.exists(output_file):
                logger.info(f"音频替换文件已存在，跳过: {output_file}")
                return {
                    "success": True,
                    "input_file": video_path,
                    "output_file": output_file,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "音频替换文件已存在"
                }

            # 构建ffmpeg命令
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,  # 输入视频
                '-i', new_audio_path,  # 输入新音频
                '-c:v', 'copy',  # 视频流直接复制
                '-c:a', 'aac',  # 音频编码为AAC
                '-b:a', '128k',  # 音频比特率
                '-map', '0:v:0',  # 映射第一个输入的视频流
                '-map', '1:a:0',  # 映射第二个输入的音频流
                '-shortest',  # 以最短的流为准
            ]

            # 添加音量调节
            if audio_volume != 1.0:
                cmd.extend(['-af', f'volume={audio_volume}'])

            # 添加淡入淡出效果
            if fade_duration > 0:
                if audio_volume != 1.0:
                    cmd[-1] += f',afade=t=in:ss=0:d={fade_duration},afade=t=out:st=-{fade_duration}:d={fade_duration}'
                else:
                    cmd.extend(['-af', f'afade=t=in:ss=0:d={fade_duration},afade=t=out:st=-{fade_duration}:d={fade_duration}'])

            cmd.append(output_file)

            logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:8])}...")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            _, stderr = await process.communicate()

            if process.returncode == 0:
                processing_time = int((time.time() - start_time) * 1000)

                # 获取文件大小
                input_size = os.path.getsize(video_path) if os.path.exists(video_path) else 0
                output_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0

                logger.info(f"音频替换成功: {file_name} ({processing_time}ms)")

                return {
                    "success": True,
                    "input_file": video_path,
                    "output_file": output_file,
                    "processing_time_ms": processing_time,
                    "input_file_size": input_size,
                    "output_file_size": output_size,
                    "message": "音频替换成功"
                }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"音频替换失败 {file_name}: {error_msg}")

                return {
                    "success": False,
                    "input_file": video_path,
                    "output_file": "",
                    "error_message": error_msg,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"替换音频异常 {file_name}: {error_msg}")

            return {
                "success": False,
                "input_file": video_path,
                "output_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }

    def _get_python_executable(self) -> str:
        """获取当前Python可执行文件路径"""
        return sys.executable

    async def _separate_audio_with_demucs(self, input_file: str, temp_dir: str, output_type: str) -> bool:
        """使用Demucs AI模型进行音频分离"""
        try:
            logger.info(f"开始使用Demucs进行AI人声分离: {input_file}")

            # 获取当前Python可执行文件路径
            python_exe = self._get_python_executable()
            logger.info(f"使用Python可执行文件: {python_exe}")

            # 检查Demucs是否可用
            try:
                # 首先尝试导入demucs模块
                import demucs
                logger.info(f"Demucs模块导入成功，版本: {demucs.__version__ if hasattr(demucs, '__version__') else '未知'}")
            except ImportError as e:
                logger.error(f"Demucs模块导入失败: {str(e)}")
                logger.error("请在当前Python环境中运行 'pip install demucs' 安装Demucs")
                return False

            # 检查命令行工具是否可用
            try:
                process = await asyncio.create_subprocess_exec(
                    python_exe, '-m', 'demucs', '--help',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                if process.returncode != 0:
                    logger.error(f"Demucs命令行工具不可用: {stderr.decode('utf-8', errors='ignore')}")
                    return False
                logger.info("Demucs命令行工具检查通过")
            except Exception as e:
                logger.error(f"Demucs命令行工具检查失败: {str(e)}")
                return False

            # 构建Demucs命令
            # 使用htdemucs模型（高质量混合变换器模型）
            cmd = [
                python_exe, '-m', 'demucs',  # 使用当前Python环境
                '--two-stems=vocals',  # 只分离人声和伴奏
                '--out', temp_dir,
                '--mp3',  # 输出MP3格式以节省空间
                '--mp3-bitrate', '320',  # 高质量MP3
                '--device', 'cpu',  # 明确指定使用CPU（避免CUDA问题）
                input_file
            ]

            logger.info(f"执行Demucs命令: {python_exe} -m demucs ...")
            logger.info(f"输出目录: {temp_dir}")
            logger.info(f"输入文件: {input_file}")

            # 执行Demucs分离
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=temp_dir  # 设置工作目录
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore')
                stdout_msg = stdout.decode('utf-8', errors='ignore')
                logger.error(f"Demucs分离失败:")
                logger.error(f"错误输出: {error_msg}")
                logger.error(f"标准输出: {stdout_msg}")
                return False

            logger.info("Demucs AI人声分离完成")
            logger.info(f"输出目录: {temp_dir}")

            # 检查输出文件是否存在
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            expected_output_dir = os.path.join(temp_dir, "separated", "htdemucs", base_name)

            # 检查可能的输出路径
            possible_paths = [
                expected_output_dir,
                os.path.join(temp_dir, "separated", "htdemucs_6s", base_name),  # 6s模型
                os.path.join(temp_dir, "separated", "htdemucs_ft", base_name),  # ft模型
                os.path.join(temp_dir, "htdemucs", base_name),  # 直接在temp_dir下
            ]

            # 也检查temp_dir下的所有子目录
            try:
                for root, dirs, files in os.walk(temp_dir):
                    if base_name in dirs:
                        possible_paths.append(os.path.join(root, base_name))
                    # 检查是否有vocals.mp3或vocals.wav文件
                    for file in files:
                        if file.startswith('vocals.') and (file.endswith('.mp3') or file.endswith('.wav')):
                            possible_paths.append(root)
            except Exception as e:
                logger.warning(f"扫描输出目录时出错: {e}")

            # 检查哪个路径存在
            actual_output_dir = None
            for path in possible_paths:
                if os.path.exists(path):
                    # 检查是否包含vocals文件
                    vocals_files = []
                    try:
                        for file in os.listdir(path):
                            if file.startswith('vocals.') and (file.endswith('.mp3') or file.endswith('.wav')):
                                vocals_files.append(file)
                    except:
                        continue

                    if vocals_files:
                        actual_output_dir = path
                        logger.info(f"找到Demucs输出目录: {actual_output_dir}")
                        logger.info(f"包含vocals文件: {vocals_files}")
                        break

            if actual_output_dir:
                logger.info(f"Demucs输出文件检查通过: {actual_output_dir}")
                return True
            else:
                logger.error(f"Demucs输出文件不存在，检查了以下路径:")
                for path in possible_paths:
                    logger.error(f"  - {path}")

                # 列出temp_dir下的所有文件和目录用于调试
                try:
                    logger.info(f"temp_dir内容:")
                    for root, dirs, files in os.walk(temp_dir):
                        level = root.replace(temp_dir, '').count(os.sep)
                        indent = ' ' * 2 * level
                        logger.info(f"{indent}{os.path.basename(root)}/")
                        subindent = ' ' * 2 * (level + 1)
                        for file in files:
                            logger.info(f"{subindent}{file}")
                except Exception as e:
                    logger.error(f"列出目录内容失败: {e}")

                return False

        except Exception as e:
            logger.error(f"Demucs分离异常: {str(e)}")
            return False

    async def _merge_separated_audio_with_video(self, input_video: str, temp_dir: str,
                                              vocals_file: str, instrumental_file: str, output_type: str,
                                              audio_base_name: str = None) -> bool:
        """将AI分离后的音频与原视频合并"""
        try:
            # 动态查找Demucs输出文件
            # 如果提供了audio_base_name，使用它；否则使用视频文件名
            base_name = audio_base_name if audio_base_name else os.path.splitext(os.path.basename(input_video))[0]
            logger.info(f"查找Demucs输出文件，使用base_name: {base_name}")

            # 查找vocals和instrumental文件
            vocals_audio = None
            instrumental_audio = None

            # 可能的输出路径
            possible_dirs = [
                os.path.join(temp_dir, "separated", "htdemucs", base_name),
                os.path.join(temp_dir, "separated", "htdemucs_6s", base_name),
                os.path.join(temp_dir, "separated", "htdemucs_ft", base_name),
                os.path.join(temp_dir, "htdemucs", base_name),
            ]

            # 扫描所有可能的目录
            for root, dirs, files in os.walk(temp_dir):
                if base_name in dirs:
                    possible_dirs.append(os.path.join(root, base_name))

            # 查找vocals和instrumental文件
            for dir_path in possible_dirs:
                if not os.path.exists(dir_path):
                    continue

                try:
                    for file in os.listdir(dir_path):
                        file_path = os.path.join(dir_path, file)
                        if file.startswith('vocals.') and (file.endswith('.mp3') or file.endswith('.wav')):
                            vocals_audio = file_path
                        elif file.startswith('no_vocals.') and (file.endswith('.mp3') or file.endswith('.wav')):
                            instrumental_audio = file_path

                    if vocals_audio and instrumental_audio:
                        logger.info(f"找到Demucs输出文件:")
                        logger.info(f"  人声: {vocals_audio}")
                        logger.info(f"  伴奏: {instrumental_audio}")
                        break
                except Exception as e:
                    logger.warning(f"扫描目录失败 {dir_path}: {e}")
                    continue

            # 检查分离结果是否存在
            if not vocals_audio or not instrumental_audio:
                logger.error(f"Demucs分离结果不存在，检查了以下目录:")
                for dir_path in possible_dirs:
                    logger.error(f"  - {dir_path}")

                # 列出temp_dir下的所有音频文件用于调试
                logger.info("temp_dir中的所有音频文件:")
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if file.endswith(('.mp3', '.wav', '.flac', '.m4a')):
                            logger.info(f"  {os.path.join(root, file)}")

                return False

            # 根据输出类型合并视频
            tasks = []

            if output_type in ['vocals', 'both']:
                # 合并人声版本
                vocals_cmd = [
                    'ffmpeg', '-y',
                    '-i', input_video,  # 原视频
                    '-i', vocals_audio,  # AI分离的人声
                    '-c:v', 'copy',  # 复制视频流
                    '-c:a', 'aac', '-b:a', '128k',  # 音频编码
                    '-map', '0:v:0',  # 映射视频流
                    '-map', '1:a:0',  # 映射人声音频流
                    '-shortest',  # 以最短流为准
                    vocals_file
                ]
                tasks.append(('vocals', vocals_cmd))

            if output_type in ['instrumental', 'both']:
                # 合并伴奏版本
                instrumental_cmd = [
                    'ffmpeg', '-y',
                    '-i', input_video,  # 原视频
                    '-i', instrumental_audio,  # AI分离的伴奏
                    '-c:v', 'copy',  # 复制视频流
                    '-c:a', 'aac', '-b:a', '128k',  # 音频编码
                    '-map', '0:v:0',  # 映射视频流
                    '-map', '1:a:0',  # 映射伴奏音频流
                    '-shortest',  # 以最短流为准
                    instrumental_file
                ]
                tasks.append(('instrumental', instrumental_cmd))

            # 执行合并任务
            for task_name, cmd in tasks:
                logger.info(f"开始合并AI分离的{task_name}版本")
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                _, stderr = await process.communicate()

                if process.returncode != 0:
                    error_msg = stderr.decode('utf-8', errors='ignore')
                    logger.error(f"AI{task_name}版本合并失败: {error_msg}")
                    return False
                else:
                    logger.info(f"AI{task_name}版本合并成功")

            return True

        except Exception as e:
            logger.error(f"AI分离音频合并异常: {str(e)}")
            return False
