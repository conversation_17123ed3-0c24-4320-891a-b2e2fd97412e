#!/usr/bin/env python3
"""
初始化AI提供商配置的脚本
创建一些默认的AI提供商配置供用户使用
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "thunderhub"

# 默认AI提供商配置
DEFAULT_AI_PROVIDERS = [
    {
        "name": "OpenAI GPT-4",
        "type": "openai",
        "endpoint": "https://api.openai.com",
        "api_key": "your-openai-api-key-here",
        "model": "gpt-4",
        "max_tokens": 4096,
        "temperature": 0.7,
        "enabled": False  # 默认禁用，需要用户配置API密钥后启用
    },
    {
        "name": "OpenAI GPT-3.5 Turbo",
        "type": "openai",
        "endpoint": "https://api.openai.com",
        "api_key": "your-openai-api-key-here",
        "model": "gpt-3.5-turbo",
        "max_tokens": 4096,
        "temperature": 0.7,
        "enabled": False
    },
    {
        "name": "Azure OpenAI",
        "type": "azure",
        "endpoint": "https://your-resource.openai.azure.com",
        "api_key": "your-azure-api-key-here",
        "model": "gpt-35-turbo",
        "max_tokens": 4096,
        "temperature": 0.7,
        "enabled": False
    },
    {
        "name": "Claude 3 Sonnet",
        "type": "claude",
        "endpoint": "https://api.anthropic.com",
        "api_key": "your-claude-api-key-here",
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 4096,
        "temperature": 0.7,
        "enabled": False
    },
    {
        "name": "Claude 3 Haiku",
        "type": "claude",
        "endpoint": "https://api.anthropic.com",
        "api_key": "your-claude-api-key-here",
        "model": "claude-3-haiku-20240307",
        "max_tokens": 4096,
        "temperature": 0.7,
        "enabled": False
    }
]

async def init_ai_providers():
    """初始化AI提供商配置"""
    try:
        # 连接数据库
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
        
        logger.info("开始初始化AI提供商配置...")
        
        # 检查是否已经有AI提供商配置
        existing_count = await db.ai_providers.count_documents({})
        if existing_count > 0:
            logger.info(f"已存在 {existing_count} 个AI提供商配置，跳过初始化")
            return
        
        # 插入默认配置
        success_count = 0
        for provider_config in DEFAULT_AI_PROVIDERS:
            try:
                now = datetime.utcnow()
                provider_config.update({
                    "created_at": now,
                    "updated_at": now
                })
                
                result = await db.ai_providers.insert_one(provider_config)
                logger.info(f"成功创建AI提供商: {provider_config['name']} (ID: {result.inserted_id})")
                success_count += 1
                
            except Exception as e:
                logger.error(f"创建AI提供商失败: {provider_config['name']}, 错误: {str(e)}")
        
        logger.info(f"AI提供商初始化完成，成功创建 {success_count} 个配置")
        logger.info("请在系统配置中更新API密钥并启用相应的提供商")
        
    except Exception as e:
        logger.error(f"初始化AI提供商配置失败: {str(e)}")
    finally:
        client.close()

async def init_copywriting_templates():
    """初始化文案二创模版"""
    try:
        # 连接数据库
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
        
        logger.info("开始初始化文案二创模版...")
        
        # 检查是否已经有文案相关的模版
        existing_count = await db.ai_role_templates.count_documents({"category": {"$in": ["文案创作", "热点营销", "情感营销", "平台专精"]}})
        if existing_count > 0:
            logger.info(f"已存在 {existing_count} 个文案相关模版，跳过初始化")
            return
        
        # 文案二创模版数据
        templates = [
            {
                "name": "爆款文案改写师",
                "description": "专业的文案改写专家，擅长将原创内容转化为更具吸引力的爆款文案",
                "category": "文案创作",
                "tags": ["文案改写", "爆款内容", "社交媒体", "内容优化"],
                "system_prompt": """你是一位资深的文案改写专家，拥有10年以上的内容营销经验。你的专长是：
1. 深度理解原文的核心价值和情感诉求
2. 运用多种文案技巧提升内容吸引力
3. 保持原意的同时增强传播效果
4. 针对不同平台优化文案风格

改写原则：
- 保持原文核心观点和价值不变
- 增强情感共鸣和用户参与感
- 优化标题和开头的吸引力
- 适配目标平台的用户习惯
- 确保内容的原创性和合规性

请始终以用户体验为中心，创作出既有吸引力又有价值的内容。""",
                "user_prompt_template": """请帮我改写以下文案：

【原文内容】：{original_content}

【目标平台】：{platform}

【改写要求】：
- 风格：{style}
- 长度：{length}
- 重点：{focus}

【特殊需求】：{special_requirements}

请提供3个不同风格的改写版本，并说明每个版本的特点和适用场景。""",
                "enabled": True
            },
            {
                "name": "小红书种草专家",
                "description": "专门针对小红书平台的种草文案专家，熟悉平台特色和用户习惯",
                "category": "平台专精",
                "tags": ["小红书", "种草文案", "产品推荐", "用户转化"],
                "system_prompt": """你是一位小红书种草文案专家，深度了解小红书平台的内容生态和用户特点。你的专业能力：
1. 熟悉小红书用户的消费心理和决策路径
2. 掌握小红书平台的内容规范和推荐机制
3. 擅长创作真实可信的种草内容
4. 善于运用小红书特有的表达方式和emoji

创作特点：
- 真实体验感强于广告宣传
- 详细的使用心得和效果对比
- 适当的价格敏感度分析
- 贴近生活的使用场景描述
- 符合小红书社区氛围的表达方式

请确保内容真实可信，避免夸大宣传。""",
                "user_prompt_template": """请为我创作小红书种草文案：

【产品信息】：
- 产品名称：{product_name}
- 产品类别：{product_category}
- 主要功效：{main_benefits}
- 价格区间：{price_range}

【种草角度】：{seeding_angle}

【目标用户】：{target_users}

请创作包含标题、正文、标签的完整小红书笔记内容，并提供配图建议。""",
                "enabled": True
            },
            {
                "name": "情感共鸣文案师",
                "description": "深谙用户心理，擅长创造情感共鸣的文案创作专家",
                "category": "情感营销",
                "tags": ["情感共鸣", "用户心理", "故事化", "情感营销"],
                "system_prompt": """你是一位情感共鸣文案专家，深度理解用户心理和情感需求。你的专业技能：
1. 精准洞察目标用户的情感痛点
2. 运用故事化手法增强代入感
3. 创造强烈的情感共鸣和认同感
4. 平衡情感表达与商业目标

创作理念：
- 真实情感胜过华丽辞藻
- 用户体验优于产品功能
- 情感连接促进行动转化
- 正向价值引导用户行为
- 适度情感避免过度煽情

请始终以真诚和温暖的态度创作内容。""",
                "user_prompt_template": """请为我创作具有情感共鸣的文案：

【目标用户】：{target_user}
【产品/服务】：{product_service}
【情感主题】：{emotional_theme}
【内容场景】：{content_scenario}
【期望效果】：{expected_effect}

请创作一篇能够触动用户内心的文案，包含具体的情感触发点和行动引导。""",
                "enabled": True
            }
        ]
        
        # 插入模版
        success_count = 0
        for template in templates:
            try:
                now = datetime.utcnow()
                template.update({
                    "created_at": now,
                    "updated_at": now
                })
                
                result = await db.ai_role_templates.insert_one(template)
                logger.info(f"成功创建AI角色模版: {template['name']} (ID: {result.inserted_id})")
                success_count += 1
                
            except Exception as e:
                logger.error(f"创建AI角色模版失败: {template['name']}, 错误: {str(e)}")
        
        logger.info(f"文案二创模版初始化完成，成功创建 {success_count} 个模版")
        
    except Exception as e:
        logger.error(f"初始化文案二创模版失败: {str(e)}")
    finally:
        client.close()

async def main():
    """主函数"""
    logger.info("开始初始化AI配置...")
    
    # 初始化AI提供商
    await init_ai_providers()
    
    # 初始化文案二创模版
    await init_copywriting_templates()
    
    logger.info("AI配置初始化完成！")
    logger.info("请在系统配置中：")
    logger.info("1. 更新AI提供商的API密钥")
    logger.info("2. 启用相应的AI提供商")
    logger.info("3. 测试AI角色模版功能")

if __name__ == "__main__":
    asyncio.run(main())
