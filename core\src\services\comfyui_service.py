"""
ComfyUI集成服务 - Core服务版本
提供与ComfyUI的连接、工作流管理、任务执行等功能
"""

import logging
import asyncio
import json
import uuid
import os
import aiohttp
import websockets
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import time
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return await func(self, *args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (2 ** attempt)  # 指数退避
                        logger.warning(f"第{attempt + 1}次尝试失败，{wait_time}秒后重试: {str(e)}")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"所有重试都失败了: {str(e)}")
            raise last_exception
        return wrapper
    return decorator

class ComfyUIService:
    """ComfyUI服务客户端"""

    def __init__(self, host: str = "localhost", port: int = 8188, max_retries: int = 3, timeout: int = 30):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.ws_url = f"ws://{host}:{port}/ws"
        self.client_id = str(uuid.uuid4())
        self.connected = False
        self.session = None
        self.websocket = None
        self.max_retries = max_retries
        self.timeout = timeout
        self.last_heartbeat = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()

    async def connect(self):
        """连接到ComfyUI服务"""
        try:
            # 创建HTTP会话，配置连接池
            connector = aiohttp.TCPConnector(
                limit=100,  # 总连接池大小
                limit_per_host=30,  # 每个主机的连接数
                ttl_dns_cache=300,  # DNS缓存时间
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.timeout),
                headers={
                    'User-Agent': 'ThunderHub-ComfyUI-Client/1.0',
                    'Accept': 'application/json'
                }
            )

            # 测试HTTP连接
            async with self.session.get(f"{self.base_url}/system_stats") as response:
                if response.status == 200:
                    logger.info(f"成功连接到ComfyUI服务: {self.base_url}")
                    self.connected = True
                    self.last_heartbeat = time.time()

                    # 启动心跳检查
                    asyncio.create_task(self._heartbeat_loop())
                    return True
                else:
                    logger.error(f"ComfyUI服务响应异常: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"连接ComfyUI服务异常: {str(e)}")
            if self.session:
                await self.session.close()
                self.session = None
            return False

    async def _heartbeat_loop(self):
        """心跳检查循环"""
        while self.connected and self.session:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                if self.connected:
                    await self._check_health()
            except Exception as e:
                logger.error(f"心跳检查失败: {str(e)}")
                break

    async def _check_health(self):
        """健康检查"""
        try:
            async with self.session.get(f"{self.base_url}/system_stats", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    self.last_heartbeat = time.time()
                    return True
                else:
                    logger.warning(f"健康检查失败: {response.status}")
                    return False
        except Exception as e:
            logger.warning(f"健康检查异常: {str(e)}")
            return False

    async def disconnect(self):
        """断开连接"""
        try:
            if self.websocket:
                await self.websocket.close()
                self.websocket = None

            if self.session:
                await self.session.close()
                self.session = None

            self.connected = False
            logger.info("已断开ComfyUI服务连接")

        except Exception as e:
            logger.error(f"断开ComfyUI连接异常: {str(e)}")
            
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接状态"""
        try:
            if not self.connected:
                success = await self.connect()
                if not success:
                    return {
                        "success": False,
                        "status": "disconnected",
                        "error": "无法连接到ComfyUI服务"
                    }

            # 获取系统状态
            async with self.session.get(f"{self.base_url}/system_stats") as response:
                if response.status == 200:
                    system_stats = await response.json()
                    return {
                        "success": True,
                        "status": "connected",
                        "system_stats": system_stats,
                        "base_url": self.base_url
                    }
                else:
                    return {
                        "success": False,
                        "status": "disconnected",
                        "error": f"服务响应异常: {response.status}"
                    }

        except Exception as e:
            return {
                "success": False,
                "status": "disconnected",
                "error": str(e)
            }
            
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            if not self.connected:
                await self.connect()

            async with self.session.get(f"{self.base_url}/queue") as response:
                if response.status == 200:
                    queue_data = await response.json()
                    return {
                        "success": True,
                        "queue": queue_data
                    }
                else:
                    return {
                        "success": False,
                        "error": f"获取队列状态失败: {response.status}"
                    }

        except Exception as e:
            logger.error(f"获取队列状态失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
            
    @retry_on_failure(max_retries=3, delay=1.0)
    async def submit_workflow(self, workflow: Dict[str, Any], prompt_id: Optional[str] = None) -> Dict[str, Any]:
        """提交工作流到ComfyUI"""
        try:
            if not self.connected:
                await self.connect()

            if not prompt_id:
                prompt_id = str(uuid.uuid4())

            # 构建提交数据
            submit_data = {
                "prompt": workflow,
                "client_id": self.client_id
            }

            logger.info(f"提交工作流到ComfyUI: {prompt_id}")

            async with self.session.post(
                f"{self.base_url}/prompt",
                json=submit_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        "success": True,
                        "prompt_id": result.get("prompt_id", prompt_id),
                        "result": result
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"提交失败 ({response.status}): {error_text}"
                    }

        except Exception as e:
            logger.error(f"提交工作流失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
            
    @retry_on_failure(max_retries=2, delay=0.5)
    async def get_workflow_status(self, prompt_id: str) -> Dict[str, Any]:
        """获取工作流执行状态"""
        try:
            if not self.connected:
                await self.connect()

            # 先检查队列中是否还在执行
            queue_status = await self.get_queue_status()
            if queue_status["success"]:
                queue_data = queue_status["queue"]

                # 检查是否在运行队列中
                for running_item in queue_data.get("queue_running", []):
                    if running_item[1] == prompt_id:
                        return {
                            "success": True,
                            "status": "running",
                            "progress": running_item[2] if len(running_item) > 2 else 0
                        }

                # 检查是否在等待队列中
                for pending_item in queue_data.get("queue_pending", []):
                    if pending_item[1] == prompt_id:
                        return {
                            "success": True,
                            "status": "pending"
                        }

            # 检查历史记录
            async with self.session.get(f"{self.base_url}/history/{prompt_id}") as response:
                if response.status == 200:
                    history_data = await response.json()
                    if prompt_id in history_data:
                        return {
                            "success": True,
                            "status": "completed",
                            "history": history_data[prompt_id]
                        }
                    else:
                        return {
                            "success": True,
                            "status": "not_found"
                        }
                else:
                    return {
                        "success": False,
                        "error": f"查询历史失败: {response.status}"
                    }

        except Exception as e:
            logger.error(f"获取工作流状态失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
            
    async def get_output_files(self, prompt_id: str) -> List[str]:
        """获取输出文件列表"""
        try:
            # 获取历史记录
            status = await self.get_workflow_status(prompt_id)
            if not status["success"] or status["status"] != "completed":
                return []

            history = status.get("history", {})
            outputs = history.get("outputs", {})

            files = []
            for node_id, node_outputs in outputs.items():
                # 处理图片输出
                if "images" in node_outputs:
                    for image in node_outputs["images"]:
                        if "filename" in image:
                            # 下载文件到本地
                            local_file = await self._download_output_file(
                                image["filename"],
                                image.get("subfolder", ""),
                                image.get("type", "output")
                            )
                            if local_file:
                                files.append(local_file)

                # 处理视频输出
                if "videos" in node_outputs:
                    for video in node_outputs["videos"]:
                        if "filename" in video:
                            local_file = await self._download_output_file(
                                video["filename"],
                                video.get("subfolder", ""),
                                video.get("type", "output")
                            )
                            if local_file:
                                files.append(local_file)

            return files

        except Exception as e:
            logger.error(f"获取输出文件失败: {str(e)}")
            return []

    async def _download_output_file(self, filename: str, subfolder: str = "", file_type: str = "output") -> Optional[str]:
        """下载ComfyUI输出文件到本地"""
        try:
            # 构建下载URL
            params = {
                "filename": filename,
                "type": file_type
            }
            if subfolder:
                params["subfolder"] = subfolder

            async with self.session.get(f"{self.base_url}/view", params=params) as response:
                if response.status == 200:
                    # 确保输出目录存在
                    output_dir = os.getenv("COMFYUI_OUTPUT_DIR", "data/comfyui_output")
                    os.makedirs(output_dir, exist_ok=True)

                    # 构建本地文件路径
                    local_filename = f"{uuid.uuid4()}_{filename}"
                    local_path = os.path.join(output_dir, local_filename)

                    # 保存文件
                    with open(local_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)

                    logger.info(f"下载文件成功: {filename} -> {local_path}")
                    return local_path
                else:
                    logger.error(f"下载文件失败: {filename}, 状态码: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"下载文件异常: {filename}, 错误: {str(e)}")
            return None

    async def get_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        try:
            if not self.connected:
                await self.connect()

            async with self.session.get(f"{self.base_url}/object_info") as response:
                if response.status == 200:
                    object_info = await response.json()

                    # 提取模型信息
                    models = {
                        "checkpoints": [],
                        "loras": [],
                        "vae": [],
                        "controlnet": [],
                        "upscale_models": []
                    }

                    # 解析模型信息
                    for node_class, node_info in object_info.items():
                        if "input" in node_info and "required" in node_info["input"]:
                            required = node_info["input"]["required"]

                            # 检查是否有模型选择字段
                            for field_name, field_info in required.items():
                                if isinstance(field_info, list) and len(field_info) > 0:
                                    if "ckpt_name" in field_name or "model_name" in field_name:
                                        if isinstance(field_info[0], list):
                                            models["checkpoints"].extend(field_info[0])
                                    elif "lora_name" in field_name:
                                        if isinstance(field_info[0], list):
                                            models["loras"].extend(field_info[0])
                                    elif "vae_name" in field_name:
                                        if isinstance(field_info[0], list):
                                            models["vae"].extend(field_info[0])

                    # 去重
                    for key in models:
                        models[key] = list(set(models[key]))

                    return {
                        "success": True,
                        "models": models
                    }
                else:
                    return {
                        "success": False,
                        "error": f"获取模型列表失败: {response.status}"
                    }

        except Exception as e:
            logger.error(f"获取模型列表失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def interrupt_execution(self) -> Dict[str, Any]:
        """中断当前执行"""
        try:
            if not self.connected:
                await self.connect()

            async with self.session.post(f"{self.base_url}/interrupt") as response:
                if response.status == 200:
                    return {"success": True}
                else:
                    return {
                        "success": False,
                        "error": f"中断执行失败: {response.status}"
                    }

        except Exception as e:
            logger.error(f"中断执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def clear_queue(self) -> Dict[str, Any]:
        """清空队列"""
        try:
            if not self.connected:
                await self.connect()

            # 先中断当前执行
            await self.interrupt_execution()

            # 清空队列
            clear_data = {"clear": True}
            async with self.session.post(f"{self.base_url}/queue", json=clear_data) as response:
                if response.status == 200:
                    return {"success": True}
                else:
                    return {
                        "success": False,
                        "error": f"清空队列失败: {response.status}"
                    }

        except Exception as e:
            logger.error(f"清空队列失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def connect_websocket(self) -> bool:
        """连接WebSocket用于实时监控"""
        try:
            if self.websocket:
                return True

            self.websocket = await websockets.connect(
                f"{self.ws_url}?clientId={self.client_id}",
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )

            logger.info(f"WebSocket连接成功: {self.ws_url}")

            # 启动消息监听
            asyncio.create_task(self._websocket_listener())
            return True

        except Exception as e:
            logger.error(f"WebSocket连接失败: {str(e)}")
            return False

    async def _websocket_listener(self):
        """WebSocket消息监听器"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self._handle_websocket_message(data)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析WebSocket消息: {message}")
                except Exception as e:
                    logger.error(f"处理WebSocket消息失败: {str(e)}")
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket连接已关闭")
        except Exception as e:
            logger.error(f"WebSocket监听异常: {str(e)}")
        finally:
            self.websocket = None

    async def _handle_websocket_message(self, data: Dict[str, Any]):
        """处理WebSocket消息"""
        message_type = data.get("type")

        if message_type == "status":
            # 处理状态更新
            status_data = data.get("data", {})
            logger.debug(f"收到状态更新: {status_data}")

        elif message_type == "progress":
            # 处理进度更新
            progress_data = data.get("data", {})
            logger.debug(f"收到进度更新: {progress_data}")

        elif message_type == "executed":
            # 处理执行完成
            executed_data = data.get("data", {})
            logger.info(f"节点执行完成: {executed_data}")

        elif message_type == "execution_start":
            # 处理执行开始
            logger.info(f"开始执行工作流: {data.get('data', {})}")

        elif message_type == "execution_cached":
            # 处理缓存执行
            logger.info(f"使用缓存结果: {data.get('data', {})}")

    async def get_real_time_progress(self, prompt_id: str) -> Dict[str, Any]:
        """获取实时进度（通过WebSocket）"""
        try:
            if not self.websocket:
                await self.connect_websocket()

            # 这里可以实现基于prompt_id的进度跟踪
            # 实际实现需要维护一个进度状态字典
            return {
                "success": True,
                "prompt_id": prompt_id,
                "progress": 0.0,
                "status": "monitoring"
            }

        except Exception as e:
            logger.error(f"获取实时进度失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局ComfyUI服务实例
_comfyui_service = None

def get_comfyui_service() -> ComfyUIService:
    """获取ComfyUI服务实例"""
    global _comfyui_service
    
    if _comfyui_service is None:
        # 从环境变量获取配置
        host = os.getenv("COMFYUI_HOST", "localhost")
        port = int(os.getenv("COMFYUI_PORT", "8188"))
        
        _comfyui_service = ComfyUIService(host=host, port=port)
        
    return _comfyui_service

async def test_comfyui_connection() -> Dict[str, Any]:
    """测试ComfyUI连接"""
    try:
        service = get_comfyui_service()
        result = await service.test_connection()
        
        if result["success"]:
            # 获取队列状态
            queue_status = await service.get_queue_status()
            if queue_status["success"]:
                queue_data = queue_status["queue"]
                result["queue_size"] = len(queue_data.get("queue_pending", []))
                result["active_tasks"] = len(queue_data.get("queue_running", []))
            else:
                result["queue_size"] = 0
                result["active_tasks"] = 0
                
        return result
        
    except Exception as e:
        logger.error(f"测试ComfyUI连接失败: {str(e)}")
        return {
            "success": False,
            "status": "disconnected",
            "error": str(e)
        }
