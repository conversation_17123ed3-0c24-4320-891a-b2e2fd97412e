from pydantic import BaseModel
from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime
from typing import Dict, Any, Optional, List
import logging
import time


class DeviceModel(BaseModel):
    """设备数据模型"""
    device_id: str
    name: str
    status: str
    device_type: str
    window_info: Dict[str, str]
    process_info: Dict[str, int]
    display_info: Dict[str, int]
    created_at: datetime

    class Config:
        json_schema_extra = {
            "example": {
                "device_id": "ldplayer_1",
                "name": "测试设备1",
                "status": "online",
                "device_type": "ldplayer",
                "window_info": {
                    "top_window": "",
                    "bound_window": ""
                },
                "process_info": {
                    "pid": 1234,
                    "vbox_pid": 5678
                },
                "display_info": {
                    "width": 1080,
                    "height": 1920,
                    "dpi": 320
                },
                "created_at": datetime.now()
            }
        }


class DeviceConfigSchema(BaseModel):
    """设备配置Schema"""
    config: dict

    class Config:
        json_schema_extra = {
            "example": {
                "config": {
                    "device_name": "test_device",
                    "platform": "android",
                    "version": "11.0"
                }
            }
        }

class DeviceIDSchema(BaseModel):
    """设备ID Schema"""
    id: str

    class Config:
        json_schema_extra = {
            "example": {
                "id": "device-123"
            }
        }

from redis import Redis

logger = logging.getLogger(__name__)

class RedisClient:
    """Redis状态管理"""

    def __init__(self, host='localhost', port=6379):
        self.redis = Redis(host=host, port=port, decode_responses=True)

    def update_device_status(self, device_id: str, status: Dict[str, Any]) -> bool:
        """更新设备实时状态"""
        try:
            # 确保Redis连接有效
            if self.redis is None:
                logger.error("Redis连接无效")
                return False

            key = f"device:status:{device_id}"
            self.redis.hset(key, mapping=status)
            self.redis.zadd("online_devices", {device_id: time.time()})
            self.redis.set(f"device:heartbeat:{device_id}", int(time.time()))
            return True
        except Exception as e:
            logger.error(f"更新设备状态失败: {str(e)}")
            raise

    def get_device_status(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取设备实时状态"""
        try:
            # 确保Redis连接有效
            if self.redis is None:
                logger.error("Redis连接无效")
                return None

            key = f"device:status:{device_id}"
            result = self.redis.hgetall(key)
            return result if result else None
        except Exception as e:
            logger.error(f"获取设备状态失败: {str(e)}")
            raise


class DeviceRepository:
    """设备仓库类，用于管理设备数据访问"""

    def __init__(self, db):
        self.db = db

    async def get_devices(self,
                  filter: Optional[Dict] = None,
                  skip: int = 0,
                  limit: int = 100,
                  query: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """获取设备列表

        Args:
            filter: 过滤条件（已废弃，保留向后兼容）
            skip: 跳过的记录数
            limit: 返回的最大记录数
            query: 查询条件，优先级高于filter

        Returns:
            设备列表
        """
        try:
            # 使用query参数，如果没有则使用filter
            query_filter = query or filter or {}
            logger.debug(f"查询设备列表: query={query_filter}, skip={skip}, limit={limit}")

            # 确保数据库连接有效
            if self.db is None:
                logger.error("数据库连接无效")
                return []

            # 查询设备集合
            try:
                # 使用异步MongoDB客户端
                cursor = self.db.devices.find(query_filter).skip(skip).limit(limit)
                devices = await cursor.to_list(length=limit)

                logger.debug(f"查询到 {len(devices)} 个设备")

                # 如果没有设备，可能是新系统，返回空列表
                if not devices:
                    logger.info("未找到任何设备，可能是新系统或数据库为空")
                    return []

                return devices
            except Exception as e:
                logger.error(f"查询设备集合失败: {str(e)}", exc_info=True)
                # 返回空列表而不是抛出异常，避免前端崩溃
                return []
        except Exception as e:
            logger.error(f"获取设备列表失败: {str(e)}", exc_info=True)
            # 返回空列表而不是抛出异常，避免前端崩溃
            return []

    async def get_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取单个设备"""
        try:
            # 确保数据库连接有效
            if self.db is None:
                logger.error("数据库连接无效")
                return None

            # 检查是否使用的是异步MongoDB客户端
            is_async = hasattr(self.db.devices, 'find_one') and callable(getattr(self.db.devices.find_one, '__await__', None))

            # 尝试直接使用字符串ID查询
            if is_async:
                device = await self.db.devices.find_one({"_id": device_id})
            else:
                device = self.db.devices.find_one({"_id": device_id})

            if device:
                return device

            # 如果找不到，尝试使用id字段查询
            if is_async:
                device = await self.db.devices.find_one({"id": device_id})
            else:
                device = self.db.devices.find_one({"id": device_id})

            if device:
                return device

            # 记录详细日志
            logger.warning(f"无法找到设备ID: {device_id}")
            return None
        except Exception as e:
            logger.error(f"获取设备{device_id}失败: {str(e)}", exc_info=True)
            raise

    async def update_device(self, device_id: str, update_data: Dict[str, Any]) -> bool:
        """更新设备信息"""
        try:
            # 确保数据库连接有效
            if self.db is None:
                logger.error("数据库连接无效")
                return False

            # 检查是否使用的是异步MongoDB客户端
            is_async = hasattr(self.db.devices, 'update_one') and callable(getattr(self.db.devices.update_one, '__await__', None))

            # 尝试使用_id字段更新
            if is_async:
                result = await self.db.devices.update_one(
                    {"_id": device_id},
                    {"$set": update_data}
                )
            else:
                result = self.db.devices.update_one(
                    {"_id": device_id},
                    {"$set": update_data}
                )

            # 如果没有更新任何文档，尝试使用id字段
            if result.matched_count == 0:
                logger.debug(f"使用_id={device_id}未找到匹配文档，尝试使用id字段")
                if is_async:
                    result = await self.db.devices.update_one(
                        {"id": device_id},
                        {"$set": update_data}
                    )
                else:
                    result = self.db.devices.update_one(
                        {"id": device_id},
                        {"$set": update_data}
                    )

            # 如果仍然没有找到，可能需要创建新设备
            if result.matched_count == 0:
                logger.warning(f"设备{device_id}不存在，考虑创建新设备")
                # 这里可以选择自动创建设备或返回失败
                return False

            return result.modified_count > 0
        except Exception as e:
            logger.error(f"更新设备{device_id}失败: {str(e)}", exc_info=True)
            raise

    def create_device(self, device_data: Dict[str, Any]) -> str:
        """创建设备"""
        try:
            result = self.db.devices.insert_one(device_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"创建设备失败: {str(e)}")
            raise

    async def delete_device(self, device_id: str) -> bool:
        """删除设备"""
        try:
            # 确保数据库连接有效
            if self.db is None:
                logger.error("数据库连接无效")
                return False

            # 检查是否使用的是异步MongoDB客户端
            is_async = hasattr(self.db.devices, 'delete_one') and callable(getattr(self.db.devices.delete_one, '__await__', None))

            if is_async:
                result = await self.db.devices.delete_one({"_id": device_id})
            else:
                result = self.db.devices.delete_one({"_id": device_id})

            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除设备{device_id}失败: {str(e)}")
            raise

    async def save_config_snapshot(self, device_id: str, config: Dict[str, Any]) -> str:
        """保存设备配置快照"""
        try:
            # 确保数据库连接有效
            if self.db is None:
                logger.error("数据库连接无效")
                return ""

            snapshot = {
                "device_id": device_id,
                "config": config,
                "timestamp": time.time(),
                "version": self._generate_version_hash(config)
            }

            # 检查是否使用的是异步MongoDB客户端
            is_async = hasattr(self.db.device_config_snapshots, 'insert_one') and callable(getattr(self.db.device_config_snapshots.insert_one, '__await__', None))

            if is_async:
                result = await self.db.device_config_snapshots.insert_one(snapshot)
            else:
                result = self.db.device_config_snapshots.insert_one(snapshot)

            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"保存配置快照失败: {str(e)}")
            raise

    def get_latest_config(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取最新设备配置"""
        try:
            return self.db.device_config_snapshots.find_one(
                {"device_id": device_id},
                sort=[("timestamp", -1)]
            )
        except Exception as e:
            logger.error(f"获取最新配置失败: {str(e)}")
            raise

    def _generate_version_hash(self, config: Dict[str, Any]) -> str:
        """生成配置版本哈希"""
        import hashlib
        config_str = str(sorted(config.items()))
        return hashlib.md5(config_str.encode()).hexdigest()

    async def bulk_insert_devices(self, devices: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量插入设备数据"""
        try:
            if not devices:
                logger.warning("批量插入的设备列表为空")
                return {"inserted_ids": [], "inserted_count": 0}

            # 确保每个设备有_id字段
            for d in devices:
                if "_id" not in d:
                    d["_id"] = d.get("device_id", str(hash(str(d))))

            result = await self.db.devices.insert_many(devices)
            return {
                "inserted_ids": [str(id) for id in result.inserted_ids],
                "inserted_count": len(result.inserted_ids)
            }
        except Exception as e:
            logger.error(f"批量插入设备失败: {str(e)}")
            raise

    async def purge_all_devices(self) -> int:
        """清空所有设备数据"""
        try:
            result = await self.db.devices.delete_many({})
            deleted_count = result.deleted_count
            logger.info(f"已删除{deleted_count}条设备记录")
            return deleted_count
        except Exception as e:
            logger.error(f"清空设备数据失败: {str(e)}")
            raise

    def cleanup_test_data(self) -> int:
        """清理测试数据"""
        try:
            result = self.db.devices.delete_many({
                "id": {"$in": ["device1", "device2"]}
            })
            logger.info(f"已清理{result.deleted_count}条测试数据")
            return result.deleted_count
        except Exception as e:
            logger.error(f"清理测试数据失败: {str(e)}")
            raise

class DeviceStatusHistoryService:
    """设备状态历史服务"""

    def __init__(self, db_name: str = "thunderhub"):
        self.client = AsyncIOMotorClient("mongodb://localhost:27017")
        self.db = self.client[db_name]

    async def record_status(self, device_id: str, status_data: Dict[str, Any]) -> str:
        """记录设备状态"""
        try:
            # 确保数据库连接有效
            if self.db is None:
                logger.error("数据库连接无效")
                return ""

            record = {
                "device_id": device_id,
                "timestamp": time.time(),
                "status_data": status_data
            }
            result = await self.db.device_status_history.insert_one(record)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"记录设备状态失败: {str(e)}")
            raise

    async def get_status_history(self, device_id: str, hours: int = 24) -> List[Dict[str, Any]]:
        """获取设备状态历史"""
        try:
            # 确保数据库连接有效
            if self.db is None:
                logger.error("数据库连接无效")
                return []

            cutoff = time.time() - hours * 3600
            cursor = self.db.device_status_history.find({
                "device_id": device_id,
                "timestamp": {"$gte": cutoff}
            }).sort("timestamp", -1)
            return await cursor.to_list(length=None)
        except Exception as e:
            logger.error(f"获取状态历史失败: {str(e)}")
            raise