from typing import Dict, Any, Optional
from appium.webdriver.webdriver import WebDriver
from appium.options.common import AppiumOptions
import logging

class AppiumDriverService:
    """Appium驱动管理服务"""
    
    def __init__(self):
        self._drivers: Dict[str, WebDriver] = {}
        self.logger = logging.getLogger(__name__)
        
    async def create_driver(
        self,
        device_id: str,
        capabilities: Dict[str, Any],
        server_url: str = "http://127.0.0.1:4723"
    ) -> WebDriver:
        """创建Appium驱动会话"""
        options = AppiumOptions().load_capabilities(capabilities)
        
        try:
            driver = WebDriver(
                command_executor=server_url,
                options=options
            )
            self._drivers[device_id] = driver
            self.logger.info(f"Appium会话建立成功: {device_id}")
            return driver
        except Exception as e:
            self.logger.error(f"创建Appium驱动失败: {str(e)}")
            raise
            
    async def quit_driver(self, device_id: str) -> bool:
        """关闭指定设备的驱动会话"""
        if driver := self._drivers.get(device_id):
            try:
                driver.quit()
                del self._drivers[device_id]
                return True
            except Exception as e:
                # 检查是否是会话已终止的错误
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in [
                    'session is either terminated or not started',
                    'session not found',
                    'invalid session id',
                    'no such session'
                ]):
                    self.logger.debug(f"设备{device_id}的WebDriver会话已终止，跳过关闭: {str(e)}")
                    # 即使会话已终止，也应该从字典中删除
                    del self._drivers[device_id]
                    return True
                else:
                    self.logger.warning(f"关闭Appium会话失败: {str(e)}")
        return False
        
    def get_driver(self, device_id: str) -> Optional[WebDriver]:
        """获取已创建的驱动实例"""
        return self._drivers.get(device_id)
        
    async def restart_driver(self, device_id: str) -> Optional[WebDriver]:
        """重启驱动会话"""
        if driver := self.get_driver(device_id):
            caps = driver.capabilities
            await self.quit_driver(device_id)
            return await self.create_driver(device_id, caps)
        return None