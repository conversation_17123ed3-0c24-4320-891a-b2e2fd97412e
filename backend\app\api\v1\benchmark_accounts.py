"""
对标账号管理API
用于管理我们账号对应的竞品账号信息
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from pymongo.database import Database
from bson import ObjectId
from datetime import datetime

from app.core.security import get_current_user
from app.core.schemas.content_models import (
    BenchmarkAccount,
    BenchmarkAccountGroup,
    CreateBenchmarkAccountRequest,
    BenchmarkAccountListRequest,
    BenchmarkAccountListResponse,
    BenchmarkAccountStats
)

# 设置日志记录器
logger = logging.getLogger(__name__)

async def find_social_account_by_id(db: Database, account_id: str):
    """
    统一的账号查找函数，按优先级尝试不同的ID字段
    1. 首先尝试通过id字段查找（推荐的标准字段）
    2. 如果没找到，尝试通过_id字段查找
    3. 返回找到的账号或None
    """
    if not account_id:
        return None

    # 首先尝试通过id字段查找（标准字段）
    account = await db.social_accounts.find_one({"id": account_id})
    if account:
        # 确保返回的账号有统一的id字段
        if not account.get("id") and account.get("_id"):
            account["id"] = str(account["_id"])
        return account

    # 如果没找到，尝试通过_id字段查找
    try:
        # 尝试作为ObjectId查找
        if len(account_id) == 24:
            account = await db.social_accounts.find_one({"_id": ObjectId(account_id)})
        else:
            account = await db.social_accounts.find_one({"_id": account_id})

        if account:
            # 确保返回的账号有统一的id字段
            if not account.get("id"):
                account["id"] = str(account["_id"])
            return account
    except Exception as e:
        logger.warning(f"ObjectId转换失败: {str(e)}")

    return None

# 定义API路由
router = APIRouter(
    prefix="/api/v1/benchmark",
    tags=["benchmark_accounts"],
    dependencies=[Depends(get_current_user)]
)


def get_db(request: Request) -> Database:
    """获取数据库连接"""
    return request.app.state.mongo_db


@router.get("/accounts", response_model=BenchmarkAccountListResponse)
async def get_benchmark_accounts(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=1000, description="每页数量，分组视图可设置更大值"),
    our_account_id: Optional[str] = Query(None, description="我们的账号ID过滤"),
    platform: Optional[str] = Query(None, description="平台过滤"),
    benchmark_type: Optional[str] = Query(None, description="对标类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    search: Optional[str] = Query(None, description="搜索关键词（支持对标账号名称、描述、标签）"),
    our_account_search: Optional[str] = Query(None, description="关联账号搜索（支持账号名称、显示名称模糊匹配）"),
    db: Database = Depends(get_db)
):
    """
    获取对标账号列表
    """
    try:
        logger.info(f"获取对标账号列表: page={page}, limit={limit}, our_account_search={our_account_search}")

        # 构建查询条件
        query = {}

        if our_account_id:
            query["our_account_id"] = our_account_id

        if platform:
            query["platform"] = platform

        if benchmark_type:
            query["benchmark_type"] = benchmark_type

        if status:
            query["status"] = status

        # 处理关联账号搜索
        if our_account_search:
            logger.info(f"执行关联账号搜索: {our_account_search}")

            # 在social_accounts集合中搜索匹配的账号
            our_account_query = {
                "$or": [
                    {"username": {"$regex": our_account_search, "$options": "i"}},
                    {"display_name": {"$regex": our_account_search, "$options": "i"}},
                    {"id": {"$regex": our_account_search, "$options": "i"}}
                ]
            }

            # 查找匹配的账号ID
            matching_accounts_cursor = db.social_accounts.find(our_account_query, {"id": 1, "_id": 1})
            matching_accounts = await matching_accounts_cursor.to_list(length=None)

            if matching_accounts:
                # 提取账号ID列表（包括id字段和_id字段）
                matching_account_ids = []
                for acc in matching_accounts:
                    if acc.get("id"):
                        matching_account_ids.append(acc["id"])
                    if acc.get("_id"):
                        matching_account_ids.append(str(acc["_id"]))

                logger.info(f"找到 {len(matching_accounts)} 个匹配的关联账号，ID列表: {matching_account_ids[:5]}...")

                # 添加到查询条件中
                if "our_account_id" in query:
                    # 如果已经有our_account_id过滤，需要同时满足两个条件
                    query["$and"] = [
                        {"our_account_id": query["our_account_id"]},
                        {"our_account_id": {"$in": matching_account_ids}}
                    ]
                    del query["our_account_id"]
                else:
                    query["our_account_id"] = {"$in": matching_account_ids}
            else:
                logger.info(f"没有找到匹配的关联账号: {our_account_search}")
                # 如果没有找到匹配的账号，返回空结果
                query["our_account_id"] = {"$in": []}

        # 处理对标账号搜索
        if search:
            search_conditions = [
                {"account_name": {"$regex": search, "$options": "i"}},
                {"description": {"$regex": search, "$options": "i"}},
                {"tags": {"$in": [search]}}
            ]

            if "$and" in query:
                # 如果已经有$and条件，添加到其中
                query["$and"].append({"$or": search_conditions})
            else:
                query["$or"] = search_conditions
        
        # 计算分页
        skip = (page - 1) * limit
        
        # 查询总数
        total = await db.benchmark_accounts.count_documents(query)

        # 查询数据
        cursor = db.benchmark_accounts.find(query).sort("created_at", -1).skip(skip).limit(limit)
        accounts = await cursor.to_list(length=limit)

        # 转换ObjectId为字符串并关联我们的账号信息
        logger.info(f"开始处理 {len(accounts)} 个对标账号的关联信息")
        for account in accounts:
            account["_id"] = str(account["_id"])

            # 查找对应的我们的账号信息
            our_account_id = account.get("our_account_id")
            logger.info(f"处理对标账号: {account.get('account_name')}, our_account_id={our_account_id}")

            if our_account_id:
                logger.info(f"查找我们的账号: our_account_id={our_account_id}")

                # 统一使用标准化的账号查找逻辑
                our_account = await find_social_account_by_id(db, our_account_id)

                if our_account:
                    logger.info(f"成功找到关联账号: {our_account.get('username', our_account.get('display_name', 'Unknown'))}")
                else:
                    logger.warning(f"未找到账号 {our_account_id}")

                # 添加我们的账号信息到响应中
                if our_account:
                    # 使用统一的id字段（find_social_account_by_id已确保有id字段）
                    account["our_account_info"] = {
                        "id": our_account.get("id"),
                        "username": our_account.get("username", ""),
                        "display_name": our_account.get("display_name", ""),
                        "platform_id": our_account.get("platform_id", ""),
                        "status": our_account.get("status", "")
                    }
                    logger.info(f"成功关联账号信息: {account['our_account_info']}")
                else:
                    # 即使没找到，也提供一个占位信息，显示查找的ID
                    account["our_account_info"] = {
                        "id": our_account_id,
                        "username": f"未找到账号({our_account_id})",
                        "display_name": f"账号不存在({our_account_id})",
                        "platform_id": "",
                        "status": "not_found"
                    }
                    logger.warning(f"未找到账号 {our_account_id}，使用占位信息")
            else:
                logger.warning(f"对标账号 {account.get('account_name')} 没有our_account_id字段")
                # 为没有our_account_id的账号也添加占位信息
                account["our_account_info"] = {
                    "id": "",
                    "username": "无关联账号",
                    "display_name": "无关联账号",
                    "platform_id": "",
                    "status": "not_found"
                }
        
        # 计算是否有下一页
        has_next = skip + limit < total
        
        return BenchmarkAccountListResponse(
            items=accounts,
            total=total,
            page=page,
            limit=limit,
            has_next=has_next
        )
        
    except Exception as e:
        logger.error(f"获取对标账号列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取对标账号列表失败: {str(e)}")


@router.post("/check-url", response_model=Dict[str, Any])
async def check_account_url(
    url_data: Dict[str, str],
    db: Database = Depends(get_db)
):
    """检查对标账号URL是否已存在"""
    try:
        account_url = url_data.get("account_url")
        our_account_id = url_data.get("our_account_id")

        if not account_url:
            raise HTTPException(status_code=400, detail="缺少account_url参数")

        logger.info(f"检查URL重复: {account_url}")

        # 检查URL是否已存在
        existing = await db.benchmark_accounts.find_one({
            "account_url": account_url
        })

        if existing:
            # 查找关联账号信息
            existing_our_account = await db.social_accounts.find_one({"id": existing.get("our_account_id")})
            existing_account_name = "未知账号"
            if existing_our_account:
                existing_account_name = existing_our_account.get("display_name") or existing_our_account.get("username", "未知账号")

            # 判断是否是同一个关联账号
            is_same_our_account = existing.get("our_account_id") == our_account_id

            return {
                "exists": True,
                "is_same_our_account": is_same_our_account,
                "existing_account": {
                    "account_name": existing.get("account_name", "未知对标账号"),
                    "our_account_name": existing_account_name,
                    "our_account_id": existing.get("our_account_id"),
                    "platform": existing.get("platform", "未知平台"),
                    "benchmark_type": existing.get("benchmark_type", "未知类型")
                },
                "message": f"该URL已被关联账号 '{existing_account_name}' 的对标账号 '{existing.get('account_name', '未知对标账号')}' 使用"
            }
        else:
            return {
                "exists": False,
                "message": "URL可用"
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查URL失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"检查URL失败: {str(e)}")


@router.post("/accounts", response_model=Dict[str, Any])
async def create_benchmark_account(
    account_data: CreateBenchmarkAccountRequest,
    current_user = Depends(get_current_user),
    db: Database = Depends(get_db)
):
    """创建对标账号"""
    try:
        logger.info(f"创建对标账号: {account_data.account_name}")
        
        # 检查账号URL是否已存在（全局检查）
        existing_url = await db.benchmark_accounts.find_one({
            "account_url": account_data.account_url
        })

        if existing_url:
            # 如果是同一个关联账号下的重复
            if existing_url.get("our_account_id") == account_data.our_account_id:
                raise HTTPException(
                    status_code=400,
                    detail=f"该对标账号URL已存在于当前关联账号下：{existing_url.get('account_name', '未知账号')}"
                )
            else:
                # 如果是不同关联账号下的重复，提供更详细的信息
                # 查找关联账号信息
                other_our_account = await db.social_accounts.find_one({"id": existing_url.get("our_account_id")})
                other_account_name = "未知账号"
                if other_our_account:
                    other_account_name = other_our_account.get("display_name") or other_our_account.get("username", "未知账号")

                raise HTTPException(
                    status_code=400,
                    detail=f"该对标账号URL已存在于其他关联账号下：{other_account_name} -> {existing_url.get('account_name', '未知对标账号')}"
                )

        # 验证我们的账号是否存在
        # 尝试多种方式查找账号，因为ID可能是字符串或ObjectId格式
        our_account = None

        # 首先尝试通过id字段查找（这是主要的查找方式）
        our_account = await db.social_accounts.find_one({"id": account_data.our_account_id})

        # 如果没找到，尝试通过_id字段查找
        if not our_account:
            try:
                # 如果是24位字符串，尝试转换为ObjectId
                if len(account_data.our_account_id) == 24:
                    our_account = await db.social_accounts.find_one({"_id": ObjectId(account_data.our_account_id)})
                else:
                    # 直接使用字符串查找_id字段
                    our_account = await db.social_accounts.find_one({"_id": account_data.our_account_id})
            except Exception as e:
                logger.warning(f"尝试ObjectId转换失败: {str(e)}")

        # 记录查找结果用于调试
        logger.info(f"查找账号结果: our_account_id={account_data.our_account_id}, found={our_account is not None}")
        if our_account:
            logger.info(f"找到账号: id={our_account.get('id')}, display_name={our_account.get('display_name')}")

        if not our_account:
            raise HTTPException(status_code=400, detail=f"指定的账号不存在: {account_data.our_account_id}")
        
        # 创建对标账号数据
        benchmark_account = {
            "our_account_id": account_data.our_account_id,
            "platform": account_data.platform,
            "account_name": account_data.account_name,
            "account_url": account_data.account_url,
            "benchmark_type": account_data.benchmark_type,
            "description": account_data.description,
            "tags": account_data.tags,
            "priority": account_data.priority,
            "status": "active",
            "account_data": {
                "followers": None,
                "following": None,
                "posts_count": None,
                "avg_views": None,
                "avg_likes": None,
                "engagement_rate": None,
                "last_post_date": None,
                "growth_rate": None
            },
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "created_by": current_user if isinstance(current_user, str) else getattr(current_user, 'username', 'unknown')
        }
        
        # 插入数据库
        result = await db.benchmark_accounts.insert_one(benchmark_account)
        account_id = str(result.inserted_id)
        
        logger.info(f"对标账号创建成功: {account_id}")
        
        return {
            "account_id": account_id,
            "message": "对标账号创建成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建对标账号失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建对标账号失败: {str(e)}")


@router.get("/accounts/{account_id}", response_model=BenchmarkAccount)
async def get_benchmark_account(
    account_id: str,
    db: Database = Depends(get_db)
):
    """获取对标账号详情"""
    try:
        logger.info(f"获取对标账号详情: {account_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(account_id):
            raise HTTPException(status_code=400, detail="无效的账号ID格式")
        
        # 查询账号
        account = await db.benchmark_accounts.find_one({"_id": ObjectId(account_id)})
        if not account:
            raise HTTPException(status_code=404, detail="对标账号不存在")
        
        # 转换ObjectId为字符串
        account["_id"] = str(account["_id"])
        
        return account
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对标账号详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取对标账号详情失败: {str(e)}")


@router.put("/accounts/{account_id}")
async def update_benchmark_account(
    account_id: str,
    update_data: Dict[str, Any],
    db: Database = Depends(get_db)
):
    """更新对标账号"""
    try:
        logger.info(f"更新对标账号: {account_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(account_id):
            raise HTTPException(status_code=400, detail="无效的账号ID格式")
        
        # 检查账号是否存在
        existing = await db.benchmark_accounts.find_one({"_id": ObjectId(account_id)})
        if not existing:
            raise HTTPException(status_code=404, detail="对标账号不存在")

        # 如果更新URL，检查是否重复
        if "account_url" in update_data and update_data["account_url"] != existing["account_url"]:
            url_exists = await db.benchmark_accounts.find_one({
                "account_url": update_data["account_url"],
                "our_account_id": existing["our_account_id"],
                "_id": {"$ne": ObjectId(account_id)}
            })
            if url_exists:
                raise HTTPException(status_code=400, detail="该对标账号URL已存在")
        
        # 添加更新时间
        update_data["updated_at"] = datetime.now()
        
        # 更新数据库
        result = await db.benchmark_accounts.update_one(
            {"_id": ObjectId(account_id)},
            {"$set": update_data}
        )

        return {
            "message": "对标账号更新成功",
            "modified_count": result.modified_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新对标账号失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新对标账号失败: {str(e)}")


@router.delete("/accounts/{account_id}")
async def delete_benchmark_account(
    account_id: str,
    db: Database = Depends(get_db)
):
    """删除对标账号"""
    try:
        logger.info(f"删除对标账号: {account_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(account_id):
            raise HTTPException(status_code=400, detail="无效的账号ID格式")
        
        # 检查账号是否存在
        account = await db.benchmark_accounts.find_one({"_id": ObjectId(account_id)})
        if not account:
            raise HTTPException(status_code=404, detail="对标账号不存在")
        
        # 删除账号
        result = await db.benchmark_accounts.delete_one({"_id": ObjectId(account_id)})

        return {
            "message": "对标账号删除成功",
            "deleted_count": result.deleted_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除对标账号失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除对标账号失败: {str(e)}")


@router.get("/stats", response_model=BenchmarkAccountStats)
async def get_benchmark_stats(
    our_account_id: Optional[str] = Query(None, description="我们的账号ID过滤"),
    db: Database = Depends(get_db)
):
    """获取对标账号统计信息"""
    try:
        logger.info("获取对标账号统计信息")
        
        # 构建基础查询条件
        base_query = {}
        if our_account_id:
            base_query["our_account_id"] = our_account_id
        
        # 总数统计
        total_count = await db.benchmark_accounts.count_documents(base_query)

        # 按平台统计
        platform_pipeline = [
            {"$match": base_query},
            {"$group": {"_id": "$platform", "count": {"$sum": 1}}}
        ]
        platform_cursor = db.benchmark_accounts.aggregate(platform_pipeline)
        platform_results = await platform_cursor.to_list(length=None)
        platform_stats = {item["_id"]: item["count"] for item in platform_results}

        # 按类型统计
        type_pipeline = [
            {"$match": base_query},
            {"$group": {"_id": "$benchmark_type", "count": {"$sum": 1}}}
        ]
        type_cursor = db.benchmark_accounts.aggregate(type_pipeline)
        type_results = await type_cursor.to_list(length=None)
        type_stats = {item["_id"]: item["count"] for item in type_results}

        # 按状态统计
        status_pipeline = [
            {"$match": base_query},
            {"$group": {"_id": "$status", "count": {"$sum": 1}}}
        ]
        status_cursor = db.benchmark_accounts.aggregate(status_pipeline)
        status_results = await status_cursor.to_list(length=None)
        status_stats = {item["_id"]: item["count"] for item in status_results}

        # 表现最好的账号（按粉丝数排序）
        top_performers_cursor = db.benchmark_accounts.find(base_query).sort("account_data.followers", -1).limit(5)
        top_performers = await top_performers_cursor.to_list(length=5)
        
        # 转换ObjectId为字符串
        for account in top_performers:
            account["_id"] = str(account["_id"])
        
        return BenchmarkAccountStats(
            total_count=total_count,
            by_platform=platform_stats,
            by_type=type_stats,
            by_status=status_stats,
            top_performers=top_performers
        )
        
    except Exception as e:
        logger.error(f"获取对标账号统计失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取对标账号统计失败: {str(e)}")


@router.post("/accounts/{account_id}/update-data")
async def update_account_data(
    account_id: str,
    account_data: Dict[str, Any],
    db: Database = Depends(get_db)
):
    """更新对标账号数据（粉丝数、互动率等）"""
    try:
        logger.info(f"更新对标账号数据: {account_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(account_id):
            raise HTTPException(status_code=400, detail="无效的账号ID格式")
        
        # 检查账号是否存在
        existing = await db.benchmark_accounts.find_one({"_id": ObjectId(account_id)})
        if not existing:
            raise HTTPException(status_code=404, detail="对标账号不存在")
        
        # 更新账号数据
        update_data = {
            "account_data": account_data,
            "updated_at": datetime.now()
        }
        
        result = await db.benchmark_accounts.update_one(
            {"_id": ObjectId(account_id)},
            {"$set": update_data}
        )

        return {
            "message": "账号数据更新成功",
            "modified_count": result.modified_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新账号数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新账号数据失败: {str(e)}")
