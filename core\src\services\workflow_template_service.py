"""
ComfyUI工作流模板管理服务
提供工作流模板的创建、编辑、删除和版本管理功能
"""

import logging
import json
import uuid
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class WorkflowTemplate:
    """工作流模板类"""
    
    def __init__(self, template_id: str = None, name: str = "", description: str = "", 
                 category: str = "general", workflow_data: Dict[str, Any] = None,
                 version: str = "1.0.0", author: str = "system", tags: List[str] = None):
        self.id = template_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.category = category
        self.workflow_data = workflow_data or {}
        self.version = version
        self.author = author
        self.tags = tags or []
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        self.is_active = True
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "workflow_data": self.workflow_data,
            "version": self.version,
            "author": self.author,
            "tags": self.tags,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "is_active": self.is_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowTemplate':
        """从字典创建实例"""
        template = cls(
            template_id=data.get("id"),
            name=data.get("name", ""),
            description=data.get("description", ""),
            category=data.get("category", "general"),
            workflow_data=data.get("workflow_data", {}),
            version=data.get("version", "1.0.0"),
            author=data.get("author", "system"),
            tags=data.get("tags", [])
        )
        template.created_at = data.get("created_at", template.created_at)
        template.updated_at = data.get("updated_at", template.updated_at)
        template.is_active = data.get("is_active", True)
        return template

class WorkflowTemplateService:
    """工作流模板管理服务"""
    
    def __init__(self, storage_path: str = None):
        self.storage_path = storage_path or os.getenv("WORKFLOW_TEMPLATES_PATH", "data/workflow_templates")
        self.templates_file = os.path.join(self.storage_path, "templates.json")
        self._ensure_storage_dir()
        self._load_templates()
    
    def _ensure_storage_dir(self):
        """确保存储目录存在"""
        os.makedirs(self.storage_path, exist_ok=True)
    
    def _load_templates(self):
        """加载模板数据"""
        try:
            if os.path.exists(self.templates_file):
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.templates = {
                        template_id: WorkflowTemplate.from_dict(template_data)
                        for template_id, template_data in data.items()
                    }
            else:
                self.templates = {}
                self._create_default_templates()
        except Exception as e:
            logger.error(f"加载工作流模板失败: {str(e)}")
            self.templates = {}
            self._create_default_templates()
    
    def _save_templates(self):
        """保存模板数据"""
        try:
            data = {
                template_id: template.to_dict()
                for template_id, template in self.templates.items()
            }
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存工作流模板失败: {str(e)}")
    
    def _create_default_templates(self):
        """创建默认模板"""
        # SDXL图片生成模板
        sdxl_template = WorkflowTemplate(
            name="SDXL图片生成",
            description="使用Stable Diffusion XL生成高质量图片",
            category="image",
            workflow_data={
                "3": {
                    "inputs": {
                        "seed": -1,
                        "steps": 20,
                        "cfg": 7.0,
                        "sampler_name": "euler",
                        "scheduler": "normal",
                        "denoise": 1.0,
                        "model": ["4", 0],
                        "positive": ["6", 0],
                        "negative": ["7", 0],
                        "latent_image": ["5", 0]
                    },
                    "class_type": "KSampler"
                },
                "4": {
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    },
                    "class_type": "CheckpointLoaderSimple"
                },
                "5": {
                    "inputs": {
                        "width": 1024,
                        "height": 1024,
                        "batch_size": 1
                    },
                    "class_type": "EmptyLatentImage"
                },
                "6": {
                    "inputs": {
                        "text": "{positive_prompt}",
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "7": {
                    "inputs": {
                        "text": "{negative_prompt}",
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "8": {
                    "inputs": {
                        "samples": ["3", 0],
                        "vae": ["4", 2]
                    },
                    "class_type": "VAEDecode"
                },
                "9": {
                    "inputs": {
                        "filename_prefix": "{output_prefix}",
                        "images": ["8", 0]
                    },
                    "class_type": "SaveImage"
                }
            },
            tags=["sdxl", "image", "generation"]
        )
        
        # SVD视频生成模板
        svd_template = WorkflowTemplate(
            name="SVD视频生成",
            description="使用Stable Video Diffusion生成视频",
            category="video",
            workflow_data={
                "1": {
                    "inputs": {
                        "width": 512,
                        "height": 512,
                        "length": 16,
                        "context": 16,
                        "motion_bucket_id": 127,
                        "fps": 6,
                        "augmentation_level": 0,
                        "text": "{prompt}"
                    },
                    "class_type": "SVD_img2vid_Conditioning"
                },
                "2": {
                    "inputs": {
                        "filename_prefix": "{output_prefix}",
                        "fps": 8,
                        "images": ["1", 0]
                    },
                    "class_type": "VHS_VideoCombine"
                }
            },
            tags=["svd", "video", "generation"]
        )
        
        # 文本生成模板
        text_template = WorkflowTemplate(
            name="文本生成",
            description="AI文本内容生成",
            category="text",
            workflow_data={
                "1": {
                    "inputs": {
                        "prompt": "{prompt}",
                        "max_tokens": 500,
                        "temperature": 0.7,
                        "top_p": 0.9
                    },
                    "class_type": "LLMTextGeneration"
                },
                "2": {
                    "inputs": {
                        "filename_prefix": "{output_prefix}",
                        "text": ["1", 0]
                    },
                    "class_type": "SaveText"
                }
            },
            tags=["text", "llm", "generation"]
        )
        
        # 保存默认模板
        self.templates[sdxl_template.id] = sdxl_template
        self.templates[svd_template.id] = svd_template
        self.templates[text_template.id] = text_template
        self._save_templates()
        
        logger.info("创建了默认工作流模板")
    
    def create_template(self, name: str, description: str, category: str, 
                       workflow_data: Dict[str, Any], author: str = "user",
                       tags: List[str] = None) -> WorkflowTemplate:
        """创建新模板"""
        template = WorkflowTemplate(
            name=name,
            description=description,
            category=category,
            workflow_data=workflow_data,
            author=author,
            tags=tags or []
        )
        
        self.templates[template.id] = template
        self._save_templates()
        
        logger.info(f"创建工作流模板: {name} ({template.id})")
        return template
    
    def get_template(self, template_id: str) -> Optional[WorkflowTemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def get_templates(self, category: str = None, tags: List[str] = None, 
                     active_only: bool = True) -> List[WorkflowTemplate]:
        """获取模板列表"""
        templates = list(self.templates.values())
        
        if active_only:
            templates = [t for t in templates if t.is_active]
        
        if category:
            templates = [t for t in templates if t.category == category]
        
        if tags:
            templates = [t for t in templates if any(tag in t.tags for tag in tags)]
        
        return sorted(templates, key=lambda t: t.updated_at, reverse=True)
    
    def update_template(self, template_id: str, **kwargs) -> Optional[WorkflowTemplate]:
        """更新模板"""
        template = self.templates.get(template_id)
        if not template:
            return None
        
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(template, key):
                setattr(template, key, value)
        
        template.updated_at = datetime.now().isoformat()
        self._save_templates()
        
        logger.info(f"更新工作流模板: {template.name} ({template_id})")
        return template
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板（软删除）"""
        template = self.templates.get(template_id)
        if not template:
            return False
        
        template.is_active = False
        template.updated_at = datetime.now().isoformat()
        self._save_templates()
        
        logger.info(f"删除工作流模板: {template.name} ({template_id})")
        return True
    
    def duplicate_template(self, template_id: str, new_name: str = None) -> Optional[WorkflowTemplate]:
        """复制模板"""
        original = self.templates.get(template_id)
        if not original:
            return None
        
        new_template = WorkflowTemplate(
            name=new_name or f"{original.name} (副本)",
            description=original.description,
            category=original.category,
            workflow_data=original.workflow_data.copy(),
            author=original.author,
            tags=original.tags.copy()
        )
        
        self.templates[new_template.id] = new_template
        self._save_templates()
        
        logger.info(f"复制工作流模板: {original.name} -> {new_template.name}")
        return new_template

# 全局工作流模板服务实例
_workflow_template_service = None

def get_workflow_template_service() -> WorkflowTemplateService:
    """获取工作流模板服务实例"""
    global _workflow_template_service
    
    if _workflow_template_service is None:
        _workflow_template_service = WorkflowTemplateService()
    
    return _workflow_template_service
