# 一键高清功能使用说明

## 功能概述

一键高清功能为文件管理系统提供了强大的视频和图片高清处理能力，支持AI超分辨率和传统算法两种处理模式。

## 功能特点

- **支持多种文件格式**：视频（mp4, avi, mov, mkv等）和图片（jpg, png, gif等）
- **AI超分辨率**：使用先进的AI算法进行高质量放大
- **传统算法**：使用经典的图像处理算法
- **批量处理**：支持同时处理多个文件
- **灵活配置**：可调节放大倍数、降噪、锐化等参数
- **并发处理**：支持多线程并发处理，提高效率

## 使用方法

### 1. 选择文件

在文件管理界面中：
1. 选择要进行高清处理的视频或图片文件
2. 可以选择单个文件或多个文件
3. 支持混合选择视频和图片文件

### 2. 启动高清处理

1. 点击工具栏中的"🎬 视频处理"下拉菜单
2. 选择"✨ 一键高清"选项
3. 如果没有选中文件，按钮会被禁用

### 3. 配置处理参数

在高清处理对话框中，可以配置以下参数：

#### 处理模式
- **AI超分辨率**：使用AI算法，效果更好但处理时间较长
- **传统算法**：使用经典算法，速度快但效果一般

#### 放大倍数
- **2倍**：推荐选项，平衡效果和处理时间
- **3倍**：更高的放大倍数
- **4倍**：最高放大倍数，处理时间最长

#### 输出质量
- **高质量**：最佳输出质量（推荐）
- **中等质量**：平衡质量和文件大小
- **低质量**：较小的文件大小

#### 降噪级别
- **无降噪**：不进行降噪处理
- **轻度降噪**：轻微降噪
- **中度降噪**：推荐设置
- **重度降噪**：强力降噪，可能影响细节

#### 锐化级别
- **无锐化**：不进行锐化处理
- **轻度锐化**：轻微锐化
- **中度锐化**：推荐设置
- **重度锐化**：强力锐化，可能产生伪影

#### 输出设置
- **覆盖原文件**：直接替换原文件
- **输出文件后缀**：如果不覆盖原文件，新文件的后缀名

#### 并发处理数
- 设置同时处理的文件数量
- 建议根据系统性能调整（1-4个）

### 4. 开始处理

1. 配置完参数后，点击"开始高清处理"按钮
2. 系统会显示处理进度
3. 处理完成后会显示结果统计

## 输出文件

### 文件位置
- 如果选择覆盖原文件：原文件会被替换
- 如果不覆盖原文件：新文件会保存在原文件目录下的"enhanced"文件夹中

### 文件命名
- 格式：`原文件名_enhanced.扩展名`
- 可以通过"输出文件后缀"参数自定义后缀

## 性能建议

### 处理时间
- AI模式比传统模式慢3-5倍
- 放大倍数越高，处理时间越长
- 视频文件比图片文件处理时间更长

### 系统要求
- 建议至少8GB内存
- 支持GPU加速（如果可用）
- 充足的磁盘空间（输出文件通常比原文件大）

### 优化建议
1. **首次使用**：建议先用小文件测试效果
2. **批量处理**：合理设置并发数，避免系统过载
3. **参数选择**：根据需求平衡质量和处理时间
4. **磁盘空间**：确保有足够空间存储输出文件

## 常见问题

### Q: 处理失败怎么办？
A: 检查文件格式是否支持，确保文件没有损坏，查看错误信息

### Q: 处理时间太长怎么办？
A: 可以选择传统模式，降低放大倍数，或减少并发处理数

### Q: 输出文件质量不满意怎么办？
A: 尝试调整降噪和锐化参数，或选择AI模式

### Q: 支持哪些文件格式？
A: 
- 视频：mp4, avi, mov, mkv, wmv, flv, m4v, webm, 3gp, ts
- 图片：jpg, jpeg, png, gif, bmp, webp, tiff

## 技术实现

### 架构设计
- 前端：Vue3 + Element Plus
- 后端：FastAPI + Python
- Core服务：gRPC + FFmpeg
- 处理引擎：FFmpeg + 自定义算法

### 处理流程
1. 前端发送处理请求到后端API
2. 后端验证参数并转发到Core服务
3. Core服务使用FFmpeg进行实际处理
4. 处理结果返回给前端显示

### 扩展性
- 支持添加新的AI模型
- 可以集成第三方超分辨率服务
- 支持自定义处理参数

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的视频和图片高清处理
- 提供AI和传统两种处理模式
- 支持批量处理和参数配置
