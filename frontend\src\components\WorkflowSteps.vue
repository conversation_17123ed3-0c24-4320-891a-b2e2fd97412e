<template>
  <div class="workflow-steps">
    <!-- 工作流概览 -->
    <div class="workflow-header" v-if="workflowContext">
      <div class="workflow-info">
        <h3>{{ workflowContext.workflow_name }}</h3>
        <div class="workflow-meta">
          <el-tag type="info" size="small">{{ workflowContext.workflow_version || 'v1.0' }}</el-tag>
          <span class="step-counter">
            步骤 {{ (workflowContext.current_step_index || 0) + 1 }} / {{ workflowContext.total_steps || 0 }}
          </span>
        </div>
      </div>
      
      <!-- 工作流控制按钮 -->
      <div class="workflow-controls" v-if="showControls">
        <el-button-group>
          <el-button 
            :icon="isPaused ? 'VideoPlay' : 'VideoPause'" 
            size="small"
            @click="handleWorkflowControl(isPaused ? 'resume' : 'pause')"
            :disabled="!canControl"
          >
            {{ isPaused ? '继续' : '暂停' }}
          </el-button>
          <el-button 
            icon="Refresh" 
            size="small"
            @click="handleWorkflowControl('retry')"
            :disabled="!canRetry"
          >
            重试
          </el-button>
          <el-button 
            icon="Right" 
            size="small"
            @click="handleWorkflowControl('skip')"
            :disabled="!canSkip"
          >
            跳过
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 工作流进度条 -->
    <div class="workflow-progress" v-if="workflowContext">
      <el-progress 
        :percentage="overallProgress" 
        :status="getOverallStatus()"
        :stroke-width="8"
        :show-text="true"
      />
      <div class="progress-details">
        <span>已完成: {{ completedSteps }}/{{ workflowContext.total_steps }}</span>
        <span v-if="failedSteps > 0" class="failed-count">失败: {{ failedSteps }}</span>
      </div>
    </div>

    <!-- 步骤列表 -->
    <div class="steps-container">
      <el-timeline>
        <el-timeline-item
          v-for="(step, index) in workflowContext?.steps || []"
          :key="step.id"
          :type="getStepTimelineType(step.status)"
          :icon="getStepIcon(step.status)"
          :color="getStepColor(step.status)"
          :size="step.status === 'running' ? 'large' : 'normal'"
        >
          <div class="step-content">
            <!-- 步骤头部 -->
            <div class="step-header">
              <div class="step-title">
                <h4>{{ step.name }}</h4>
                <el-tag 
                  :type="getStepTagType(step.status)" 
                  size="small"
                  :effect="step.status === 'running' ? 'dark' : 'plain'"
                >
                  {{ getStepStatusText(step.status) }}
                </el-tag>
              </div>
              
              <!-- 步骤控制按钮 -->
              <div class="step-controls" v-if="showControls && canControlStep(step)">
                <el-button-group size="small">
                  <el-button 
                    v-if="step.status === 'failed'"
                    icon="Refresh" 
                    @click="handleStepRetry(step.id)"
                    :loading="retryingSteps.includes(step.id)"
                  >
                    重试
                  </el-button>
                  <el-button 
                    v-if="step.status === 'failed' && !step.required"
                    icon="Right" 
                    @click="handleStepSkip(step.id)"
                  >
                    跳过
                  </el-button>
                  <el-button 
                    v-if="step.status === 'paused'"
                    icon="VideoPlay" 
                    @click="handleStepResume(step.id)"
                  >
                    继续
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <!-- 步骤描述 -->
            <div class="step-description" v-if="step.description">
              <p>{{ step.description }}</p>
            </div>

            <!-- 步骤进度 -->
            <div class="step-progress" v-if="step.status === 'running' || step.progress > 0">
              <el-progress 
                :percentage="step.progress" 
                :status="step.status === 'failed' ? 'exception' : undefined"
                :stroke-width="4"
                :show-text="false"
              />
              <span class="progress-text">{{ step.progress }}%</span>
            </div>

            <!-- 步骤详情 -->
            <div class="step-details">
              <div class="step-meta">
                <span v-if="step.start_time" class="meta-item">
                  <el-icon><Clock /></el-icon>
                  开始: {{ formatTime(step.start_time) }}
                </span>
                <span v-if="step.end_time" class="meta-item">
                  <el-icon><Clock /></el-icon>
                  结束: {{ formatTime(step.end_time) }}
                </span>
                <span v-if="step.duration" class="meta-item">
                  <el-icon><Timer /></el-icon>
                  耗时: {{ formatDuration(step.duration) }}
                </span>
                <span v-if="step.retry_count && step.retry_count > 0" class="meta-item">
                  <el-icon><Refresh /></el-icon>
                  重试: {{ step.retry_count }}/{{ step.max_retries || 3 }}
                </span>
                <span v-if="step.required" class="meta-item required">
                  <el-icon><Star /></el-icon>
                  必需步骤
                </span>
              </div>

              <!-- 步骤参数（展开时显示） -->
              <div v-if="expandedSteps.includes(step.id) && step.params && Object.keys(step.params).length > 0" class="step-params">
                <div class="params-header">
                  <el-icon><Setting /></el-icon>
                  <span>步骤参数</span>
                </div>
                <el-descriptions :column="2" size="small" border>
                  <el-descriptions-item
                    v-for="(value, key) in step.params"
                    :key="key"
                    :label="key"
                  >
                    <el-tag size="small" type="info">{{ formatParamValue(value) }}</el-tag>
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 步骤执行详情（展开时显示） -->
              <div v-if="expandedSteps.includes(step.id)" class="step-execution-details">
                <div class="execution-info">
                  <div class="info-item" v-if="step.action">
                    <span class="info-label">执行动作:</span>
                    <el-tag size="small">{{ step.action }}</el-tag>
                  </div>
                  <div class="info-item" v-if="step.element">
                    <span class="info-label">目标元素:</span>
                    <el-tag size="small" type="success">{{ step.element }}</el-tag>
                  </div>
                  <div class="info-item" v-if="step.condition">
                    <span class="info-label">执行条件:</span>
                    <el-tag size="small" type="warning">{{ step.condition }}</el-tag>
                  </div>
                </div>
              </div>

              <!-- 错误信息 -->
              <div class="step-error" v-if="step.error_message">
                <el-alert 
                  :title="step.error_message" 
                  type="error" 
                  :closable="false"
                  show-icon
                />
              </div>

              <!-- 步骤日志 -->
              <div class="step-logs" v-if="expandedSteps.includes(step.id) && step.logs && step.logs.length > 0">
                <div class="logs-header">
                  <span>执行日志</span>
                  <el-button 
                    size="small" 
                    text 
                    @click="refreshStepLogs(step.id)"
                    :loading="loadingLogs.includes(step.id)"
                  >
                    刷新
                  </el-button>
                </div>
                <div class="logs-content">
                  <div 
                    v-for="(log, logIndex) in step.logs" 
                    :key="logIndex"
                    class="log-entry"
                  >
                    {{ log }}
                  </div>
                </div>
              </div>

              <!-- 展开/收起按钮 -->
              <div class="step-actions">
                <el-button 
                  size="small" 
                  text 
                  @click="toggleStepExpand(step.id)"
                >
                  {{ expandedSteps.includes(step.id) ? '收起详情' : '展开详情' }}
                </el-button>
                <el-button 
                  v-if="canResumeFromStep(index)"
                  size="small" 
                  text 
                  type="primary"
                  @click="handleResumeFromStep(index)"
                >
                  从此步骤继续
                </el-button>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!workflowContext || !workflowContext.steps?.length">
      <el-empty description="暂无工作流信息" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock,
  Timer,
  Refresh,
  VideoPlay,
  VideoPause,
  Right,
  Check,
  Close,
  Warning,
  Star,
  Setting,
  Loading
} from '@element-plus/icons-vue'
import {
  WorkflowContext,
  WorkflowStep,
  getTaskWorkflow,
  getTaskWorkflowLogs,
  controlWorkflow,
  resumeWorkflowFromStep,
  retryWorkflowStep,
  skipWorkflowStep
} from '@/api/task'

// Props
interface Props {
  taskId: string
  showControls?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  showControls: true,
  autoRefresh: true,
  refreshInterval: 5000
})

// Emits
const emit = defineEmits<{
  workflowUpdated: [context: WorkflowContext]
  stepCompleted: [stepId: string]
  workflowCompleted: []
  workflowFailed: [error: string]
}>()

// 响应式数据
const workflowContext = ref<WorkflowContext | null>(null)
const expandedSteps = ref<string[]>([])
const retryingSteps = ref<string[]>([])
const loadingLogs = ref<string[]>([])
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const overallProgress = computed(() => {
  if (!workflowContext.value) return 0
  const steps = workflowContext.value.steps || []
  if (steps.length === 0) return 0

  const totalProgress = steps.reduce((sum, step) => sum + (step.progress || 0), 0)
  return Math.round(totalProgress / steps.length)
})

const completedSteps = computed(() => {
  if (!workflowContext.value) return 0
  const steps = workflowContext.value.steps || []
  return steps.filter(step => step.status === 'completed').length
})

const failedSteps = computed(() => {
  if (!workflowContext.value) return 0
  const steps = workflowContext.value.steps || []
  return steps.filter(step => step.status === 'failed').length
})

const isPaused = computed(() => {
  if (!workflowContext.value) return false
  const steps = workflowContext.value.steps || []
  return steps.some(step => step.status === 'paused')
})

const canControl = computed(() => {
  if (!workflowContext.value) return false
  const steps = workflowContext.value.steps || []
  return steps.some(step =>
    ['running', 'paused', 'failed'].includes(step.status)
  )
})

const canRetry = computed(() => {
  if (!workflowContext.value) return false
  const steps = workflowContext.value.steps || []
  return steps.some(step => step.status === 'failed')
})

const canSkip = computed(() => {
  if (!workflowContext.value) return false
  const steps = workflowContext.value.steps || []
  return steps.some(step =>
    step.status === 'failed' && !step.required
  )
})

// 方法
const fetchWorkflowContext = async () => {
  try {
    console.log('开始获取工作流信息，任务ID:', props.taskId)
    const response = await getTaskWorkflow(props.taskId)
    console.log('工作流API响应:', response)

    if (response.success && response.data) {
      // 确保数据结构完整性
      const data = response.data
      if (!data.steps) {
        data.steps = []
      }

      // 确保工作流基本属性有默认值
      data.workflow_id = data.workflow_id || ''
      data.workflow_name = data.workflow_name || '未知工作流'
      data.workflow_version = data.workflow_version || 'v1.0'
      data.current_step_index = typeof data.current_step_index === 'number' ? data.current_step_index : 0
      data.total_steps = typeof data.total_steps === 'number' ? data.total_steps : data.steps.length
      data.can_resume = data.can_resume !== false

      // 确保每个步骤都有必要的属性
      data.steps = data.steps.map(step => ({
        id: step.id || '',
        name: step.name || '未知步骤',
        description: step.description || '',
        status: step.status || 'pending',
        progress: typeof step.progress === 'number' ? step.progress : 0,
        required: step.required !== false,
        start_time: step.start_time || null,
        end_time: step.end_time || null,
        duration: step.duration || null,
        error_message: step.error_message || null,
        retry_count: typeof step.retry_count === 'number' ? step.retry_count : 0,
        max_retries: typeof step.max_retries === 'number' ? step.max_retries : 3,
        action: step.action || null,
        element: step.element || null,
        condition: step.condition || null,
        params: step.params || {},
        logs: step.logs || []
      }))

      console.log('工作流数据规范化后:', data)

      workflowContext.value = data
      emit('workflowUpdated', data)
    }
  } catch (error) {
    console.error('获取工作流信息失败:', error)
  }
}

const handleWorkflowControl = async (action: 'pause' | 'resume' | 'retry' | 'skip') => {
  try {
    const response = await controlWorkflow(props.taskId, action)
    if (response.success) {
      ElMessage.success(response.data?.message || '操作成功')
      await fetchWorkflowContext()
    } else {
      ElMessage.error(response.error || '操作失败')
    }
  } catch (error) {
    console.error('工作流控制失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleStepRetry = async (stepId: string) => {
  retryingSteps.value.push(stepId)
  try {
    const response = await retryWorkflowStep(props.taskId, stepId)
    if (response.success) {
      ElMessage.success('步骤重试成功')
      await fetchWorkflowContext()
    } else {
      ElMessage.error(response.error || '重试失败')
    }
  } catch (error) {
    console.error('步骤重试失败:', error)
    ElMessage.error('重试失败')
  } finally {
    retryingSteps.value = retryingSteps.value.filter(id => id !== stepId)
  }
}

const handleStepSkip = async (stepId: string) => {
  try {
    await ElMessageBox.confirm('确定要跳过此步骤吗？', '确认跳过', {
      type: 'warning'
    })

    const response = await skipWorkflowStep(props.taskId, stepId)
    if (response.success) {
      ElMessage.success('步骤已跳过')
      await fetchWorkflowContext()
    } else {
      ElMessage.error(response.error || '跳过失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('步骤跳过失败:', error)
      ElMessage.error('跳过失败')
    }
  }
}

const handleStepResume = async (stepId: string) => {
  try {
    const response = await controlWorkflow(props.taskId, 'resume', stepId)
    if (response.success) {
      ElMessage.success('步骤已恢复')
      await fetchWorkflowContext()
    } else {
      ElMessage.error(response.error || '恢复失败')
    }
  } catch (error) {
    console.error('步骤恢复失败:', error)
    ElMessage.error('恢复失败')
  }
}

const handleResumeFromStep = async (stepIndex: number) => {
  try {
    await ElMessageBox.confirm(
      `确定要从第 ${stepIndex + 1} 步开始重新执行工作流吗？`,
      '确认恢复',
      { type: 'warning' }
    )

    const response = await resumeWorkflowFromStep(props.taskId, stepIndex)
    if (response.success) {
      ElMessage.success('工作流已从指定步骤恢复')
      await fetchWorkflowContext()
    } else {
      ElMessage.error(response.error || '恢复失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('工作流恢复失败:', error)
      ElMessage.error('恢复失败')
    }
  }
}

const refreshStepLogs = async (stepId: string) => {
  loadingLogs.value.push(stepId)
  try {
    const response = await getTaskWorkflowLogs(props.taskId, stepId)
    if (response.success && response.data) {
      // 更新步骤日志
      const steps = workflowContext.value?.steps || []
      const step = steps.find(s => s.id === stepId)
      if (step) {
        step.logs = response.data.logs.map(log =>
          `[${new Date(log.timestamp).toLocaleTimeString()}] ${log.level.toUpperCase()}: ${log.message}`
        )
      }
    }
  } catch (error) {
    console.error('获取步骤日志失败:', error)
    ElMessage.error('获取步骤日志失败')
  } finally {
    loadingLogs.value = loadingLogs.value.filter(id => id !== stepId)
  }
}

const toggleStepExpand = (stepId: string) => {
  const index = expandedSteps.value.indexOf(stepId)
  if (index > -1) {
    expandedSteps.value.splice(index, 1)
  } else {
    expandedSteps.value.push(stepId)
  }
}

// 辅助方法
const getOverallStatus = () => {
  if (!workflowContext.value) return undefined

  const steps = workflowContext.value.steps || []
  const hasRunning = steps.some(step => step.status === 'running')
  const hasFailed = steps.some(step => step.status === 'failed')
  const allCompleted = steps.every(step =>
    ['completed', 'skipped'].includes(step.status)
  )

  if (hasFailed) return 'exception'
  if (hasRunning) return undefined
  if (allCompleted) return 'success'
  return undefined
}

const getStepTimelineType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'running': return 'primary'
    case 'paused': return 'warning'
    case 'skipped': return 'info'
    default: return 'info'
  }
}

const getStepIcon = (status: string) => {
  switch (status) {
    case 'completed': return Check
    case 'failed': return Close
    case 'running': return Loading
    case 'paused': return VideoPause
    case 'skipped': return Right
    default: return Clock
  }
}

const getStepColor = (status: string) => {
  switch (status) {
    case 'completed': return '#67c23a'
    case 'failed': return '#f56c6c'
    case 'running': return '#409eff'
    case 'paused': return '#e6a23c'
    case 'skipped': return '#909399'
    default: return '#c0c4cc'
  }
}

const getStepTagType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'running': return 'primary'
    case 'paused': return 'warning'
    case 'skipped': return 'info'
    default: return 'info'
  }
}

const getStepStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '等待中'
    case 'running': return '执行中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    case 'skipped': return '已跳过'
    case 'paused': return '已暂停'
    default: return '未知'
  }
}

const canControlStep = (step: WorkflowStep) => {
  return ['failed', 'paused'].includes(step.status)
}

const canResumeFromStep = (stepIndex: number) => {
  if (!workflowContext.value) return false
  return workflowContext.value.can_resume && stepIndex < workflowContext.value.current_step_index
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

const formatDuration = (duration: number) => {
  if (!duration) return '-'
  const minutes = Math.floor(duration / 60)
  const seconds = duration % 60
  return minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`
}

// 格式化参数值
const formatParamValue = (value: any) => {
  if (value === null || value === undefined) return '无'
  if (typeof value === 'boolean') return value ? '是' : '否'
  if (typeof value === 'object') return JSON.stringify(value)
  if (typeof value === 'string' && value.length > 50) {
    return value.substring(0, 50) + '...'
  }
  return String(value)
}

// 生命周期
onMounted(() => {
  fetchWorkflowContext()

  if (props.autoRefresh) {
    refreshTimer.value = setInterval(fetchWorkflowContext, props.refreshInterval)
  }
})

// 监听器
watch(() => props.taskId, () => {
  fetchWorkflowContext()
})

// 清理定时器
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.workflow-steps {
  padding: 16px;
}

.workflow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.workflow-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.workflow-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-counter {
  font-size: 14px;
  color: #606266;
}

.workflow-progress {
  margin-bottom: 24px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.failed-count {
  color: #f56c6c;
}

.step-content {
  width: 100%;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.step-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-title h4 {
  margin: 0;
  color: #303133;
}

.step-description {
  margin-bottom: 12px;
  color: #606266;
  font-size: 14px;
}

.step-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
}

.step-details {
  margin-top: 12px;
}

.step-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.step-error {
  margin: 12px 0;
}

.step-logs {
  margin-top: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 12px;
  font-weight: 500;
}

.logs-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px 12px;
}

.log-entry {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #606266;
  margin-bottom: 4px;
}

.step-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}
</style>
