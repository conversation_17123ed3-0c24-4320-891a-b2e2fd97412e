"""
结果预览和编辑服务
提供生成结果的在线预览、缩略图生成和简单编辑功能
"""

import logging
import os
import json
import base64
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import mimetypes

logger = logging.getLogger(__name__)

class PreviewType(Enum):
    """预览类型"""
    THUMBNAIL = "thumbnail"
    PREVIEW = "preview"
    FULL = "full"

class EditOperation(Enum):
    """编辑操作"""
    RESIZE = "resize"
    CROP = "crop"
    ROTATE = "rotate"
    FILTER = "filter"
    TEXT_OVERLAY = "text_overlay"
    WATERMARK = "watermark"

@dataclass
class PreviewConfig:
    """预览配置"""
    width: int = 300
    height: int = 300
    quality: int = 85
    format: str = "JPEG"

@dataclass
class EditConfig:
    """编辑配置"""
    operation: EditOperation
    parameters: Dict[str, Any]
    preserve_original: bool = True

class ResultPreviewService:
    """结果预览服务"""
    
    def __init__(self, storage_service=None):
        self.storage_service = storage_service
        self.preview_cache: Dict[str, str] = {}  # 预览缓存
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        self.supported_video_formats = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'}
        self.supported_audio_formats = {'.mp3', '.wav', '.flac', '.aac', '.ogg'}
        self.supported_text_formats = {'.txt', '.md', '.json', '.xml', '.csv'}
    
    def generate_thumbnail(self, result_id: str, config: PreviewConfig = None) -> Optional[str]:
        """生成缩略图"""
        try:
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            result = self.storage_service.get_result(result_id)
            if not result or not os.path.exists(result.file_path):
                return None
            
            config = config or PreviewConfig()
            file_ext = Path(result.file_path).suffix.lower()
            
            # 根据文件类型生成缩略图
            if file_ext in self.supported_image_formats:
                return self._generate_image_thumbnail(result, config)
            elif file_ext in self.supported_video_formats:
                return self._generate_video_thumbnail(result, config)
            elif file_ext in self.supported_text_formats:
                return self._generate_text_preview(result, config)
            else:
                return self._generate_default_thumbnail(result, config)
                
        except Exception as e:
            logger.error(f"生成缩略图失败: {str(e)}")
            return None
    
    def _generate_image_thumbnail(self, result, config: PreviewConfig) -> Optional[str]:
        """生成图片缩略图"""
        try:
            # 这里应该使用PIL或其他图像处理库
            # 暂时返回模拟的base64数据
            
            # 检查缓存
            cache_key = f"{result.id}_thumb_{config.width}x{config.height}"
            if cache_key in self.preview_cache:
                return self.preview_cache[cache_key]
            
            # 模拟生成缩略图
            thumbnail_data = self._create_placeholder_image(config.width, config.height, "Image")
            
            # 缓存结果
            self.preview_cache[cache_key] = thumbnail_data
            
            # 保存缩略图文件
            thumbnail_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "thumbnails")
            )
            os.makedirs(thumbnail_dir, exist_ok=True)
            
            thumbnail_path = os.path.join(thumbnail_dir, f"{result.id}_thumb.jpg")
            self._save_base64_image(thumbnail_data, thumbnail_path)
            
            return thumbnail_data
            
        except Exception as e:
            logger.error(f"生成图片缩略图失败: {str(e)}")
            return None
    
    def _generate_video_thumbnail(self, result, config: PreviewConfig) -> Optional[str]:
        """生成视频缩略图"""
        try:
            # 这里应该使用ffmpeg提取视频帧
            # 暂时返回模拟数据
            
            cache_key = f"{result.id}_video_thumb_{config.width}x{config.height}"
            if cache_key in self.preview_cache:
                return self.preview_cache[cache_key]
            
            # 模拟生成视频缩略图
            thumbnail_data = self._create_placeholder_image(config.width, config.height, "Video")
            
            self.preview_cache[cache_key] = thumbnail_data
            return thumbnail_data
            
        except Exception as e:
            logger.error(f"生成视频缩略图失败: {str(e)}")
            return None
    
    def _generate_text_preview(self, result, config: PreviewConfig) -> Optional[str]:
        """生成文本预览"""
        try:
            # 读取文本内容的前几行
            with open(result.file_path, 'r', encoding='utf-8') as f:
                content = f.read(500)  # 读取前500个字符
            
            # 生成文本预览图
            thumbnail_data = self._create_text_preview_image(content, config.width, config.height)
            
            cache_key = f"{result.id}_text_preview"
            self.preview_cache[cache_key] = thumbnail_data
            
            return thumbnail_data
            
        except Exception as e:
            logger.error(f"生成文本预览失败: {str(e)}")
            return None
    
    def _generate_default_thumbnail(self, result, config: PreviewConfig) -> Optional[str]:
        """生成默认缩略图"""
        try:
            file_ext = Path(result.file_path).suffix.lower()
            label = f"{file_ext.upper()}\nFile"
            
            thumbnail_data = self._create_placeholder_image(config.width, config.height, label)
            
            cache_key = f"{result.id}_default_thumb"
            self.preview_cache[cache_key] = thumbnail_data
            
            return thumbnail_data
            
        except Exception as e:
            logger.error(f"生成默认缩略图失败: {str(e)}")
            return None
    
    def _create_placeholder_image(self, width: int, height: int, text: str) -> str:
        """创建占位符图片"""
        # 这里应该使用PIL创建实际的占位符图片
        # 暂时返回模拟的base64数据
        
        # 创建一个简单的SVG作为占位符
        svg_content = f'''
        <svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" 
                  font-family="Arial, sans-serif" font-size="14" fill="#666">
                {text}
            </text>
        </svg>
        '''
        
        # 将SVG转换为base64
        svg_base64 = base64.b64encode(svg_content.encode('utf-8')).decode('utf-8')
        return f"data:image/svg+xml;base64,{svg_base64}"
    
    def _create_text_preview_image(self, text: str, width: int, height: int) -> str:
        """创建文本预览图片"""
        # 截断文本
        if len(text) > 100:
            text = text[:100] + "..."
        
        # 转义HTML特殊字符
        text = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
        
        svg_content = f'''
        <svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#fff" stroke="#ddd" stroke-width="1"/>
            <foreignObject x="10" y="10" width="{width-20}" height="{height-20}">
                <div xmlns="http://www.w3.org/1999/xhtml" 
                     style="font-family: monospace; font-size: 10px; line-height: 1.2; 
                            overflow: hidden; word-wrap: break-word;">
                    {text}
                </div>
            </foreignObject>
        </svg>
        '''
        
        svg_base64 = base64.b64encode(svg_content.encode('utf-8')).decode('utf-8')
        return f"data:image/svg+xml;base64,{svg_base64}"
    
    def _save_base64_image(self, base64_data: str, file_path: str):
        """保存base64图片到文件"""
        try:
            # 提取base64数据部分
            if ',' in base64_data:
                base64_data = base64_data.split(',')[1]
            
            # 解码并保存
            image_data = base64.b64decode(base64_data)
            with open(file_path, 'wb') as f:
                f.write(image_data)
                
        except Exception as e:
            logger.error(f"保存base64图片失败: {str(e)}")
    
    def get_preview_url(self, result_id: str, preview_type: PreviewType = PreviewType.THUMBNAIL) -> Optional[str]:
        """获取预览URL"""
        try:
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            result = self.storage_service.get_result(result_id)
            if not result:
                return None
            
            if preview_type == PreviewType.FULL:
                # 返回原文件路径（需要通过API提供访问）
                return f"/api/v1/results/{result_id}/file"
            elif preview_type == PreviewType.THUMBNAIL:
                # 返回缩略图路径
                return f"/api/v1/results/{result_id}/thumbnail"
            else:
                # 返回预览路径
                return f"/api/v1/results/{result_id}/preview"
                
        except Exception as e:
            logger.error(f"获取预览URL失败: {str(e)}")
            return None
    
    def apply_edit(self, result_id: str, edit_config: EditConfig) -> Optional[str]:
        """应用编辑操作"""
        try:
            if not self.storage_service:
                from .result_storage_service import get_result_storage_service
                self.storage_service = get_result_storage_service()
            
            result = self.storage_service.get_result(result_id)
            if not result or not os.path.exists(result.file_path):
                return None
            
            # 根据操作类型应用编辑
            if edit_config.operation == EditOperation.RESIZE:
                return self._apply_resize(result, edit_config)
            elif edit_config.operation == EditOperation.CROP:
                return self._apply_crop(result, edit_config)
            elif edit_config.operation == EditOperation.ROTATE:
                return self._apply_rotate(result, edit_config)
            elif edit_config.operation == EditOperation.FILTER:
                return self._apply_filter(result, edit_config)
            elif edit_config.operation == EditOperation.TEXT_OVERLAY:
                return self._apply_text_overlay(result, edit_config)
            elif edit_config.operation == EditOperation.WATERMARK:
                return self._apply_watermark(result, edit_config)
            else:
                logger.warning(f"不支持的编辑操作: {edit_config.operation}")
                return None
                
        except Exception as e:
            logger.error(f"应用编辑操作失败: {str(e)}")
            return None
    
    def _apply_resize(self, result, edit_config: EditConfig) -> Optional[str]:
        """应用调整大小操作"""
        try:
            params = edit_config.parameters
            width = params.get('width', 800)
            height = params.get('height', 600)
            
            # 这里应该使用PIL或其他图像处理库
            # 暂时返回模拟结果
            
            # 生成处理后的文件路径
            processed_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "processed")
            )
            os.makedirs(processed_dir, exist_ok=True)
            
            processed_filename = f"{result.id}_resized_{width}x{height}.jpg"
            processed_path = os.path.join(processed_dir, processed_filename)
            
            # 模拟处理过程
            logger.info(f"调整图片大小: {result.file_path} -> {width}x{height}")
            
            # 这里应该实际处理图片
            # 暂时复制原文件
            import shutil
            shutil.copy2(result.file_path, processed_path)
            
            return processed_path
            
        except Exception as e:
            logger.error(f"调整大小失败: {str(e)}")
            return None
    
    def _apply_crop(self, result, edit_config: EditConfig) -> Optional[str]:
        """应用裁剪操作"""
        try:
            params = edit_config.parameters
            x = params.get('x', 0)
            y = params.get('y', 0)
            width = params.get('width', 100)
            height = params.get('height', 100)
            
            # 模拟裁剪操作
            logger.info(f"裁剪图片: {result.file_path} -> ({x},{y},{width},{height})")
            
            # 生成处理后的文件
            processed_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "processed")
            )
            os.makedirs(processed_dir, exist_ok=True)
            
            processed_filename = f"{result.id}_cropped_{x}_{y}_{width}_{height}.jpg"
            processed_path = os.path.join(processed_dir, processed_filename)
            
            # 暂时复制原文件
            import shutil
            shutil.copy2(result.file_path, processed_path)
            
            return processed_path
            
        except Exception as e:
            logger.error(f"裁剪失败: {str(e)}")
            return None
    
    def _apply_rotate(self, result, edit_config: EditConfig) -> Optional[str]:
        """应用旋转操作"""
        try:
            params = edit_config.parameters
            angle = params.get('angle', 90)
            
            logger.info(f"旋转图片: {result.file_path} -> {angle}度")
            
            # 生成处理后的文件
            processed_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "processed")
            )
            os.makedirs(processed_dir, exist_ok=True)
            
            processed_filename = f"{result.id}_rotated_{angle}.jpg"
            processed_path = os.path.join(processed_dir, processed_filename)
            
            # 暂时复制原文件
            import shutil
            shutil.copy2(result.file_path, processed_path)
            
            return processed_path
            
        except Exception as e:
            logger.error(f"旋转失败: {str(e)}")
            return None
    
    def _apply_filter(self, result, edit_config: EditConfig) -> Optional[str]:
        """应用滤镜操作"""
        try:
            params = edit_config.parameters
            filter_type = params.get('type', 'blur')
            intensity = params.get('intensity', 1.0)
            
            logger.info(f"应用滤镜: {result.file_path} -> {filter_type}({intensity})")
            
            # 生成处理后的文件
            processed_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "processed")
            )
            os.makedirs(processed_dir, exist_ok=True)
            
            processed_filename = f"{result.id}_filtered_{filter_type}.jpg"
            processed_path = os.path.join(processed_dir, processed_filename)
            
            # 暂时复制原文件
            import shutil
            shutil.copy2(result.file_path, processed_path)
            
            return processed_path
            
        except Exception as e:
            logger.error(f"应用滤镜失败: {str(e)}")
            return None
    
    def _apply_text_overlay(self, result, edit_config: EditConfig) -> Optional[str]:
        """应用文字叠加操作"""
        try:
            params = edit_config.parameters
            text = params.get('text', 'Sample Text')
            x = params.get('x', 10)
            y = params.get('y', 10)
            font_size = params.get('font_size', 24)
            color = params.get('color', '#FFFFFF')
            
            logger.info(f"添加文字叠加: {result.file_path} -> '{text}'")
            
            # 生成处理后的文件
            processed_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "processed")
            )
            os.makedirs(processed_dir, exist_ok=True)
            
            processed_filename = f"{result.id}_text_overlay.jpg"
            processed_path = os.path.join(processed_dir, processed_filename)
            
            # 暂时复制原文件
            import shutil
            shutil.copy2(result.file_path, processed_path)
            
            return processed_path
            
        except Exception as e:
            logger.error(f"添加文字叠加失败: {str(e)}")
            return None
    
    def _apply_watermark(self, result, edit_config: EditConfig) -> Optional[str]:
        """应用水印操作"""
        try:
            params = edit_config.parameters
            watermark_text = params.get('text', 'Watermark')
            position = params.get('position', 'bottom-right')
            opacity = params.get('opacity', 0.5)
            
            logger.info(f"添加水印: {result.file_path} -> '{watermark_text}'")
            
            # 生成处理后的文件
            processed_dir = os.path.join(
                os.path.dirname(result.file_path).replace("originals", "processed")
            )
            os.makedirs(processed_dir, exist_ok=True)
            
            processed_filename = f"{result.id}_watermarked.jpg"
            processed_path = os.path.join(processed_dir, processed_filename)
            
            # 暂时复制原文件
            import shutil
            shutil.copy2(result.file_path, processed_path)
            
            return processed_path
            
        except Exception as e:
            logger.error(f"添加水印失败: {str(e)}")
            return None
    
    def get_edit_history(self, result_id: str) -> List[Dict[str, Any]]:
        """获取编辑历史"""
        try:
            # 这里应该从数据库或文件中读取编辑历史
            # 暂时返回模拟数据
            return [
                {
                    "operation": "resize",
                    "parameters": {"width": 800, "height": 600},
                    "timestamp": datetime.now().isoformat(),
                    "user": "system"
                }
            ]
            
        except Exception as e:
            logger.error(f"获取编辑历史失败: {str(e)}")
            return []

# 全局预览服务实例
_preview_service = None

def get_preview_service() -> ResultPreviewService:
    """获取预览服务实例"""
    global _preview_service
    
    if _preview_service is None:
        _preview_service = ResultPreviewService()
    
    return _preview_service
