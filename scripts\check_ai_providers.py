#!/usr/bin/env python3
"""
检查和修复AI提供商配置的脚本
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "thunderhub"

async def check_ai_providers():
    """检查AI提供商配置"""
    try:
        # 连接数据库
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
        
        logger.info("检查AI提供商配置...")
        
        # 获取所有AI提供商
        providers = await db.ai_providers.find({}).to_list(length=None)
        
        if not providers:
            logger.warning("没有找到任何AI提供商配置")
            return
        
        logger.info(f"找到 {len(providers)} 个AI提供商配置:")
        
        enabled_count = 0
        for provider in providers:
            provider_id = str(provider["_id"])
            name = provider.get("name", "未知")
            provider_type = provider.get("type", "未知")
            enabled = provider.get("enabled", False)
            endpoint = provider.get("endpoint", "")
            api_key = provider.get("api_key", "")
            
            status = "✅ 启用" if enabled else "❌ 禁用"
            api_key_status = "✅ 已配置" if api_key and api_key != "your-openai-api-key-here" and api_key != "your-azure-api-key-here" and api_key != "your-claude-api-key-here" else "❌ 未配置"
            
            logger.info(f"  - {name} ({provider_type}) - {status} - API密钥: {api_key_status}")
            logger.info(f"    ID: {provider_id}")
            logger.info(f"    端点: {endpoint}")
            
            if enabled:
                enabled_count += 1
        
        logger.info(f"启用的提供商数量: {enabled_count}")
        
        if enabled_count == 0:
            logger.warning("没有启用的AI提供商！AI功能将无法正常工作。")
            logger.info("请执行以下操作之一:")
            logger.info("1. 在系统配置中配置API密钥并启用提供商")
            logger.info("2. 运行 create_demo_provider() 创建一个演示提供商")
        
    except Exception as e:
        logger.error(f"检查AI提供商配置失败: {str(e)}")
    finally:
        client.close()

async def create_demo_provider():
    """创建一个演示用的AI提供商（用于测试）"""
    try:
        # 连接数据库
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
        
        logger.info("创建演示AI提供商...")
        
        # 检查是否已经有演示提供商
        existing = await db.ai_providers.find_one({"name": "演示提供商"})
        if existing:
            logger.info("演示提供商已存在，更新配置...")
            await db.ai_providers.update_one(
                {"_id": existing["_id"]},
                {"$set": {"enabled": True, "updated_at": datetime.utcnow()}}
            )
        else:
            # 创建新的演示提供商
            demo_provider = {
                "name": "演示提供商",
                "type": "custom",
                "endpoint": "https://api.openai.com",  # 使用OpenAI端点作为示例
                "api_key": "demo-key-for-testing",
                "model": "gpt-3.5-turbo",
                "max_tokens": 2000,
                "temperature": 0.7,
                "enabled": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            result = await db.ai_providers.insert_one(demo_provider)
            logger.info(f"创建演示提供商成功: {result.inserted_id}")
        
        logger.info("演示提供商已启用，现在可以测试AI功能了")
        logger.warning("注意: 这只是演示配置，实际使用时请配置真实的API密钥")
        
    except Exception as e:
        logger.error(f"创建演示提供商失败: {str(e)}")
    finally:
        client.close()

async def enable_first_provider():
    """启用第一个AI提供商"""
    try:
        # 连接数据库
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
        
        logger.info("启用第一个AI提供商...")
        
        # 获取第一个提供商
        provider = await db.ai_providers.find_one({})
        if not provider:
            logger.warning("没有找到任何AI提供商")
            return
        
        # 启用该提供商
        await db.ai_providers.update_one(
            {"_id": provider["_id"]},
            {"$set": {"enabled": True, "updated_at": datetime.utcnow()}}
        )
        
        logger.info(f"已启用提供商: {provider.get('name', '未知')}")
        logger.warning("注意: 请确保该提供商的API密钥配置正确")
        
    except Exception as e:
        logger.error(f"启用提供商失败: {str(e)}")
    finally:
        client.close()

async def main():
    """主函数"""
    logger.info("AI提供商配置检查工具")
    logger.info("=" * 50)
    
    # 检查当前配置
    await check_ai_providers()
    
    print("\n选择操作:")
    print("1. 重新检查配置")
    print("2. 创建演示提供商")
    print("3. 启用第一个提供商")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            await check_ai_providers()
        elif choice == "2":
            await create_demo_provider()
        elif choice == "3":
            await enable_first_provider()
        elif choice == "4":
            logger.info("退出")
        else:
            logger.warning("无效选择")
            
    except KeyboardInterrupt:
        logger.info("\n用户取消操作")
    except Exception as e:
        logger.error(f"操作失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
