"""
文件服务gRPC实现
"""

import os
import logging
import yaml
import time
import hashlib
import json
import subprocess
import shutil
from typing import Dict, List, Any, Optional

from src.main_service import CoreMainService
from src.api import file_pb2
from src.api import file_pb2_grpc
from src.services.triple_video_service import TripleVideoService
from src.services.video_merge_service import VideoMergeService
from src.services.video_watermark_service import VideoWatermarkService
from src.services.video_rotation_service import VideoRotationService
from src.services.video_preview_service import VideoPreviewService
from src.services.audio_processing_service import AudioProcessingService
from src.services.video_acceleration_service import VideoAccelerationService
from src.services.video_intro_outro_service import VideoIntroOutroService
from src.services.video_clip_service import VideoClipService
from src.services.video_enhance_service import VideoEnhanceService

logger = logging.getLogger(__name__)

def safe_file_path(file_path: str) -> str:
    """安全处理文件路径，确保编码正确"""
    try:
        # 确保路径是正确的编码
        if isinstance(file_path, bytes):
            file_path = file_path.decode('utf-8', errors='ignore')

        # 规范化路径
        file_path = os.path.normpath(file_path)

        return file_path
    except Exception as e:
        logger.warning(f"文件路径处理失败: {str(e)}")
        return file_path

def calculate_file_md5(file_path: str) -> str:
    """计算文件的MD5哈希值"""
    try:
        safe_path = safe_file_path(file_path)
        hash_md5 = hashlib.md5()
        with open(safe_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"计算MD5失败 {file_path}: {str(e)}")
        return ""

def get_media_info(file_path: str) -> Optional[Dict]:
    """获取媒体文件信息"""
    try:
        # 安全处理文件路径
        safe_path = safe_file_path(file_path)

        # 使用ffprobe获取媒体信息
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams', safe_path
        ]

        # 使用正确的编码处理，避免中文路径问题
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0  # Windows下隐藏窗口
            )
        except FileNotFoundError:
            logger.error(f"ffprobe未找到，请确保已安装FFmpeg: {safe_path}")
            return None
        except subprocess.TimeoutExpired:
            logger.error(f"ffprobe执行超时: {safe_path}")
            return None

        if result.returncode != 0:
            logger.warning(f"ffprobe执行失败 (返回码: {result.returncode}): {safe_path}")
            if result.stderr:
                logger.debug(f"ffprobe错误输出: {result.stderr}")
            return None

        # 检查输出是否为空
        if not result.stdout or result.stdout.strip() == '':
            logger.warning(f"ffprobe输出为空: {safe_path}")
            return None

        try:
            data = json.loads(result.stdout)
        except json.JSONDecodeError as e:
            logger.error(f"解析ffprobe JSON输出失败: {str(e)}")
            logger.debug(f"ffprobe原始输出前500字符: {result.stdout[:500]}...")
            return None

        # 提取基本信息
        format_info = data.get('format', {})
        streams = data.get('streams', [])

        # 查找视频流和音频流
        video_stream = None
        audio_stream = None

        for stream in streams:
            if stream.get('codec_type') == 'video' and video_stream is None:
                video_stream = stream
            elif stream.get('codec_type') == 'audio' and audio_stream is None:
                audio_stream = stream

        # 构建媒体信息
        media_info = {
            'duration': int(float(format_info.get('duration', 0))),
            'resolution': '',
            'video_codec': '',
            'audio_codec': '',
            'frame_rate': 0.0,
            'bitrate': int(format_info.get('bit_rate', 0)) // 1000  # 转换为kbps
        }

        if video_stream:
            width = video_stream.get('width', 0)
            height = video_stream.get('height', 0)
            if width and height:
                media_info['resolution'] = f"{width}x{height}"

            media_info['video_codec'] = video_stream.get('codec_name', '')

            # 获取帧率
            r_frame_rate = video_stream.get('r_frame_rate', '0/1')
            if '/' in r_frame_rate:
                try:
                    num, den = r_frame_rate.split('/')
                    if int(den) != 0:
                        media_info['frame_rate'] = float(num) / float(den)
                except:
                    pass

        if audio_stream:
            media_info['audio_codec'] = audio_stream.get('codec_name', '')

        return media_info

    except subprocess.TimeoutExpired:
        logger.error(f"ffprobe超时: {file_path}")
        return None
    except UnicodeDecodeError as e:
        logger.error(f"ffprobe输出编码错误: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取媒体信息失败 {file_path}: {str(e)}")
        return None

def is_video_file(file_path: str) -> bool:
    """判断是否为视频文件"""
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', '.webm', '.3gp', '.ts'}
    return os.path.splitext(file_path.lower())[1] in video_extensions

class FileServiceImpl(file_pb2_grpc.FileServiceServicer):
    """文件服务gRPC实现类"""

    def __init__(self, main_service: CoreMainService):
        """初始化文件服务

        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.triple_video_service = TripleVideoService()
        self.video_merge_service = VideoMergeService()
        self.video_watermark_service = VideoWatermarkService()
        self.video_rotation_service = VideoRotationService()
        self.video_preview_service = VideoPreviewService()
        self.audio_processing_service = AudioProcessingService()
        self.video_acceleration_service = VideoAccelerationService()
        self.video_intro_outro_service = VideoIntroOutroService()
        self.video_clip_service = VideoClipService()
        self.video_enhance_service = VideoEnhanceService()
        logger.info("文件服务gRPC实现初始化")

    async def GetFilePaths(self, request, context):
        """获取文件路径配置"""
        try:
            platform_id = request.platform_id if request.platform_id else ""
            logger.info(f"收到获取文件路径配置请求: platform_id={platform_id}")
            logger.info(f"请求详情: {request}")
            logger.info(f"上下文详情: {context}")

            # 获取Core配置
            core_config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'core_config.yaml')
            core_config = {}

            try:
                if os.path.exists(core_config_path):
                    with open(core_config_path, 'r', encoding='utf-8') as f:
                        core_config = yaml.safe_load(f)
                    logger.info(f"成功加载Core配置: {core_config_path}")
                else:
                    logger.warning(f"Core配置文件不存在: {core_config_path}")
            except Exception as e:
                logger.error(f"加载Core配置失败: {str(e)}", exc_info=True)

            # 获取文件根路径
            file_root_path = "H:\\PublishSystem"  # 默认路径

            # 获取路径模板
            platform_path_template = "{root_path}\\{platform_id}"
            device_path_template = "{platform_path}\\{device_name}"
            content_path_template = "{device_path}\\content"

            # 如果Core配置中有文件路径配置，使用配置中的值
            if core_config and 'files' in core_config and 'paths' in core_config['files']:
                file_paths = core_config['files']['paths']
                file_root_path = file_paths.get('root_path', file_root_path)
                platform_path_template = file_paths.get('platform_path_template', platform_path_template)
                device_path_template = file_paths.get('device_path_template', device_path_template)
                content_path_template = file_paths.get('content_path_template', content_path_template)
                logger.info(f"从Core配置中获取文件路径配置: root_path={file_root_path}")
            else:
                logger.warning("Core配置中没有文件路径配置，使用默认值")

                # 如果没有文件路径配置，尝试从设备配置中获取
                if core_config and 'devices' in core_config and 'ldconsole_path' in core_config['devices']:
                    ldconsole_path = core_config['devices']['ldconsole_path']
                    if ldconsole_path:
                        # 使用ldconsole路径的父目录的父目录作为文件根路径
                        ldplayer_dir = os.path.dirname(ldconsole_path)
                        file_root_path = os.path.dirname(ldplayer_dir)
                        logger.info(f"从ldconsole路径获取文件根路径: {file_root_path}")

            # 构建响应
            response = file_pb2.FilePathsResponse()
            response.file_root_path = file_root_path
            response.platform_path_template = platform_path_template
            response.device_path_template = device_path_template
            response.content_path_template = content_path_template

            logger.info(f"返回文件路径配置: root_path={file_root_path}")
            logger.info(f"返回文件路径配置: platform_path_template={platform_path_template}")
            logger.info(f"返回文件路径配置: device_path_template={device_path_template}")
            logger.info(f"返回文件路径配置: content_path_template={content_path_template}")
            logger.info(f"完整响应对象: {response}")
            return response

        except Exception as e:
            logger.error(f"获取文件路径配置异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取文件路径配置失败: {str(e)}")
            return file_pb2.FilePathsResponse()

    async def ListDirectory(self, request, context):
        """列出目录内容"""
        try:
            path = request.path
            filter_extensions = list(request.filter_extensions) if request.filter_extensions else None
            include_md5 = request.include_md5
            include_media_info = request.include_media_info

            logger.info(f"收到列出目录请求: path={path}")
            logger.info(f"过滤扩展名: {filter_extensions}, MD5: {include_md5}, 媒体信息: {include_media_info}")

            # 检查路径是否存在
            if not os.path.exists(path):
                context.set_code(5)  # NOT_FOUND
                context.set_details(f"路径不存在: {path}")
                return file_pb2.ListDirectoryResponse()

            # 检查是否是目录
            if not os.path.isdir(path):
                context.set_code(3)  # INVALID_ARGUMENT
                context.set_details(f"路径不是目录: {path}")
                return file_pb2.ListDirectoryResponse()

            # 列出目录内容
            response = file_pb2.ListDirectoryResponse()

            try:
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)
                    is_dir = os.path.isdir(item_path)

                    # 如果是文件且有扩展名过滤，检查是否匹配
                    if not is_dir and filter_extensions:
                        file_ext = os.path.splitext(item.lower())[1][1:]  # 去掉点号
                        if file_ext not in [ext.lower() for ext in filter_extensions]:
                            continue

                    # 获取文件信息
                    file_info = response.files.add()
                    file_info.name = item
                    file_info.path = item_path
                    file_info.is_directory = is_dir

                    # 获取文件大小和修改时间
                    try:
                        stat_info = os.stat(item_path)
                        file_info.size = stat_info.st_size
                        file_info.modified_time = int(stat_info.st_mtime)
                    except Exception as e:
                        logger.warning(f"获取文件信息失败: {str(e)}")
                        file_info.size = 0
                        file_info.modified_time = 0

                    # 计算MD5（仅对文件）
                    if not is_dir and include_md5:
                        logger.debug(f"计算MD5: {item_path}")
                        file_info.md5_hash = calculate_file_md5(item_path)

                    # 获取媒体信息（仅对视频文件）
                    if not is_dir and include_media_info and is_video_file(item_path):
                        logger.debug(f"获取媒体信息: {item_path}")
                        media_info_dict = get_media_info(item_path)
                        if media_info_dict:
                            media_info = file_info.media_info
                            media_info.duration = media_info_dict.get('duration', 0)
                            media_info.resolution = media_info_dict.get('resolution', '')
                            media_info.video_codec = media_info_dict.get('video_codec', '')
                            media_info.audio_codec = media_info_dict.get('audio_codec', '')
                            media_info.frame_rate = media_info_dict.get('frame_rate', 0.0)
                            media_info.bitrate = media_info_dict.get('bitrate', 0)

            except Exception as e:
                logger.error(f"列出目录内容失败: {str(e)}", exc_info=True)
                context.set_code(13)  # INTERNAL
                context.set_details(f"列出目录内容失败: {str(e)}")
                return file_pb2.ListDirectoryResponse()

            logger.info(f"返回目录内容，共{len(response.files)}个项目")
            return response

        except Exception as e:
            logger.error(f"列出目录异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"列出目录失败: {str(e)}")
            return file_pb2.ListDirectoryResponse()

    async def CheckPathExists(self, request, context):
        """检查路径是否存在"""
        try:
            path = request.path
            logger.info(f"收到检查路径请求: path={path}")

            # 检查路径是否存在
            exists = os.path.exists(path)
            is_directory = os.path.isdir(path) if exists else False

            # 构建响应
            response = file_pb2.PathExistsResponse()
            response.exists = exists
            response.is_directory = is_directory

            logger.info(f"路径 {path} 存在: {exists}, 是目录: {is_directory}")
            return response

        except Exception as e:
            logger.error(f"检查路径异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"检查路径失败: {str(e)}")
            return file_pb2.PathExistsResponse()

    async def DeleteFiles(self, request, context):
        """删除文件"""
        try:
            file_paths = list(request.file_paths)
            logger.info(f"收到删除文件请求，文件数量: {len(file_paths)}")

            deleted_count = 0
            errors = []

            for file_path in file_paths:
                try:
                    logger.info(f"准备删除文件: {file_path}")

                    # 检查文件是否存在
                    if not os.path.exists(file_path):
                        error_msg = f"文件不存在: {file_path}"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

                    # 检查路径类型并删除
                    if os.path.isfile(file_path):
                        # 删除文件
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"文件删除成功: {file_path}")
                    elif os.path.isdir(file_path):
                        # 删除文件夹（包括所有内容）
                        import shutil
                        shutil.rmtree(file_path)
                        deleted_count += 1
                        logger.info(f"文件夹删除成功: {file_path}")
                    else:
                        error_msg = f"路径既不是文件也不是文件夹: {file_path}"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

                except PermissionError as e:
                    error_msg = f"权限不足，无法删除文件 {file_path}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                except Exception as e:
                    error_msg = f"删除文件失败 {file_path}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            # 构建响应
            response = file_pb2.DeleteFilesResponse()
            response.success = deleted_count > 0
            response.deleted_count = deleted_count
            response.total_count = len(file_paths)
            response.errors.extend(errors)

            logger.info(f"删除完成: 成功删除 {deleted_count}/{len(file_paths)} 个项目（文件/文件夹）")
            if errors:
                logger.warning(f"删除过程中有 {len(errors)} 个错误")

            return response

        except Exception as e:
            logger.error(f"删除文件异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"删除文件失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.DeleteFilesResponse()
            response.success = False
            response.deleted_count = 0
            response.total_count = len(request.file_paths) if request.file_paths else 0
            response.errors.append(f"删除文件异常: {str(e)}")
            return response

    async def MoveFiles(self, request, context):
        """移动文件"""
        try:
            operations = list(request.operations)
            logger.info(f"收到移动文件请求，操作数量: {len(operations)}")

            moved_count = 0
            errors = []

            for operation in operations:
                try:
                    source_path = operation.source_path
                    target_path = operation.target_path

                    logger.info(f"准备移动文件: {source_path} -> {target_path}")

                    # 检查源文件是否存在
                    if not os.path.exists(source_path):
                        error_msg = f"源文件不存在: {source_path}"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

                    # 确保目标目录存在
                    target_dir = os.path.dirname(target_path)
                    if not os.path.exists(target_dir):
                        os.makedirs(target_dir, exist_ok=True)
                        logger.info(f"创建目标目录: {target_dir}")

                    # 如果目标文件已存在，添加时间戳后缀
                    if os.path.exists(target_path):
                        timestamp = int(time.time())
                        name, ext = os.path.splitext(target_path)
                        target_path = f"{name}_{timestamp}{ext}"
                        logger.info(f"目标文件已存在，重命名为: {target_path}")

                    # 移动文件
                    shutil.move(source_path, target_path)
                    moved_count += 1
                    logger.info(f"文件移动成功: {source_path} -> {target_path}")

                except PermissionError as e:
                    error_msg = f"权限不足，无法移动文件 {source_path}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                except Exception as e:
                    error_msg = f"移动文件失败 {source_path}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            # 构建响应
            response = file_pb2.MoveFilesResponse()
            response.success = moved_count > 0
            response.moved_count = moved_count
            response.total_count = len(operations)
            response.errors.extend(errors)

            logger.info(f"移动完成: 成功移动 {moved_count}/{len(operations)} 个文件")
            if errors:
                logger.warning(f"移动过程中有 {len(errors)} 个错误")

            return response

        except Exception as e:
            logger.error(f"移动文件异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"移动文件失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.MoveFilesResponse()
            response.success = False
            response.moved_count = 0
            response.total_count = len(request.operations) if request.operations else 0
            response.errors.append(f"移动文件异常: {str(e)}")
            return response

    async def CreateDirectory(self, request, context):
        """创建文件夹"""
        try:
            directory_path = request.directory_path
            create_parents = request.create_parents

            logger.info(f"收到创建文件夹请求: {directory_path}, 创建父目录: {create_parents}")

            # 检查文件夹是否已存在
            if os.path.exists(directory_path):
                if os.path.isdir(directory_path):
                    logger.info(f"文件夹已存在: {directory_path}")
                    response = file_pb2.CreateDirectoryResponse()
                    response.success = True
                    response.created_path = directory_path
                    return response
                else:
                    error_msg = f"路径已存在但不是文件夹: {directory_path}"
                    logger.error(error_msg)
                    response = file_pb2.CreateDirectoryResponse()
                    response.success = False
                    response.error = error_msg
                    return response

            # 创建文件夹
            try:
                if create_parents:
                    os.makedirs(directory_path, exist_ok=True)
                else:
                    os.mkdir(directory_path)

                logger.info(f"文件夹创建成功: {directory_path}")

                response = file_pb2.CreateDirectoryResponse()
                response.success = True
                response.created_path = directory_path
                return response

            except PermissionError as e:
                error_msg = f"权限不足，无法创建文件夹 {directory_path}: {str(e)}"
                logger.error(error_msg)
                response = file_pb2.CreateDirectoryResponse()
                response.success = False
                response.error = error_msg
                return response

        except Exception as e:
            logger.error(f"创建文件夹异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"创建文件夹失败: {str(e)}")

            response = file_pb2.CreateDirectoryResponse()
            response.success = False
            response.error = f"创建文件夹异常: {str(e)}"
            return response

    async def ArchivePublishedFiles(self, request, context):
        """归档已发布文件"""
        try:
            folder_path = request.folder_path
            archive_folder_name = request.archive_folder_name
            platforms = list(request.platforms) if request.platforms else []
            published_status = dict(request.published_status)

            logger.info(f"收到归档已发布文件请求:")
            logger.info(f"  - 文件夹: {folder_path}")
            logger.info(f"  - 归档文件夹: {archive_folder_name}")
            logger.info(f"  - 检查平台: {platforms}")
            logger.info(f"  - 发布状态映射: {len(published_status)} 个文件")

            # 检查文件夹是否存在
            if not os.path.exists(folder_path):
                error_msg = f"文件夹不存在: {folder_path}"
                logger.error(error_msg)
                context.set_code(5)  # NOT_FOUND
                context.set_details(error_msg)
                response = file_pb2.ArchivePublishedFilesResponse()
                response.success = False
                response.errors.append(error_msg)
                return response

            if not os.path.isdir(folder_path):
                error_msg = f"路径不是文件夹: {folder_path}"
                logger.error(error_msg)
                context.set_code(3)  # INVALID_ARGUMENT
                context.set_details(error_msg)
                response = file_pb2.ArchivePublishedFilesResponse()
                response.success = False
                response.errors.append(error_msg)
                return response

            # 创建归档文件夹
            archive_folder_path = os.path.join(folder_path, archive_folder_name)
            if not os.path.exists(archive_folder_path):
                os.makedirs(archive_folder_path, exist_ok=True)
                logger.info(f"创建归档文件夹: {archive_folder_path}")

            # 获取文件夹中的所有文件
            all_files = []
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isfile(item_path) and item != archive_folder_name:
                    all_files.append(item)

            archived_count = 0
            skipped_count = 0
            archived_files = []
            errors = []

            # 处理每个文件
            for filename in all_files:
                try:
                    file_path = os.path.join(folder_path, filename)

                    # 计算文件MD5
                    md5_hash = calculate_file_md5(file_path)
                    if not md5_hash:
                        skipped_count += 1
                        logger.warning(f"无法计算MD5，跳过文件: {filename}")
                        continue

                    # 检查文件是否已发布
                    is_published = published_status.get(md5_hash, False)

                    if is_published:
                        # 移动文件到归档文件夹
                        archive_file_path = os.path.join(archive_folder_path, filename)

                        # 如果归档文件夹中已存在同名文件，添加时间戳后缀
                        if os.path.exists(archive_file_path):
                            timestamp = int(time.time())
                            name, ext = os.path.splitext(filename)
                            new_filename = f"{name}_{timestamp}{ext}"
                            archive_file_path = os.path.join(archive_folder_path, new_filename)
                            logger.info(f"文件已存在，重命名为: {new_filename}")

                        # 移动文件
                        shutil.move(file_path, archive_file_path)
                        archived_count += 1
                        archived_files.append(filename)
                        logger.info(f"归档文件: {filename} -> {archive_folder_name}/{os.path.basename(archive_file_path)}")
                    else:
                        skipped_count += 1
                        logger.debug(f"文件未发布，跳过: {filename}")

                except Exception as e:
                    error_msg = f"归档文件失败 {filename}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            # 构建响应
            response = file_pb2.ArchivePublishedFilesResponse()
            response.success = True
            response.archived_count = archived_count
            response.skipped_count = skipped_count
            response.archive_folder = archive_folder_path
            response.archived_files.extend(archived_files)
            response.errors.extend(errors)

            logger.info(f"归档完成: 归档 {archived_count} 个文件，跳过 {skipped_count} 个文件")
            if errors:
                logger.warning(f"归档过程中有 {len(errors)} 个错误")

            return response

        except Exception as e:
            logger.error(f"归档已发布文件异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"归档已发布文件失败: {str(e)}")

            response = file_pb2.ArchivePublishedFilesResponse()
            response.success = False
            response.archived_count = 0
            response.skipped_count = 0
            response.errors.append(f"归档已发布文件异常: {str(e)}")
            return response

    async def CreateTripleVideo(self, request, context):
        """创建三拼视频"""
        try:
            folder_path = request.folder_path
            output_path = request.output_path
            video_duration_per_segment = request.video_duration_per_segment
            transition_duration = request.transition_duration
            output_quality = request.output_quality

            logger.info(f"收到创建三拼视频请求:")
            logger.info(f"  - 文件夹: {folder_path}")
            logger.info(f"  - 输出: {output_path}")
            logger.info(f"  - 每段时长: {video_duration_per_segment}秒")
            logger.info(f"  - 转场时长: {transition_duration}秒")
            logger.info(f"  - 输出质量: {output_quality}")

            # 调用三拼视频服务
            result = await self.triple_video_service.create_triple_video(
                folder_path=folder_path,
                output_path=output_path,
                video_duration=video_duration_per_segment,
                output_quality=output_quality
            )

            # 构建响应
            response = file_pb2.CreateTripleVideoResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.output_file = result.get("output_file", output_path)
            response.processed_videos = result.get("processed_videos", 0)

            if response.success:
                logger.info(f"三拼视频创建成功: {response.output_file}, 处理了 {response.processed_videos} 个视频")
            else:
                logger.error(f"三拼视频创建失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"创建三拼视频异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"创建三拼视频失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.CreateTripleVideoResponse()
            response.success = False
            response.error = f"创建三拼视频异常: {str(e)}"
            response.output_file = ""
            response.processed_videos = 0
            return response

    async def MergeVideos(self, request, context):
        """合并视频"""
        try:
            folder_path = request.folder_path
            target_duration_min = request.target_duration_min
            target_duration_max = request.target_duration_max
            enable_transitions = request.enable_transitions
            output_quality = request.output_quality
            max_videos_per_merge = request.max_videos_per_merge

            logger.info(f"收到视频合并请求:")
            logger.info(f"  - 文件夹: {folder_path}")
            logger.info(f"  - 目标时长: {target_duration_min}-{target_duration_max}秒")
            logger.info(f"  - 启用转场: {enable_transitions}")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 每组最大视频数: {max_videos_per_merge}")

            # 调用视频合并服务
            result = await self.video_merge_service.merge_videos(
                folder_path=folder_path,
                target_duration_min=target_duration_min,
                target_duration_max=target_duration_max,
                enable_transitions=enable_transitions,
                output_quality=output_quality,
                max_videos_per_merge=max_videos_per_merge
            )

            # 构建响应
            response = file_pb2.MergeVideosResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.output_files.extend(result.get("output_files", []))
            response.processed_videos = result.get("processed_videos", 0)
            response.successful_merges = result.get("successful_merges", 0)

            if response.success:
                logger.info(f"视频合并成功: 处理了 {response.processed_videos} 个视频，成功合并 {response.successful_merges} 组")
            else:
                logger.error(f"视频合并失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"视频合并异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"视频合并失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.MergeVideosResponse()
            response.success = False
            response.error = f"视频合并异常: {str(e)}"
            response.processed_videos = 0
            response.successful_merges = 0
            return response

    async def DetectWatermark(self, request, context):
        """检测视频水印"""
        try:
            video_path = request.video_path
            detection_mode = request.detection_mode
            template_path = request.template_path
            detection_region = request.detection_region
            sensitivity = request.sensitivity
            save_detection_result = request.save_detection_result

            logger.info(f"收到检测视频水印请求:")
            logger.info(f"  - 视频路径: {video_path}")
            logger.info(f"  - 检测模式: {detection_mode}")
            logger.info(f"  - 敏感度: {sensitivity}")

            # 构建检测配置
            detection_config = {
                'detection_mode': detection_mode or 'auto',
                'template_path': template_path,
                'detection_region': detection_region,
                'sensitivity': sensitivity if sensitivity > 0 else 0.7,
                'save_detection_result': save_detection_result
            }

            # 调用水印检测服务
            result = await self.video_watermark_service.detect_watermark(video_path, detection_config)

            # 构建响应
            response = file_pb2.DetectWatermarkResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.watermark_detected = result.get("watermark_detected", False)
            response.detection_result_path = result.get("detection_result_path", "")
            response.detection_time_ms = result.get("detection_time_ms", 0)

            # 添加水印信息
            for watermark in result.get("watermarks", []):
                wm_info = response.watermarks.add()
                wm_info.watermark_type = watermark.get("watermark_type", "")
                wm_info.position = watermark.get("position", "")
                wm_info.confidence = watermark.get("confidence", 0.0)
                wm_info.description = watermark.get("description", "")
                wm_info.time_range = watermark.get("time_range", "")

            if response.success:
                logger.info(f"水印检测成功: 检测到 {len(response.watermarks)} 个水印，耗时 {response.detection_time_ms}ms")
            else:
                logger.error(f"水印检测失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"检测视频水印异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"检测视频水印失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.DetectWatermarkResponse()
            response.success = False
            response.error = f"检测视频水印异常: {str(e)}"
            response.watermark_detected = False
            response.detection_time_ms = 0
            return response

    async def RemoveWatermark(self, request, context):
        """清除视频水印"""
        try:
            input_video_path = request.input_video_path
            output_video_path = request.output_video_path
            removal_mode = request.removal_mode
            watermark_regions = list(request.watermark_regions)
            inpaint_method = request.inpaint_method
            output_quality = request.output_quality
            preserve_encoding = request.preserve_encoding

            logger.info(f"收到清除视频水印请求:")
            logger.info(f"  - 输入视频: {input_video_path}")
            logger.info(f"  - 输出视频: {output_video_path}")
            logger.info(f"  - 清除模式: {removal_mode}")
            logger.info(f"  - 水印区域数量: {len(watermark_regions)}")

            # 构建清除配置
            removal_config = {
                'removal_mode': removal_mode or 'auto',
                'watermark_regions': watermark_regions,
                'inpaint_method': inpaint_method or 'blur',
                'output_quality': output_quality or 'medium',
                'preserve_encoding': preserve_encoding
            }

            # 调用水印清除服务
            result = await self.video_watermark_service.remove_watermark(
                input_video_path, output_video_path, removal_config
            )

            # 构建响应
            response = file_pb2.RemoveWatermarkResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.output_file_path = result.get("output_file_path", "")
            response.processing_time_ms = result.get("processing_time_ms", 0)
            response.original_file_size = result.get("original_file_size", 0)
            response.output_file_size = result.get("output_file_size", 0)
            response.removed_watermarks_count = result.get("removed_watermarks_count", 0)

            if response.success:
                logger.info(f"水印清除成功: {response.output_file_path}，耗时 {response.processing_time_ms}ms")
            else:
                logger.error(f"水印清除失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"清除视频水印异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"清除视频水印失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.RemoveWatermarkResponse()
            response.success = False
            response.error = f"清除视频水印异常: {str(e)}"
            response.output_file_path = ""
            response.processing_time_ms = 0
            response.original_file_size = 0
            response.output_file_size = 0
            response.removed_watermarks_count = 0
            return response

    async def BatchProcessWatermark(self, request, context):
        """批量处理视频水印"""
        try:
            input_folder_path = request.input_folder_path
            output_folder_path = request.output_folder_path
            process_mode = request.process_mode
            file_filters = list(request.file_filters)
            recursive = request.recursive
            max_concurrent = request.max_concurrent

            logger.info(f"收到批量处理视频水印请求:")
            logger.info(f"  - 输入文件夹: {input_folder_path}")
            logger.info(f"  - 输出文件夹: {output_folder_path}")
            logger.info(f"  - 处理模式: {process_mode}")
            logger.info(f"  - 文件过滤器: {file_filters}")
            logger.info(f"  - 递归处理: {recursive}")
            logger.info(f"  - 最大并发: {max_concurrent}")

            # 构建批量处理配置
            batch_config = {
                'input_folder_path': input_folder_path,
                'output_folder_path': output_folder_path,
                'process_mode': process_mode or 'detect_and_remove',
                'file_filters': file_filters if file_filters else ['*.mp4'],
                'recursive': recursive,
                'max_concurrent': max_concurrent if max_concurrent > 0 else 3
            }

            # 添加检测配置
            if request.detection_config:
                detection_config = {
                    'detection_mode': request.detection_config.detection_mode or 'auto',
                    'template_path': request.detection_config.template_path,
                    'detection_region': request.detection_config.detection_region,
                    'sensitivity': request.detection_config.sensitivity if request.detection_config.sensitivity > 0 else 0.7,
                    'save_detection_result': request.detection_config.save_detection_result
                }
                batch_config['detection_config'] = detection_config

            # 添加清除配置
            if request.removal_config:
                removal_config = {
                    'removal_mode': request.removal_config.removal_mode or 'auto',
                    'watermark_regions': list(request.removal_config.watermark_regions),
                    'inpaint_method': request.removal_config.inpaint_method or 'blur',
                    'output_quality': request.removal_config.output_quality or 'medium',
                    'preserve_encoding': request.removal_config.preserve_encoding
                }
                batch_config['removal_config'] = removal_config

            # 调用批量处理服务
            result = await self.video_watermark_service.batch_process_watermark(batch_config)

            # 构建响应
            response = file_pb2.BatchProcessWatermarkResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.total_files = result.get("total_files", 0)
            response.successful_files = result.get("successful_files", 0)
            response.failed_files = result.get("failed_files", 0)
            response.total_processing_time_ms = result.get("total_processing_time_ms", 0)

            # 添加处理结果
            for batch_result in result.get("results", []):
                result_item = response.results.add()
                result_item.file_path = batch_result.get("file_path", "")
                result_item.status = batch_result.get("status", "")
                result_item.error_message = batch_result.get("error_message", "")
                result_item.processing_time_ms = batch_result.get("processing_time_ms", 0)

                # 添加检测结果
                detection_result = batch_result.get("detection_result")
                if detection_result:
                    det_resp = result_item.detection_result
                    det_resp.success = detection_result.get("success", False)
                    det_resp.error = detection_result.get("error", "")
                    det_resp.watermark_detected = detection_result.get("watermark_detected", False)
                    det_resp.detection_time_ms = detection_result.get("detection_time_ms", 0)

                    for watermark in detection_result.get("watermarks", []):
                        wm_info = det_resp.watermarks.add()
                        wm_info.watermark_type = watermark.get("watermark_type", "")
                        wm_info.position = watermark.get("position", "")
                        wm_info.confidence = watermark.get("confidence", 0.0)
                        wm_info.description = watermark.get("description", "")

                # 添加清除结果
                removal_result = batch_result.get("removal_result")
                if removal_result:
                    rem_resp = result_item.removal_result
                    rem_resp.success = removal_result.get("success", False)
                    rem_resp.error = removal_result.get("error", "")
                    rem_resp.output_file_path = removal_result.get("output_file_path", "")
                    rem_resp.processing_time_ms = removal_result.get("processing_time_ms", 0)
                    rem_resp.original_file_size = removal_result.get("original_file_size", 0)
                    rem_resp.output_file_size = removal_result.get("output_file_size", 0)
                    rem_resp.removed_watermarks_count = removal_result.get("removed_watermarks_count", 0)

            if response.success:
                logger.info(f"批量处理完成: 总计 {response.total_files} 个文件，成功 {response.successful_files} 个，失败 {response.failed_files} 个")
            else:
                logger.error(f"批量处理失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"批量处理视频水印异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"批量处理视频水印失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.BatchProcessWatermarkResponse()
            response.success = False
            response.error = f"批量处理视频水印异常: {str(e)}"
            response.total_files = 0
            response.successful_files = 0
            response.failed_files = 0
            response.total_processing_time_ms = 0
            return response

    async def RotateVideos(self, request, context):
        """旋转视频"""
        try:
            video_paths = list(request.video_paths)
            rotation_angle = request.rotation_angle
            output_quality = request.output_quality or 'medium'
            overwrite_original = request.overwrite_original
            output_suffix = request.output_suffix or '_rotated'

            logger.info(f"收到旋转视频请求:")
            logger.info(f"  - 视频文件数量: {len(video_paths)}")
            logger.info(f"  - 旋转角度: {rotation_angle}°")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 覆盖原文件: {overwrite_original}")
            logger.info(f"  - 输出后缀: {output_suffix}")

            # 调用视频旋转服务
            result = await self.video_rotation_service.rotate_videos(
                video_paths=video_paths,
                rotation_angle=rotation_angle,
                output_quality=output_quality,
                overwrite_original=overwrite_original,
                output_suffix=output_suffix
            )

            # 构建响应
            response = file_pb2.RotateVideosResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.successful_count = result.get("successful_count", 0)
            response.failed_count = result.get("failed_count", 0)
            response.total_processing_time_ms = result.get("total_processing_time_ms", 0)

            # 添加旋转结果
            for rotation_result in result.get("results", []):
                result_item = response.results.add()
                result_item.original_path = rotation_result.get("original_path", "")
                result_item.output_path = rotation_result.get("output_path", "")
                result_item.success = rotation_result.get("success", False)
                result_item.error_message = rotation_result.get("error_message", "")
                result_item.processing_time_ms = rotation_result.get("processing_time_ms", 0)
                result_item.original_file_size = rotation_result.get("original_file_size", 0)
                result_item.output_file_size = rotation_result.get("output_file_size", 0)

            if response.success:
                logger.info(f"视频旋转完成: 成功 {response.successful_count} 个，失败 {response.failed_count} 个")
            else:
                logger.error(f"视频旋转失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"旋转视频异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"旋转视频失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.RotateVideosResponse()
            response.success = False
            response.error = f"旋转视频异常: {str(e)}"
            response.successful_count = 0
            response.failed_count = len(request.video_paths) if request.video_paths else 0
            response.total_processing_time_ms = 0

            return response

    async def GenerateVideoThumbnail(self, request, context):
        """生成视频缩略图"""
        try:
            video_path = safe_file_path(request.video_path)
            logger.info(f"收到生成视频缩略图请求: {video_path}")

            # 验证文件存在
            if not os.path.exists(video_path):
                error_msg = f"视频文件不存在: {video_path}"
                logger.error(error_msg)
                context.set_code(5)  # NOT_FOUND
                context.set_details(error_msg)

                response = file_pb2.GenerateVideoThumbnailResponse()
                response.success = False
                response.error = error_msg
                return response

            # 调用视频预览服务生成缩略图
            result = await self.video_preview_service.generate_thumbnail(
                video_path=video_path,
                output_path=request.thumbnail_path if request.thumbnail_path else None,
                timestamp=request.timestamp if request.timestamp > 0 else None,
                max_width=request.width if request.width > 0 else 320,
                max_height=request.height if request.height > 0 else 180,
                quality=request.quality if request.quality > 0 else 85,
                force_regenerate=request.force_regenerate
            )

            # 构建响应
            response = file_pb2.GenerateVideoThumbnailResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.thumbnail_path = result.get("thumbnail_path", "")
            response.thumbnail_url = result.get("thumbnail_url", "")
            response.thumbnail_size = result.get("thumbnail_size", 0)
            response.generation_time_ms = result.get("generation_time_ms", 0)
            response.from_cache = result.get("from_cache", False)

            if response.success:
                logger.info(f"缩略图生成成功: {response.thumbnail_path}")
            else:
                logger.error(f"缩略图生成失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"生成视频缩略图异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"生成视频缩略图失败: {str(e)}")

            response = file_pb2.GenerateVideoThumbnailResponse()
            response.success = False
            response.error = f"生成视频缩略图异常: {str(e)}"
            return response

    async def GetVideoPreviewInfo(self, request, context):
        """获取视频预览信息"""
        try:
            video_path = safe_file_path(request.video_path)
            logger.info(f"收到获取视频预览信息请求: {video_path}")

            # 验证文件存在
            if not os.path.exists(video_path):
                error_msg = f"视频文件不存在: {video_path}"
                logger.error(error_msg)
                context.set_code(5)  # NOT_FOUND
                context.set_details(error_msg)

                response = file_pb2.GetVideoPreviewInfoResponse()
                response.success = False
                response.error = error_msg
                return response

            # 获取视频基本信息
            video_info = await self.video_preview_service.get_video_info(video_path)

            # 构建响应
            response = file_pb2.GetVideoPreviewInfoResponse()
            response.success = True

            # 填充媒体信息
            media_info = response.media_info
            media_info.duration = int(video_info.get("duration", 0))
            media_info.resolution = f"{video_info['video']['width']}x{video_info['video']['height']}"
            media_info.video_codec = video_info['video'].get("codec", "")
            media_info.frame_rate = video_info['video'].get("frame_rate", 0.0)
            media_info.bitrate = video_info['video'].get("bitrate", 0)

            if video_info.get("audio"):
                media_info.audio_codec = video_info['audio'].get("codec", "")

            # 如果需要缩略图信息
            if request.include_thumbnail:
                try:
                    thumbnail_result = await self.video_preview_service.generate_thumbnail(
                        video_path=video_path,
                        force_regenerate=False
                    )

                    if thumbnail_result.get("success"):
                        thumbnail_info = response.thumbnail_info
                        thumbnail_info.thumbnail_path = thumbnail_result.get("thumbnail_path", "")
                        thumbnail_info.thumbnail_size = thumbnail_result.get("thumbnail_size", 0)
                        thumbnail_info.width = 320  # 默认宽度
                        thumbnail_info.height = 180  # 默认高度
                        thumbnail_info.exists = os.path.exists(thumbnail_result.get("thumbnail_path", ""))
                        thumbnail_info.generated_time = int(time.time())
                except Exception as e:
                    logger.warning(f"生成缩略图信息失败: {str(e)}")

            logger.info(f"视频预览信息获取成功: {video_path}")
            return response

        except Exception as e:
            logger.error(f"获取视频预览信息异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取视频预览信息失败: {str(e)}")

            response = file_pb2.GetVideoPreviewInfoResponse()
            response.success = False
            response.error = f"获取视频预览信息异常: {str(e)}"
            return response

    async def GenerateVideoPreviewClip(self, request, context):
        """生成视频预览片段"""
        try:
            video_path = safe_file_path(request.video_path)
            logger.info(f"收到生成视频预览片段请求: {video_path}")

            # 验证文件存在
            if not os.path.exists(video_path):
                error_msg = f"视频文件不存在: {video_path}"
                logger.error(error_msg)
                context.set_code(5)  # NOT_FOUND
                context.set_details(error_msg)

                response = file_pb2.GenerateVideoPreviewClipResponse()
                response.success = False
                response.error = error_msg
                return response

            # 调用视频预览服务生成预览片段
            result = await self.video_preview_service.generate_preview_clip(
                video_path=video_path,
                output_path=request.preview_clip_path if request.preview_clip_path else None,
                start_time=request.start_time,
                duration=request.duration if request.duration > 0 else 30,
                output_quality=request.output_quality if request.output_quality else "medium",
                force_regenerate=request.force_regenerate
            )

            # 构建响应
            response = file_pb2.GenerateVideoPreviewClipResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.preview_clip_path = result.get("preview_clip_path", "")
            response.preview_clip_size = result.get("preview_clip_size", 0)
            response.generation_time_ms = result.get("generation_time_ms", 0)
            response.from_cache = result.get("from_cache", False)

            if response.success:
                logger.info(f"预览片段生成成功: {response.preview_clip_path}")
            else:
                logger.error(f"预览片段生成失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"生成视频预览片段异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"生成视频预览片段失败: {str(e)}")

            response = file_pb2.GenerateVideoPreviewClipResponse()
            response.success = False
            response.error = f"生成视频预览片段异常: {str(e)}"
            return response

    async def GenerateSubtitlesBatch(self, request, context):
        """批量生成字幕"""
        try:
            logger.info(f"收到批量生成字幕请求: {request.folder_path}")

            # 调用音频处理服务
            result = await self.audio_processing_service.generate_subtitles_batch(
                folder_path=request.folder_path,
                output_format=request.output_format or 'srt',
                language=request.language or 'auto',
                model_size=request.model_size or 'base',
                max_concurrent=request.max_concurrent or 2
            )

            # 构建响应
            response = file_pb2.GenerateSubtitlesBatchResponse()
            response.success = result.get('success', False)
            response.error = result.get('error', '')
            response.processed_files = result.get('processed_files', 0)
            response.successful_count = result.get('successful_count', 0)
            response.failed_count = result.get('failed_count', 0)
            response.output_directory = result.get('output_directory', '')

            # 添加处理结果详情
            for item in result.get('results', []):
                result_item = response.results.add()
                result_item.success = item.get('success', False)
                result_item.input_file = item.get('input_file', '')
                result_item.output_file = item.get('output_file', '')
                result_item.error_message = item.get('error_message', '')
                result_item.processing_time_ms = item.get('processing_time_ms', 0)
                result_item.input_file_size = item.get('input_file_size', 0)
                result_item.output_file_size = item.get('output_file_size', 0)
                result_item.message = item.get('message', '')

            if response.success:
                logger.info(f"批量生成字幕成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量生成字幕失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"批量生成字幕异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"批量生成字幕失败: {str(e)}")

            response = file_pb2.GenerateSubtitlesBatchResponse()
            response.success = False
            response.error = f"批量生成字幕异常: {str(e)}"
            return response

    async def ExtractAudioBatch(self, request, context):
        """批量分离音频"""
        try:
            logger.info(f"收到批量分离音频请求: {request.folder_path}")

            # 调用音频处理服务
            result = await self.audio_processing_service.extract_audio_batch(
                folder_path=request.folder_path,
                output_format=request.output_format or 'wav',
                quality=request.quality or 'high',
                max_concurrent=request.max_concurrent or 3
            )

            # 构建响应
            response = file_pb2.ExtractAudioBatchResponse()
            response.success = result.get('success', False)
            response.error = result.get('error', '')
            response.processed_files = result.get('processed_files', 0)
            response.successful_count = result.get('successful_count', 0)
            response.failed_count = result.get('failed_count', 0)
            response.output_directory = result.get('output_directory', '')

            # 添加处理结果详情
            for item in result.get('results', []):
                result_item = response.results.add()
                result_item.success = item.get('success', False)
                result_item.input_file = item.get('input_file', '')
                result_item.output_file = item.get('output_file', '')
                result_item.error_message = item.get('error_message', '')
                result_item.processing_time_ms = item.get('processing_time_ms', 0)
                result_item.input_file_size = item.get('input_file_size', 0)
                result_item.output_file_size = item.get('output_file_size', 0)
                result_item.message = item.get('message', '')

            if response.success:
                logger.info(f"批量分离音频成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量分离音频失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"批量分离音频异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"批量分离音频失败: {str(e)}")

            response = file_pb2.ExtractAudioBatchResponse()
            response.success = False
            response.error = f"批量分离音频异常: {str(e)}"
            return response

    async def SeparateVocalsBatch(self, request, context):
        """批量人声分离"""
        try:
            logger.info(f"收到批量人声分离请求: {request.folder_path}")
            if request.selected_files:
                logger.info(f"选中文件数量: {len(request.selected_files)}")
            else:
                logger.info("处理整个文件夹")

            # 调用音频处理服务
            result = await self.audio_processing_service.separate_vocals_batch(
                folder_path=request.folder_path,
                output_type=request.output_type or 'vocals',
                separation_method=request.separation_method or 'ffmpeg',
                output_quality=request.output_quality or 'high',
                max_concurrent=request.max_concurrent or 2,
                selected_files=list(request.selected_files) if request.selected_files else None
            )

            # 构建响应
            response = file_pb2.SeparateVocalsBatchResponse()
            response.success = result.get('success', False)
            response.error = result.get('error', '')
            response.processed_files = result.get('processed_files', 0)
            response.successful_count = result.get('successful_count', 0)
            response.failed_count = result.get('failed_count', 0)
            response.output_directory = result.get('output_directory', '')

            # 添加处理结果详情
            for item in result.get('results', []):
                result_item = response.results.add()
                result_item.success = item.get('success', False)
                result_item.input_file = item.get('input_file', '')
                result_item.vocals_file = item.get('vocals_file', '')
                result_item.instrumental_file = item.get('instrumental_file', '')
                result_item.error_message = item.get('error_message', '')
                result_item.processing_time_ms = item.get('processing_time_ms', 0)
                result_item.input_file_size = item.get('input_file_size', 0)
                result_item.vocals_file_size = item.get('vocals_file_size', 0)
                result_item.instrumental_file_size = item.get('instrumental_file_size', 0)
                result_item.message = item.get('message', '')

            if response.success:
                logger.info(f"批量人声分离成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量人声分离失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"批量人声分离异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"批量人声分离失败: {str(e)}")

            response = file_pb2.SeparateVocalsBatchResponse()
            response.success = False
            response.error = f"批量人声分离异常: {str(e)}"
            return response

    async def ReplaceAudioBatch(self, request, context):
        """批量替换音频"""
        try:
            logger.info(f"收到批量替换音频请求: {request.folder_path}")

            # 调用音频处理服务
            result = await self.audio_processing_service.replace_audio_batch(
                folder_path=request.folder_path,
                new_audio_path=request.new_audio_path,
                audio_volume=request.audio_volume or 1.0,
                fade_duration=request.fade_duration or 0.5,
                max_concurrent=request.max_concurrent or 3
            )

            # 构建响应
            response = file_pb2.ReplaceAudioBatchResponse()
            response.success = result.get('success', False)
            response.error = result.get('error', '')
            response.processed_files = result.get('processed_files', 0)
            response.successful_count = result.get('successful_count', 0)
            response.failed_count = result.get('failed_count', 0)
            response.output_directory = result.get('output_directory', '')
            response.new_audio_file = result.get('new_audio_file', '')

            # 添加处理结果详情
            for item in result.get('results', []):
                result_item = response.results.add()
                result_item.success = item.get('success', False)
                result_item.input_file = item.get('input_file', '')
                result_item.output_file = item.get('output_file', '')
                result_item.error_message = item.get('error_message', '')
                result_item.processing_time_ms = item.get('processing_time_ms', 0)
                result_item.input_file_size = item.get('input_file_size', 0)
                result_item.output_file_size = item.get('output_file_size', 0)
                result_item.message = item.get('message', '')

            if response.success:
                logger.info(f"批量替换音频成功: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量替换音频失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"批量替换音频异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"批量替换音频失败: {str(e)}")

            response = file_pb2.ReplaceAudioBatchResponse()
            response.success = False
            response.error = f"批量替换音频异常: {str(e)}"
            return response

    async def AccelerateVideos(self, request, context):
        """批量视频加速"""
        try:
            video_paths = list(request.video_paths)
            target_duration = request.target_duration or 59
            output_quality = request.output_quality or 'medium'
            overwrite_original = request.overwrite_original
            output_suffix = request.output_suffix or '_accelerated'

            logger.info(f"收到批量视频加速请求:")
            logger.info(f"  - 视频文件数量: {len(video_paths)}")
            logger.info(f"  - 目标时长: {target_duration}秒")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 覆盖原文件: {overwrite_original}")
            if not overwrite_original:
                logger.info(f"  - 输出到 accelerated_videos 文件夹，保持原文件名")

            # 调用视频加速服务
            result = await self.video_acceleration_service.accelerate_videos(
                video_paths=video_paths,
                target_duration=target_duration,
                output_quality=output_quality,
                overwrite_original=overwrite_original,
                output_suffix=output_suffix
            )

            # 构建响应
            response = file_pb2.AccelerateVideosResponse()
            response.success = result.get('success', False)
            response.error = result.get('error', '')
            response.total_processing_time_ms = result.get('total_processing_time_ms', 0)
            response.successful_count = result.get('successful_count', 0)
            response.failed_count = result.get('failed_count', 0)

            # 添加处理结果详情
            # 清空results字段（不能直接赋值空列表）
            del response.results[:]
            for item in result.get('results', []):
                result_item = file_pb2.VideoAccelerationResult()
                result_item.original_path = item.get('original_path', '')
                result_item.output_path = item.get('output_path', '')
                result_item.success = item.get('success', False)
                result_item.error_message = item.get('error_message', '')
                result_item.processing_time_ms = item.get('processing_time_ms', 0)
                result_item.original_file_size = item.get('original_file_size', 0)
                result_item.output_file_size = item.get('output_file_size', 0)
                result_item.original_duration = item.get('original_duration', 0.0)
                result_item.output_duration = item.get('output_duration', 0.0)
                result_item.speed_factor = item.get('speed_factor', 0.0)
                response.results.append(result_item)

            if response.success:
                logger.info(f"批量视频加速成功: 处理了 {len(video_paths)} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量视频加速失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"批量视频加速异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"批量视频加速失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.AccelerateVideosResponse()
            response.success = False
            response.error = f"批量视频加速异常: {str(e)}"
            response.successful_count = 0
            response.failed_count = len(request.video_paths) if request.video_paths else 0
            response.total_processing_time_ms = 0

            return response

    async def BatchAddIntroOutro(self, request, context):
        """批量添加视频片头片尾"""
        try:
            folder_path = request.folder_path
            intro_path = request.intro_path if request.intro_path else None
            outro_path = request.outro_path if request.outro_path else None
            output_folder = request.output_folder if request.output_folder else None
            transition_effect = request.transition_effect or 'fade'
            transition_duration = request.transition_duration or 1.0
            output_quality = request.output_quality or 'medium'
            max_concurrent = request.max_concurrent or 3
            overwrite_original = request.overwrite_original
            output_suffix = request.output_suffix or '_with_intro_outro'

            # 获取选中文件列表
            selected_files = list(request.selected_files) if hasattr(request, 'selected_files') and request.selected_files else None

            logger.info(f"收到批量添加片头片尾请求:")
            logger.info(f"  - 文件夹路径: {folder_path}")
            logger.info(f"  - request 对象属性: {dir(request)}")
            logger.info(f"  - hasattr selected_files: {hasattr(request, 'selected_files')}")
            if hasattr(request, 'selected_files'):
                logger.info(f"  - request.selected_files 内容: {list(request.selected_files)}")

            if selected_files:
                logger.info(f"  - 选中文件: {selected_files}")
                logger.info(f"  - 选中文件数量: {len(selected_files)}")
            else:
                logger.info(f"  - 处理模式: 整个文件夹")
            logger.info(f"  - 片头文件: {intro_path}")
            logger.info(f"  - 片尾文件: {outro_path}")
            logger.info(f"  - 输出文件夹: {output_folder}")
            logger.info(f"  - 转场效果: {transition_effect}")
            logger.info(f"  - 转场时长: {transition_duration}秒")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 最大并发数: {max_concurrent}")
            logger.info(f"  - 覆盖原文件: {overwrite_original}")
            if not overwrite_original:
                logger.info(f"  - 输出后缀: {output_suffix}")

            # 调用片头片尾处理服务
            result = await self.video_intro_outro_service.batch_add_intro_outro(
                folder_path=folder_path,
                selected_files=selected_files,
                intro_path=intro_path,
                outro_path=outro_path,
                output_folder=output_folder,
                transition_effect=transition_effect,
                transition_duration=transition_duration,
                output_quality=output_quality,
                max_concurrent=max_concurrent,
                overwrite_original=overwrite_original,
                output_suffix=output_suffix
            )

            # 构建响应
            response = file_pb2.BatchAddIntroOutroResponse()
            response.success = result.get('success', False)
            response.error = result.get('error', '')
            response.total_processing_time_ms = int(result.get('total_processing_time', 0) * 1000)
            response.successful_count = result.get('processed_videos', 0)
            response.failed_count = result.get('failed_videos', 0)
            response.total_count = result.get('total_videos', 0)
            response.output_folder = result.get('output_folder', '')
            response.total_output_size = result.get('total_output_size', 0)

            # 添加处理结果详情
            for item in result.get('results', []):
                result_item = file_pb2.IntroOutroProcessResult()
                result_item.input_path = str(item.get('input_path', ''))
                result_item.output_path = str(item.get('output_path', ''))
                result_item.success = bool(item.get('success', False))
                result_item.error_message = str(item.get('error_message', ''))
                result_item.processing_time_ms = int(item.get('processing_time', 0) * 1000)
                result_item.output_size = int(item.get('output_size', 0))
                response.results.append(result_item)

            if response.success:
                logger.info(f"批量添加片头片尾成功: 处理了 {response.total_count} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量添加片头片尾失败: {response.error}")

            return response

        except Exception as e:
            logger.error(f"批量添加片头片尾异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"批量添加片头片尾失败: {str(e)}")

            # 返回失败响应
            response = file_pb2.BatchAddIntroOutroResponse()
            response.success = False
            response.error = f"批量添加片头片尾异常: {str(e)}"
            response.successful_count = 0
            response.failed_count = 0
            response.total_count = 0
            response.total_processing_time_ms = 0
            response.output_folder = ''
            response.total_output_size = 0

            return response

    async def ClipVideos(self, request, context):
        """智能视频裁剪"""
        logger.info(f"收到视频裁剪请求: {len(request.video_paths)} 个文件, 模式: {request.clip_mode}")

        try:
            # 调用视频裁剪服务
            result = await self.video_clip_service.clip_videos(
                video_paths=list(request.video_paths),
                clip_mode=request.clip_mode,
                segment_count=request.segment_count,
                segment_duration=request.segment_duration,
                buffer_duration=request.buffer_duration,
                output_quality=request.output_quality,
                output_folder=request.output_folder,
                filename_template=request.filename_template,
                preserve_audio_quality=request.preserve_audio_quality,
                max_concurrent=request.max_concurrent,
                volume_sensitivity=request.volume_sensitivity,
                min_segment_duration=request.min_segment_duration
            )

            # 构建响应
            response = file_pb2.ClipVideosResponse()
            response.success = result["success"]
            response.error = result.get("error", "")
            response.total_processing_time_ms = result.get("total_processing_time_ms", 0)
            response.successful_count = result.get("successful_count", 0)
            response.failed_count = result.get("failed_count", 0)
            response.total_count = result.get("total_count", 0)
            response.output_folder = result.get("output_folder", "")
            response.total_output_size = result.get("total_output_size", 0)
            response.total_segments = result.get("total_segments", 0)

            # 添加处理结果
            for result_item in result.get("results", []):
                clip_result = response.results.add()
                clip_result.input_path = result_item["input_path"]
                clip_result.success = result_item["success"]
                clip_result.error_message = result_item["error_message"]
                clip_result.processing_time_ms = result_item["processing_time_ms"]
                clip_result.original_duration = result_item["original_duration"]
                clip_result.volume_analysis_data = result_item["volume_analysis_data"]

                # 添加片段信息
                for segment in result_item.get("segments", []):
                    segment_info = clip_result.segments.add()
                    segment_info.file_path = segment["file_path"]
                    segment_info.segment_index = segment["segment_index"]
                    segment_info.start_time = segment["start_time"]
                    segment_info.end_time = segment["end_time"]
                    segment_info.duration = segment["duration"]
                    segment_info.file_size = segment["file_size"]
                    segment_info.start_volume = segment["start_volume"]
                    segment_info.end_volume = segment["end_volume"]

            logger.info(f"视频裁剪完成: 成功 {response.successful_count}, 失败 {response.failed_count}, 总片段 {response.total_segments}")
            return response

        except Exception as e:
            logger.error(f"视频裁剪异常: {str(e)}")

            response = file_pb2.ClipVideosResponse()
            response.success = False
            response.error = f"视频裁剪异常: {str(e)}"
            response.successful_count = 0
            response.failed_count = 0
            response.total_count = 0
            response.total_processing_time_ms = 0
            response.output_folder = ''
            response.total_output_size = 0
            response.total_segments = 0

            return response

    async def EnhanceFiles(self, request, context):
        """高清处理文件（支持视频和图片）"""
        try:
            logger.info(f"收到高清处理请求: {len(request.file_paths)} 个文件")
            logger.info(f"处理模式: {request.enhance_mode}, 放大倍数: {request.scale_factor}x")

            # 调用高清处理服务
            result = await self.video_enhance_service.enhance_files(
                file_paths=list(request.file_paths),
                enhance_mode=request.enhance_mode,
                scale_factor=request.scale_factor,
                output_quality=request.output_quality,
                denoise_level=request.denoise_level,
                sharpen_level=request.sharpen_level,
                overwrite_original=request.overwrite_original,
                output_suffix=request.output_suffix,
                max_concurrent=request.max_concurrent
            )

            # 构建响应
            response = file_pb2.EnhanceFilesResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.total_processing_time_ms = result.get("total_processing_time_ms", 0)
            response.successful_count = result.get("successful_count", 0)
            response.failed_count = result.get("failed_count", 0)
            response.total_count = result.get("total_count", 0)
            response.output_folder = result.get("output_folder", "")
            response.total_output_size = result.get("total_output_size", 0)

            # 添加处理结果
            for file_result in result.get("results", []):
                result_item = response.results.add()
                result_item.original_path = file_result.get("original_path", "")
                result_item.output_path = file_result.get("output_path", "")
                result_item.success = file_result.get("success", False)
                result_item.error_message = file_result.get("error_message", "")
                result_item.processing_time_ms = file_result.get("processing_time_ms", 0)
                result_item.original_file_size = file_result.get("original_file_size", 0)
                result_item.output_file_size = file_result.get("output_file_size", 0)
                result_item.original_resolution = file_result.get("original_resolution", "")
                result_item.output_resolution = file_result.get("output_resolution", "")
                result_item.scale_factor = file_result.get("scale_factor", 0)

            logger.info(f"高清处理完成: 成功 {response.successful_count} 个，失败 {response.failed_count} 个")
            return response

        except Exception as e:
            logger.error(f"高清处理异常: {str(e)}")
            response = file_pb2.EnhanceFilesResponse()
            response.success = False
            response.error = str(e)
            response.total_processing_time_ms = 0
            response.successful_count = 0
            response.failed_count = len(request.file_paths)
            response.total_count = len(request.file_paths)
            response.output_folder = ""
            response.total_output_size = 0

            return response

    async def GetFaceTemplatesInfo(self, request, context):
        """获取人脸模板信息"""
        try:
            logger.info(f"收到获取人脸模板信息请求: {request.folder_path}")

            # 调用人脸换脸服务
            from src.services.face_swap_service import FaceSwapService
            face_swap_service = FaceSwapService()

            result = await face_swap_service.get_face_templates_info(request.folder_path)

            # 构建响应
            response = file_pb2.GetFaceTemplatesInfoResponse()
            response.success = result.get("success", False)
            response.message = result.get("message", "")
            response.templates_folder = result.get("templates_folder", "")
            response.target_faces.extend(result.get("target_faces", []))
            response.source_face = result.get("source_face", "")
            response.total_target_faces = result.get("total_target_faces", 0)
            response.has_source_face = result.get("has_source_face", False)

            logger.info(f"获取人脸模板信息完成: {response.message}")
            return response

        except Exception as e:
            logger.error(f"获取人脸模板信息异常: {str(e)}")
            response = file_pb2.GetFaceTemplatesInfoResponse()
            response.success = False
            response.message = f"获取人脸模板信息失败: {str(e)}"
            response.templates_folder = ""
            response.total_target_faces = 0
            response.has_source_face = False
            return response

    async def DetectFacesFromVideos(self, request, context):
        """从视频中检测人脸"""
        try:
            logger.info(f"收到视频人脸检测请求: {request.folder_path}")

            # 调用人脸换脸服务
            from src.services.face_swap_service import FaceSwapService
            face_swap_service = FaceSwapService()

            result = await face_swap_service.detect_faces_from_videos(
                folder_path=request.folder_path,
                max_faces_per_video=request.max_faces_per_video,
                sample_frames=request.sample_frames
            )

            # 构建响应
            response = file_pb2.DetectFacesFromVideosResponse()
            response.success = result.get("success", False)
            response.message = result.get("message", "")
            response.error = result.get("error", "")
            response.video_count = result.get("video_count", 0)
            response.total_faces_detected = result.get("total_faces_detected", 0)
            response.faces_output_dir = result.get("faces_output_dir", "")

            # 添加检测到的人脸信息
            for face_info in result.get("detected_faces", []):
                face_item = response.detected_faces.add()
                face_item.id = face_info.get("id", "")
                face_item.filename = face_info.get("filename", "")
                face_item.path = face_info.get("path", "")
                face_item.video_name = face_info.get("video_name", "")
                face_item.video_path = face_info.get("video_path", "")
                face_item.frame_index = face_info.get("frame_index", 0)
                face_item.face_location.extend(face_info.get("face_location", []))
                face_item.confidence = face_info.get("confidence", 0.0)
                face_item.encoding.extend(face_info.get("encoding", []))

            logger.info(f"视频人脸检测完成: 检测到 {len(response.detected_faces)} 个人脸")
            return response

        except Exception as e:
            logger.error(f"视频人脸检测异常: {str(e)}")
            response = file_pb2.DetectFacesFromVideosResponse()
            response.success = False
            response.error = f"视频人脸检测失败: {str(e)}"
            response.video_count = 0
            response.total_faces_detected = 0
            return response

    async def FaceSwapWithSelection(self, request, context):
        """基于选择的换脸处理"""
        try:
            logger.info(f"收到基于选择的换脸请求: {request.folder_path}")

            # 调用人脸换脸服务
            from src.services.face_swap_service import FaceSwapService
            face_swap_service = FaceSwapService()

            result = await face_swap_service.face_swap_with_selection(
                folder_path=request.folder_path,
                selected_target_faces=list(request.selected_target_faces),
                source_face_path=request.source_face_path,
                max_concurrent=request.max_concurrent,
                quality=request.quality,
                overwrite_original=request.overwrite_original
            )

            # 构建响应
            response = file_pb2.FaceSwapWithSelectionResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.total_processing_time_ms = result.get("total_processing_time_ms", 0)
            response.successful_count = result.get("successful_count", 0)
            response.failed_count = result.get("failed_count", 0)
            response.total_count = result.get("total_count", 0)
            response.output_folder = result.get("output_folder", "")
            response.selected_faces_count = result.get("selected_faces_count", 0)
            response.source_face_used = result.get("source_face_used", "")

            # 添加处理结果
            for result_item in result.get("results", []):
                swap_result = response.results.add()
                swap_result.success = result_item.get("success", False)
                swap_result.input_file = result_item.get("input_file", "")
                swap_result.output_file = result_item.get("output_file", "")
                swap_result.error_message = result_item.get("error_message", "")
                swap_result.processing_time_ms = result_item.get("processing_time_ms", 0)
                swap_result.message = result_item.get("message", "")

            logger.info(f"基于选择的换脸处理完成: 成功 {response.successful_count} 个")
            return response

        except Exception as e:
            logger.error(f"基于选择的换脸处理异常: {str(e)}")
            response = file_pb2.FaceSwapWithSelectionResponse()
            response.success = False
            response.error = f"基于选择的换脸处理失败: {str(e)}"
            response.total_processing_time_ms = 0
            response.successful_count = 0
            response.failed_count = 0
            return response

    async def BatchFaceSwap(self, request, context):
        """批量换脸处理"""
        try:
            logger.info(f"收到批量换脸请求: {request.folder_path}")

            # 调用人脸换脸服务
            from src.services.face_swap_service import FaceSwapService
            face_swap_service = FaceSwapService()

            result = await face_swap_service.batch_face_swap(
                folder_path=request.folder_path,
                target_faces=list(request.target_faces) if request.target_faces else None,
                source_face=request.source_face if request.source_face else None,
                max_concurrent=request.max_concurrent,
                quality=request.quality,
                overwrite_original=request.overwrite_original
            )

            # 构建响应
            response = file_pb2.BatchFaceSwapResponse()
            response.success = result.get("success", False)
            response.error = result.get("error", "")
            response.total_processing_time_ms = result.get("total_processing_time_ms", 0)
            response.successful_count = result.get("successful_count", 0)
            response.failed_count = result.get("failed_count", 0)
            response.total_count = result.get("total_count", 0)
            response.output_folder = result.get("output_folder", "")

            # 添加处理结果
            for result_item in result.get("results", []):
                swap_result = response.results.add()
                swap_result.success = result_item.get("success", False)
                swap_result.input_file = result_item.get("input_file", "")
                swap_result.output_file = result_item.get("output_file", "")
                swap_result.error_message = result_item.get("error_message", "")
                swap_result.processing_time_ms = result_item.get("processing_time_ms", 0)
                swap_result.message = result_item.get("message", "")

            logger.info(f"批量换脸处理完成: 成功 {response.successful_count} 个")
            return response

        except Exception as e:
            logger.error(f"批量换脸处理异常: {str(e)}")
            response = file_pb2.BatchFaceSwapResponse()
            response.success = False
            response.error = f"批量换脸处理失败: {str(e)}"
            response.total_processing_time_ms = 0
            response.successful_count = 0
            response.failed_count = 0
            return response
