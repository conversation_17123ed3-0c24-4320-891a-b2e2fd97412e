"""
系统配置相关API - 简化版本
"""
from fastapi import APIRouter, HTTPException, Depends, Query, Request
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import logging

from app.core.security import get_current_user
from app.services.ai_service import AIService
from app.models.user import User

# 设置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/system-config", tags=["system-config"])

# 数据库服务获取函数
def get_db(request: Request):
    """获取数据库服务实例"""
    return request.app.state.mongo_db

def get_ai_service(request: Request) -> AIService:
    """获取AI服务实例"""
    db = get_db(request)
    return AIService(db)

# AI提供商相关模型
class AIProviderBase(BaseModel):
    name: str = Field(..., description="提供商名称")
    type: str = Field(..., description="提供商类型")
    endpoint: str = Field(..., description="API端点")
    api_key: str = Field(..., description="API密钥")
    model: str = Field(..., description="模型名称")
    max_tokens: Optional[int] = Field(4096, description="最大令牌数")
    temperature: Optional[float] = Field(0.7, description="温度参数")
    enabled: bool = Field(True, description="是否启用")

class AIProviderCreate(AIProviderBase):
    pass

class AIProvider(AIProviderBase):
    id: str
    created_at: datetime
    updated_at: datetime

# AI角色模板相关模型
class AIRoleTemplateBase(BaseModel):
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    system_prompt: str = Field(..., description="系统提示词")
    user_prompt_template: str = Field(..., description="用户提示词模板")
    category: str = Field(..., description="分类")
    tags: List[str] = Field(default_factory=list, description="标签")
    enabled: bool = Field(True, description="是否启用")

class AIRoleTemplateCreate(AIRoleTemplateBase):
    pass

class AIRoleTemplate(AIRoleTemplateBase):
    id: str
    created_at: datetime
    updated_at: datetime

# AI角色实例相关模型
class AIRoleInstanceBase(BaseModel):
    name: str = Field(..., description="角色名称")
    description: str = Field(..., description="角色描述")
    template_id: str = Field(..., description="基于的模板ID")
    provider_id: str = Field(..., description="使用的AI提供商ID")

    # AI模型配置
    model: str = Field(..., description="AI模型名称")
    max_tokens: int = Field(2000, description="最大令牌数")
    temperature: float = Field(0.7, description="创造性参数")

    # 角色个性化配置
    personality: str = Field("", description="角色个性描述")
    expertise: List[str] = Field(default_factory=list, description="专业领域")
    tone: str = Field("professional", description="语调风格")

    # 工作流配置
    workflow_types: List[str] = Field(default_factory=list, description="支持的工作流类型")
    auto_suggestions: bool = Field(True, description="是否自动提供建议")

    # 记忆和上下文
    memory_enabled: bool = Field(True, description="是否启用记忆")
    context_window: int = Field(4000, description="上下文窗口大小")

    enabled: bool = Field(True, description="是否启用")

class AIRoleInstanceCreate(AIRoleInstanceBase):
    pass

class AIRoleInstance(AIRoleInstanceBase):
    id: str
    created_at: datetime
    updated_at: datetime
    last_used_at: Optional[datetime] = None
    usage_count: int = 0

# 通用响应模型
class ListResponse(BaseModel):
    items: List[dict]
    total: int
    page: int
    page_size: int

# AI提供商相关接口

@router.get("/ai-providers")
async def get_ai_providers(
    request: Request,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    enabled: Optional[bool] = Query(None),
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """获取AI提供商列表"""
    try:
        # 构建查询条件
        query = {}
        if enabled is not None:
            query["enabled"] = enabled
        
        # 分页查询
        skip = (page - 1) * page_size
        cursor = db.ai_providers.find(query).skip(skip).limit(page_size)
        providers = await cursor.to_list(length=page_size)
        
        # 获取总数
        total = await db.ai_providers.count_documents(query)
        
        # 转换数据格式
        items = []
        for provider in providers:
            provider["id"] = str(provider["_id"])
            del provider["_id"]
            items.append(provider)
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI提供商列表失败: {str(e)}")

@router.post("/ai-providers")
async def create_ai_provider(
    provider_data: AIProviderCreate,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """创建AI提供商"""
    try:
        now = datetime.utcnow()
        provider_dict = provider_data.dict()
        provider_dict.update({
            "created_at": now,
            "updated_at": now
        })

        result = await db.ai_providers.insert_one(provider_dict)

        # 构建返回数据，确保ObjectId被正确转换为字符串
        response_data = {
            "id": str(result.inserted_id),
            "name": provider_dict["name"],
            "type": provider_dict["type"],
            "endpoint": provider_dict["endpoint"],
            "api_key": provider_dict["api_key"],
            "model": provider_dict["model"],
            "max_tokens": provider_dict.get("max_tokens"),
            "temperature": provider_dict.get("temperature"),
            "enabled": provider_dict.get("enabled", True),
            "created_at": now.isoformat(),
            "updated_at": now.isoformat()
        }

        return response_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建AI提供商失败: {str(e)}")

@router.post("/ai-providers/{provider_id}/test")
async def test_ai_provider(
    provider_id: str,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    ai_service: AIService = Depends(get_ai_service)
):
    """测试AI提供商连接"""
    try:
        logger.info(f"开始测试AI提供商连接: {provider_id}")

        # 调用AI服务进行连接测试
        result = await ai_service.test_provider_connection(provider_id)

        logger.info(f"AI提供商测试结果: {result}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试AI提供商连接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试AI提供商连接失败: {str(e)}")

@router.delete("/ai-providers/{provider_id}")
async def delete_ai_provider(
    provider_id: str,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """删除AI提供商"""
    try:
        from bson import ObjectId
        
        result = await db.ai_providers.delete_one({"_id": ObjectId(provider_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="AI提供商不存在")
        
        return {"message": "删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除AI提供商失败: {str(e)}")

# AI角色模板相关接口

@router.get("/ai-role-templates")
async def get_ai_role_templates(
    request: Request,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    enabled: Optional[bool] = Query(None),
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """获取AI角色模板列表"""
    try:
        # 构建查询条件
        query = {}
        if category:
            query["category"] = category
        if enabled is not None:
            query["enabled"] = enabled
        
        # 分页查询
        skip = (page - 1) * page_size
        cursor = db.ai_role_templates.find(query).skip(skip).limit(page_size)
        templates = await cursor.to_list(length=page_size)
        
        # 获取总数
        total = await db.ai_role_templates.count_documents(query)
        
        # 转换数据格式
        items = []
        for template in templates:
            template["id"] = str(template["_id"])
            del template["_id"]
            items.append(template)
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI角色模板列表失败: {str(e)}")

@router.get("/ai-role-templates/categories")
async def get_template_categories(
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """获取模板分类列表"""
    try:
        categories = await db.ai_role_templates.distinct("category")
        return categories
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")

@router.post("/ai-role-templates")
async def create_ai_role_template(
    template_data: AIRoleTemplateCreate,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """创建AI角色模板"""
    try:
        now = datetime.utcnow()
        template_dict = template_data.dict()
        template_dict.update({
            "created_at": now,
            "updated_at": now
        })

        result = await db.ai_role_templates.insert_one(template_dict)

        # 构建返回数据，确保ObjectId被正确转换为字符串
        response_data = {
            "id": str(result.inserted_id),
            "name": template_dict["name"],
            "description": template_dict["description"],
            "system_prompt": template_dict["system_prompt"],
            "user_prompt_template": template_dict["user_prompt_template"],
            "category": template_dict["category"],
            "tags": template_dict.get("tags", []),
            "enabled": template_dict.get("enabled", True),
            "created_at": now.isoformat(),
            "updated_at": now.isoformat()
        }

        return response_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建AI角色模板失败: {str(e)}")

@router.delete("/ai-role-templates/{template_id}")
async def delete_ai_role_template(
    template_id: str,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """删除AI角色模板"""
    try:
        from bson import ObjectId

        result = await db.ai_role_templates.delete_one({"_id": ObjectId(template_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="AI角色模板不存在")

        return {"message": "删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除AI角色模板失败: {str(e)}")


# 测试请求模型
class TestTemplateRequest(BaseModel):
    test_input: str = Field("", description="测试输入内容")

@router.post("/ai-role-templates/{template_id}/test")
async def test_ai_role_template(
    template_id: str,
    test_request: TestTemplateRequest,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    ai_service: AIService = Depends(get_ai_service)
):
    """测试AI角色模板"""
    try:
        logger.info(f"开始测试AI角色模板: {template_id}")

        # 调用AI服务进行测试
        result = await ai_service.test_role_template(template_id, test_request.test_input)

        if result["success"]:
            logger.info(f"AI角色模板测试成功: {template_id}")
            return {
                "response": result["response"],
                "template_name": result.get("template_name", ""),
                "provider_name": result.get("provider_name", "")
            }
        else:
            logger.warning(f"AI角色模板测试失败: {template_id}, 错误: {result['message']}")
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试AI角色模板异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")




# ==================== AI角色实例相关API ====================

@router.get("/ai-role-instances")
async def get_ai_role_instances(
    request: Request,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    enabled: Optional[bool] = Query(None),
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """获取AI角色实例列表"""
    try:
        # 构建查询条件
        query = {}
        if enabled is not None:
            query["enabled"] = enabled

        # 分页查询
        skip = (page - 1) * page_size
        cursor = db.ai_role_instances.find(query).skip(skip).limit(page_size)
        instances = await cursor.to_list(length=page_size)

        # 获取总数
        total = await db.ai_role_instances.count_documents(query)

        # 转换数据格式并关联模板信息
        items = []
        for instance in instances:
            instance["id"] = str(instance["_id"])
            del instance["_id"]

            # 获取关联的模板信息
            if instance.get("template_id"):
                try:
                    from bson import ObjectId
                    template = await db.ai_role_templates.find_one({"_id": ObjectId(instance["template_id"])})
                    if template:
                        instance["template_name"] = template.get("name", "未知模板")
                        instance["template_category"] = template.get("category", "")
                except:
                    instance["template_name"] = "模板已删除"
                    instance["template_category"] = ""

            # 获取关联的提供商信息
            if instance.get("provider_id"):
                try:
                    from bson import ObjectId
                    provider = await db.ai_providers.find_one({"_id": ObjectId(instance["provider_id"])})
                    if provider:
                        instance["provider_name"] = provider.get("name", "未知提供商")
                        instance["provider_type"] = provider.get("type", "")
                except:
                    instance["provider_name"] = "提供商已删除"
                    instance["provider_type"] = ""

            items.append(instance)

        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取AI角色实例列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取AI角色实例列表失败: {str(e)}")

@router.post("/ai-role-instances")
async def create_ai_role_instance(
    instance_data: AIRoleInstanceCreate,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """创建AI角色实例"""
    try:
        now = datetime.utcnow()
        instance_dict = instance_data.dict()
        instance_dict.update({
            "created_at": now,
            "updated_at": now,
            "last_used_at": None,
            "usage_count": 0
        })

        result = await db.ai_role_instances.insert_one(instance_dict)

        # 构建返回数据
        response_data = {
            "id": str(result.inserted_id),
            "name": instance_dict["name"],
            "description": instance_dict["description"],
            "template_id": instance_dict["template_id"],
            "provider_id": instance_dict["provider_id"],
            "personality": instance_dict.get("personality", ""),
            "expertise": instance_dict.get("expertise", []),
            "tone": instance_dict.get("tone", "professional"),
            "workflow_types": instance_dict.get("workflow_types", []),
            "auto_suggestions": instance_dict.get("auto_suggestions", True),
            "memory_enabled": instance_dict.get("memory_enabled", True),
            "context_window": instance_dict.get("context_window", 4000),
            "enabled": instance_dict.get("enabled", True),
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "last_used_at": None,
            "usage_count": 0
        }

        return response_data
    except Exception as e:
        logger.error(f"创建AI角色实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建AI角色实例失败: {str(e)}")

@router.put("/ai-role-instances/{instance_id}")
async def update_ai_role_instance(
    instance_id: str,
    instance_data: AIRoleInstanceCreate,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """更新AI角色实例"""
    try:
        from bson import ObjectId

        # 检查实例是否存在
        existing = await db.ai_role_instances.find_one({"_id": ObjectId(instance_id)})
        if not existing:
            raise HTTPException(status_code=404, detail="AI角色实例不存在")

        # 更新数据
        update_data = instance_data.dict()
        update_data["updated_at"] = datetime.utcnow()

        await db.ai_role_instances.update_one(
            {"_id": ObjectId(instance_id)},
            {"$set": update_data}
        )

        # 返回更新后的数据
        updated = await db.ai_role_instances.find_one({"_id": ObjectId(instance_id)})

        # 构建返回数据
        response_data = {
            "id": str(updated["_id"]),
            "name": updated["name"],
            "description": updated["description"],
            "template_id": updated["template_id"],
            "provider_id": updated["provider_id"],
            "model": updated["model"],
            "max_tokens": updated["max_tokens"],
            "temperature": updated["temperature"],
            "personality": updated.get("personality", ""),
            "expertise": updated.get("expertise", []),
            "tone": updated.get("tone", "professional"),
            "workflow_types": updated.get("workflow_types", []),
            "auto_suggestions": updated.get("auto_suggestions", True),
            "memory_enabled": updated.get("memory_enabled", True),
            "context_window": updated.get("context_window", 4000),
            "enabled": updated.get("enabled", True),
            "created_at": updated["created_at"].isoformat(),
            "updated_at": updated["updated_at"].isoformat(),
            "usage_count": updated.get("usage_count", 0)
        }

        return response_data
    except Exception as e:
        logger.error(f"更新AI角色实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新AI角色实例失败: {str(e)}")

@router.delete("/ai-role-instances/{instance_id}")
async def delete_ai_role_instance(
    instance_id: str,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    db = Depends(get_db)
):
    """删除AI角色实例"""
    try:
        from bson import ObjectId

        result = await db.ai_role_instances.delete_one({"_id": ObjectId(instance_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="AI角色实例不存在")

        return {"message": "删除成功"}
    except Exception as e:
        logger.error(f"删除AI角色实例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除AI角色实例失败: {str(e)}")


# 使用AI角色实例的请求模型
class UseRoleInstanceRequest(BaseModel):
    user_input: str = Field(..., description="用户输入内容")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")

@router.post("/ai-role-instances/{instance_id}/use")
async def use_ai_role_instance(
    instance_id: str,
    use_request: UseRoleInstanceRequest,
    request: Request,
    # current_user: User = Depends(get_current_user),  # 临时禁用认证
    ai_service: AIService = Depends(get_ai_service)
):
    """使用AI角色实例"""
    try:
        logger.info(f"开始使用AI角色实例: {instance_id}")

        # 调用AI服务使用角色实例
        result = await ai_service.use_role_instance(
            instance_id,
            use_request.user_input,
            use_request.context
        )

        if result["success"]:
            logger.info(f"AI角色实例使用成功: {instance_id}")
            return {
                "response": result["response"],
                "instance_name": result.get("instance_name", ""),
                "template_name": result.get("template_name", ""),
                "provider_name": result.get("provider_name", "")
            }
        else:
            logger.warning(f"AI角色实例使用失败: {instance_id}, 错误: {result['message']}")
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"使用AI角色实例异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"使用失败: {str(e)}")
