"""
任务队列服务
实现分布式任务队列，支持优先级、负载均衡和故障恢复
"""

import logging
import asyncio
import json
import uuid
import time
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
import heapq
from collections import defaultdict

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class BatchStatus(Enum):
    """批处理状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIAL_COMPLETED = "partial_completed"

@dataclass
class QueueTask:
    """队列任务"""
    id: str
    type: str
    priority: TaskPriority
    payload: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    created_at: float = 0.0
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 5分钟超时
    worker_id: Optional[str] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.created_at == 0.0:
            self.created_at = time.time()
    
    def __lt__(self, other):
        """优先级比较，用于堆排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value  # 高优先级在前
        return self.created_at < other.created_at  # 相同优先级按时间排序
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['priority'] = self.priority.value
        result['status'] = self.status.value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QueueTask':
        """从字典创建实例"""
        data = data.copy()
        data['priority'] = TaskPriority(data['priority'])
        data['status'] = TaskStatus(data['status'])
        return cls(**data)

@dataclass
class BatchTask:
    """批处理任务"""
    id: str
    name: str
    description: str = ""
    task_ids: List[str] = None
    status: BatchStatus = BatchStatus.PENDING
    created_at: float = 0.0
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    progress: float = 0.0

    def __post_init__(self):
        if self.task_ids is None:
            self.task_ids = []
        if self.created_at == 0.0:
            self.created_at = time.time()
        self.total_tasks = len(self.task_ids)

    def update_progress(self, completed: int, failed: int):
        """更新进度"""
        self.completed_tasks = completed
        self.failed_tasks = failed

        if self.total_tasks > 0:
            self.progress = (completed + failed) / self.total_tasks

        # 更新状态
        if completed + failed == self.total_tasks:
            if failed == 0:
                self.status = BatchStatus.COMPLETED
            elif completed == 0:
                self.status = BatchStatus.FAILED
            else:
                self.status = BatchStatus.PARTIAL_COMPLETED
            self.completed_at = time.time()
        elif completed + failed > 0:
            self.status = BatchStatus.RUNNING
            if not self.started_at:
                self.started_at = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['status'] = self.status.value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BatchTask':
        """从字典创建实例"""
        data = data.copy()
        data['status'] = BatchStatus(data['status'])
        return cls(**data)

class TaskWorker:
    """任务工作器"""
    
    def __init__(self, worker_id: str, task_handlers: Dict[str, Callable]):
        self.worker_id = worker_id
        self.task_handlers = task_handlers
        self.current_task: Optional[QueueTask] = None
        self.is_running = False
        self.last_heartbeat = time.time()
    
    async def process_task(self, task: QueueTask) -> bool:
        """处理任务"""
        try:
            self.current_task = task
            task.status = TaskStatus.RUNNING
            task.started_at = time.time()
            task.worker_id = self.worker_id
            
            logger.info(f"Worker {self.worker_id} 开始处理任务 {task.id}")
            
            # 获取任务处理器
            handler = self.task_handlers.get(task.type)
            if not handler:
                raise ValueError(f"未找到任务类型 {task.type} 的处理器")
            
            # 执行任务
            result = await handler(task.payload)
            
            # 任务完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = time.time()
            task.result = result
            
            logger.info(f"Worker {self.worker_id} 完成任务 {task.id}")
            return True
            
        except Exception as e:
            logger.error(f"Worker {self.worker_id} 处理任务 {task.id} 失败: {str(e)}")
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = time.time()
            return False
        finally:
            self.current_task = None
    
    def update_heartbeat(self):
        """更新心跳"""
        self.last_heartbeat = time.time()
    
    def is_alive(self, timeout: int = 60) -> bool:
        """检查工作器是否存活"""
        return time.time() - self.last_heartbeat < timeout

class TaskQueueService:
    """任务队列服务"""
    
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.pending_queue = []  # 优先级队列
        self.running_tasks: Dict[str, QueueTask] = {}
        self.completed_tasks: Dict[str, QueueTask] = {}
        self.failed_tasks: Dict[str, QueueTask] = {}
        self.workers: Dict[str, TaskWorker] = {}
        self.task_handlers: Dict[str, Callable] = {}
        self.is_running = False

        # 批处理相关
        self.batch_tasks: Dict[str, BatchTask] = {}

        self.stats = {
            "total_submitted": 0,
            "total_completed": 0,
            "total_failed": 0,
            "current_pending": 0,
            "current_running": 0,
            "total_batches": 0,
            "completed_batches": 0
        }
    
    def register_handler(self, task_type: str, handler: Callable):
        """注册任务处理器"""
        self.task_handlers[task_type] = handler
        logger.info(f"注册任务处理器: {task_type}")
    
    async def submit_task(self, task_type: str, payload: Dict[str, Any], 
                         priority: TaskPriority = TaskPriority.NORMAL,
                         max_retries: int = 3, timeout: int = 300) -> str:
        """提交任务"""
        task_id = str(uuid.uuid4())
        
        task = QueueTask(
            id=task_id,
            type=task_type,
            priority=priority,
            payload=payload,
            max_retries=max_retries,
            timeout=timeout
        )
        
        # 添加到优先级队列
        heapq.heappush(self.pending_queue, task)
        
        self.stats["total_submitted"] += 1
        self.stats["current_pending"] = len(self.pending_queue)
        
        logger.info(f"提交任务: {task_id} (类型: {task_type}, 优先级: {priority.name})")
        
        # 触发任务调度
        asyncio.create_task(self._schedule_tasks())
        
        return task_id

    async def submit_batch_tasks(self, batch_name: str, tasks: List[Dict[str, Any]],
                                description: str = "",
                                priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """批量提交任务"""
        batch_id = str(uuid.uuid4())
        task_ids = []

        # 创建批处理任务
        batch_task = BatchTask(
            id=batch_id,
            name=batch_name,
            description=description
        )

        # 提交所有子任务
        for task_data in tasks:
            task_type = task_data.get("type")
            payload = task_data.get("payload", {})
            task_priority = TaskPriority(task_data.get("priority", priority.value))
            max_retries = task_data.get("max_retries", 3)
            timeout = task_data.get("timeout", 300)

            task_id = await self.submit_task(
                task_type=task_type,
                payload=payload,
                priority=task_priority,
                max_retries=max_retries,
                timeout=timeout
            )
            task_ids.append(task_id)

        # 更新批处理任务
        batch_task.task_ids = task_ids
        batch_task.total_tasks = len(task_ids)
        self.batch_tasks[batch_id] = batch_task

        self.stats["total_batches"] += 1

        logger.info(f"批量提交任务: {batch_id} (名称: {batch_name}, 任务数: {len(task_ids)})")

        # 启动批处理监控
        asyncio.create_task(self._monitor_batch(batch_id))

        return batch_id

    async def _monitor_batch(self, batch_id: str):
        """监控批处理任务"""
        batch = self.batch_tasks.get(batch_id)
        if not batch:
            return

        last_status = batch.status
        last_progress = batch.progress

        while batch.status in [BatchStatus.PENDING, BatchStatus.RUNNING]:
            completed_count = 0
            failed_count = 0

            # 检查所有子任务状态
            for task_id in batch.task_ids:
                task = self.get_task_status(task_id)
                if task:
                    if task.status == TaskStatus.COMPLETED:
                        completed_count += 1
                    elif task.status == TaskStatus.FAILED:
                        failed_count += 1

            # 更新批处理进度
            old_status = batch.status
            batch.update_progress(completed_count, failed_count)

            # 广播状态变化
            if batch.status != last_status:
                await self._broadcast_batch_status_change(batch, last_status, batch.status)
                last_status = batch.status

            # 广播进度变化
            if abs(batch.progress - last_progress) > 0.01:  # 进度变化超过1%才广播
                await self._broadcast_batch_progress(batch)
                last_progress = batch.progress

            # 如果批处理完成，更新统计
            if batch.status in [BatchStatus.COMPLETED, BatchStatus.FAILED, BatchStatus.PARTIAL_COMPLETED]:
                self.stats["completed_batches"] += 1
                await self._broadcast_batch_progress(batch)  # 最终进度广播
                logger.info(f"批处理任务完成: {batch_id} (状态: {batch.status.value})")
                break

            await asyncio.sleep(2)  # 每2秒检查一次

    def get_batch_status(self, batch_id: str) -> Optional[BatchTask]:
        """获取批处理状态"""
        return self.batch_tasks.get(batch_id)

    def get_batch_tasks(self, batch_id: str) -> List[QueueTask]:
        """获取批处理的所有子任务"""
        batch = self.batch_tasks.get(batch_id)
        if not batch:
            return []

        tasks = []
        for task_id in batch.task_ids:
            task = self.get_task_status(task_id)
            if task:
                tasks.append(task)

        return tasks

    async def cancel_batch(self, batch_id: str) -> bool:
        """取消批处理任务"""
        batch = self.batch_tasks.get(batch_id)
        if not batch:
            return False

        # 取消所有未完成的子任务
        cancelled_count = 0
        for task_id in batch.task_ids:
            if await self.cancel_task(task_id):
                cancelled_count += 1

        # 更新批处理状态
        batch.status = BatchStatus.CANCELLED
        batch.completed_at = time.time()

        logger.info(f"取消批处理任务: {batch_id} (取消了 {cancelled_count} 个子任务)")
        return True

    async def cancel_task(self, task_id: str) -> bool:
        """取消单个任务"""
        # 从待处理队列中移除
        for i, task in enumerate(self.pending_queue):
            if task.id == task_id:
                task.status = TaskStatus.CANCELLED
                self.pending_queue.pop(i)
                heapq.heapify(self.pending_queue)  # 重新堆化
                return True

        # 如果任务正在运行，标记为取消（实际中断需要工作器支持）
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.status = TaskStatus.CANCELLED
            # 这里可以发送中断信号给工作器
            return True

        return False

    async def _broadcast_task_status_change(self, task: QueueTask, old_status: TaskStatus, new_status: TaskStatus):
        """广播任务状态变化"""
        try:
            from .progress_monitor_service import get_progress_monitor_service

            monitor = get_progress_monitor_service()
            await monitor.broadcast_task_status_change(
                task_id=task.id,
                old_status=old_status.value,
                new_status=new_status.value,
                additional_data={
                    "task_type": task.type,
                    "priority": task.priority.value,
                    "retry_count": task.retry_count,
                    "worker_id": task.worker_id,
                    "created_at": task.created_at,
                    "started_at": task.started_at,
                    "completed_at": task.completed_at,
                    "error_message": task.error_message
                }
            )
        except Exception as e:
            logger.error(f"广播任务状态变化失败: {str(e)}")

    async def _broadcast_task_progress(self, task: QueueTask, progress: float, message: str = ""):
        """广播任务进度"""
        try:
            from .progress_monitor_service import get_progress_monitor_service

            monitor = get_progress_monitor_service()
            await monitor.broadcast_task_progress(
                task_id=task.id,
                progress_data={
                    "progress": progress,
                    "message": message,
                    "status": task.status.value,
                    "task_type": task.type,
                    "worker_id": task.worker_id
                }
            )
        except Exception as e:
            logger.error(f"广播任务进度失败: {str(e)}")

    async def _broadcast_batch_status_change(self, batch: BatchTask, old_status: BatchStatus, new_status: BatchStatus):
        """广播批处理状态变化"""
        try:
            from .progress_monitor_service import get_progress_monitor_service

            monitor = get_progress_monitor_service()
            await monitor.broadcast_batch_status_change(
                batch_id=batch.id,
                old_status=old_status.value,
                new_status=new_status.value,
                additional_data={
                    "name": batch.name,
                    "description": batch.description,
                    "total_tasks": batch.total_tasks,
                    "completed_tasks": batch.completed_tasks,
                    "failed_tasks": batch.failed_tasks,
                    "progress": batch.progress,
                    "created_at": batch.created_at,
                    "started_at": batch.started_at,
                    "completed_at": batch.completed_at
                }
            )
        except Exception as e:
            logger.error(f"广播批处理状态变化失败: {str(e)}")

    async def _broadcast_batch_progress(self, batch: BatchTask):
        """广播批处理进度"""
        try:
            from .progress_monitor_service import get_progress_monitor_service

            monitor = get_progress_monitor_service()
            await monitor.broadcast_batch_progress(
                batch_id=batch.id,
                progress_data={
                    "progress": batch.progress,
                    "status": batch.status.value,
                    "total_tasks": batch.total_tasks,
                    "completed_tasks": batch.completed_tasks,
                    "failed_tasks": batch.failed_tasks,
                    "running_tasks": batch.total_tasks - batch.completed_tasks - batch.failed_tasks
                }
            )
        except Exception as e:
            logger.error(f"广播批处理进度失败: {str(e)}")

    async def _handle_task_failure(self, task: QueueTask):
        """处理任务失败"""
        try:
            from .error_handler_service import get_error_handler_service

            error_handler = get_error_handler_service()

            # 分析错误并获取重试建议
            error_result = await error_handler.handle_task_error(
                task_id=task.id,
                error_message=task.error_message or "任务执行失败",
                stack_trace=""
            )

            should_retry = error_result.get("should_retry", False)
            retry_delay = error_result.get("retry_delay", 0.0)

            if should_retry and task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                task.worker_id = None
                task.started_at = None

                # 广播重试状态
                await self._broadcast_task_status_change(task, TaskStatus.FAILED, TaskStatus.RETRYING)

                # 如果有延迟，等待后再重新加入队列
                if retry_delay > 0:
                    asyncio.create_task(self._delayed_retry(task, retry_delay))
                else:
                    # 立即重新加入队列
                    heapq.heappush(self.pending_queue, task)

                logger.info(f"任务 {task.id} 将重试 ({task.retry_count}/{task.max_retries}), 延迟: {retry_delay:.2f}秒")
            else:
                # 最终失败
                self.failed_tasks[task.id] = task
                self.stats["total_failed"] += 1

                # 广播任务最终失败
                await self._broadcast_task_status_change(task, TaskStatus.RUNNING, TaskStatus.FAILED)

                logger.error(f"任务 {task.id} 最终失败: {task.error_message}")

        except Exception as e:
            logger.error(f"处理任务失败异常: {str(e)}")
            # 降级处理：使用原有逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                task.worker_id = None
                task.started_at = None
                heapq.heappush(self.pending_queue, task)
            else:
                self.failed_tasks[task.id] = task
                self.stats["total_failed"] += 1

    async def _delayed_retry(self, task: QueueTask, delay: float):
        """延迟重试任务"""
        try:
            await asyncio.sleep(delay)

            # 检查任务是否已被取消
            if task.status == TaskStatus.RETRYING:
                heapq.heappush(self.pending_queue, task)
                self.stats["current_pending"] = len(self.pending_queue)

                # 触发调度
                await self._schedule_tasks()

        except Exception as e:
            logger.error(f"延迟重试任务失败: {str(e)}")

    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        try:
            from .error_handler_service import get_error_handler_service

            error_handler = get_error_handler_service()
            return error_handler.get_error_statistics()

        except Exception as e:
            logger.error(f"获取错误统计失败: {str(e)}")
            return {}

    def get_task_errors(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务错误记录"""
        try:
            from .error_handler_service import get_error_handler_service

            error_handler = get_error_handler_service()
            error_records = error_handler.get_task_errors(task_id)

            return [
                {
                    "error_type": record.error_type.value,
                    "error_message": record.error_message,
                    "timestamp": record.timestamp,
                    "retry_count": record.retry_count,
                    "resolved": record.resolved
                }
                for record in error_records
            ]

        except Exception as e:
            logger.error(f"获取任务错误记录失败: {str(e)}")
            return []
    
    async def _schedule_tasks(self):
        """任务调度"""
        if not self.is_running:
            return
        
        # 清理超时任务
        await self._cleanup_timeout_tasks()
        
        # 清理死亡工作器
        await self._cleanup_dead_workers()
        
        # 分配任务给空闲工作器
        while (self.pending_queue and 
               len(self.running_tasks) < self.max_workers and
               len([w for w in self.workers.values() if w.current_task is None]) > 0):
            
            task = heapq.heappop(self.pending_queue)
            
            # 找到空闲工作器
            idle_worker = None
            for worker in self.workers.values():
                if worker.current_task is None and worker.is_alive():
                    idle_worker = worker
                    break
            
            if idle_worker:
                # 分配任务
                self.running_tasks[task.id] = task
                asyncio.create_task(self._execute_task(idle_worker, task))
            else:
                # 没有空闲工作器，重新放回队列
                heapq.heappush(self.pending_queue, task)
                break
        
        self.stats["current_pending"] = len(self.pending_queue)
        self.stats["current_running"] = len(self.running_tasks)
    
    async def _execute_task(self, worker: TaskWorker, task: QueueTask):
        """执行任务"""
        try:
            # 广播任务开始
            await self._broadcast_task_status_change(task, TaskStatus.PENDING, TaskStatus.RUNNING)

            success = await worker.process_task(task)

            # 从运行队列移除
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]

            if success:
                self.completed_tasks[task.id] = task
                self.stats["total_completed"] += 1
                # 广播任务完成
                await self._broadcast_task_status_change(task, TaskStatus.RUNNING, TaskStatus.COMPLETED)
            else:
                # 使用错误处理服务分析错误
                await self._handle_task_failure(task)

        except Exception as e:
            logger.error(f"执行任务异常: {str(e)}")

        finally:
            # 继续调度
            await self._schedule_tasks()
    
    async def _cleanup_timeout_tasks(self):
        """清理超时任务"""
        current_time = time.time()
        timeout_tasks = []
        
        for task_id, task in self.running_tasks.items():
            if (task.started_at and 
                current_time - task.started_at > task.timeout):
                timeout_tasks.append(task_id)
        
        for task_id in timeout_tasks:
            task = self.running_tasks.pop(task_id)
            task.status = TaskStatus.FAILED
            task.error_message = "任务超时"
            task.completed_at = current_time
            
            # 释放工作器
            if task.worker_id and task.worker_id in self.workers:
                self.workers[task.worker_id].current_task = None
            
            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                task.worker_id = None
                task.started_at = None
                heapq.heappush(self.pending_queue, task)
            else:
                self.failed_tasks[task_id] = task
                self.stats["total_failed"] += 1
            
            logger.warning(f"任务 {task_id} 超时")
    
    async def _cleanup_dead_workers(self):
        """清理死亡工作器"""
        dead_workers = []
        
        for worker_id, worker in self.workers.items():
            if not worker.is_alive():
                dead_workers.append(worker_id)
                
                # 如果工作器有正在执行的任务，重新调度
                if worker.current_task:
                    task = worker.current_task
                    task.status = TaskStatus.PENDING
                    task.worker_id = None
                    task.started_at = None
                    heapq.heappush(self.pending_queue, task)
                    
                    if task.id in self.running_tasks:
                        del self.running_tasks[task.id]
        
        for worker_id in dead_workers:
            del self.workers[worker_id]
            logger.warning(f"清理死亡工作器: {worker_id}")
    
    def register_worker(self, worker_id: str) -> TaskWorker:
        """注册工作器"""
        worker = TaskWorker(worker_id, self.task_handlers)
        self.workers[worker_id] = worker
        logger.info(f"注册工作器: {worker_id}")
        return worker
    
    def get_task_status(self, task_id: str) -> Optional[QueueTask]:
        """获取任务状态"""
        # 检查各个队列
        for task in self.pending_queue:
            if task.id == task_id:
                return task
        
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        
        if task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        
        if task_id in self.failed_tasks:
            return self.failed_tasks[task_id]
        
        return None
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计"""
        return {
            **self.stats,
            "workers": {
                "total": len(self.workers),
                "active": len([w for w in self.workers.values() if w.current_task is not None]),
                "idle": len([w for w in self.workers.values() if w.current_task is None])
            },
            "queue_length": len(self.pending_queue)
        }
    
    async def start(self):
        """启动队列服务"""
        self.is_running = True
        logger.info("任务队列服务启动")
        
        # 启动调度循环
        asyncio.create_task(self._schedule_loop())
    
    async def stop(self):
        """停止队列服务"""
        self.is_running = False
        logger.info("任务队列服务停止")
    
    async def _schedule_loop(self):
        """调度循环"""
        while self.is_running:
            try:
                await self._schedule_tasks()
                await asyncio.sleep(1)  # 每秒调度一次
            except Exception as e:
                logger.error(f"调度循环异常: {str(e)}")
                await asyncio.sleep(5)

# 任务处理器
async def handle_image_generation(payload: Dict[str, Any]) -> Dict[str, Any]:
    """处理图片生成任务"""
    try:
        from .comfyui_service import get_comfyui_service

        comfyui = get_comfyui_service()

        # 构建工作流
        workflow = payload.get("workflow", {})

        # 提交到ComfyUI
        result = await comfyui.submit_workflow(workflow)

        if result["success"]:
            prompt_id = result["prompt_id"]

            # 等待完成
            while True:
                status = await comfyui.get_workflow_status(prompt_id)
                if status["success"]:
                    if status["status"] == "completed":
                        # 获取输出文件
                        files = await comfyui.get_output_files(prompt_id)
                        return {
                            "success": True,
                            "prompt_id": prompt_id,
                            "files": files
                        }
                    elif status["status"] == "failed":
                        return {
                            "success": False,
                            "error": "ComfyUI执行失败"
                        }

                await asyncio.sleep(2)  # 每2秒检查一次
        else:
            return {
                "success": False,
                "error": result.get("error", "提交失败")
            }

    except Exception as e:
        logger.error(f"图片生成任务处理失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def handle_video_generation(payload: Dict[str, Any]) -> Dict[str, Any]:
    """处理视频生成任务"""
    try:
        from .comfyui_service import get_comfyui_service

        comfyui = get_comfyui_service()

        # 构建工作流
        workflow = payload.get("workflow", {})

        # 提交到ComfyUI
        result = await comfyui.submit_workflow(workflow)

        if result["success"]:
            prompt_id = result["prompt_id"]

            # 等待完成
            while True:
                status = await comfyui.get_workflow_status(prompt_id)
                if status["success"]:
                    if status["status"] == "completed":
                        files = await comfyui.get_output_files(prompt_id)
                        return {
                            "success": True,
                            "prompt_id": prompt_id,
                            "files": files
                        }
                    elif status["status"] == "failed":
                        return {
                            "success": False,
                            "error": "ComfyUI执行失败"
                        }

                await asyncio.sleep(3)  # 视频生成较慢，每3秒检查一次
        else:
            return {
                "success": False,
                "error": result.get("error", "提交失败")
            }

    except Exception as e:
        logger.error(f"视频生成任务处理失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def handle_text_generation(payload: Dict[str, Any]) -> Dict[str, Any]:
    """处理文本生成任务"""
    try:
        # 模拟文本生成
        prompt = payload.get("prompt", "")
        max_tokens = payload.get("max_tokens", 500)

        # 这里应该调用实际的文本生成服务
        # 暂时返回模拟结果
        generated_text = f"基于提示 '{prompt}' 生成的文本内容..."

        return {
            "success": True,
            "text": generated_text,
            "tokens_used": len(generated_text.split())
        }

    except Exception as e:
        logger.error(f"文本生成任务处理失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# 全局任务队列服务实例
_task_queue_service = None

def get_task_queue_service() -> TaskQueueService:
    """获取任务队列服务实例"""
    global _task_queue_service

    if _task_queue_service is None:
        _task_queue_service = TaskQueueService()

        # 注册任务处理器
        _task_queue_service.register_handler("image_generation", handle_image_generation)
        _task_queue_service.register_handler("video_generation", handle_video_generation)
        _task_queue_service.register_handler("text_generation", handle_text_generation)

    return _task_queue_service

async def initialize_task_queue():
    """初始化任务队列"""
    service = get_task_queue_service()
    await service.start()

    # 注册默认工作器
    for i in range(3):  # 创建3个工作器
        worker_id = f"worker_{i+1}"
        service.register_worker(worker_id)

    logger.info("任务队列初始化完成")
