<template>
  <div class="ai-providers-page">
    <div class="page-header">
      <div class="header-left">
        <h1>AI提供商配置</h1>
        <p>配置AI服务提供商，支持OpenAI通用接口</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加提供商
        </el-button>
      </div>
    </div>

    <!-- 提供商列表 -->
    <div class="providers-list">
      <el-table
        :data="providers"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column label="名称" width="200">
          <template #default="{ row }">
            <div class="provider-name">
              <el-tag :type="getProviderTypeColor(row.type)" size="small">
                {{ getProviderTypeName(row.type) }}
              </el-tag>
              <span style="margin-left: 8px;">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="端点" prop="endpoint" show-overflow-tooltip />
        
        <el-table-column label="模型" prop="model" width="150" />
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              @change="toggleProvider(row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="连接状态" width="120">
          <template #default="{ row }">
            <div class="connection-status">
              <el-tooltip
                v-if="connectionStatus[row.id]?.status === 'success'"
                :content="`连接测试成功: ${connectionStatus[row.id]?.message || '连接正常'}`"
                placement="top"
              >
                <el-tag type="success" size="small">
                  <el-icon><CircleCheck /></el-icon>
                  连接正常
                </el-tag>
              </el-tooltip>

              <el-tooltip
                v-else-if="connectionStatus[row.id]?.status === 'error'"
                :content="`连接测试失败: ${connectionStatus[row.id]?.message || '未知错误'}`"
                placement="top"
                effect="dark"
              >
                <el-tag type="danger" size="small">
                  <el-icon><CircleClose /></el-icon>
                  连接失败
                </el-tag>
              </el-tooltip>

              <el-tooltip
                v-else-if="connectionStatus[row.id]?.status === 'testing'"
                content="正在测试连接，请稍候..."
                placement="top"
              >
                <el-tag type="warning" size="small">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  测试中
                </el-tag>
              </el-tooltip>

              <el-tooltip
                v-else
                content="点击测试连接按钮检查连接状态"
                placement="top"
              >
                <el-tag type="info" size="small">
                  <el-icon><QuestionFilled /></el-icon>
                  未测试
                </el-tag>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-tooltip
              :content="getTestButtonTooltip(row)"
              placement="top"
              :disabled="testingProviders.has(row.id)"
            >
              <el-button
                size="small"
                @click="testProvider(row)"
                :disabled="testingProviders.has(row.id)"
                :type="getTestButtonType(row)"
              >
                {{ testingProviders.has(row.id) ? '测试中' : '测试连接' }}
              </el-button>
            </el-tooltip>

            <el-button size="small" @click="editProvider(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteProvider(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="dialogMode === 'create' ? '添加AI提供商' : '编辑AI提供商'"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="提供商名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入提供商名称" />
        </el-form-item>
        
        <el-form-item label="提供商类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择提供商类型">
            <el-option label="OpenAI" value="openai" />
            <el-option label="Azure OpenAI" value="azure" />
            <el-option label="Claude" value="claude" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="API端点" prop="endpoint">
          <el-input v-model="form.endpoint" placeholder="https://api.openai.com/v1" />
        </el-form-item>
        
        <el-form-item label="API密钥" prop="api_key">
          <el-input
            v-model="form.api_key"
            type="password"
            placeholder="请输入API密钥"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="模型名称" prop="model">
          <el-input v-model="form.model" placeholder="gpt-3.5-turbo" />
        </el-form-item>
        
        <el-form-item label="最大令牌数" prop="max_tokens">
          <el-input-number
            v-model="form.max_tokens"
            :min="1"
            :max="32000"
            placeholder="4096"
          />
        </el-form-item>
        
        <el-form-item label="温度参数" prop="temperature">
          <el-input-number
            v-model="form.temperature"
            :min="0"
            :max="2"
            :step="0.1"
            placeholder="0.7"
          />
        </el-form-item>
        
        <el-form-item label="启用状态">
          <el-switch v-model="form.enabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ dialogMode === 'create' ? '创建' : '更新' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, CircleCheck, CircleClose, Loading, QuestionFilled } from '@element-plus/icons-vue'
import {
  getAIProviders,
  createAIProvider,
  updateAIProvider,
  deleteAIProvider,
  testAIProvider,
  type AIProvider,
  type CreateAIProviderRequest
} from '@/api/system-config'

// 响应式数据
const loading = ref(false)
const providers = ref<AIProvider[]>([])
const dialogVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const submitting = ref(false)
const formRef = ref()

// 测试状态管理
const testingProviders = ref(new Set<string>())
const connectionStatus = ref<Record<string, {
  status: 'success' | 'error' | 'testing' | 'unknown',
  message?: string,
  timestamp?: number
}>>({})

// 表单数据
const form = reactive<CreateAIProviderRequest>({
  name: '',
  type: 'openai',
  endpoint: '',
  api_key: '',
  model: '',
  max_tokens: 4096,
  temperature: 0.7,
  enabled: true
})

const currentEditId = ref('')

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入提供商名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择提供商类型', trigger: 'change' }
  ],
  endpoint: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  api_key: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ]
}

// 获取提供商列表
const fetchProviders = async () => {
  try {
    loading.value = true
    const response = await getAIProviders()
    if (response.data) {
      providers.value = response.data.items
    }
  } catch (error) {
    ElMessage.error('获取提供商列表失败')
  } finally {
    loading.value = false
  }
}

// 显示创建对话框
const showCreateDialog = () => {
  dialogMode.value = 'create'
  dialogVisible.value = true
  resetForm()
}

// 编辑提供商
const editProvider = (provider: AIProvider) => {
  dialogMode.value = 'edit'
  currentEditId.value = provider.id
  
  // 填充表单数据
  Object.assign(form, {
    name: provider.name,
    type: provider.type,
    endpoint: provider.endpoint,
    api_key: provider.api_key,
    model: provider.model,
    max_tokens: provider.max_tokens,
    temperature: provider.temperature,
    enabled: provider.enabled
  })
  
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (dialogMode.value === 'create') {
      await createAIProvider(form)
      ElMessage.success('创建成功')
    } else {
      await updateAIProvider(currentEditId.value, form)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    fetchProviders()
  } catch (error) {
    ElMessage.error(dialogMode.value === 'create' ? '创建失败' : '更新失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    type: 'openai',
    endpoint: '',
    api_key: '',
    model: '',
    max_tokens: 4096,
    temperature: 0.7,
    enabled: true
  })
  currentEditId.value = ''
  formRef.value?.clearValidate()
}

// 切换提供商状态
const toggleProvider = async (provider: AIProvider) => {
  try {
    await updateAIProvider(provider.id, { enabled: provider.enabled })
    ElMessage.success(provider.enabled ? '已启用' : '已禁用')
  } catch (error) {
    provider.enabled = !provider.enabled // 回滚状态
    ElMessage.error('状态更新失败')
  }
}

// 测试提供商连接
const testProvider = async (provider: AIProvider) => {
  // 防止重复测试
  if (testingProviders.value.has(provider.id)) {
    return
  }

  try {
    // 设置测试状态
    testingProviders.value.add(provider.id)
    connectionStatus.value[provider.id] = {
      status: 'testing',
      message: '正在测试连接...',
      timestamp: Date.now()
    }

    console.log('🔍 开始测试提供商:', provider.id)

    // 显示开始测试的消息
    ElMessage.info({
      message: `正在测试 ${provider.name} 的连接...`,
      duration: 2000
    })

    const response = await testAIProvider(provider.id)
    console.log('🔍 测试响应:', response)

    // 检查不同的响应格式
    const result = response.data || response
    console.log('🔍 处理后的结果:', result)

    if (result?.success) {
      // 更新连接状态为成功
      connectionStatus.value[provider.id] = {
        status: 'success',
        message: result.message || '连接测试成功',
        timestamp: Date.now()
      }

      ElMessage.success({
        message: `${provider.name}: ${result.message || '连接测试成功'}`,
        duration: 3000
      })
    } else {
      // 更新连接状态为失败
      connectionStatus.value[provider.id] = {
        status: 'error',
        message: result?.message || '连接测试失败',
        timestamp: Date.now()
      }

      ElMessage.error({
        message: `${provider.name}: ${result?.message || '连接测试失败'}`,
        duration: 5000
      })
    }
  } catch (error: any) {
    console.error('🔍 测试连接错误:', error)

    // 更新连接状态为错误
    connectionStatus.value[provider.id] = {
      status: 'error',
      message: error.message || '连接测试异常',
      timestamp: Date.now()
    }

    ElMessage.error({
      message: `${provider.name}: 连接测试失败 - ${error.message || error}`,
      duration: 5000
    })
  } finally {
    // 清除测试状态
    testingProviders.value.delete(provider.id)
  }
}

// 删除提供商
const deleteProvider = async (provider: AIProvider) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除提供商 "${provider.name}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    await deleteAIProvider(provider.id)
    ElMessage.success('删除成功')
    fetchProviders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 工具函数
const getProviderTypeName = (type: string) => {
  const typeMap = {
    openai: 'OpenAI',
    azure: 'Azure',
    claude: 'Claude',
    custom: '自定义'
  }
  return typeMap[type] || type
}

const getProviderTypeColor = (type: string) => {
  const colorMap = {
    openai: 'success',
    azure: 'primary',
    claude: 'warning',
    custom: 'info'
  }
  return colorMap[type] || 'info'
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 获取测试按钮的提示文本
const getTestButtonTooltip = (provider: AIProvider) => {
  const status = connectionStatus.value[provider.id]
  if (!status) {
    return '点击测试与AI提供商的连接'
  }

  switch (status.status) {
    case 'success':
      return `上次测试成功: ${status.message}`
    case 'error':
      return `上次测试失败: ${status.message}`
    case 'testing':
      return '正在测试连接...'
    default:
      return '点击测试与AI提供商的连接'
  }
}

// 获取测试按钮的类型
const getTestButtonType = (provider: AIProvider) => {
  const status = connectionStatus.value[provider.id]
  if (!status) return 'default'

  switch (status.status) {
    case 'success':
      return 'success'
    case 'error':
      return 'danger'
    case 'testing':
      return 'warning'
    default:
      return 'default'
  }
}

// 初始化
onMounted(() => {
  fetchProviders()
})
</script>

<style scoped>
.ai-providers-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.providers-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.provider-name {
  display: flex;
  align-items: center;
}

/* 连接状态样式 */
.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.connection-status .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.connection-status .el-icon {
  font-size: 12px;
}

/* 测试按钮加载状态 */
.el-button.is-loading .el-icon {
  margin-right: 4px;
}

/* 表格行悬停效果 */
.el-table__row:hover .connection-status .el-tag {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.is-loading {
  animation: spin 1s linear infinite;
}
</style>
