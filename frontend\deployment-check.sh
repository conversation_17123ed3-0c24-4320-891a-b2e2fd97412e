#!/bin/bash
# 部署后验证脚本
# 用于检查部署到服务器后的前端资源是否正常

set -e

# 配置
SERVER_URL="${1:-http://192.168.123.137}"
TIMEOUT=10

echo "🔍 开始检查部署状态..."
echo "服务器地址: $SERVER_URL"
echo "超时时间: ${TIMEOUT}s"
echo

# 检查主页是否可访问
echo "📄 检查主页访问..."
if curl -s --max-time $TIMEOUT "$SERVER_URL" > /dev/null; then
    echo "✅ 主页可访问"
else
    echo "❌ 主页无法访问"
    exit 1
fi

# 获取主页内容并提取资源引用
echo
echo "🔗 检查资源文件引用..."
INDEX_CONTENT=$(curl -s --max-time $TIMEOUT "$SERVER_URL")

# 提取 JavaScript 文件
JS_FILES=$(echo "$INDEX_CONTENT" | grep -oP 'src="[^"]*\.js"' | sed 's/src="//g' | sed 's/"//g')
CSS_FILES=$(echo "$INDEX_CONTENT" | grep -oP 'href="[^"]*\.css"' | sed 's/href="//g' | sed 's/"//g')

echo "发现的 JavaScript 文件:"
for js_file in $JS_FILES; do
    echo "  - $js_file"
done

echo
echo "发现的 CSS 文件:"
for css_file in $CSS_FILES; do
    echo "  - $css_file"
done

echo
echo "🧪 验证资源文件可访问性..."

# 检查 JavaScript 文件
JS_SUCCESS=0
JS_TOTAL=0
for js_file in $JS_FILES; do
    JS_TOTAL=$((JS_TOTAL + 1))
    full_url="$SERVER_URL$js_file"
    
    if curl -s --head --max-time $TIMEOUT "$full_url" | head -n 1 | grep -q "200 OK"; then
        echo "✅ $js_file"
        JS_SUCCESS=$((JS_SUCCESS + 1))
    else
        echo "❌ $js_file (无法访问)"
    fi
done

# 检查 CSS 文件
CSS_SUCCESS=0
CSS_TOTAL=0
for css_file in $CSS_FILES; do
    CSS_TOTAL=$((CSS_TOTAL + 1))
    full_url="$SERVER_URL$css_file"
    
    if curl -s --head --max-time $TIMEOUT "$full_url" | head -n 1 | grep -q "200 OK"; then
        echo "✅ $css_file"
        CSS_SUCCESS=$((CSS_SUCCESS + 1))
    else
        echo "❌ $css_file (无法访问)"
    fi
done

echo
echo "📊 检查结果统计:"
echo "JavaScript 文件: $JS_SUCCESS/$JS_TOTAL 可访问"
echo "CSS 文件: $CSS_SUCCESS/$CSS_TOTAL 可访问"

# 检查关键模块
echo
echo "🎯 检查关键模块:"
CRITICAL_MODULES=("Management" "publish-management" "api-module" "vue-vendor" "element-plus")

for module in "${CRITICAL_MODULES[@]}"; do
    found=false
    for js_file in $JS_FILES; do
        if [[ "$js_file" == *"$module"* ]]; then
            full_url="$SERVER_URL$js_file"
            if curl -s --head --max-time $TIMEOUT "$full_url" | head -n 1 | grep -q "200 OK"; then
                echo "✅ $module 模块正常"
                found=true
                break
            fi
        fi
    done
    
    if [ "$found" = false ]; then
        echo "❌ $module 模块缺失或无法访问"
    fi
done

# 总结
echo
if [ $JS_SUCCESS -eq $JS_TOTAL ] && [ $CSS_SUCCESS -eq $CSS_TOTAL ]; then
    echo "🎉 部署验证通过！所有资源文件都可正常访问。"
    exit 0
else
    echo "❌ 部署验证失败！存在无法访问的资源文件。"
    echo
    echo "🔧 建议的解决步骤:"
    echo "1. 检查构建过程是否正常完成"
    echo "2. 确认所有构建产物都已正确复制到服务器"
    echo "3. 检查 nginx 配置是否正确"
    echo "4. 清理浏览器缓存后重试"
    echo "5. 重新构建并部署"
    exit 1
fi
