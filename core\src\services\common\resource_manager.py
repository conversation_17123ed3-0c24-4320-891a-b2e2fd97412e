"""
资源管理器
统一管理所有系统资源，防止泄露
"""

import logging
import asyncio
from typing import Set, Dict, Any
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class ResourceManager:
    """全局资源管理器"""
    
    def __init__(self):
        self.active_tasks: Set[asyncio.Task] = set()
        self.thread_pools: Set[ThreadPoolExecutor] = set()
        self.redis_clients: Set[Any] = set()
        self.webdriver_sessions: Set[Any] = set()
        self.grpc_servers: Set[Any] = set()
        self._shutdown_callbacks = []
        
    def register_task(self, task: asyncio.Task) -> asyncio.Task:
        """注册异步任务"""
        self.active_tasks.add(task)
        
        def cleanup_task(future):
            self.active_tasks.discard(future)
            if future.exception():
                logger.error(f"异步任务异常: {future.exception()}")
        
        task.add_done_callback(cleanup_task)
        return task
    
    def register_thread_pool(self, pool: ThreadPoolExecutor) -> ThreadPoolExecutor:
        """注册线程池"""
        self.thread_pools.add(pool)
        return pool
    
    def register_redis_client(self, client: Any) -> Any:
        """注册Redis客户端"""
        self.redis_clients.add(client)
        return client
    
    def register_webdriver(self, driver: Any) -> Any:
        """注册WebDriver会话"""
        self.webdriver_sessions.add(driver)
        return driver
    
    def register_grpc_server(self, server: Any) -> Any:
        """注册gRPC服务器"""
        self.grpc_servers.add(server)
        return server
    
    def unregister_webdriver(self, driver: Any):
        """注销WebDriver会话"""
        self.webdriver_sessions.discard(driver)
    
    def unregister_redis_client(self, client: Any):
        """注销Redis客户端"""
        self.redis_clients.discard(client)
    
    def add_shutdown_callback(self, callback):
        """添加关闭回调"""
        self._shutdown_callbacks.append(callback)
    
    async def shutdown_all(self):
        """关闭所有资源"""
        logger.info("开始关闭所有资源...")
        
        # 执行自定义关闭回调
        for callback in self._shutdown_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                logger.error(f"执行关闭回调失败: {str(e)}")
        
        # 取消所有活跃任务
        if self.active_tasks:
            logger.info(f"取消 {len(self.active_tasks)} 个活跃任务...")
            for task in list(self.active_tasks):
                if not task.done():
                    task.cancel()
            
            # 等待任务取消完成
            if self.active_tasks:
                try:
                    await asyncio.wait(self.active_tasks, timeout=5.0)
                except asyncio.TimeoutError:
                    logger.warning("部分任务取消超时")
        
        # 关闭所有WebDriver会话
        if self.webdriver_sessions:
            logger.info(f"关闭 {len(self.webdriver_sessions)} 个WebDriver会话...")
            for driver in list(self.webdriver_sessions):
                try:
                    driver.quit()
                except Exception as e:
                    # 检查是否是会话已终止的错误，如果是则忽略
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in [
                        'session is either terminated or not started',
                        'session not found',
                        'invalid session id',
                        'no such session'
                    ]):
                        logger.debug(f"WebDriver会话已终止，忽略关闭错误: {str(e)}")
                    else:
                        logger.warning(f"关闭WebDriver会话失败: {str(e)}")
            self.webdriver_sessions.clear()
        
        # 关闭所有Redis连接
        if self.redis_clients:
            logger.info(f"关闭 {len(self.redis_clients)} 个Redis连接...")
            for client in list(self.redis_clients):
                try:
                    if hasattr(client, 'close'):
                        if asyncio.iscoroutinefunction(client.close):
                            await client.close()
                        else:
                            client.close()
                except Exception as e:
                    logger.error(f"关闭Redis连接失败: {str(e)}")
            self.redis_clients.clear()
        
        # 关闭所有gRPC服务器
        if self.grpc_servers:
            logger.info(f"关闭 {len(self.grpc_servers)} 个gRPC服务器...")
            for server in list(self.grpc_servers):
                try:
                    await server.stop(5)
                except Exception as e:
                    logger.error(f"关闭gRPC服务器失败: {str(e)}")
            self.grpc_servers.clear()
        
        # 关闭所有线程池
        if self.thread_pools:
            logger.info(f"关闭 {len(self.thread_pools)} 个线程池...")
            for pool in list(self.thread_pools):
                try:
                    # 尝试使用timeout参数，如果不支持则回退到基本方式
                    try:
                        pool.shutdown(wait=True, timeout=10)
                    except TypeError:
                        # Python版本不支持timeout参数，使用基本方式
                        logger.debug("使用基本方式关闭线程池（不支持timeout参数）")
                        pool.shutdown(wait=True)
                except Exception as e:
                    logger.error(f"关闭线程池失败: {str(e)}")
            self.thread_pools.clear()
        
        logger.info("所有资源关闭完成")
    
    def get_resource_stats(self) -> Dict[str, int]:
        """获取资源统计"""
        return {
            "active_tasks": len(self.active_tasks),
            "thread_pools": len(self.thread_pools),
            "redis_clients": len(self.redis_clients),
            "webdriver_sessions": len(self.webdriver_sessions),
            "grpc_servers": len(self.grpc_servers)
        }

# 全局资源管理器实例
resource_manager = ResourceManager()

def create_managed_task(coro) -> asyncio.Task:
    """创建受管理的异步任务"""
    task = asyncio.create_task(coro)
    return resource_manager.register_task(task)

def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器"""
    return resource_manager
