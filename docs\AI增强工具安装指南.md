# AI增强工具安装指南

为了获得最佳的高清处理效果，建议安装以下AI超分辨率工具。如果不安装这些工具，系统会自动使用优化的FFmpeg算法。

## Real-ESRGAN (推荐)

Real-ESRGAN是目前最先进的AI超分辨率工具之一，特别适合处理真实世界的图片和视频。

### Windows安装

1. **下载预编译版本**
   ```
   https://github.com/xinntao/Real-ESRGAN/releases
   ```
   下载 `realesrgan-ncnn-vulkan-20220424-windows.zip`

2. **解压到系统目录**
   ```
   解压到: C:\Program Files\Real-ESRGAN\
   ```

3. **添加到环境变量**
   - 打开系统环境变量设置
   - 在PATH中添加: `C:\Program Files\Real-ESRGAN`

4. **验证安装**
   ```cmd
   realesrgan-ncnn-vulkan.exe --help
   ```

### Linux/Mac安装

1. **使用包管理器安装**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install realesrgan-ncnn-vulkan
   
   # macOS (使用Homebrew)
   brew install realesrgan-ncnn-vulkan
   ```

2. **手动编译安装**
   ```bash
   git clone https://github.com/xinntao/Real-ESRGAN.git
   cd Real-ESRGAN
   # 按照官方文档编译
   ```

3. **验证安装**
   ```bash
   realesrgan-ncnn-vulkan --help
   ```

## waifu2x (备选方案)

waifu2x是另一个优秀的AI超分辨率工具，特别适合动漫风格的图片。

### Windows安装

1. **下载预编译版本**
   ```
   https://github.com/nihui/waifu2x-ncnn-vulkan/releases
   ```
   下载 `waifu2x-ncnn-vulkan-20220728-windows.zip`

2. **解压到系统目录**
   ```
   解压到: C:\Program Files\waifu2x\
   ```

3. **添加到环境变量**
   - 在PATH中添加: `C:\Program Files\waifu2x`

4. **验证安装**
   ```cmd
   waifu2x-ncnn-vulkan.exe --help
   ```

### Linux/Mac安装

1. **使用包管理器**
   ```bash
   # Ubuntu/Debian
   sudo apt install waifu2x-ncnn-vulkan
   
   # macOS
   brew install waifu2x-ncnn-vulkan
   ```

2. **验证安装**
   ```bash
   waifu2x-ncnn-vulkan --help
   ```

## 系统要求

### 硬件要求
- **GPU**: 支持Vulkan的显卡（推荐）
- **内存**: 至少8GB RAM
- **存储**: 足够的磁盘空间存储输出文件

### 支持的GPU
- **NVIDIA**: GTX 1050及以上
- **AMD**: RX 560及以上  
- **Intel**: UHD 630及以上

## 使用说明

### 自动检测
系统会自动检测已安装的AI工具：
1. 优先使用Real-ESRGAN
2. 如果Real-ESRGAN不可用，尝试waifu2x
3. 如果AI工具都不可用，使用优化的FFmpeg算法

### 处理流程
```
选择AI模式 → 检测AI工具 → 执行AI处理 → 降级到FFmpeg（如果需要）
```

### 性能对比

| 工具 | 处理速度 | 画质提升 | GPU使用 | 适用场景 |
|------|----------|----------|---------|----------|
| Real-ESRGAN | 慢 | 极佳 | 高 | 真实照片、视频 |
| waifu2x | 中等 | 很好 | 中等 | 动漫图片 |
| FFmpeg优化 | 快 | 良好 | 低 | 通用场景 |

## 故障排除

### 常见问题

1. **"命令未找到"错误**
   - 检查工具是否正确安装
   - 验证环境变量PATH设置
   - 重启终端或系统

2. **GPU相关错误**
   - 更新显卡驱动
   - 检查Vulkan支持
   - 尝试使用CPU模式

3. **内存不足错误**
   - 减少并发处理数
   - 处理较小的文件
   - 增加系统内存

4. **处理失败**
   - 检查输入文件格式
   - 确保有足够磁盘空间
   - 查看详细错误日志

### 日志查看
系统会在控制台输出详细的处理日志：
```
2024-07-24 18:00:00 - INFO - 使用Real-ESRGAN进行AI超分辨率处理
2024-07-24 18:00:01 - INFO - Real-ESRGAN处理成功
```

## 性能优化建议

### 提升处理速度
1. **使用SSD存储**：提高文件读写速度
2. **充足内存**：避免内存不足导致的性能下降
3. **GPU加速**：确保GPU驱动最新
4. **合理并发**：根据系统性能调整并发数

### 提升处理质量
1. **选择合适的工具**：
   - 真实照片 → Real-ESRGAN
   - 动漫图片 → waifu2x
   - 通用场景 → FFmpeg优化
2. **调整参数**：
   - 适当的放大倍数
   - 合理的降噪级别
   - 适度的锐化强度

## 更新和维护

### 定期更新
- 定期检查工具的新版本
- 更新显卡驱动
- 关注新的AI模型发布

### 备份重要文件
- 处理前备份原始文件
- 测试新工具前备份系统
- 保存成功的配置参数

## 技术支持

如果遇到问题，请：
1. 查看系统日志
2. 检查工具版本兼容性
3. 参考官方文档
4. 联系技术支持

---

**注意**: AI增强工具是可选的。即使不安装这些工具，系统仍然可以使用优化的FFmpeg算法提供良好的高清处理效果。
