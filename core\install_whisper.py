#!/usr/bin/env python3
"""
Whisper安装脚本
确保openai-whisper正确安装并可用
"""

import subprocess
import sys
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(cmd):
    """执行命令"""
    try:
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"命令执行成功")
        if result.stdout:
            logger.info(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {e.stderr}")
        return False

def check_current_environment():
    """检查当前Python环境"""
    logger.info("检查当前Python环境...")
    logger.info(f"Python可执行文件: {sys.executable}")
    logger.info(f"Python版本: {sys.version}")

    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.info("✅ 检测到虚拟环境")
    else:
        logger.warning("⚠️  当前不在虚拟环境中，建议使用虚拟环境")

def install_whisper():
    """安装Whisper"""
    logger.info("开始安装Whisper...")

    # 检查当前环境
    check_current_environment()

    # 安装openai-whisper
    logger.info("安装 openai-whisper...")
    if not run_command([sys.executable, "-m", "pip", "install", "openai-whisper"]):
        logger.error("安装 openai-whisper 失败")
        return False

    logger.info("Whisper安装完成!")

    # 测试Whisper是否可用
    logger.info("测试Whisper是否可用...")

    # 先测试模块导入
    try:
        import whisper
        logger.info(f"✅ Whisper模块导入成功")
        if hasattr(whisper, '__version__'):
            logger.info(f"   版本: {whisper.__version__}")
    except ImportError as e:
        logger.error(f"❌ Whisper模块导入失败: {e}")
        return False

    # 测试命令行工具
    try:
        result = subprocess.run([sys.executable, "-m", "whisper", "--help"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info("✅ Whisper Python模块测试成功!")
        else:
            logger.warning("⚠️  Whisper Python模块测试失败，但模块导入成功")
    except Exception as e:
        logger.warning(f"⚠️  Whisper命令行测试失败: {e}")

    # 检查whisper命令是否在PATH中
    try:
        result = subprocess.run(["whisper", "--help"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info("✅ Whisper命令行工具在PATH中可用!")
        else:
            logger.warning("⚠️  Whisper命令行工具不在PATH中")
    except Exception as e:
        logger.warning(f"⚠️  Whisper命令行工具不在PATH中: {e}")
        logger.info("💡 这是正常的，系统会使用Python API作为备选方案")

    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🎵 ThunderHub Whisper安装")
    print("=" * 60)

    success = install_whisper()

    print("\n" + "=" * 60)
    if success:
        print("🎉 Whisper安装成功!")
        print("=" * 60)
        print("\n📋 使用说明:")
        print("1. 系统会优先尝试使用Python API生成字幕")
        print("2. 如果Python API失败，会回退到命令行工具")
        print("3. 支持多种输出格式：srt, vtt, txt")
        print(f"4. 当前Python环境: {sys.executable}")
        print("\n现在可以在ThunderHub中使用字幕生成功能了！")
    else:
        print("❌ Whisper安装失败!")
        print("=" * 60)
        print("\n🔧 故障排除:")
        print("1. 确保在正确的Python环境中运行此脚本")
        print("2. 检查网络连接是否正常")
        print("3. 尝试手动安装: pip install openai-whisper")
        print(f"4. 当前Python环境: {sys.executable}")
        sys.exit(1)
    print("=" * 60)
