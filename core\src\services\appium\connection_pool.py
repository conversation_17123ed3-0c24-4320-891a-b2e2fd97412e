"""
Appium连接池管理器
解决多设备并发连接时的session管理和端口冲突问题
"""

import asyncio
import logging
from typing import Dict, Optional, Set
from dataclasses import dataclass
from appium.webdriver.webdriver import WebDriver
from appium.options.android import UiAutomator2Options
from selenium.webdriver.remote import webdriver
import time

logger = logging.getLogger(__name__)

@dataclass
class AppiumConnection:
    """Appium连接信息"""
    device_id: str
    driver: WebDriver
    system_port: int
    mjpeg_port: int
    created_at: float
    last_used: float
    is_healthy: bool = True

class AppiumConnectionPool:
    """Appium连接池管理器"""
    
    def __init__(self, appium_server: str = 'http://localhost:4723'):
        self.appium_server = appium_server
        self.connections: Dict[str, AppiumConnection] = {}
        self.port_allocations: Set[int] = set()
        self.base_system_port = 8200
        self.base_mjpeg_port = 7800
        self.max_connections = 20
        self.connection_timeout = 1800  # 30分钟超时
        self.health_check_interval = 300  # 5分钟健康检查
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """启动后台维护任务"""
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._cleanup_expired_connections())
    
    def _allocate_ports(self, device_id: str) -> tuple[int, int]:
        """为设备分配端口
        
        Args:
            device_id: 设备ID
            
        Returns:
            tuple: (system_port, mjpeg_port)
        """
        try:
            # 使用设备ID计算偏移量
            if device_id.isdigit():
                offset = int(device_id) % 100
            else:
                import re
                numbers = re.findall(r'\d+', device_id)
                if numbers:
                    offset = int(numbers[-1]) % 100
                else:
                    offset = hash(device_id) % 100
            
            system_port = self.base_system_port + offset
            mjpeg_port = self.base_mjpeg_port + offset
            
            # 确保端口未被占用
            while system_port in self.port_allocations:
                offset = (offset + 1) % 100
                system_port = self.base_system_port + offset
                mjpeg_port = self.base_mjpeg_port + offset
            
            self.port_allocations.add(system_port)
            self.port_allocations.add(mjpeg_port)
            
            logger.info(f"为设备{device_id}分配端口 - systemPort: {system_port}, mjpegServerPort: {mjpeg_port}")
            return system_port, mjpeg_port
            
        except Exception as e:
            logger.error(f"端口分配失败: {str(e)}")
            # 使用默认端口
            return self.base_system_port, self.base_mjpeg_port
    
    def _release_ports(self, system_port: int, mjpeg_port: int):
        """释放端口"""
        self.port_allocations.discard(system_port)
        self.port_allocations.discard(mjpeg_port)
    
    async def get_connection(self, device_id: str, app_package: str = None, app_activity: str = None) -> Optional[WebDriver]:
        """获取或创建设备连接
        
        Args:
            device_id: 设备ID
            app_package: 应用包名
            app_activity: 应用活动
            
        Returns:
            WebDriver实例或None
        """
        try:
            current_time = time.time()
            
            # 检查是否已有连接
            if device_id in self.connections:
                connection = self.connections[device_id]
                
                # 检查连接是否健康且未超时
                if (connection.is_healthy and 
                    current_time - connection.created_at < self.connection_timeout):
                    
                    # 更新最后使用时间
                    connection.last_used = current_time
                    logger.info(f"复用设备{device_id}的现有连接")
                    return connection.driver
                else:
                    # 连接不健康或超时，需要重新创建
                    logger.warning(f"设备{device_id}的连接不健康或超时，重新创建")
                    await self._close_connection(device_id)
            
            # 创建新连接
            return await self._create_connection(device_id, app_package, app_activity)
            
        except Exception as e:
            logger.error(f"获取设备{device_id}连接失败: {str(e)}")
            return None
    
    async def _create_connection(self, device_id: str, app_package: str = None, app_activity: str = None) -> Optional[WebDriver]:
        """创建新的Appium连接
        
        Args:
            device_id: 设备ID
            app_package: 应用包名
            app_activity: 应用活动
            
        Returns:
            WebDriver实例或None
        """
        try:
            # 检查连接数限制
            if len(self.connections) >= self.max_connections:
                logger.warning(f"连接数已达上限({self.max_connections})，清理旧连接")
                await self._cleanup_oldest_connection()
            
            # 分配端口
            system_port, mjpeg_port = self._allocate_ports(device_id)
            
            # 创建连接选项
            options = UiAutomator2Options()
            options.platform_name = 'Android'
            options.device_name = device_id
            options.udid = device_id
            options.automation_name = 'UiAutomator2'
            options.platform_version = '9.0'
            
            if app_package:
                options.app_package = app_package
            if app_activity:
                options.app_activity = app_activity
            
            options.no_reset = True
            options.fast_reset = False
            options.auto_grant_permissions = True
            options.new_command_timeout = 1200
            
            # UiAutomator2 稳定性配置
            options.capabilities.update({
                'appium:uiautomator2ServerLaunchTimeout': 120000,
                'appium:uiautomator2ServerInstallTimeout': 120000,
                'appium:uiautomator2ServerReadTimeout': 300000,
                'appium:androidInstallTimeout': 180000,
                'appium:adbExecTimeout': 120000,
                'appium:clearSystemFiles': True,
                'appium:enforceXPath1': True,
                'appium:disableWindowAnimation': True,
                'appium:skipServerInstallation': False,
                'appium:ignoreUnimportantViews': True,
                'appium:waitForIdleTimeout': 20000,
                'appium:newCommandTimeout': 1200,
                'appium:keepAliveTimeout': 600,
                'appium:uiautomator2ServerSocketTimeout': 120000,
                'appium:systemPort': system_port,
                'appium:mjpegServerPort': mjpeg_port,
            })
            
            logger.info(f"创建设备{device_id}的Appium连接，端口: {system_port}/{mjpeg_port}")
            
            # 创建WebDriver
            driver = webdriver.Remote(self.appium_server, options=options)
            
            # 创建连接对象
            current_time = time.time()
            connection = AppiumConnection(
                device_id=device_id,
                driver=driver,
                system_port=system_port,
                mjpeg_port=mjpeg_port,
                created_at=current_time,
                last_used=current_time,
                is_healthy=True
            )
            
            self.connections[device_id] = connection
            logger.info(f"设备{device_id}的Appium连接创建成功")
            
            return driver
            
        except Exception as e:
            logger.error(f"创建设备{device_id}的Appium连接失败: {str(e)}")
            # 释放分配的端口
            self._release_ports(system_port, mjpeg_port)
            return None
    
    async def _close_connection(self, device_id: str):
        """关闭指定设备的连接"""
        if device_id in self.connections:
            connection = self.connections[device_id]
            try:
                connection.driver.quit()
            except Exception as e:
                # 检查是否是会话已终止的错误
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in [
                    'session is either terminated or not started',
                    'session not found',
                    'invalid session id',
                    'no such session'
                ]):
                    logger.debug(f"设备{device_id}的WebDriver会话已终止，跳过关闭: {str(e)}")
                else:
                    logger.warning(f"关闭设备{device_id}连接时出错: {str(e)}")

            # 释放端口
            self._release_ports(connection.system_port, connection.mjpeg_port)

            # 移除连接
            del self.connections[device_id]
            logger.info(f"已关闭设备{device_id}的连接")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_checks()
            except Exception as e:
                logger.error(f"健康检查异常: {str(e)}")
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        for device_id, connection in list(self.connections.items()):
            try:
                # 简单的健康检查
                connection.driver.current_activity
                connection.is_healthy = True
            except Exception as e:
                logger.warning(f"设备{device_id}健康检查失败: {str(e)}")
                connection.is_healthy = False
    
    async def _cleanup_expired_connections(self):
        """清理过期连接"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                current_time = time.time()
                
                expired_devices = []
                for device_id, connection in self.connections.items():
                    if (current_time - connection.last_used > self.connection_timeout or
                        not connection.is_healthy):
                        expired_devices.append(device_id)
                
                for device_id in expired_devices:
                    logger.info(f"清理过期连接: {device_id}")
                    await self._close_connection(device_id)
                    
            except Exception as e:
                logger.error(f"清理过期连接异常: {str(e)}")
    
    async def _cleanup_oldest_connection(self):
        """清理最旧的连接"""
        if not self.connections:
            return
        
        oldest_device = min(self.connections.keys(), 
                          key=lambda d: self.connections[d].last_used)
        await self._close_connection(oldest_device)
    
    async def close_all(self):
        """关闭所有连接"""
        for device_id in list(self.connections.keys()):
            await self._close_connection(device_id)
        logger.info("已关闭所有Appium连接")

# 全局连接池实例
_connection_pool = None

def get_appium_connection_pool() -> AppiumConnectionPool:
    """获取全局Appium连接池实例"""
    global _connection_pool
    if _connection_pool is None:
        _connection_pool = AppiumConnectionPool()
    return _connection_pool
