#!/usr/bin/env python3
"""
AI人声分离依赖安装脚本
安装Demucs和相关依赖
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(cmd):
    """执行命令"""
    try:
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"命令执行成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {e.stderr}")
        return False

def check_current_environment():
    """检查当前Python环境"""
    logger.info("检查当前Python环境...")
    logger.info(f"Python可执行文件: {sys.executable}")
    logger.info(f"Python版本: {sys.version}")

    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.info("✅ 检测到虚拟环境")
    else:
        logger.warning("⚠️  当前不在虚拟环境中，建议使用虚拟环境")

def install_ai_dependencies():
    """安装AI人声分离依赖"""
    logger.info("开始安装AI人声分离依赖...")

    # 检查当前环境
    check_current_environment()

    # 安装Demucs
    packages = [
        "demucs",
        "torch",
        "torchaudio",
        "librosa",
        "soundfile",
        "pydub"
    ]

    for package in packages:
        logger.info(f"安装 {package}...")
        if not run_command([sys.executable, "-m", "pip", "install", package]):
            logger.error(f"安装 {package} 失败")
            return False

    logger.info("所有依赖安装完成!")

    # 测试Demucs是否可用
    logger.info("测试Demucs是否可用...")
    logger.info(f"使用Python: {sys.executable}")

    # 先测试模块导入
    try:
        import demucs
        logger.info(f"✅ Demucs模块导入成功")
        if hasattr(demucs, '__version__'):
            logger.info(f"   版本: {demucs.__version__}")
    except ImportError as e:
        logger.error(f"❌ Demucs模块导入失败: {e}")
        return False

    # 再测试命令行工具
    if run_command([sys.executable, "-m", "demucs", "--help"]):
        logger.info("✅ Demucs命令行工具测试成功!")
        return True
    else:
        logger.error("❌ Demucs命令行工具测试失败")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎵 ThunderHub AI人声分离依赖安装")
    print("=" * 60)

    success = install_ai_dependencies()

    print("\n" + "=" * 60)
    if success:
        print("🎉 AI人声分离依赖安装成功!")
        print("=" * 60)
        print("\n📋 使用说明:")
        print("1. 在ThunderHub中选择 separation_method='librosa' 启用AI分离")
        print("2. AI分离效果更好但处理时间较长")
        print("3. 系统会自动使用当前Python环境中的Demucs")
        print(f"4. 当前Python环境: {sys.executable}")
        print("\n现在可以在ThunderHub中使用AI人声分离功能了！")
    else:
        print("❌ AI人声分离依赖安装失败!")
        print("=" * 60)
        print("\n🔧 故障排除:")
        print("1. 确保在正确的Python环境中运行此脚本")
        print("2. 检查网络连接是否正常")
        print("3. 尝试手动安装: pip install demucs torch torchaudio")
        print(f"4. 当前Python环境: {sys.executable}")
        sys.exit(1)
    print("=" * 60)
