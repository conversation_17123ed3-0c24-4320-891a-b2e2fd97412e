"""
文件存储服务 - Core服务版本（简化版）
负责管理生成内容的文件存储，支持按账号隔离
暂时使用同步文件操作，不依赖aiofiles
"""

import logging
import os
import shutil
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class FileStorageService:
    """文件存储服务"""
    
    def __init__(self, base_path: str = "data/content_generation"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
    def get_account_storage_path(self, account_id: str, content_type: str = "video") -> Path:
        """获取账号专属存储路径"""
        account_path = self.base_path / "files" / account_id / content_type
        account_path.mkdir(parents=True, exist_ok=True)
        return account_path
        
    async def copy_file_for_account(self, source_path: str, account_id: str, task_id: str, 
                                  content_type: str = "video") -> Dict[str, Any]:
        """为指定账号复制文件"""
        try:
            source = Path(source_path)
            
            if not source.exists():
                return {
                    "success": False,
                    "error": f"源文件不存在: {source_path}"
                }
                
            # 生成目标文件名
            file_id = str(uuid.uuid4())
            file_ext = source.suffix
            filename = f"{task_id}_{file_id}{file_ext}"
            
            # 获取账号专属存储路径
            storage_path = self.get_account_storage_path(account_id, content_type)
            target_path = storage_path / filename
            
            # 复制文件
            shutil.copy2(source, target_path)
            
            # 获取文件信息
            file_size = target_path.stat().st_size
            
            logger.info(f"文件复制成功: {source} -> {target_path}")
            
            return {
                "success": True,
                "file_id": file_id,
                "filename": filename,
                "file_path": str(target_path.relative_to(self.base_path)),
                "file_size": file_size,
                "content_type": content_type,
                "account_id": account_id,
                "created_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"复制文件失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def save_file_for_account(self, file_data: bytes, filename: str, account_id: str,
                                  content_type: str = "video") -> Dict[str, Any]:
        """为指定账号保存文件（简化版）"""
        try:
            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            file_ext = os.path.splitext(filename)[1]
            unique_filename = f"{file_id}{file_ext}"

            # 获取账号专属存储路径
            storage_path = self.get_account_storage_path(account_id, content_type)
            file_path = storage_path / unique_filename

            # 保存文件（同步方式）
            with open(file_path, 'wb') as f:
                f.write(file_data)

            # 获取文件信息
            file_size = len(file_data)

            logger.info(f"文件保存成功: {file_path}, 大小: {file_size} bytes")

            return {
                "success": True,
                "file_id": file_id,
                "filename": unique_filename,
                "original_filename": filename,
                "file_path": str(file_path.relative_to(self.base_path)),
                "file_size": file_size,
                "content_type": content_type,
                "account_id": account_id,
                "created_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def get_file_info(self, file_path: str, account_id: str) -> Dict[str, Any]:
        """获取文件信息（需要账号验证）"""
        try:
            full_path = self.base_path / file_path
            
            # 验证文件是否属于指定账号
            if not self._verify_file_access(full_path, account_id):
                return {
                    "success": False,
                    "error": "无权访问该文件"
                }
            
            if not full_path.exists():
                return {
                    "success": False,
                    "error": "文件不存在"
                }
                
            stat = full_path.stat()
            
            return {
                "success": True,
                "file_path": file_path,
                "filename": full_path.name,
                "file_size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "exists": True
            }
            
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def read_file(self, file_path: str, account_id: str) -> Dict[str, Any]:
        """读取文件内容（需要账号验证，简化版）"""
        try:
            full_path = self.base_path / file_path

            # 验证文件是否属于指定账号
            if not self._verify_file_access(full_path, account_id):
                return {
                    "success": False,
                    "error": "无权访问该文件"
                }

            if not full_path.exists():
                return {
                    "success": False,
                    "error": "文件不存在"
                }

            # 同步读取文件
            with open(full_path, 'rb') as f:
                content = f.read()

            return {
                "success": True,
                "content": content,
                "file_size": len(content),
                "filename": full_path.name
            }

        except Exception as e:
            logger.error(f"读取文件失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    async def delete_file(self, file_path: str, account_id: str) -> Dict[str, Any]:
        """删除文件（需要账号验证）"""
        try:
            full_path = self.base_path / file_path
            
            # 验证文件是否属于指定账号
            if not self._verify_file_access(full_path, account_id):
                return {
                    "success": False,
                    "error": "无权删除该文件"
                }
            
            if not full_path.exists():
                return {
                    "success": False,
                    "error": "文件不存在"
                }
                
            full_path.unlink()
            
            logger.info(f"文件删除成功: {full_path}")
            
            return {
                "success": True,
                "message": "文件删除成功"
            }
            
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }
            
    def _verify_file_access(self, file_path: Path, account_id: str) -> bool:
        """验证文件访问权限"""
        try:
            # 检查文件路径是否在账号目录下
            account_path = self.base_path / "files" / account_id
            
            # 获取相对路径
            try:
                relative_path = file_path.relative_to(account_path)
                return True
            except ValueError:
                # 文件不在账号目录下
                return False
                
        except Exception as e:
            logger.error(f"验证文件访问权限失败: {str(e)}")
            return False
            
    async def get_account_storage_stats(self, account_id: str) -> Dict[str, Any]:
        """获取账号存储统计信息"""
        try:
            account_path = self.base_path / "files" / account_id
            
            if not account_path.exists():
                return {
                    "success": True,
                    "stats": {
                        "total_files": 0,
                        "total_size": 0,
                        "content_types": {}
                    }
                }
            
            stats = {
                "total_files": 0,
                "total_size": 0,
                "content_types": {}
            }
            
            for content_type_dir in account_path.iterdir():
                if content_type_dir.is_dir():
                    content_type = content_type_dir.name
                    file_count = 0
                    total_size = 0
                    
                    for file_path in content_type_dir.rglob("*"):
                        if file_path.is_file():
                            file_count += 1
                            total_size += file_path.stat().st_size
                            
                    stats["content_types"][content_type] = {
                        "file_count": file_count,
                        "total_size": total_size
                    }
                    
                    stats["total_files"] += file_count
                    stats["total_size"] += total_size
                    
            return {
                "success": True,
                "stats": stats
            }
            
        except Exception as e:
            logger.error(f"获取存储统计失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }

# 全局文件存储服务实例
_file_storage_service = None

def get_file_storage_service() -> FileStorageService:
    """获取文件存储服务实例"""
    global _file_storage_service
    
    if _file_storage_service is None:
        base_path = os.getenv("CONTENT_GENERATION_STORAGE_PATH", "data/content_generation")
        _file_storage_service = FileStorageService(base_path)
        
    return _file_storage_service
