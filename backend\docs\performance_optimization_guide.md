# Backend 多任务处理性能优化指南

## 🚀 优化概述

本次优化主要针对 Backend 在处理多任务时的阻塞问题，通过以下几个方面进行改进：

### 1. 高性能任务队列管理器

**新增组件：** `TaskQueueManager`

**主要特性：**
- ✅ 按优先级分组的任务队列
- ✅ 信号量控制并发数量
- ✅ 线程池处理CPU密集型任务
- ✅ 自动重试机制
- ✅ 任务超时控制
- ✅ 详细的统计信息

**配置参数：**
```python
# 在 settings.py 中配置
max_concurrent_tasks: int = 20  # 最大并发任务数
task_queue_size: int = 1000     # 任务队列大小
worker_count: int = 5           # 工作者数量
```

### 2. 连接池管理器

**新增组件：** `ConnectionPoolManager`

**主要特性：**
- ✅ Redis 连接池优化
- ✅ MongoDB 连接池优化
- ✅ 连接健康检查
- ✅ 自动连接恢复
- ✅ 连接统计监控

**配置参数：**
```python
# Redis 连接池配置
redis_pool_size: int = 20       # Redis 连接池大小
redis_timeout: int = 5          # Redis 操作超时

# MongoDB 连接池配置
pool_min: int = 5               # 最小连接数
pool_max: int = 50              # 最大连接数
```

### 3. gRPC 调用优化

**优化内容：**
- ✅ 减少 gRPC 超时时间（60s → 30s）
- ✅ 增加重试机制
- ✅ 异步非阻塞调用

**配置参数：**
```python
grpc_timeout: int = 30          # gRPC 超时时间
grpc_retry_count: int = 3       # gRPC 重试次数
```

## 📊 性能监控 API

新增性能监控接口，实时查看系统状态：

### 1. 获取性能指标
```http
GET /api/v1/performance/metrics
```

**响应内容：**
- 系统资源使用情况（CPU、内存、磁盘）
- 任务队列统计信息
- 连接池状态
- 优化建议

### 2. 系统健康检查
```http
GET /api/v1/performance/health
```

**响应内容：**
- 整体健康状态
- 各组件状态详情
- 发现的问题列表

### 3. 任务队列统计
```http
GET /api/v1/performance/task-queue/stats
```

### 4. 连接池统计
```http
GET /api/v1/performance/connection-pools/stats
```

### 5. 系统优化
```http
POST /api/v1/performance/optimize
```

## 🔧 使用方法

### 1. 启动优化后的服务

服务启动时会自动初始化：
- 任务队列管理器
- 连接池管理器
- 性能监控

### 2. 提交任务到队列

```python
from app.services.task_queue_manager import task_queue_manager, TaskPriority

# 提交高优先级任务
await task_queue_manager.submit_task(
    task_id="my_task_001",
    task_func=my_async_function,
    arg1, arg2,
    priority=TaskPriority.HIGH,
    timeout=120.0,
    max_retries=3,
    kwarg1="value1"
)
```

### 3. 使用连接池

```python
from app.services.connection_pool_manager import connection_pool_manager

# 使用 Redis 连接池
async with connection_pool_manager.get_redis_connection(redis_url) as redis_client:
    await redis_client.set("key", "value")

# 使用 MongoDB 连接池
async with connection_pool_manager.get_mongo_connection(mongo_url, "db_name") as db:
    await db.collection.find_one({"_id": "123"})
```

## 📈 性能提升预期

| 指标 | 优化前 | 优化后 | 提升 |
|------|--------|--------|------|
| 任务处理延迟 | 100-500ms | 10-50ms | **10x** |
| 并发处理能力 | 5个任务/时间 | 20个任务/时间 | **4x** |
| 连接复用率 | 低 | 高 | **显著提升** |
| 系统响应性 | 阻塞式 | 非阻塞式 | **显著提升** |
| 错误恢复能力 | 单点失败 | 自动重试 | **更稳定** |

## ⚙️ 配置调优建议

### 1. 根据硬件配置调整参数

**CPU 核心数较多的服务器：**
```python
max_concurrent_tasks = CPU核心数 * 2
worker_count = CPU核心数
thread_pool_size = CPU核心数
```

**内存较大的服务器：**
```python
task_queue_size = 2000
pool_max = 100
redis_pool_size = 50
```

### 2. 根据业务负载调整

**高并发场景：**
```python
max_concurrent_tasks = 50
worker_count = 10
redis_pool_size = 30
```

**低延迟要求：**
```python
grpc_timeout = 15
redis_timeout = 3
max_retries = 1
```

## 🔍 监控和调试

### 1. 查看实时性能指标

访问性能监控接口，观察：
- CPU 和内存使用率
- 任务队列积压情况
- 连接池使用情况
- 失败任务比例

### 2. 日志监控

关注以下日志：
```
任务队列管理器已启动，5 个工作者
Redis连接池已初始化
MongoDB连接池已初始化
工作者 worker-0 开始执行任务: task_001
任务执行成功: task_001
```

### 3. 告警设置

建议设置以下告警：
- CPU 使用率 > 80%
- 内存使用率 > 85%
- 任务失败率 > 10%
- 队列积压 > 100个任务

## 🚨 注意事项

1. **渐进式部署**：建议先在测试环境验证，再逐步部署到生产环境
2. **参数调优**：根据实际负载情况调整并发参数
3. **监控观察**：部署后密切观察系统性能指标
4. **备份方案**：保留原有的任务处理逻辑作为备份

## 🔄 回滚方案

如果遇到问题，可以通过以下方式回滚：

1. 注释掉任务队列相关代码
2. 恢复原有的 `asyncio.create_task()` 调用
3. 使用原有的连接方式

## 📞 技术支持

如有问题，请查看：
1. 性能监控 API 的详细信息
2. 系统日志中的错误信息
3. 任务队列和连接池的统计数据
