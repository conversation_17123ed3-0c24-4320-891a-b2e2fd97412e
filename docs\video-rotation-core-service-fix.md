# 视频旋转功能Core服务选择修复

## 问题描述

视频旋转功能虽然后端API已经支持Core服务选择，但前端实现存在问题：
1. 使用原生 `fetch` 而不是封装的API函数
2. 没有传递用户选择的Core服务ID参数
3. 缺少统一的API接口定义

## 问题分析

### 后端API状态 ✅

后端 `rotate_videos` API已经正确实现了Core服务选择：

```python
@router.post("/rotate-videos", response_model=VideoRotationResponse)
async def rotate_videos(
    request: VideoRotationRequest,
    core_service_id: Optional[str] = Query(None, description="Core服务ID")
):
    # 根据core_service_id选择Core服务
    if core_service_id:
        core_client = get_core_client_by_service_id(core_service_id)
        logger.info(f"使用指定的Core服务: {core_service_id}")
    else:
        from app.core.grpc_client import get_core_client
        core_client = get_core_client()
        logger.info("使用默认Core服务")
```

### 前端实现问题 ❌

前端使用原生fetch调用，没有传递Core服务ID：

```javascript
// 问题代码
const response = await fetch('/api/v1/filesystem/rotate-videos', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  },
  body: JSON.stringify(requestData)
})
```

## 修复内容

### 1. 添加API接口定义

在 `frontend/src/api/social.ts` 中添加：

```typescript
// 视频旋转请求接口
export interface VideoRotationRequest {
  video_paths: string[]  // 要旋转的视频文件路径列表
  rotation_angle: number  // 旋转角度（90: 向右90度, -90: 向左90度, 180: 180度）
  output_quality: 'high' | 'medium' | 'low'  // 输出质量
  overwrite_original: boolean  // 是否覆盖原文件
  output_suffix: string  // 输出文件名后缀（当不覆盖原文件时使用）
}

// 视频旋转响应接口
export interface VideoRotationResponse {
  success: boolean
  error?: string
  results: Array<{
    original_path: string
    output_path: string
    success: boolean
    error_message?: string
    processing_time_ms: number
    original_file_size: number
    output_file_size: number
  }>
  successful_count: number
  failed_count: number
  total_processing_time_ms: number
}

// 旋转视频API函数
export const rotateVideos = (data: VideoRotationRequest, coreServiceId?: string) => {
  return request<VideoRotationResponse>({
    url: '/api/v1/filesystem/rotate-videos',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}
```

### 2. 修改前端调用方式

在 `frontend/src/views/doc/Manager.vue` 中：

**修复前**：
```javascript
// 使用原生fetch，没有传递Core服务ID
const response = await fetch('/api/v1/filesystem/rotate-videos', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  },
  body: JSON.stringify(requestData)
})
```

**修复后**：
```javascript
// 使用封装的API函数，传递选择的Core服务ID
const result = await rotateVideos(requestData, selectedCoreService.value || undefined)
```

### 3. 改进响应处理

添加了详细的响应日志和安全检查：

```javascript
console.log('视频旋转响应:', result)
console.log('响应类型:', typeof result)
console.log('响应键:', result ? Object.keys(result) : 'null')

// 视频旋转API返回{success: true, successful_count: ..., results: [...]}
if (result && result.success) {
  ElMessage.success(`视频旋转完成！成功 ${result.successful_count} 个，失败 ${result.failed_count} 个`)
  
  // 显示详细结果
  if (result.failed_count > 0 && result.results) {
    const failedFiles = result.results.filter((r: any) => !r.success)
    const failedMessages = failedFiles.map((r: any) => `${r.original_path}: ${r.error_message}`).join('\n')
    ElNotification({
      title: '部分文件旋转失败',
      message: failedMessages,
      type: 'warning',
      duration: 10000
    })
  }
} else {
  console.error('视频旋转失败，响应:', result)
  throw new Error(result?.error || '视频旋转失败')
}
```

### 4. 添加导入声明

在文件管理页面添加必要的导入：

```typescript
import {
  // ... 其他导入
  // 视频处理相关API
  rotateVideos,
  type VideoRotationRequest,
  type VideoRotationResponse,
} from '@/api/social'
```

## 技术实现细节

### Core服务选择流程

1. **用户选择Core服务**：在文件管理页面的下拉菜单中选择Core服务
2. **前端传递参数**：通过查询参数 `core_service_id` 传递给后端
3. **后端服务选择**：根据参数选择指定的Core服务或默认服务
4. **执行视频旋转**：使用选择的Core服务执行视频旋转操作

### 响应格式处理

视频旋转API返回包含 `success` 字段的响应，会被响应拦截器直接返回：

```javascript
// 响应格式
{
  success: true,
  successful_count: 2,
  failed_count: 0,
  results: [
    {
      original_path: "/path/to/video1.mp4",
      output_path: "/path/to/video1_rotated.mp4",
      success: true,
      processing_time_ms: 5000,
      original_file_size: 1024000,
      output_file_size: 1020000
    }
  ],
  total_processing_time_ms: 5000
}
```

## 使用方式

### 在文件管理页面

1. 用户选择要旋转的视频文件
2. 在Core服务下拉菜单中选择要使用的Core服务
3. 点击"视频处理" → "旋转视频"
4. 配置旋转参数（角度、质量、是否覆盖等）
5. 点击"开始旋转"
6. 系统使用选择的Core服务执行旋转操作

### 日志记录

修复后的实现包含详细的日志记录：

- 前端：记录请求数据、使用的Core服务、响应详情
- 后端：记录Core服务选择、处理进度、结果统计

## 测试建议

1. **Core服务选择测试**：
   - 选择不同的Core服务进行视频旋转
   - 验证后端日志中显示正确的Core服务ID
   - 确认旋转操作在正确的Core服务上执行

2. **功能测试**：
   - 测试不同旋转角度（90°、-90°、180°）
   - 测试不同输出质量设置
   - 测试覆盖原文件和生成新文件两种模式

3. **错误处理测试**：
   - 测试Core服务不可用时的错误处理
   - 测试文件不存在时的错误提示
   - 测试部分文件旋转失败时的详细错误信息

4. **响应处理测试**：
   - 验证成功时的消息显示
   - 验证失败时的错误信息显示
   - 验证文件列表的自动刷新

## 影响范围

这次修复主要影响：

1. **视频旋转功能**：现在使用选择的Core服务进行旋转
2. **API调用方式**：从原生fetch改为封装的API函数
3. **响应处理**：改进了错误处理和用户反馈
4. **代码质量**：添加了TypeScript类型定义和详细日志

## 后续改进建议

1. **统一API调用方式**：将其他使用原生fetch的功能也改为使用封装的API函数
2. **添加更多视频处理API**：为视频裁剪、加速等功能添加类似的API函数
3. **改进错误处理**：为所有视频处理功能添加统一的错误处理机制
4. **性能优化**：考虑添加进度显示和取消功能

## 相关功能状态

现在文件管理功能中支持Core服务选择的操作包括：

- ✅ 文件列表获取
- ✅ 文件删除
- ✅ 预发布到publishing文件夹
- ✅ 归档已发布文件
- ✅ 视频缩略图生成
- ✅ 视频预览信息获取
- ✅ 视频预览片段生成
- ✅ **视频旋转** 🆕 已修复
- 🔄 视频裁剪（需要类似修复）
- 🔄 视频加速（需要类似修复）
- 🔄 水印处理（需要添加Core服务支持）

修复后，视频旋转功能应该能够正确使用用户选择的Core服务了！
