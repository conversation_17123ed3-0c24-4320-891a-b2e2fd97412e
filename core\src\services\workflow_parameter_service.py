"""
工作流参数配置服务
提供工作流参数的动态配置、验证和模板化功能
"""

import logging
import json
import re
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

class ParameterType(Enum):
    """参数类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    SELECT = "select"
    MULTISELECT = "multiselect"
    FILE = "file"
    COLOR = "color"
    RANGE = "range"

@dataclass
class ParameterDefinition:
    """参数定义"""
    name: str
    type: ParameterType
    label: str
    description: str = ""
    default_value: Any = None
    required: bool = False
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    options: Optional[List[Dict[str, Any]]] = None
    validation_pattern: Optional[str] = None
    depends_on: Optional[str] = None
    group: str = "general"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['type'] = self.type.value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ParameterDefinition':
        """从字典创建实例"""
        data = data.copy()
        data['type'] = ParameterType(data['type'])
        return cls(**data)

class WorkflowParameterService:
    """工作流参数配置服务"""
    
    def __init__(self):
        self.parameter_definitions = {}
        self._init_default_parameters()
    
    def _init_default_parameters(self):
        """初始化默认参数定义"""
        # 图片生成参数
        image_params = [
            ParameterDefinition(
                name="positive_prompt",
                type=ParameterType.STRING,
                label="正向提示词",
                description="描述想要生成的内容",
                required=True,
                group="prompt"
            ),
            ParameterDefinition(
                name="negative_prompt",
                type=ParameterType.STRING,
                label="负向提示词",
                description="描述不想要的内容",
                default_value="low quality, blurry, bad anatomy",
                group="prompt"
            ),
            ParameterDefinition(
                name="width",
                type=ParameterType.INTEGER,
                label="宽度",
                description="图片宽度（像素）",
                default_value=1024,
                min_value=512,
                max_value=2048,
                group="size"
            ),
            ParameterDefinition(
                name="height",
                type=ParameterType.INTEGER,
                label="高度",
                description="图片高度（像素）",
                default_value=1024,
                min_value=512,
                max_value=2048,
                group="size"
            ),
            ParameterDefinition(
                name="steps",
                type=ParameterType.INTEGER,
                label="生成步数",
                description="扩散模型的采样步数",
                default_value=20,
                min_value=1,
                max_value=100,
                group="generation"
            ),
            ParameterDefinition(
                name="cfg_scale",
                type=ParameterType.FLOAT,
                label="CFG Scale",
                description="分类器自由引导强度",
                default_value=7.0,
                min_value=1.0,
                max_value=20.0,
                group="generation"
            ),
            ParameterDefinition(
                name="sampler",
                type=ParameterType.SELECT,
                label="采样器",
                description="选择采样算法",
                default_value="euler",
                options=[
                    {"value": "euler", "label": "Euler"},
                    {"value": "euler_a", "label": "Euler Ancestral"},
                    {"value": "dpm_2", "label": "DPM++ 2M"},
                    {"value": "dpm_2_a", "label": "DPM++ 2M Karras"},
                    {"value": "ddim", "label": "DDIM"}
                ],
                group="generation"
            ),
            ParameterDefinition(
                name="seed",
                type=ParameterType.INTEGER,
                label="随机种子",
                description="控制生成的随机性，-1为随机",
                default_value=-1,
                min_value=-1,
                max_value=2147483647,
                group="generation"
            )
        ]
        
        # 视频生成参数
        video_params = [
            ParameterDefinition(
                name="prompt",
                type=ParameterType.STRING,
                label="视频描述",
                description="描述想要生成的视频内容",
                required=True,
                group="prompt"
            ),
            ParameterDefinition(
                name="duration",
                type=ParameterType.INTEGER,
                label="视频时长",
                description="视频时长（秒）",
                default_value=4,
                min_value=2,
                max_value=10,
                group="video"
            ),
            ParameterDefinition(
                name="fps",
                type=ParameterType.INTEGER,
                label="帧率",
                description="每秒帧数",
                default_value=8,
                min_value=6,
                max_value=30,
                group="video"
            ),
            ParameterDefinition(
                name="motion_bucket_id",
                type=ParameterType.INTEGER,
                label="运动强度",
                description="控制视频中的运动幅度",
                default_value=127,
                min_value=1,
                max_value=255,
                group="video"
            )
        ]
        
        # 文本生成参数
        text_params = [
            ParameterDefinition(
                name="prompt",
                type=ParameterType.STRING,
                label="生成提示",
                description="描述要生成的文本内容",
                required=True,
                group="prompt"
            ),
            ParameterDefinition(
                name="max_tokens",
                type=ParameterType.INTEGER,
                label="最大长度",
                description="生成文本的最大token数",
                default_value=500,
                min_value=50,
                max_value=2000,
                group="generation"
            ),
            ParameterDefinition(
                name="temperature",
                type=ParameterType.FLOAT,
                label="创造性",
                description="控制生成文本的随机性",
                default_value=0.7,
                min_value=0.1,
                max_value=2.0,
                group="generation"
            ),
            ParameterDefinition(
                name="top_p",
                type=ParameterType.FLOAT,
                label="核采样",
                description="控制词汇选择的多样性",
                default_value=0.9,
                min_value=0.1,
                max_value=1.0,
                group="generation"
            )
        ]
        
        # 注册参数定义
        self.parameter_definitions["image"] = {param.name: param for param in image_params}
        self.parameter_definitions["video"] = {param.name: param for param in video_params}
        self.parameter_definitions["text"] = {param.name: param for param in text_params}
    
    def get_parameters_for_category(self, category: str) -> Dict[str, ParameterDefinition]:
        """获取指定类别的参数定义"""
        return self.parameter_definitions.get(category, {})
    
    def get_parameter_groups(self, category: str) -> Dict[str, List[ParameterDefinition]]:
        """按组获取参数定义"""
        params = self.get_parameters_for_category(category)
        groups = {}
        
        for param in params.values():
            group = param.group
            if group not in groups:
                groups[group] = []
            groups[group].append(param)
        
        return groups
    
    def validate_parameters(self, category: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数值"""
        param_defs = self.get_parameters_for_category(category)
        validated = {}
        errors = []
        
        # 检查必需参数
        for name, param_def in param_defs.items():
            if param_def.required and name not in parameters:
                errors.append(f"缺少必需参数: {param_def.label} ({name})")
        
        # 验证每个参数
        for name, value in parameters.items():
            if name not in param_defs:
                continue  # 忽略未定义的参数
            
            param_def = param_defs[name]
            
            try:
                validated_value = self._validate_single_parameter(param_def, value)
                validated[name] = validated_value
            except ValueError as e:
                errors.append(f"参数 {param_def.label} ({name}) 验证失败: {str(e)}")
        
        # 添加默认值
        for name, param_def in param_defs.items():
            if name not in validated and param_def.default_value is not None:
                validated[name] = param_def.default_value
        
        return {
            "valid": len(errors) == 0,
            "parameters": validated,
            "errors": errors
        }
    
    def _validate_single_parameter(self, param_def: ParameterDefinition, value: Any) -> Any:
        """验证单个参数"""
        if value is None:
            if param_def.required:
                raise ValueError("参数不能为空")
            return param_def.default_value
        
        # 类型验证
        if param_def.type == ParameterType.STRING:
            value = str(value)
            if param_def.validation_pattern:
                if not re.match(param_def.validation_pattern, value):
                    raise ValueError("格式不正确")
        
        elif param_def.type == ParameterType.INTEGER:
            value = int(value)
            if param_def.min_value is not None and value < param_def.min_value:
                raise ValueError(f"值不能小于 {param_def.min_value}")
            if param_def.max_value is not None and value > param_def.max_value:
                raise ValueError(f"值不能大于 {param_def.max_value}")
        
        elif param_def.type == ParameterType.FLOAT:
            value = float(value)
            if param_def.min_value is not None and value < param_def.min_value:
                raise ValueError(f"值不能小于 {param_def.min_value}")
            if param_def.max_value is not None and value > param_def.max_value:
                raise ValueError(f"值不能大于 {param_def.max_value}")
        
        elif param_def.type == ParameterType.BOOLEAN:
            value = bool(value)
        
        elif param_def.type == ParameterType.SELECT:
            if param_def.options:
                valid_values = [opt["value"] for opt in param_def.options]
                if value not in valid_values:
                    raise ValueError(f"值必须是以下之一: {', '.join(valid_values)}")
        
        return value
    
    def apply_parameters_to_workflow(self, workflow: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """将参数应用到工作流模板"""
        workflow_str = json.dumps(workflow)
        
        # 替换参数占位符
        for name, value in parameters.items():
            placeholder = f"{{{name}}}"
            workflow_str = workflow_str.replace(placeholder, str(value))
        
        return json.loads(workflow_str)

# 全局参数服务实例
_parameter_service = None

def get_parameter_service() -> WorkflowParameterService:
    """获取参数服务实例"""
    global _parameter_service
    
    if _parameter_service is None:
        _parameter_service = WorkflowParameterService()
    
    return _parameter_service
